# CDS后端JAVA开发规约
## 一、模块划分
> 模块的划分有以下几种情况：

1. 基础的工具类，通用的数据传输类
2. 基于数据的，提供服务的微服务应用
3. 基于业务的，通过调用微服务组装接口的业务应用

> 模块可以当前模块主要业务命名，也可用概括单词来命名。
****
### 1.1 微服务层模块划分及内容说明：
> 微服务应用，需划分子模块，api层和service层，api层供其他模块引入，service层完成业务逻辑及数据操作。

#### 1.1.1 API层说明
1.包名：基于公共包`com.danding.cds`，再加上自己的`业务名称.api`，api包下，再根据类的作用进行划分，例如`.dto` `.enums` `.service`。

* dto：数据传输对象，用于存储API接口层的数据传输对象，包含提交及返回两种数据类，类名格式不做约束，表达清楚数据传输目的即可。
* enums：枚举类，类名格式`名词Enum.java`
* service：支持Dubbo调用的Service接口，类名格式`业务Service.java`

2.示例：见图[规约示例/API模块说明.jpg]
****
#### 1.1.2 实现层说明
1.包名：基于公共包`com.danding.cds`，再加上自己的`业务名称.impl`，api包下，再根据类的作用进行划分，例如`.entity` `.mapper` `.service`。

* entity：数据库持久化对象，类名格式`业务DO.java`
* mapper：数据库操作类，类名格式`业务Mapper.java`
* service：服务实现类，类名格式`业务ServiceImpl.java`

2.resources：创建`mapper`文件，用于存储数据库映射文件,映射文件命名格式为`业务Mapper.xml`

3.启动类：`App模块名Server.java`

4.示例：见图[规约示例/服务模块说明.jpg]
### 1.2 业务应用层模块划分及内容说明：
#### 1.1.1 web模块
1.包名：基于公共包`com.danding.cds`，再加上自己的`web.业务名称`，api包下，再根据类的作用进行划分，例如`.vo`。
* vo：View-Model，视图对象，类名格式`业务VO.java`

2.控制器：业务`Controller.java`

3.启动类：`WebApplication.java`
#### 1.1.1 web-swagger模块
该模块是为了快读生成API文档创建的，它只是在web模块上额外加了swagger的支持，内部没有什么东西，仅在需要生成文档的时候启动就行。

## 二、依赖管理
* 一方库: 本工程内部子项目模块依赖的库（jar 包）。
* 二方库: 公司内部发布到中央仓库，可供公司内部其它应用依赖的库（jar 包）。
* 三方库: 公司之外的开源库（jar 包）

1.版本管理：版本都交由主工程进行管理，子工程仅引用依赖。

2.引用关系：

**一方库：**
* API层引用common
* 服务层引用API
* Web层引用API
* swagger引用web

**二方库：**
* 通用层引用common-api
* 服务层引用mybatis-component、dubbo-seata-component、boost-component
* web层引入web-component

**三方库：**
* 通用层引入通用工具类、swagger注解
* 服务层引用web-start、test-start[?这里web-start存疑，可能改成dubbo-start]
* web层引入dubbo、apollo、web-start、xxl

## 三、数据库管理
### 3.1 数据库配置
字符类型：

`utf8mb4 -- UTF-8 Unicode `

`utf8mb4_general_ci`

数据库连接：
```
spring.shardingsphere.datasource.names = cds
spring.shardingsphere.datasource.cds.type = com.alibaba.druid.pool.DruidDataSource
spring.shardingsphere.datasource.cds.driver-class-name = com.mysql.cj.jdbc.Driver
spring.shardingsphere.datasource.cds.url = ****************************************************************************
spring.shardingsphere.datasource.cds.username = root
spring.shardingsphere.datasource.cds.password = dd123456
```
PS：需指定字符编码 `useUnicode=true&characterEncoding=utf-8`
### 3.2 数据表配置
* 数据库建表语句，先统一在doc/schema.sql中管理
* 后续表变更，在doc/db_change.sql中管理
* 建表语句
```
CREATE TABLE `ccs_company` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(40) DEFAULT '' COMMENT '企业名称',
  `code` varchar(40) DEFAULT '' COMMENT '海关十位备案编码',
  `qualify_json` varchar(40) DEFAULT '' COMMENT '企业资质列表',
  `district_json` varchar(1024) DEFAULT '' COMMENT '地方关区信息',
  `extra_json` text DEFAULT NULL COMMENT 'json储存的其他属性键值对',
  `remark` varchar(516) DEFAULT '' COMMENT '备注',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_by` int(11) DEFAULT NULL,
  `update_by` int(11) DEFAULT NULL,
  `deleted` tinyint(1) DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='企业表';
```
PS：
1. 需指定字符编码 CHARSET=utf8mb4

2. 每个表固定6个字段必须创建 `id` `create_time` `update_time` `create_by` `update_by` `deleted`
## 四、开发说明
### 4.1 VO渲染
已开启VO自动渲染，请求url前面自动添加`/xhr`，返回结果及异常自动封装`Response`。