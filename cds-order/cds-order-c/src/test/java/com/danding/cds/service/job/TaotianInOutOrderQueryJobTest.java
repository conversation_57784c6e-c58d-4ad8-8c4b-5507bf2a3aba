package com.danding.cds.service.job;

import com.alibaba.fastjson.JSON;
import com.danding.cds.invenorder.api.dto.InventoryOrderInfoDTO;
import com.danding.cds.invenorder.api.service.InventoryOrderInfoService;
import com.danding.cds.invenorder.impl.entity.InventoryOrderInfoDO;
import com.danding.cds.v2.service.base.InventoryOrderInfoBaseService;
import com.xxl.job.core.biz.model.ReturnT;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * TaotianInOutOrderQueryJob 测试类
 */
@ExtendWith(MockitoExtension.class)
class TaotianInOutOrderQueryJobTest {

    @Mock
    private InventoryOrderInfoService inventoryOrderInfoService;

    @Mock
    private InventoryOrderInfoBaseService inventoryOrderInfoBaseService;

    @InjectMocks
    private TaotianInOutOrderQueryJob taotianInOutOrderQueryJob;

    private TaotianInOutOrderQueryJob.JobParam jobParam;

    @BeforeEach
    void setUp() {
        jobParam = new TaotianInOutOrderQueryJob.JobParam();
        jobParam.setCompanyIds(Arrays.asList(1001L, 1002L));
        jobParam.setChannels(Arrays.asList(4));
        jobParam.setWebhook("https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=test");
        jobParam.setPhoneList(Arrays.asList("13800138000"));
    }

    @Test
    void testExecute_Success() throws Exception {
        // 准备测试数据
        String param = JSON.toJSONString(jobParam);
        
        // Mock查询结果
        List<InventoryOrderInfoDO> mockDOList = createMockInventoryOrderDOList();
        when(inventoryOrderInfoBaseService.selectByExample(any(Example.class)))
                .thenReturn(mockDOList);

        // 执行测试
        ReturnT<String> result = taotianInOutOrderQueryJob.execute(param);

        // 验证结果
        assertEquals(ReturnT.SUCCESS_CODE, result.getCode());
        verify(inventoryOrderInfoBaseService, times(1)).selectByExample(any(Example.class));
    }

    @Test
    void testExecute_EmptyParam() throws Exception {
        // 测试空参数
        ReturnT<String> result = taotianInOutOrderQueryJob.execute("");
        assertEquals(ReturnT.FAIL_CODE, result.getCode());
    }

    @Test
    void testExecute_InvalidParam() throws Exception {
        // 测试无效参数
        ReturnT<String> result = taotianInOutOrderQueryJob.execute("invalid json");
        assertEquals(ReturnT.FAIL_CODE, result.getCode());
    }

    @Test
    void testExecute_EmptyCompanyIds() throws Exception {
        // 测试空的企业ID列表
        jobParam.setCompanyIds(new ArrayList<>());
        String param = JSON.toJSONString(jobParam);
        
        ReturnT<String> result = taotianInOutOrderQueryJob.execute(param);
        assertEquals(ReturnT.SUCCESS_CODE, result.getCode());
        
        // 验证没有执行查询
        verify(inventoryOrderInfoBaseService, never()).selectByExample(any(Example.class));
    }

    @Test
    void testExecute_WithException() throws Exception {
        // 模拟查询异常
        String param = JSON.toJSONString(jobParam);
        when(inventoryOrderInfoBaseService.selectByExample(any(Example.class)))
                .thenThrow(new RuntimeException("Database error"));

        ReturnT<String> result = taotianInOutOrderQueryJob.execute(param);
        assertEquals(ReturnT.FAIL_CODE, result.getCode());
    }

    /**
     * 创建模拟的清关单DO列表
     */
    private List<InventoryOrderInfoDO> createMockInventoryOrderDOList() {
        List<InventoryOrderInfoDO> list = new ArrayList<>();
        
        InventoryOrderInfoDO order1 = new InventoryOrderInfoDO();
        order1.setId(1L);
        order1.setInveCustomsSn("TEST001");
        order1.setInveCompanyId(1001L);
        order1.setChannel(4);
        order1.setInveBusinessType("ONELINE_IN");
        order1.setRemark("TEST_REMARK_001");
        order1.setDeleted(false);
        list.add(order1);
        
        InventoryOrderInfoDO order2 = new InventoryOrderInfoDO();
        order2.setId(2L);
        order2.setInveCustomsSn("TEST002");
        order2.setInveCompanyId(1002L);
        order2.setChannel(4);
        order2.setInveBusinessType("ONELINE_OUT");
        order2.setRemark("TEST_REMARK_002");
        order2.setDeleted(false);
        list.add(order2);
        
        return list;
    }
}
