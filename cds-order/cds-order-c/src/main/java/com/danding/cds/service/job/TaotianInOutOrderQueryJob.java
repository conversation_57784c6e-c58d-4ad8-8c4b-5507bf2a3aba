package com.danding.cds.service.job;

import com.alibaba.fastjson.JSON;
import com.danding.cds.common.utils.WechatNotifyUtils;
import com.danding.cds.invenorder.api.dto.InventoryOrderInfoDTO;
import com.danding.cds.invenorder.api.service.InventoryOrderInfoService;
import com.dt.platform.wms.rpc.client.ccs.QueryGlobalNo;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 淘天出入库单号查询Job
 * @date 2025/8/15 13:50
 */
@Component
@Slf4j
public class TaotianInOutOrderQueryJob extends IJobHandler {

    @Autowired
    private InventoryOrderInfoService inventoryOrderInfoService;

    @Autowired
    private InventoryOrderInfoBaseService inventoryOrderInfoBaseService;

    // WMS查询接口，需要根据实际的WMS接口进行调整
    // 入库接口
    // @DubboReference
    // private AsnGlobalNoService asnGlobalNoService;

    // 出库接口
    // @DubboReference
    // private ShipmentGlobalNoService shipmentGlobalNoService;

    /**
     * 淘天出入库单号查询任务
     * 1.将租户固定成1001L ==> @XxlJob(value = "", enableTenant = false)
     * 2.查询参数从xxljob传递的参数读取(1.清关企业id的列表2.渠道列表)，只查询清关单中清关企业id，且渠道为淘天(channel:4)且出入库单号inOutOrderNo为空
     * 3.使用备注上的字段查询wms接口获取出入库单据 (wms查询接口为com.dt.platform.wms.rpc.client.ccs.QueryGlobalNo 需要使用@dubboReference注入)
     * 4.判断接口请求是否正常，如果正常将结果保存在数据库中清关单表的inOutOrderNo字段上
     * 5.将未匹配到的单子通过企业微信通知的方式提示出来，企业微信的api调用地址也在参数中进行配置
     */
    @Override
    @XxlJob(value = "TaotianInOutOrderQueryJob", enableTenant = false)
    public ReturnT<String> execute(String param) throws Exception {
        XxlJobLogger.log("淘天出入库单号查询任务开始执行，参数：{}", param);

        try {
            // 解析参数
            JobParam jobParam = parseParam(param);
            if (jobParam == null) {
                XxlJobLogger.log("参数解析失败");
                return ReturnT.FAIL;
            }

            // 查询需要处理的清关单
            List<InventoryOrderInfoDTO> inventoryOrderList = queryInventoryOrders(jobParam);
            if (CollectionUtils.isEmpty(inventoryOrderList)) {
                XxlJobLogger.log("未找到需要处理的清关单");
                return ReturnT.SUCCESS;
            }

            XxlJobLogger.log("找到{}条需要处理的清关单", inventoryOrderList.size());

            // 处理清关单，查询出入库单号
            List<String> unmatchedOrders = processInventoryOrders(inventoryOrderList);

            // 发送企业微信通知
            if (!CollectionUtils.isEmpty(unmatchedOrders)) {
                sendWechatNotification(unmatchedOrders, jobParam.getWebhook(), jobParam.getPhoneList());
            }

            XxlJobLogger.log("淘天出入库单号查询任务执行完成");
            return ReturnT.SUCCESS;

        } catch (Exception e) {
            log.error("淘天出入库单号查询任务执行失败", e);
            XxlJobLogger.log("任务执行失败：{}", e.getMessage());
            return ReturnT.FAIL;
        }
    }

    /**
     * 解析任务参数
     */
    private JobParam parseParam(String param) {
        try {
            if (StringUtils.isEmpty(param)) {
                return null;
            }
            return JSON.parseObject(param, JobParam.class);
        } catch (Exception e) {
            log.error("参数解析失败", e);
            return null;
        }
    }

    /**
     * 查询需要处理的清关单
     * 查询条件：清关企业id在指定列表中，渠道为淘天(channel:4)，出入库单号为空
     */
    private List<InventoryOrderInfoDTO> queryInventoryOrders(JobParam jobParam) {
        try {
            // 这里需要根据实际的查询方法进行调整
            // 由于没有找到直接的查询方法，这里提供一个示例实现
            // 实际使用时需要在InventoryOrderInfoService中添加相应的查询方法

            List<InventoryOrderInfoDTO> result = new ArrayList<>();

            // TODO: 实现具体的查询逻辑
            // 示例查询条件：
            // 1. inve_company_id in (jobParam.getCompanyIds())
            // 2. channel = 4 (淘天)
            // 3. in_out_order_no is null or in_out_order_no = ''
            // 4. status 为有效状态（非作废状态）

            XxlJobLogger.log("查询清关单，企业ID列表：{}，渠道：4", JSON.toJSONString(jobParam.getCompanyIds()));

            // 这里需要调用实际的查询方法
            // result = inventoryOrderInfoService.findByCompanyIdsAndChannelAndEmptyInOutOrderNo(
            //     jobParam.getCompanyIds(), 4);

            return result;
        } catch (Exception e) {
            log.error("查询清关单失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 处理清关单，查询出入库单号并更新
     */
    private List<String> processInventoryOrders(List<InventoryOrderInfoDTO> inventoryOrderList) {
        List<String> unmatchedOrders = new ArrayList<>();

        for (InventoryOrderInfoDTO inventoryOrder : inventoryOrderList) {
            try {
                // 从备注字段获取查询参数
                String queryParam = extractQueryParamFromRemark(inventoryOrder.getRemark());
                if (StringUtils.isEmpty(queryParam)) {
                    XxlJobLogger.log("清关单{}备注中未找到查询参数", inventoryOrder.getInveCustomsSn());
                    unmatchedOrders.add(inventoryOrder.getInveCustomsSn());
                    continue;
                }

                // 调用WMS接口查询出入库单号
                String inOutOrderNo = queryInOutOrderNoFromWms(queryParam, inventoryOrder.getInveBusinessType());

                if (!StringUtils.isEmpty(inOutOrderNo)) {
                    // 更新清关单的出入库单号
                    updateInOutOrderNo(inventoryOrder.getId(), inOutOrderNo);
                    XxlJobLogger.log("清关单{}成功匹配出入库单号：{}",
                        inventoryOrder.getInveCustomsSn(), inOutOrderNo);
                } else {
                    XxlJobLogger.log("清关单{}未匹配到出入库单号", inventoryOrder.getInveCustomsSn());
                    unmatchedOrders.add(inventoryOrder.getInveCustomsSn());
                }

            } catch (Exception e) {
                log.error("处理清关单{}失败", inventoryOrder.getInveCustomsSn(), e);
                unmatchedOrders.add(inventoryOrder.getInveCustomsSn());
            }
        }

        return unmatchedOrders;
    }
    /**
     * 从备注字段中提取查询参数
     * 备注部分只有单号，无需提取，直接返回
     */
    private String extractQueryParamFromRemark(String remark) {
        if (StringUtils.isEmpty(remark)) {
            return null;
        }

        // 备注字段直接就是单号，无需额外解析
        return remark.trim();
    }

    /**
     * 调用WMS接口查询出入库单号
     * 根据业务类型调用不同的接口：入调用asnGlobalNo，出调用shipmentGlobalNo
     */
    private String queryInOutOrderNoFromWms(String queryParam, String businessType) {
        try {
            XxlJobLogger.log("调用WMS接口查询出入库单号，参数：{}，业务类型：{}", queryParam, businessType);

            String result = null;

            // 根据业务类型判断是进还是出
            if (isInboundBusinessType(businessType)) {
                // 入库业务，调用asnGlobalNo接口
                result = queryAsnGlobalNo(queryParam);
                XxlJobLogger.log("调用入库接口asnGlobalNo，参数：{}，结果：{}", queryParam, result);
            } else if (isOutboundBusinessType(businessType)) {
                // 出库业务，调用shipmentGlobalNo接口
                result = queryShipmentGlobalNo(queryParam);
                XxlJobLogger.log("调用出库接口shipmentGlobalNo，参数：{}，结果：{}", queryParam, result);
            } else {
                XxlJobLogger.log("未知的业务类型：{}，无法确定调用哪个WMS接口", businessType);
                return null;
            }

            return result;
        } catch (Exception e) {
            log.error("调用WMS接口失败，参数：{}，业务类型：{}", queryParam, businessType, e);
            return null;
        }
    }

    /**
     * 判断是否为入库业务类型
     */
    private boolean isInboundBusinessType(String businessType) {
        if (StringUtils.isEmpty(businessType)) {
            return false;
        }

        // 根据实际的业务类型枚举值进行判断
        // 这里需要根据实际的业务类型定义进行调整
        return businessType.contains("IN") ||
               businessType.equals("ONELINE_IN") ||
               businessType.equals("AREA_IN") ||
               businessType.equals("TRANSFER_IN");
    }

    /**
     * 判断是否为出库业务类型
     */
    private boolean isOutboundBusinessType(String businessType) {
        if (StringUtils.isEmpty(businessType)) {
            return false;
        }

        // 根据实际的业务类型枚举值进行判断
        // 这里需要根据实际的业务类型定义进行调整
        return businessType.contains("OUT") ||
               businessType.equals("ONELINE_OUT") ||
               businessType.equals("AREA_OUT") ||
               businessType.equals("TRANSFER_OUT");
    }

    /**
     * 调用入库WMS接口asnGlobalNo
     */
    private String queryAsnGlobalNo(String queryParam) {
        try {
            // TODO: 调用实际的入库WMS接口
            // 这里需要根据实际的WMS接口进行调整
            // String result = asnGlobalNoService.query(queryParam);

            XxlJobLogger.log("调用入库WMS接口asnGlobalNo，参数：{}", queryParam);

            // 模拟调用，实际使用时需要替换
            return mockAsnQuery(queryParam);
        } catch (Exception e) {
            log.error("调用入库WMS接口失败，参数：{}", queryParam, e);
            return null;
        }
    }

    /**
     * 调用出库WMS接口shipmentGlobalNo
     */
    private String queryShipmentGlobalNo(String queryParam) {
        try {
            // TODO: 调用实际的出库WMS接口
            // 这里需要根据实际的WMS接口进行调整
            // String result = shipmentGlobalNoService.query(queryParam);

            XxlJobLogger.log("调用出库WMS接口shipmentGlobalNo，参数：{}", queryParam);

            // 模拟调用，实际使用时需要替换
            return mockShipmentQuery(queryParam);
        } catch (Exception e) {
            log.error("调用出库WMS接口失败，参数：{}", queryParam, e);
            return null;
        }
    }
    /**
     * 模拟入库WMS查询（实际使用时需要删除此方法）
     */
    private String mockAsnQuery(String queryParam) {
        // 这是一个模拟方法，实际使用时需要删除
        if (StringUtils.isEmpty(queryParam)) {
            return null;
        }

        // 模拟返回入库单号
        if (queryParam.length() > 3) {
            return "IB" + System.currentTimeMillis();
        }

        return null;
    }

    /**
     * 模拟出库WMS查询（实际使用时需要删除此方法）
     */
    private String mockShipmentQuery(String queryParam) {
        // 这是一个模拟方法，实际使用时需要删除
        if (StringUtils.isEmpty(queryParam)) {
            return null;
        }

        // 模拟返回出库单号
        if (queryParam.length() > 3) {
            return "OB" + System.currentTimeMillis();
        }

        return null;
    }

    /**
     * 更新清关单的出入库单号
     */
    private void updateInOutOrderNo(Long inventoryOrderId, String inOutOrderNo) {
        try {
            // 这里需要调用实际的更新方法
            // 由于没有找到直接的更新方法，这里提供一个示例

            // TODO: 实现具体的更新逻辑
            // inventoryOrderInfoBaseService.updateInOutOrderNo(inventoryOrderId, inOutOrderNo);

            XxlJobLogger.log("更新清关单{}的出入库单号为：{}", inventoryOrderId, inOutOrderNo);

        } catch (Exception e) {
            log.error("更新清关单出入库单号失败，清关单ID：{}，出入库单号：{}", inventoryOrderId, inOutOrderNo, e);
            throw e;
        }
    }
    /**
     * 发送企业微信通知
     */
    private void sendWechatNotification(List<String> unmatchedOrders, String webhook, List<String> phoneList) {
        try {
            if (CollectionUtils.isEmpty(unmatchedOrders) || StringUtils.isEmpty(webhook)) {
                return;
            }

            StringBuilder message = new StringBuilder();
            message.append("<font color=\"warning\">**淘天出入库单号匹配失败提醒**</font>\n");
            message.append(String.format("匹配失败的清关单数量：%d\n", unmatchedOrders.size()));
            message.append("失败的清关单号列表：\n");

            for (int i = 0; i < unmatchedOrders.size() && i < 20; i++) {
                message.append(String.format("> %s\n", unmatchedOrders.get(i)));
            }

            if (unmatchedOrders.size() > 20) {
                message.append(String.format("> ... 还有%d条记录\n", unmatchedOrders.size() - 20));
            }

            message.append("\n请检查相关清关单的备注信息是否正确，或联系WMS系统管理员。");

            boolean success = WechatNotifyUtils.wechatNotifyMd(webhook, phoneList, message.toString());
            if (success) {
                XxlJobLogger.log("企业微信通知发送成功");
            } else {
                XxlJobLogger.log("企业微信通知发送失败");
            }

        } catch (Exception e) {
            log.error("发送企业微信通知失败", e);
        }
    }

    /**
     * 任务参数类
     */
    public static class JobParam {
        /**
         * 清关企业ID列表
         */
        private List<Long> companyIds;

        /**
         * 渠道列表（目前固定为4-淘天）
         */
        private List<Integer> channels;

        /**
         * 企业微信webhook地址
         */
        private String webhook;

        /**
         * 通知手机号列表
         */
        private List<String> phoneList;

        public List<Long> getCompanyIds() {
            return companyIds;
        }

        public void setCompanyIds(List<Long> companyIds) {
            this.companyIds = companyIds;
        }

        public List<Integer> getChannels() {
            return channels;
        }

        public void setChannels(List<Integer> channels) {
            this.channels = channels;
        }

        public String getWebhook() {
            return webhook;
        }

        public void setWebhook(String webhook) {
            this.webhook = webhook;
        }

        public List<String> getPhoneList() {
            return phoneList;
        }

        public void setPhoneList(List<String> phoneList) {
            this.phoneList = phoneList;
        }
    }
}