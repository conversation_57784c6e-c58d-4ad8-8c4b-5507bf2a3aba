# TaotianInOutOrderQueryJob 使用说明

## 功能描述

淘天出入库单号查询Job，用于自动查询并更新清关单的出入库单号。

## 主要功能

1. **租户固定**: 使用 `@XxlJob(enableTenant = false)` 将租户固定为1001L
2. **条件查询**: 查询清关企业ID在指定列表中，渠道为淘天(channel:4)，且出入库单号为空的清关单
3. **WMS接口调用**:
   - 备注字段直接包含单号，无需额外解析
   - 根据清关单inOutFlag字段使用InventoryInOutEnum枚举判断进出库：IN调用asnGlobalNo，OUT调用shipmentGlobalNo
4. **数据更新**: 将查询到的出入库单号更新到清关单表的inOutOrderNo字段
5. **异常通知**: 通过企业微信通知未匹配到的清关单

## 参数配置

任务参数为JSON格式：

```json
{
  "companyIds": [1001, 1002, 1003],
  "channels": [4],
  "webhook": "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=your-key",
  "phoneList": ["13800138000", "13900139000"]
}
```

### 参数说明

- `companyIds`: 清关企业ID列表
- `channels`: 渠道列表（目前固定为4-淘天）
- `webhook`: 企业微信机器人webhook地址
- `phoneList`: 需要@的手机号列表

## 待完善的部分

### 1. WMS接口集成

当前代码中WMS接口调用部分需要完善：

```java
// 需要取消注释并配置正确的WMS接口
// 入库接口
@DubboReference
private AsnGlobalNoService asnGlobalNoService;

// 出库接口
@DubboReference
private ShipmentGlobalNoService shipmentGlobalNoService;

// 在对应方法中调用实际接口
String asnResult = asnGlobalNoService.query(queryParam);  // 入库
String shipmentResult = shipmentGlobalNoService.query(queryParam);  // 出库
```

### 2. 清关单查询方法 ✅

已实现具体的查询逻辑，将查询逻辑移动到order服务中：

```java
// 在InventoryOrderInfoService中新增方法：
List<InventoryOrderInfoDTO> findTaotianEmptyInOutOrderNos(List<Long> companyIds);

// Job中调用服务方法：
List<InventoryOrderInfoDTO> result = inventoryOrderInfoService.findTaotianEmptyInOutOrderNos(jobParam.getCompanyIds());

// 查询条件：
// 1. 清关企业ID在指定列表中
// 2. 渠道为淘天(channel:4)
// 3. 出入库单号为空
// 4. 状态为有效状态（非作废状态）
// 5. 备注不为空（需要从备注中获取查询参数）
// 6. 逻辑删除标记为false
```

### 3. 出入库单号更新方法 ✅

已实现具体的更新逻辑，通过服务方法更新：

```java
// 先查询清关单信息
InventoryOrderInfoDTO inventoryOrderDTO = inventoryOrderInfoService.findById(inventoryOrderId);

// 更新出入库单号
inventoryOrderDTO.setInOutOrderNo(inOutOrderNo);
inventoryOrderInfoService.updateInventoryOrderInfoDTO(inventoryOrderDTO);
```

### 4. 出入库类型判断 ✅

使用InventoryInOutEnum枚举进行类型安全的判断：

```java
// 使用枚举判断出入库类型
InventoryInOutEnum inOutEnum = InventoryInOutEnum.getEnum(inOutFlag);

if (InventoryInOutEnum.IN.equals(inOutEnum)) {
    // 入库业务，调用asnGlobalNo接口
    result = queryAsnGlobalNo(queryParam);
} else if (InventoryInOutEnum.OUT.equals(inOutEnum)) {
    // 出库业务，调用shipmentGlobalNo接口
    result = queryShipmentGlobalNo(queryParam);
}
```

**枚举值说明**：
- `InventoryInOutEnum.IN` (code: "in"): 入区业务
- `InventoryInOutEnum.OUT` (code: "out"): 出区业务
- `InventoryInOutEnum.NULL` (code: ""): 空值

### 5. 备注字段格式

备注字段直接包含单号，无需特殊格式，代码会直接使用备注内容作为WMS查询参数。

## 部署配置

1. 在XXL-Job管理平台中创建任务
2. 设置任务名称为：`TaotianInOutOrderQueryJob`
3. 配置执行参数（JSON格式）
4. 设置执行周期（建议每小时执行一次）

## 日志监控

任务执行过程中会输出详细日志：
- 参数解析结果
- 查询到的清关单数量
- WMS接口调用结果
- 更新成功/失败的记录
- 企业微信通知发送结果

## 注意事项

1. 确保WMS接口的稳定性和可用性
2. 监控任务执行频率，避免对数据库造成过大压力
3. 定期检查企业微信通知是否正常发送
4. 关注未匹配清关单的数量趋势，及时处理异常情况

## 测试建议

1. 先在测试环境验证WMS接口调用
2. 测试企业微信通知功能
3. 验证数据库更新操作的正确性
4. 进行异常情况的测试（如WMS接口不可用等）
