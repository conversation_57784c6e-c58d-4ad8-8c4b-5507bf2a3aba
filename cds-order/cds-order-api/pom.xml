<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>cds-order</artifactId>
        <groupId>com.danding</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <!--                <version>3.1.5-SNAPSHOT</version>-->
    <!--            <version>2.0.5-RELEASE</version>-->
    <!--            <version>3.1.6-RELEASE</version>-->
    <modelVersion>4.0.0</modelVersion>
    <artifactId>cds-order-api</artifactId>
    <dependencies>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-common</artifactId>
            <!--            <version>3.1.0-RELEASE</version>-->
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-company-api</artifactId>
            <!--            <version>3.1.0-RELEASE</version>-->
        </dependency>
        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
            <version>1.8.3</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
    <build>
        <finalName>ccs-order-api</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>