package com.danding.cds.v2.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 清关单待办标记枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum InventoryOrderTodoTagEnums {

    NULL(1 >> 1, "无"),
    ENDORSEMENT_NOT_DELETED(1, "核注未删除"),
    TRANSIT_IN_NOT_GENERATE(1 << 1, "调入未生成"),
    TRANSIT_OUT_NOT_GENERATE(1 << 2, "调出未生成"),
    WAIT_CARRYOVER_DETAIL(1 << 3, "结转明细待确认"),
    UPDATE_ITEM_LIST_FAIL(1 << 4, "表体更新失败"),
    CREATE_ENDORSEMENT_OVERDUE(1 << 5, "建单超时");

    private final Integer code;
    private final String desc;

    public static List<Integer> getOrderTodoTags(Integer useTag) {
        List<Integer> orderTagList = new ArrayList<>();
        if (useTag == null || useTag == 0) {
            return orderTagList;
        }
        for (InventoryOrderTodoTagEnums value : InventoryOrderTodoTagEnums.values()) {
            // 判断下orderTag是否包含有数据
            Integer code = value.getCode();
            // 排除下没有用途的占位
            if (Objects.equals(code, 0)) {
                continue;
            }
            if ((code & useTag) == code) {
                orderTagList.add(code);
            }
        }
        return orderTagList;
    }

    public static List<String> getOrderTodoTagsDesc(Integer useTag) {
        List<String> orderTagList = new ArrayList<>();
        if (useTag == null || useTag == 0) {
            return orderTagList;
        }
        for (InventoryOrderTodoTagEnums value : InventoryOrderTodoTagEnums.values()) {
            // 判断下orderTag是否包含有数据
            Integer code = value.getCode();
            // 排除下没有用途的占位
            if (Objects.equals(code, 0)) {
                continue;
            }
            if ((code & useTag) == code) {
                orderTagList.add(value.getDesc());
            }
        }
        return orderTagList;
    }

    public static Integer remove(Integer orderTag, InventoryOrderTodoTagEnums enums) {
        if (Objects.isNull(orderTag)) {
            orderTag = 0;
        }
        Integer code = enums.getCode();
        if ((code & orderTag) == code) {
            orderTag -= code;
        }
        return orderTag;
    }

    public static Integer add(Integer orderTag, InventoryOrderTodoTagEnums enums) {
        if (Objects.isNull(orderTag)) {
            orderTag = 0;
        }
        Integer code = enums.getCode();
        if ((code & orderTag) != code) {
            orderTag += enums.getCode();
        }
        return orderTag;
    }

    public static Boolean contains(Integer orderTags, InventoryOrderTodoTagEnums enums) {
        if (Objects.isNull(orderTags) || orderTags.equals(0)) {
            return false;
        }
        return Objects.equals(enums.getCode(), orderTags & enums.getCode());
    }

    public static List<InventoryOrderTodoTagEnums> valuesV2() {
        return Arrays.stream(InventoryOrderTodoTagEnums.values()).filter(i ->
                !i.equals(InventoryOrderTodoTagEnums.NULL)
        ).collect(Collectors.toList());
    }
}
