package com.danding.cds.transwork.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: yousx
 * @Date: 2024/03/12
 * @Description:
 */
@AllArgsConstructor
@Getter
public enum TransOverlayTrayEnum {

    NULL("", ""),
    YES("1", "是"),
    NO("0", "否"),
    ;
    private final String code;
    private final String desc;

    public static TransOverlayTrayEnum getEnum(String code) {
        for (TransOverlayTrayEnum e : TransOverlayTrayEnum.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return NULL;
    }
}
