package com.danding.cds.v2.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BizDeclareFormItemEndprdTypeEnums {

    // 1：成品，2：残次品，3：边角料，4：副产品。外发加工专用，默认为空
    NULL("", ""),
    PRODUCT("1", "成品"),
    RESIDUAL("2", "残次品"),
    EDGE_MATERIAL("3", "边角料"),
    SUB_PRODUCT("4", "副产品");

    private final String code;
    private final String desc;
}
