package com.danding.cds.transwork.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel
public class TransWorkOrderAttachmentsDTO implements Serializable {

    /**
     * url
     */
    @ApiModelProperty(value = "url")
    private String url;

    /**
     * 提单文件
     */
    @ApiModelProperty(value = "文件名称")
    private String name;

    /**
     * 文件类型（MSDS=MSDS文件、BOL=提单文件）
     */
    @ApiModelProperty(value = "文件类型（MSDS=MSDS文件、BOL=提单文件）")
    private String attachmentType;

    @ApiModelProperty(value = "文件类型（MSDS=MSDS文件、BOL=提单文件）")
    private String attachmentTypeDesc;
}