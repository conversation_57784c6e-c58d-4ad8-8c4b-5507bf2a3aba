package com.danding.cds.cull.api.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * <AUTHOR>
 * @description
 * @date 2022/5/19
 */
@Data
public class CullOrderResVO implements Serializable {

    private String id;

    /**
     * 申报单号
     */
    private String declareOrderNo;

    /**
     * 海关清单编号
     */
    private String customsInventorySn;

    /**
     * 清单编码
     */
    private String inveSn;

    /**
     * 运单编号
     */
    private String customsLogisticsSn;

    /**
     * 快递
     */
    private Long expressId;

    /**
     * 快递desc
     */
    private String expressIdDesc;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 状态Desc
     */
    private String statusDesc;

    /**
     * 核注状态
     * @link: InventoryReviwStatus
     */
    private Integer reviewStatus;

    /**
     * 核注状态Desc
     * @link: InventoryReviwStatus
     */
    private String reviewStatusDesc;

    /**
     * 撤单完成0否1是
     */
    private Integer finishStatus;

    /**
     * 撤单完成0否1是Desc
     */
    private String finishStatusDesc;

    /**
     * 剔除时间|核注单的更新时间
     */
    private Date cullTime;

    /**
     * 账册id
     */
    private Long accountBookId;
    private String accountBookNo;



    /**
     * 区内企业
     */
    private Long areaCompanyId;

    /**
     * 区内企业
     */
    private String areaCompanyIdDesc;

    /**
     * 清单放行时间
     */
    private Date customsPassTime;

    /**
     * 清单id
     */
    private Long inveId;

    /**
     * 清单创建时间
     */
    private Date invTime;

}
