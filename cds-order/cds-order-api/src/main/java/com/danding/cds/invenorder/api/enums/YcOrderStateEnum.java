package com.danding.cds.invenorder.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: yousx
 * @Date: 2024/02/22
 * @Description:
 */
@AllArgsConstructor
@Getter
public enum YcOrderStateEnum {

    CREATED(10, "已创建"),
    UNDER_REVIEW(20, "审核中"),
    QUOTED(30, "已报价"),
    AWAITING_VEHICLE_INFO(40, "等待车辆信息"),
    COMPLETED(50, "约车完成"),
    COMPLETED_2(60, "约车完成"),
    CANCELED(100, "已取消"),
    ;

    private final Integer code;

    private final String desc;


    public static YcOrderStateEnum getEnumByCode(Integer code) {
        for (YcOrderStateEnum stateEnum : YcOrderStateEnum.values()) {
            if (stateEnum.getCode().equals(code)) {
                return stateEnum;
            }
        }
        return null;
    }

    public static String getDescByCode(Integer code) {
        for (YcOrderStateEnum stateEnum : YcOrderStateEnum.values()) {
            if (stateEnum.getCode().equals(code)) {
                return stateEnum.getDesc();
            }
        }
        return "";
    }
}
