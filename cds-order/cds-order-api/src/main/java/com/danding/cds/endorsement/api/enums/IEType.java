package com.danding.cds.endorsement.api.enums;

public enum IEType {
    NULL(0,"空"),
    EXPORT(1,"出"),
    IMPORT(2,"入"),
    ;

    private Integer value;

    private String desc;

    IEType(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
