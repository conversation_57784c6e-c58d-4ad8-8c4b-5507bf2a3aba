package com.danding.cds.order.api.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 收件人信息
 *
 * <AUTHOR>
 */
@Data
public class ConsigneeInfoRes implements Serializable {

    /**
     * 申报单sn
     */
    private String orderSn;
    /**
     * 订单id
     */
    private Long id;

    /**
     * 收件人姓名
     */
    private String name;

    /**
     * 收件人地址
     */
    private String address;

    /**
     * 收件人手机号码
     */
    private String telephone;

    /**
     * 订购人姓名
     */
    private String buyerName;

    /**
     * 订购人电话
     */
    private String buyerTelephone;

    /**
     * 订单来源:目前之有pdd
     */
    private String origin;

    /**
     * 收件人姓名密文
     */
    private String encryptName;

    /**
     * 收件人地址密文
     */
    private String encryptAddress;

    /**
     * 收件人手机号码密文
     */
    private String encryptTelephone;

    /**
     * 订购人姓名密文
     */
    private String encryptBuyerName;

    /**
     * 订购人电话密文
     */
    private String encryptBuyerTelephone;
}
