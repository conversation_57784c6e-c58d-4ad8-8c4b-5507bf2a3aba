package com.danding.cds.transwork.api.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: yousx
 * @Date: 2024/03/12
 * @Description:
 */
@Data
public class TransWorkPageVO implements Serializable {

    private Long id;

    /**
     * 运输作业单号
     */
    @ApiModelProperty(value = "运输作业单号")
    private String transNo;

    /**
     * 作业单类型
     */
    @ApiModelProperty(value = "作业单类型")
    private String orderType;


    @ApiModelProperty(value = "作业单类型desc")
    private String orderTypeDesc;

    @ApiModelProperty(value = "作业单状态")
    private Integer status;

    @ApiModelProperty(value = "作业单状态desc")
    private String statusDesc;

    @ApiModelProperty(value = "外部单号")
    private String outOrderNo;

    /**
     * 车辆资源编号
     */
    @ApiModelProperty(value = "车辆资源编号")
    private String truckResourceNo;

    /**
     * 车牌号
     */
    @ApiModelProperty(value = "车牌号")
    private String truckNo;


    /**
     * 运次编号
     */
    @ApiModelProperty(value = "运次编号")
    private String shipmentNo;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String ownerCode;

    @ApiModelProperty(value = "货主名称")
    private String ownerName;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    /**
     * 单据来源
     */
    @ApiModelProperty(value = "单据来源")
    private String orderOrigin;

    @ApiModelProperty(value = "单据来源desc")
    private String orderOriginDesc;
    /**
     * 完成时间
     */
    @ApiModelProperty(value = "完成时间")
    private Date finishTime;

    /**
     * 实际到达时间
     */
    @ApiModelProperty(value = "实际到达时间")
    private Date actArrTime;

    /**
     * 实际发车时间
     */
    @ApiModelProperty(value = "实际发车时间")
    private Date actDepTime;

    /**
     * 实际签收时间
     */
    @ApiModelProperty(value = "实际签收时间")
    private Date actSignTime;


    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}
