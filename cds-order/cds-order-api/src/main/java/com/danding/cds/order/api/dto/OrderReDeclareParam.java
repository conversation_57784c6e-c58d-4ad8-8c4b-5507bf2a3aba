package com.danding.cds.order.api.dto;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

@Data
@ApiModel("单据重新申报")
public class OrderReDeclareParam implements Serializable {

    @ApiModelProperty("申报项")
    private String action;

    @ApiModelProperty("逗号隔开的若干个id，使用这个提交id")
    private String ids;

    @ApiModelProperty("重推ID")
    private List<Long> idList;

    private List<String> snList;

    private Boolean forcePush;

    public List<Long> getIdList() {
        if (CollectionUtils.isEmpty(idList) && !StringUtils.isEmpty(ids)) {
            idList = Lists.newArrayList(ids.split(",")).stream().map(Long::valueOf).collect(Collectors.toList());
        }
        return idList;
    }
}
