package com.danding.cds.order.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: Raymond
 * @Date: 2020/8/27 18:19
 * @Description:
 */
@Data
@ApiModel
public class CustomsPaymentBaseInfoSubmit implements Serializable {
    @ApiModelProperty("证件类型")
    private String buyerIdType;
    @ApiModelProperty("证件号")
    private String buyerIdNo;
    @ApiModelProperty("支付人姓名")
    private String buyerName;
}
