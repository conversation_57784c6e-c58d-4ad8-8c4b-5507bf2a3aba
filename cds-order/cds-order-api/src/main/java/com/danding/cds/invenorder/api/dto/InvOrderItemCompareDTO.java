package com.danding.cds.invenorder.api.dto;

import com.danding.cds.common.annotations.TrackLogCompare;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 清关单表体 - 表体比对
 */
@Data
public class InvOrderItemCompareDTO implements Serializable {

    /**
     * 是否存在差异
     */
    private Boolean exitsDiff = false;

    /**
     * 是否存在比对失败
     */
    private Boolean exitsFail = false;

    /**
     * 统一料号
     */
    private String productId;

    /**
     * 当前表体
     */
    private CompareItem item;

    /**
     * 比较表体
     */
    private CompareItem compareItem;

    /**
     * 商品备案/账册库存
     */
    private CompareItem recordItem;

    @Data
    public static class CompareItem implements Serializable {

        /**
         * id
         */
        private Long id;

        /**
         * 数据来源
         */
        private String source;

        /**
         * HS编码
         */
        private String hsCode;

        /**
         * 原产国（地区）
         */
        @TrackLogCompare(aliasName = "原产国（地区）")
        private String originCountry;

        /**
         * 商品名称
         */
        private String goodsName;

        /**
         * 申报单位
         */
        @TrackLogCompare(aliasName = "申报单位")
        private String unit;

        /**
         * 申报数量
         */
        @TrackLogCompare(aliasName = "申报数量")
        private BigDecimal declareUnitQfy;

        /**
         * 法定数量（总）
         */
        private BigDecimal firstUnitQfy;

        /**
         * 法定计量单位
         */
        private String firstUnit;

        /**
         * 第二法定数量（总）
         */
        private BigDecimal secondUnitQfy;

        /**
         * 法定第二计量单位
         */
        private String secondUnit;

        /**
         * 币制
         */
        @TrackLogCompare(aliasName = "币制")
        private String currency;

        /**
         * 申报单价
         */
        @TrackLogCompare(aliasName = "申报单价")
        private BigDecimal declarePrice;

        /**
         * 申报总价
         */
        private BigDecimal declareTotalPrice;

        /**
         * 单据来源
         */
        private Integer channel;

        /**
         * 总毛重
         */
        @TrackLogCompare(aliasName = "总毛重")
        private BigDecimal totalGrossWeight;

        /**
         * 总净重
         */
        @TrackLogCompare(aliasName = "总净重")
        private BigDecimal totalNetWeight;
    }

}
