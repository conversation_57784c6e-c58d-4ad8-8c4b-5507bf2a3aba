package com.danding.cds.transwork.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel
public class TransWorkOrderPackagingItemDTO implements Serializable {

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String itemCode;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private Long itemQuantity;

    /**
     * 托盘号
     */
    @ApiModelProperty(value = "托盘号")
    private String palletNo;

    /**
     * 箱号
     */
    @ApiModelProperty(value = "箱号")
    private String boxNo;

    /**
     * 装箱明细行号
     */
    @ApiModelProperty(value = "装箱明细行号")
    private String packagingItemLineNo;

}