package com.danding.cds.order.api.enums;

/**
 * @Author: Raymond
 * @Date: 2020/8/13 11:41
 * @Description:
 */
public enum OrderChannel {
    NULL(0,"空"),
    LOGISTICS(1,"出入库系统");


    private Integer value;

    private String desc;

    OrderChannel(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static OrderChannel getEnum(Integer value){
        for (OrderChannel orderChannel : OrderChannel.values()) {
            if (orderChannel.getValue().equals(value)){
                return orderChannel;
            }
        }
        return NULL;
    }
}
