package com.danding.cds.transwork.api.service;

import com.danding.cds.transwork.api.vo.*;
import com.danding.cds.v2.bean.dto.TTGeneralCancelDTO;
import com.danding.cds.v2.bean.dto.TTMainlineTransportorderCreateDTO;
import com.danding.cds.v2.bean.dto.TTPortdepartureTransportorderCreateDTO;
import com.danding.cds.v2.bean.dto.TransWorkOrderDTO;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;

import java.util.List;

/**
 * @Author: yousx
 * @Date: 2024/03/11
 * @Description:
 */
public interface TransWorkService {

    /**
     * 创建港到仓作业单
     * @param request
     * @return
     */
    String createPortdeparture(TTPortdepartureTransportorderCreateDTO request) throws ArgsErrorException;

    /**
     * 创建干线作业单
     * @param request
     * @return
     */
    String createMainlineTransportorder(TTMainlineTransportorderCreateDTO request) throws ArgsErrorException;

    /**
     * 分页
     * @param search
     * @return
     */
    ListVO<TransWorkPageVO> paging(TransWorkSearch search);

    /**
     * 详情
     * @param id
     * @return
     */
    TransWorkDetailVO detail(Long id);

    /**
     * 作业单商品分页
     * @param id
     * @return
     */
    ListVO<TransWorkItemVO> itemPaging(Long id);

    /**
     * 作业单商品编辑
     * @param param
     * @return
     */
    void itemEdit(TransWorkItemEditVO param) throws ArgsErrorException;

    /**
     * 作业单时间编辑
     * @param param
     * @return
     */
    void editTimeInfo(TransWorkTimeEditVO param) throws ArgsErrorException;

    /**
     * 添加车辆信息
     * @param param
     */
    void addTruckInfo(TransTruckInfoVO param) throws ArgsErrorException;

    List<TransWorkOrderDTO> findByTruckResourceCode(String vehicleResourceCode);

    List<TransWorkOrderDTO> findByTruckResourceCodeAndPlate(String truckResourceCode, String truckNo);

    /**
     * 完成运输
     * @param id
     */
    void finishTransWork(Long id) throws ArgsErrorException;

    void cancelTransportOrder(TTGeneralCancelDTO request) throws ArgsErrorException;

    List<TransWorkOrderPackagingItemsVO> getPackagingItems(Long id);

    void packagingItemEdit(TransWorkItemEditVO param) throws ArgsErrorException;

    void addTruckNo(TransAddTruckNoVO param) throws ArgsErrorException;
}
