package com.danding.cds.jieztech;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 此接口涉及申报发起方需要获取清单申报状态，数字清关公共服务平台向申报发起方回传申报结果，通过 Http Post 方式参数以 Json 数据格式发送请求。
 * URL 地址:${网关地址}/api/declare/entry/hzCrossAgentCallback
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class JieztechCallbackReq extends JieztechCommonReq {
    @ApiModelProperty("申报海关代码")
    private String customsCode;

    @ApiModelProperty("电商平台代码")
    private String ebpCode;

    @ApiModelProperty("电商企业代码")
    private String ebcCode;

    @ApiModelProperty("申报企业代码")
    private String agentCode;

    @ApiModelProperty("企业内部编号")
    private String copNo;

    /**
     * 非必填
     */
    @ApiModelProperty("预录入编号")
    private String preNo;

    /**
     * 非必填
     */
    @ApiModelProperty("清单编号")
    private String invtNo;

    /**
     * 物流公司 code
     * 电商平台的物流
     * CODS， 目前只有淘
     * 宝平台的物流
     * CODS， 例如：
     * YUNDA 韵达快递
     * STO 申通快递
     * YTO 圆通快递
     * HTKY 百世快递
     * ZTO 中通快递
     * SF 顺丰快递
     */
    private String logisticsCode;

    /**
     * 操作结果(1 电子口岸已暂存/2 电子口岸申报中/3 发送海关成功/4 发送海关失败
     * /100 海关退单/120 海关入库/300 人工审核/399 海关审结 /800 放行/899 结关
     * /500查验/501 扣留移送通关/502 扣留移送私/503 扣留移送法规
     * /599 其它扣留 /700 退运),若小于 0 数字表示处理异常回执
     */
    @ApiModelProperty("回执状态")
    private String returnStatus;

    /**
     * 操作时间(格 式:YYYYMMddHHmmssSSS)
     */
    @ApiModelProperty("回执时间")
    private String returnTime;

    /**
     * 备注(如:退单原因)
     */
    @ApiModelProperty("回执信息")
    private String returnInfo;
}
