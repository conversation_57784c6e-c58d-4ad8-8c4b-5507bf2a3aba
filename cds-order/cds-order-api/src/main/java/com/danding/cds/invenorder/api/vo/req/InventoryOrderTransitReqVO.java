package com.danding.cds.invenorder.api.vo.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 清关单-发起中转请求VO
 *
 * <AUTHOR>
 */
@Data
public class InventoryOrderTransitReqVO implements Serializable {

    /**
     * 清关单id
     */
    @NotNull(message = "清关单id不能为空")
    private Long inveOrderId;

    /**
     * 清关企业(中转)
     */
    @NotNull(message = "清关企业(中转)不能为空")
    private Long inveCompanyId;

    /**
     * 账册编号(中转)
     */
    @NotNull(message = "账册编号(中转)不能为空")
    private Long bookId;

    /**
     * 中转目的仓
     */
//    @NotNull(message = "中转目的仓不能为空")
    private String entityWarehouseCode;
}
