package com.danding.cds.cull.api.enums;


public enum CancelInfoEnums {
    NO(0,"否"),
    YES(1,"是");


    private Integer code;

    private String desc;

    CancelInfoEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static CancelInfoEnums getEnum(Integer code){
        for (CancelInfoEnums value : CancelInfoEnums.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        throw new IllegalArgumentException("unknown CancelInfoEnums.code: " + code);
    }
}
