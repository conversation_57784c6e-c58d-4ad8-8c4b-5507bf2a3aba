package com.danding.cds.customs.inventory.api.enums;

/**
 * @Author: Raymond
 * @Date: 2020/10/14 9:30
 * @Description: 取消类型
 */
public enum InventoryCalloffTypeEnum {
    CALLOFF_EMPTY("", "空"),
    @Deprecated
    CALLOFF_DIRECT("0", "直接取消"),
    CALLOFF_CACEL("1", "撤单"),
    CALLOFF_RETURN("2", "退货"),
    @Deprecated

    CALLOFF_PRE("3", "预处理"),

    //直接取消 和 预处理 之后统一合并为拦截申报
    INTERCEPTION_DECLARE("4", "拦截申报");
    private String code;
    private String desc;

    private InventoryCalloffTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }
    public String getDesc() {
        return desc;
    }
    public static InventoryCalloffTypeEnum getEnum(String value){
        for (InventoryCalloffTypeEnum step : InventoryCalloffTypeEnum.values()) {
            if (step.getCode().equals(value)){
                return step;
            }
        }
        return CALLOFF_EMPTY;
    }
}
