package com.danding.cds.customs.order.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class CustomsOrderItem implements Serializable {
    private static final long serialVersionUID = 3660224909121394961L;

    /**
     * 企业自定义的商品编码
     */
    private String goodsNo;

    /**
     * 账册ID
     */
    private Long bookId;

    /**
     * 序号
     */
    private String goodsSeqNo;

    /**
     * 料号
     */
    private String recordNo;

    /**
     * 物品名称
     */
    private String goodsName;

    /**
     * 商品规格、型号
     */
    private String goodsModel;

    /**
     * HS编码
     */
    private String hsCode;

    /**
     * 申报单价
     */
    private BigDecimal unitPrice;

    /**
     * 申报计量单位
     */
    private String goodsUnit;

    /**
     * 申报数量
     */
    private int goodsCount;

    /**
     * 产销国 原产国
     */
    private String originCountry;
}
