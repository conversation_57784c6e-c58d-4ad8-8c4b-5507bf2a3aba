package com.danding.cds.handoverOrder.api.service;

import com.danding.cds.handoverOrder.api.dto.HandoverOrderDetailDTO;
import com.danding.cds.handoverOrder.api.dto.HandoverOrderDetailParam;
import com.danding.cds.handoverOrder.api.dto.HandoverOrderDetailVO;
import com.danding.cds.handoverOrder.api.enums.HandoverDetailOutBoundStatus;
import com.danding.logistics.api.common.response.ListVO;

import java.util.List;

public interface HandoverOrderDetailService {

    /**
     * 批量新增交接单明细
     */
    void addListDetail(List<HandoverOrderDetailDTO> detailDTOList);

    void updateListDetail(List<HandoverOrderDetailDTO> orderDetailDTOList);

    /**
     * 根据运单获取交接单
     *
     * @param waybillSn
     * @return
     */
    List<HandoverOrderDetailDTO> getHandoverOrderDetailByMailNo(String waybillSn);

    List<HandoverOrderDetailDTO> getHandoverOrderDetailByMailNo(List<String> waybillSn);

    /**
     * 修改交接单明细出库单号
     *
     * @param mailNo
     */
    void updHandoverSn(String mailNo, HandoverDetailOutBoundStatus status, String exportOrderSn);

    /**
     * 根据交接单号查询
     *
     * @param handoverSn
     * @return
     */
    List<HandoverOrderDetailDTO> getDetailBySnStatus(String handoverSn, HandoverDetailOutBoundStatus status);

    /**
     * 分页查询
     *
     * @param param
     * @return
     */
    ListVO<HandoverOrderDetailVO> paging(HandoverOrderDetailParam param);

    List<HandoverOrderDetailDTO> getDetailByHandoverOrder(String handoverSn);

    /**
     * 根据绑定该出库单的表体
     *
     * @param exportOrderSn
     * @return
     */
    List<HandoverOrderDetailDTO> getDetailByOutboundOrder(String exportOrderSn);

    /**
     * 清空绑定该出库单的表体的关联关系
     *
     * @param exportOrderSn
     * @return
     */
    void disassociateDetailByOutboundOrder(String exportOrderSn);


    List<HandoverOrderDetailDTO> getDetailByHandoverOrder(List<String> handoverSnList);

    /**
     * 更新出库单号
     */
    void updateDetailExportOrder(List<String> mailNoList, String sn);

    void cleanExportOrderInfo(List<String> snList);

    void addExportOrderInfo(List<String> addInventorySnList, String sn);

    List<String> getHandoverSnByOutBoundOrder(List<String> outboundOrder);

    List<HandoverOrderDetailDTO> getDetailByHandoverSnListAndStatus(List<String> HandoverSn, HandoverDetailOutBoundStatus status);

}
