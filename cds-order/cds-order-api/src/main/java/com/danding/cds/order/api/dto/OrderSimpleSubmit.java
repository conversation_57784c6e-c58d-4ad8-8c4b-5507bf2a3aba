package com.danding.cds.order.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * @Author: Raymond
 * @Date: 2020/9/9 10:48
 * @Description:进销存系统申报单提交对象
 */

@Data
@ApiModel
public class OrderSimpleSubmit  implements Serializable {
    private static final long serialVersionUID = 6677018036727894308L;

    @ApiModelProperty("申报结果回传地址")
    private String notifyUrl;

    @ApiModelProperty("申报单流入渠道（1:出入库系统）")
    private Integer channel;

    @ApiModelProperty("全局单号")
    @NotBlank(message = "全局单号不能为空")
    private String systemGlobalSn;

    @ApiModelProperty("物流快递代码")
    @NotBlank(message = "物流快递代码不能为空")
    private String expressCode;

    @ApiModelProperty("物流运单编号")
    // @NotBlank(message = "物流运单编号不能为空")
    private String logisticsNo;

    @ApiModelProperty("商品项")
    @NotEmpty(message = "商品项不能为空")
    private List<OrderSimpleSubmitItem> itemList;

    @ApiModelProperty("仓库编码")
    @NotEmpty(message = "仓库编码不能为空")
    private String wareCode;

    // --- 清单收件人 ---
    @ApiModelProperty("收件人省")
    @NotBlank(message = "收件人省不能为空")
    private String consigneeProvince;

    @ApiModelProperty("收件人市")
    @NotBlank(message = "收件人市不能为空")
    private String consigneeCity;

    @ApiModelProperty("收件人区")
    private String consigneeDistrict;

    @ApiModelProperty("收件人地址")
    @NotBlank(message = "收件人地址不能为空")
    private String consigneeAddress;

    @ApiModelProperty("收件人邮箱 非必填")
    private String consigneeEmail;

    @ApiModelProperty("收件人电话")
    @NotBlank(message = "收件人电话不能为空")
    private String consigneeTel;

    @ApiModelProperty("收件人姓名")
    @NotBlank(message = "收件人姓名不能为空")
    private String consigneeName;

    @ApiModelProperty("申报路由标识1")
    private String firstIdentify;

    @ApiModelProperty("申报路由标识2")
    private String secondIdentify;

    @ApiModelProperty("申报路由标识3")
    private String thirdIdentify;


    // --- 租户定制化功能 ----
    @ApiModelProperty("租户ID")
    private String tenantOuterId;

    @ApiModelProperty("租户名称")
    private String tenantName;

    /**
     * 以下是从上报订单中获取
     */
//    @ApiModelProperty("渠道单号|外部单号")
//    private String outOrderNo;
//
//    @ApiModelProperty("外部交易流水号")
//    private String outTradeNo;
//
//    @NotBlank(message = "申报单号不能为空")
//    private String declareOrderNo;
//
//    /**
//     * 申报路径 基础 A1 必填二选一
//     */
//    @ApiModelProperty("申报路径编码")
//    private String routeCode;
//
//    @ApiModelProperty("申报路由标识1")
//    private String firstIdentify;
//
//    @ApiModelProperty("申报路由标识2")
//    private String secondIdentify;
//
//    @ApiModelProperty("申报路由标识3")
//    private String thirdIdentify;
//
//    @ApiModelProperty("运费 非必填") //上报订单
//    private BigDecimal feeAmount = BigDecimal.ZERO;
//
//    /**
//     * 保费
//     */
//    @ApiModelProperty("保费 非必填")
//    private BigDecimal insureAmount;
//
//    @ApiModelProperty("税费")
//    private BigDecimal taxAmount;
//
//    @ApiModelProperty("折扣")
//    private BigDecimal discount;
//
//    @ApiModelProperty("商品总价")
//    private BigDecimal goodsSumAmount;//用于校验
//
//    // --- 清单订购人 ---
//    @ApiModelProperty("购买人电话 不传时取收件人电话")
//    private String buyerTelNumber;
//
//    @ApiModelProperty("订购人证件号码")
//    private String buyerIdNumber;
//
//    @ApiModelProperty("订购人姓名")
//    private String buyerName;
//
//    // --- 订单基础 ---
//
//    @ApiModelProperty("付款方式")
//    private String payChannel;
//
//    @ApiModelProperty("支付申报流水号")
//    private String declarePayNo;
//
//    @ApiModelProperty("交易时间|付款时间")
//    private Long tradeTime;
//
//    @ApiModelProperty("发件人姓名")
//    private String senderName;
//
//    // --- 海关179公告对接 ---  ///////////////////////上报订单
//    @ApiModelProperty("是否开启海关179备查")
//    private Boolean payInfoDataCheckFlag = false;
//
//    @ApiModelProperty("支付交易流水号")
//    private String tradePayNo; // 订单里存一份，以防万一需要查
//
//    @ApiModelProperty("验核机构交易流水号")
//    private String payTransactionId;
//
//    @ApiModelProperty("验核机构名称")
//    private String verDept;
//
//    @ApiModelProperty("海关订单支付方式")
//    private String payWay;
//
//    @ApiModelProperty("支付单总金额")
//    private BigDecimal payTransactionAmount;
//
//    @ApiModelProperty("收款企业社会信用代码")
//    private String recpCode;
//
//    @ApiModelProperty("收款企业工商备案名称")
//    private String recpName;
//
//    @ApiModelProperty("收款渠道下的账号")
//    private String recpAccount;
//
//    @ApiModelProperty("支付请求原始数据")
//    private String payRequestMessage;
//
//    @ApiModelProperty("支付返回原始数据")
//    private String payResponseMessage;
//
//    @ApiModelProperty("原始商品信息 一个原始商品可能对应多个发货SKU")
//    private List<PayInfoGoodsInfo> origGoodsInfoList;
//
//    /**
//     * 新增
//     */
//    @ApiModelProperty("申报口岸编码") //通过仓库编码 获取 CustomsDistrictEnum
//    private String customsCode;
//
//    @ApiModelProperty("商户编号")
//    private String merchantCode;
}
