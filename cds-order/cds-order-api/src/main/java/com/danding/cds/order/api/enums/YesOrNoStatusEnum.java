package com.danding.cds.order.api.enums;

public enum YesOrNoStatusEnum {

    NO(0, "否"),
    YES(1, "是");


    private Integer value;

    private String desc;

    YesOrNoStatusEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static YesOrNoStatusEnum getEnum(Integer value) {
        for (YesOrNoStatusEnum orderChannel : YesOrNoStatusEnum.values()) {
            if (orderChannel.getValue().equals(value)) {
                return orderChannel;
            }
        }
        return YES;
    }
}
