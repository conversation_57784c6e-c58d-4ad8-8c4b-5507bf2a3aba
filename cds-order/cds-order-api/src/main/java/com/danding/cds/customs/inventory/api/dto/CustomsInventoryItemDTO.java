package com.danding.cds.customs.inventory.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class CustomsInventoryItemDTO implements Serializable {

    private Long id;

    /**
     * 清单ID
     */
    private Long customsInventoryId;

    /**
     * 账册商品ID
     */
    private Long bookItemId;

    /**
     * 企业商品货号
     */
    private String itemNo;

    /**
     * 企业商品品名
     */
    private String itemName;

    /**
     * 数量
     */
    private Integer count;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 备注
     */
    private String note;

    /**
     * json储存的其他属性键值对
     */
    private String extraJson;

    private Date createTime;
    /**
     * 排序
     */
    private Integer sort;

    /**
     * 逆向申报数量
     */
    private Integer refundDeclareQty;

    /**
     * 表体标签
     * {@link com.danding.cds.c.api.bean.enums.OrderItemTagEnum}
     */
    private Integer itemTag;

    /**
     * 总价
     */
    private BigDecimal totalPrice;

}
