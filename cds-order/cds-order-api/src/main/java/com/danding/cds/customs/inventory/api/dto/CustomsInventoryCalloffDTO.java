package com.danding.cds.customs.inventory.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: Raymond
 * @Date: 2020/10/14 9:25
 * @Description:
 */
@Data
public class CustomsInventoryCalloffDTO implements Serializable {

    private static final long serialVersionUID = -3037375954449195976L;

    private Long id;

    /**
     * 清单系统编号
     */
    private String sn;

    /**
     * 申报单系统编号
     */
    private String orderSn;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 账册ID
     */
    private Long accountBookId;

    /**
     * 订单编号 申报单号
     */
    private String declareOrderNo;

    /**
     * 清关状态
     */
    private String customsStatus;

    /**
     * 出区状态
     */
    private Integer exitRegionStatus;

    /**
     * 取消状态
     */
    private String calloffStatus;

    /**
     * 取消类型
     */
    private String calloffType;

    /**
     * 取消原因
     */
    private String calloffReason;

    /**
     * 海关售后回执
     */
    private String cusAfterSalesCallback;

    /**
     * 海关售后回执描述
     */
    private String cusAfterSalesCallbackDesc;

    /**
     * 海关售后回执详情
     */
    private String cusAfterSalesCallbackDetail;


    /**
     * 清单放行时间
     */
    private Date customsPassTime;

    /**
     * 驳回原因
     */
    private String rejectReason;

    /**
     * 物流运单编号
     */
    private String logisticsNo;

    /**
     * 退货运单编号
     */
    private String refundLogisticsNo;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 清单号
     */
    private String inventoryNo;

    /**
     * 电商企业
     */
    private Long ebcId;


    /**
     * 申报企业
     */
    private Long agentCompanyId;

    /**
     * 区内企业
     */
    private Long areaCompanyId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 取消时间
     */
    private Date calloffTime;

    private Date createTime;

    private String oper;

    /**
     * 退款记录图片集合json串
     */
    private String picJson;

    private String entityWarehouseCode;

    private String entityWarehouseName;

    private String ownerCode;

    private String ownerName;


    /**
     * 取消单 位运算
     */
    private Integer orderTag;

    /**
     * 取消指令报文
     */
    private String linkCustomsContent;

    /**
     * 部分退标记
     */
    private Integer partRefundFlag;

    /**
     * 退货商品明细
     */
    private String refundGoodsInfoJson;

    /**
     * 退入实体仓编码（wms）
     */
    private String refundEntityWarehouseCode;

    /**
     * 退入实体仓名称
     */
    private String refundEntityWarehouseName;

    /**
     * 退入货主编码
     */
    private String refundOwnerCode;

    /**
     * 退入货主名称
     */
    private String refundOwnerName;

    /**
     * 原始报文json
     */
    private String originMsgJson;
}
