package com.danding.cds.customs.inventory.api.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description:
 * @date 2022/8/18 10:03
 */
@Data
@Accessors(chain = true)
public class StockOccupiedDetailDTO implements Serializable {
    /**
     * es查询出来的 直接去清单的id 供前端刷新用
     */
    private String id;

    /**
     * 申报单号
     */
    private String declareOrderNo;

    /**
     * 清单编号
     */
    private String inventoryNo;

    /**
     * 数量
     */
    private Integer count;

    /**
     * 售后状态
     */
    private String afterSaleStatus;

    /**
     * 清单状态
     * {@link com.danding.cds.customs.inventory.api.enums.CustomsActionStatus}
     */
    private String inventoryStatus;

    /**
     * 核注状态
     */
    private String endorsementStatus;

    /**
     * 核注单号
     */
    private String endorsementSn;

    /**
     * 核放单号
     */
    private String checklistSn;
}
