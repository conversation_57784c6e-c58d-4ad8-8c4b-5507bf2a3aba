package com.danding.cds.customs.inventory.api.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/6/19 13:54
 */
@Data
public class InventoryCallOffOverTimeCountVO implements Serializable {
    //撤单待取消超24h
//    private Integer cancelCalloffWaiting12hTo24h = 0;
//    //撤单待取消超24h
//    private Integer cancelCalloffWaiting24hAndMore = 0;
    //撤单待取消超3d-5d
    private Integer cancelCalloffWaiting3dTo5d = 0;
    //撤单待取消超5d
    private Integer cancelCalloffWaiting5dAndMore = 0;
    //退货待取消超7d
    private Integer refundCalloffWaiting7dTo10d = 0;
    //退货待取消超10d
    private Integer refundCalloffWaiting10dAndMore = 0;
    //取消中超24h
    private Integer calloffing24hTo36h = 0;
    //取消中超36h
    private Integer calloffing36hAndMore = 0;
}
