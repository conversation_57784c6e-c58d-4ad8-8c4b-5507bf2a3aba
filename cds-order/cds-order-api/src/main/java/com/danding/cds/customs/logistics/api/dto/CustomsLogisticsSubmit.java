package com.danding.cds.customs.logistics.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class CustomsLogisticsSubmit implements Serializable {
    private String routeCode;
    /**
     * 上游单号
     */
    private String outOrderNo;

    /**
     * 订单编号 申报单号
     */
    private String declareOrderNo;

    /**
     * 快递标识
     */
    private String expressCode;

    /**
     * 关区口岸
     */
    private String customs;

    /**
     * 收件人姓名
     */
    private String consigneeName;

    /**
     * 收件人省
     */
    private String consigneeProvince;

    /**
     * 收件人市
     */
    private String consigneeCity;

    /**
     * 收件人区
     */
    private String consigneeDistrict;

    /**
     * 收件人街道
     */
    private String consigneeStreet;

    /**
     * 收件人地址
     */
    private String consigneeAddress;

    /**
     * 收件人电话
     */
    private String consigneeTel;

    /**
     * 物流运单编号
     */
    private String logisticsNo;

    /**
     * 货值
     */
    private BigDecimal goodsTotalAmount;
    /**
     * 运费
     */
    private BigDecimal feeAmount;

    /**
     * 毛重（公斤）
     */
    private BigDecimal grossWeight;

    /**
     * 净重
     */
    private BigDecimal netWeight;

    /**
     * 保费
     */
    private BigDecimal insureAmount;


    /**
     * 货品信息
     */
    private List<CustomsLogisticsItem> itemList;

    /**
     * 租户ID
     */
    private String tenantId;
}
