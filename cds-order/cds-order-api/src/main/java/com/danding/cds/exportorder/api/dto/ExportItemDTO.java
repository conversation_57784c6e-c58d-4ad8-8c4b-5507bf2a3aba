package com.danding.cds.exportorder.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ExportItemDTO implements Serializable {
    private static final long serialVersionUID = -3636596538949142097L;

    private Long id;

    /**
     * 出区单ID
     */
    private Long exportOrderId;

    /**
     * 核注清单Id
     */
    private Long endorsementOrderId;

    /**
     * 放行清单编号
     */
    private String customsInventorySn;

    /**
     * 快递方式ID
     */
    private Long expressId;

    /**
     * 快递编号
     */
    private String mailNo;

    /**
     * 托盘号
     */
    private String trayNo;

    /**
     * 操作员工号
     */
    private String operatorNo;

    /**
     * 工作台编号
     */
    private String stationNo;

    /**
     * 包裹号
     */
    private String bizId;

    /**
     * SKU种类信息
     */
    private String skuJson;

    /**
     * 毛重（公斤）
     */
    private BigDecimal grossWeight;

    /**
     * 净重（公斤）
     */
    private BigDecimal netWeight;

    /**
     * 账册id
     */
    private Long accountBookId;
}
