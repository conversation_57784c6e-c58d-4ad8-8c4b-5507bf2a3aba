package com.danding.cds.invenorder.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class InventoryOrderItemDTO implements Serializable {
    private static final long serialVersionUID = -2433113850499947709L;
    @ApiModelProperty("主键ID")
    private Long id;
    /**
     * 清关单ID
     */
    @ApiModelProperty("清关单ID")
    private Long refInveOrderId;
    /**
     *清关单SN
     */
    @ApiModelProperty("清关单SN")
    private String  refInveOrderSn;
    /**
     * 是否是新的(0:旧,1:新)
     */
    @ApiModelProperty("是否是新的(old:旧,new:新)")
    private String isNew;
    /**
     * sku
     */
    @ApiModelProperty("sku")
    private String skuId;

    @ApiModelProperty("原始料号") // 统一料号
    private String originProductId;

    /**
     * 海关备案料号
     * 由上游指定
     */
    @ApiModelProperty("海关备案料号")
    private String customsRecordProductId;

    /**
     * 商品料号
     */
    @ApiModelProperty("商品料号")
    private String productId;
    /**
     * 账册项号
     */
    @ApiModelProperty("商品序号")
    private String goodsSeqNo;

    /**
     * 记账金二序号
     */
    private String customsCallBackSeqNo;

    @ApiModelProperty("商品id")
    private String goodsCode;

    @ApiModelProperty("商品名称")
    private String goodsName;

    @ApiModelProperty("来源标识")
    private String goodsSource;
    private String goodsSourceDesc;

    @ApiModelProperty("商品备案名称")
    private String recordProductName;
    /**
     * HS代码
     */
    @ApiModelProperty("HS代码")
    private String hsCode;
    /**
     * 计划申报数量
     */
    @ApiModelProperty("计划申报数量")
    private BigDecimal planDeclareQty;
    /**
     * 出库单号
     */
    @ApiModelProperty("出库单号，多个用/分开")
    private String outBoundNo;
    /**
     * 实际理货数量
     */
    @ApiModelProperty("实际理货数量")
    private BigDecimal actualTallyQty;
    /**
     * 申报单位数量
     */
    @ApiModelProperty("申报单位数量")
    private BigDecimal declareUnitQfy;

    /**
     * json 属性
     */
    @ApiModelProperty("计量单位")
    private String unit;
    @ApiModelProperty("第一单位")
    private String firstUnit;
    @ApiModelProperty("第一单位数量")
    private BigDecimal firstUnitQfy;
    @ApiModelProperty("第二单位")
    private String secondUnit;
    @ApiModelProperty("第二单位数量")
    private BigDecimal secondUnitQfy;
    @ApiModelProperty("毛重")
    private BigDecimal grossWeight;
    @ApiModelProperty("净重")
    private BigDecimal netweight;
    @ApiModelProperty("申报要素")
    private String declareFactor;
    @ApiModelProperty("原产国")
    private String originCountry;
    @ApiModelProperty("商品条码")
    private String goodsBar;
    @ApiModelProperty("生产企业")
    private String productCompany;
    @ApiModelProperty("国检备案号")
    private String countryRecordNo;

    @ApiModelProperty("申报单价")//add
    private BigDecimal declarePrice;
    @ApiModelProperty("申报总价")//add
    private BigDecimal declareTotalPrice;

    @ApiModelProperty("报关单商品序号")
    private  String declareCustomsGoodsSeqNo;//add
    @ApiModelProperty("规格型号")//add
    private String goodsModel;
    @ApiModelProperty("最终目的国")
    private String destinationCountry;//add
    @ApiModelProperty("币制")//add
    private String currency;
    @ApiModelProperty("币制")//add
    private String currencyDesc;
    @ApiModelProperty("免征方式")//add
    private String avoidTaxMethod="3";
    private String avoidTaxMethodDesc = "全免";
    @ApiModelProperty("单号版本号")//add
    private String orderVersion;

    @ApiModelProperty("计量单位描述")
    private String unitDesc;
    @ApiModelProperty("第一单位描述")
    private String firstUnitDesc;
    @ApiModelProperty("第二单位")
    private String secondUnitDesc;
    @ApiModelProperty("原产国描述")
    private String originCountryDesc;
    @ApiModelProperty("目的国描述")
    private String destinationCountryDesc;
    /**
     * 额外其他属性
     */
    //@ApiModelProperty("额外其他属性")
    private String extraJson;

    /**
     * 核注清单ID
     */
    @ApiModelProperty("核注清单ID")
    private Long refEndorsementId;

    /**
     * 核注清单SN
     */
    @ApiModelProperty("核注清单SN")
    private String refEndorsementSn;

    @ApiModelProperty("通关校验结果")
    private List<InventoryVerifyResult> verifyResultList;

    @ApiModelProperty("是否涉濒危")
    private Boolean endangered;

    @ApiModelProperty("物种证明附件名称")
    private String speciesCertificateAttachmentName;

    @ApiModelProperty("物种证明附件URL")
    private String speciesCertificateAttachmentUrl;

    //---------------------------------------------------

    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("更新时间")
    private Date updateTime;
    @ApiModelProperty("创建人ID")
    private Integer createBy;
    @ApiModelProperty("更新人ID")
    private Integer updateBy;
    @ApiModelProperty("逻辑删除")
    private Boolean deleted = false;

    @ApiModelProperty("生化品标志  1是  0否")
    private String dangerousFlag;
    /**
     * 行号
     */
    private String idx;

    /**
     * 申报表序号
     */
    private Integer declareFormItemSeqNo;

    // ---------------------------------- 溯源信息----------------------------------
    /**
     * 关联一线入境报关单号
     */
    private String customsEntryNo;

    /**
     * 报关日期
     */
    private Date customsEntryTime;

    /**
     * 启运国
     */
    private String shipmentCountry;

    /**
     * 启运港
     */
    private String fromLocation;

    /**
     * 运输方式
     */
    private String transportMode;

    /**
     * 进境口岸
     */
    private String entryPort;

    // ---------------------------------- 溯源信息----------------------------------

    //额外数据 没存数据库
    /**
     * 来源
     */
    private String source;

    /**
     * 总毛重
     */
    private BigDecimal totalGrossWeight;

    /**
     * 总净重
     */
    private BigDecimal totalNetWeight;
}
