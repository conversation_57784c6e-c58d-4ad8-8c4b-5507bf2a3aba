package com.danding.cds.collaborateorder.api.service;


import com.danding.cds.collaborateorder.api.dto.TallyReportDTO;
import com.danding.cds.collaborateorder.api.vo.CollaborateOrderDetailsResVO;
import com.danding.cds.invenorder.api.dto.InventoryOrderInfoDTO;
import com.danding.cds.invenorder.api.dto.InventoryOrderItemDTO;
import com.danding.cds.v2.bean.dto.InventoryFlowStateDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 协同单
 * @date 2022/3/29
 */
public interface CollaborateOrderDetailsService {

    /**
     * 协同单详情
     *
     * @param collaborateOrderId
     * @return
     */
    List<CollaborateOrderDetailsResVO> selectByCollaborateOrderId(Long collaborateOrderId);

    InventoryFlowStateDTO getFlowState(InventoryOrderInfoDTO infoDTO,String collaborateOrderId);

    /**
     * 生成协同单详情
     * @param inveCustomsSn
     * @param collaborateId
     */
    void saveCollaborateOrderDetails(String inveCustomsSn,Long collaborateId);


    /**
     * 根据理货报告修改详情
     *
     * @param tallyReportDTO
     * @param collaborateOrderId
     */
    int updateDetailsOneStepDeclare(TallyReportDTO tallyReportDTO, Long collaborateOrderId, InventoryOrderInfoDTO infoDTO);

    /**
     * 修改两步申报协同单详情
     *
     * @param reportDTO
     * @param collaborateOrderId
     */
    void updateDetailsTwoStepDeclare(TallyReportDTO reportDTO, Long collaborateOrderId, InventoryOrderInfoDTO infoDTO);


    /**
     * 更新
     *
     * @param currentOrderItemList 当前
     * @param deleteOrderItemList 删除
     * @param addOrderItemList 新增
     */
    void updateByGrossWeightAndGoodsSeqNo(String inveSn, List<InventoryOrderItemDTO> currentOrderItemList, List<InventoryOrderItemDTO> deleteOrderItemList, List<InventoryOrderItemDTO> addOrderItemList);

    /**
     * 更新
     *
     * @param orginOrderItemList 原始的清关单
     * @param currentItemList    当前的清关单表体
     */
    void updateByGrossWeightAndGoodsSeqNo(String inveSn, List<InventoryOrderItemDTO> orginOrderItemList, List<InventoryOrderItemDTO> currentItemList);


    /**
         * 根据清关单id修改申报数量
         * @param inveId
         */
    void updateDeclareQtyByInveId(Long inveId);

    /**
     * 查询差异数量
     * @param inveSn
     * @return
     */
    int updateDeclareQtyByInveSn(String inveSn);
}
