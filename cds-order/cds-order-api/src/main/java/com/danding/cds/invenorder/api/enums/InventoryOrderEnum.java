package com.danding.cds.invenorder.api.enums;

import com.danding.cds.endorsement.api.enums.EndorsementOrderStatus;
import com.google.common.base.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum InventoryOrderEnum {
    EMPTY("", "未知", true, EndorsementOrderStatus.NULL),
    STATUS_CREATED("CREATED", "已创建", true, EndorsementOrderStatus.NULL),
    STATUS_CONFIRMING("CONFIRMING", "提交资料(待确认)", true, EndorsementOrderStatus.NULL),
    //    STATUS_AUDITING("AUDITING","待审核"),
    STATUS_PERFECT("ERFECT", "已完善", true, EndorsementOrderStatus.NULL),
    STATUS_AUDITED("AUDITED", "审核通过", true, EndorsementOrderStatus.NULL),
    //    STATUS_REJECT("REJECT","已驳回"),
    STATUS_ENDORSEMENT("ENDORSEMENT", "生成核注单", true, EndorsementOrderStatus.INIT),
    //(暂存中)
    STATUS_START_STORAGING("START_STORAGING", "清关开始(暂存中)", true, EndorsementOrderStatus.STORAGING),
    //(已暂存)
    STATUS_START_STORAGED("START_STORAGED", "清关开始(已暂存)", true, EndorsementOrderStatus.STORAGED),

    //只用于汇总 清关开始（暂存中）, 清关开始（已暂存），没有实际业务场景
    STATUS_START("START_STORAGED,START_STORAGING", "清关开始", false, EndorsementOrderStatus.NULL),
    STATUS_SERVERING("SERVERING", "清关服务中", true, EndorsementOrderStatus.DECALRING),
    /**
     * 关仓出入库 可以取消
     */
    STATUS_COMPLETE("COMPLETE", "清关完成(放行)", true, EndorsementOrderStatus.EXAMINE),
    STATUS_FAILURE("FAILURE", "清关失败", true, EndorsementOrderStatus.EXCEPTION),
    /**
     * 关仓出入库 可以取消
     */
    STATUS_FINISH("FINISH", "服务完成", true, EndorsementOrderStatus.FINISH),
    STATUS_DISCARD("DISCARD", "作废", false, EndorsementOrderStatus.DISCARD);

    private String code;
    private String desc;
    /**
     * 清关单当前状态是否允许取消
     */
    private Boolean allowCancel;
    /**
     * 清关单当前状态对应核注状态
     */
    private EndorsementOrderStatus endorsementStatus;

    public static InventoryOrderEnum getEnum(String value) {
        for (InventoryOrderEnum orderStatus : InventoryOrderEnum.values()) {
            if (orderStatus.getCode().equals(value)) {
                return orderStatus;
            }
        }
        return EMPTY;
    }

    public static String getDesc(String value) {
        for (InventoryOrderEnum orderStatus : InventoryOrderEnum.values()) {
            if (orderStatus.getCode().equals(value)) {
                return orderStatus.getDesc();
            }
        }
        return EMPTY.getDesc();
    }

    /**
     * 获取枚举值，通过核注状态
     *
     * @param endorsementStatus
     * @return
     */
    public static InventoryOrderEnum getByEndorsementStatus(String endorsementStatus) {
        for (InventoryOrderEnum orderStatus : InventoryOrderEnum.values()) {
            if (Objects.equal(orderStatus.getEndorsementStatus().getCode(), endorsementStatus)) {
                return orderStatus;
            }
        }
        return EMPTY;
    }
}
