package com.danding.cds.v2.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BizDeclareFormImportantGoodsMarkEnums {

    //I-料件E-成品
    NOT_IMPORTANT_GOODS("0", "非重点商品"),
    CATALOGUE_IMPORTANT_GOODS("1", "目录重点商品");

    private final String code;
    private final String desc;

    public static BizDeclareFormImportantGoodsMarkEnums getEnums(String code) {
        for (BizDeclareFormImportantGoodsMarkEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    public static String getDesc(String code) {
        for (BizDeclareFormImportantGoodsMarkEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums.getDesc();
            }
        }
        return "";
    }

}
