package com.danding.cds.customs.inventory.api.dto;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Author: Raymond
 * @Date: 2020/10/14 16:59
 * @Description:
 */
@Data
public class CustomsInventoryCalloffVO implements Serializable {
    @ApiModelProperty("id")
    private Long id;

    /**
     * 系统编号
     */
    @ApiModelProperty("系统编号")
    private String sn;

    /**
     * 申报单系统编号
     */
    @ApiModelProperty("申报单系统编号")
    private String orderSn;

    /**
     * 订单ID
     */
    @ApiModelProperty("订单ID")
    private String orderId;

    /**
     * 订单编号 申报单号
     */
    @ApiModelProperty("申报单号")
    private String declareOrderNo;

    /**
     * 清关状态
     */
    @ApiModelProperty("清关状态")
    private String customsStatus;



    @ApiModelProperty("出区状态")
    private String exitRegionStatusDesc;

    /**
     * 取消状态
     */
    @ApiModelProperty("取消状态")
    private String calloffStatus;

    /**
     * 取消类型
     */
    @ApiModelProperty("取消类型")
    private String calloffType;

    /**
     * 海关售后回执
     */
    private String cusAfterSalesCallback;

    /**
     * 海关售后回执描述
     */
    private String cusAfterSalesCallbackDesc;

    /**
     * 海关售后回执详情
     */
    private String cusAfterSalesCallbackDetail;

    /**
     * 取消原因
     */
    @ApiModelProperty("取消原因")
    private String calloffReason;

    /**
     * 驳回原因
     */
    @ApiModelProperty("驳回原因")
    private String rejectReason;

    /**
     * 物流运单编号
     */
    @ApiModelProperty("物流运单编号")
    private String logisticsNo;

    /**
     * 退货运单编号
     */
    @ApiModelProperty("物流运单编号")
    private String refundLogisticsNo;

    /**
     * 清单号
     */
    @ApiModelProperty("清单号")
    private String inventoryNo;

    @ApiModelProperty("电商")
    private String ebcName;

    /**
     * 申报企业
     */
    @ApiModelProperty("清关申报企业")
    private String agentCompanyName;

    /**
     * 区内企业
     */
    @ApiModelProperty("区内企业")
    private String areaCompanyName;

    @ApiModelProperty("取消单标记")
    private List<String> orderTagList;

    /**
     * 用户名称
     */
    @ApiModelProperty("用户名称")
    private String userName;

    @Deprecated
    @ApiModelProperty("操作人")
    private String oper;

    /**
     * 清单放行时间
     */
    private Date customsPassTime;

    /**
     * 完成时间
     * （原 取消时间）
     */
    @ApiModelProperty("完成时间")
    private Date calloffTime;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("清单节点：0可撤清单 1可退清单")
    private String orderStep;

    @ApiModelProperty("实体仓名称")
    private String entityWarehouseName;

    @ApiModelProperty("货主名称")
    private String ownerName;

    @ApiModelProperty("剔除标记")
    private String label;

    /**
     * 逆向实体仓
     */
    private String refundEntityWarehouseName;

    /**
     * 逆向货主
     */
    private String refundOwnerName;

    /**
     * 退款截图图片链接集合
     */
    private List<String> picList;

    public List<String> getPicList() {
        if (CollUtil.isEmpty(picList)) {
            return Lists.newArrayList();
        }
        return picList;
    }
}
