package com.danding.cds.v2.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 关企传输状态枚举
 */
@AllArgsConstructor
@Getter
public enum GQTransmitBizTypeEnums {

    IN_OUT_WAREHOUSE("IN_OUT_WAREHOUSE", "出入库", "https://cds-oss.oss-cn-hangzhou.aliyuncs.com/ccs/%E5%85%B3%E4%BC%81%E4%BC%A0%E8%BE%93-%E6%B5%B7%E5%85%B3%E5%87%BA%E5%85%A5%E5%BA%93%E6%A8%A1%E6%9D%BF.xlsx"),

    WAREHOUSE_IN_TRANSFORM("WAREHOUSE_IN_TRANSFORM", "库内转化", "https://cds-oss.oss-cn-hangzhou.aliyuncs.com/ccs/%E5%85%B3%E4%BC%81%E4%BC%A0%E8%BE%93-%E5%BA%93%E5%86%85%E8%BD%AC%E5%8C%96%E6%A8%A1%E6%9D%BF.xlsx"),
    ;

    private final String code;

    private final String desc;

    private final String templateUrl;

    public static GQTransmitBizTypeEnums getByCode(String code) {
        for (GQTransmitBizTypeEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }
}
