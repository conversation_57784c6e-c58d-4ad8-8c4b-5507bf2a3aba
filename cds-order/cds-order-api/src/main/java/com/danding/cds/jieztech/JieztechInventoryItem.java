package com.danding.cds.jieztech;

import lombok.Data;

import java.io.Serializable;


@Data
public class JieztechInventoryItem implements Serializable {
    private String gnum;

    private String itemRecordNo;

    private String itemNo;

    private String itemName;

    private String gcode;

    private String gname;

    private String gmodel;

    private String barCode;

    private String country;

    private String tradeCountry;

    private String currency;

    private String qty;

    private String qty1;

    private String qty2;

    private String unit;

    private String unit1;

    private String unit2;

    private String price;

    private String totalPrice;

    private String note;
}
