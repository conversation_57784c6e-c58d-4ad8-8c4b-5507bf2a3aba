package com.danding.cds.exportorder.api.enums;

import java.util.Objects;

public enum ExportOrderStatus {
//    # -- 出区单状态：订单录入中|出区申报中|出区完成
    NULL(0,"空"),
    WRITING(10,"已创建"),//订单录入中
    DECLARING(20,"待申报"),//出区申报中
    FINISH(30,"已完成"),
    DISCARD(-10,"已作废"),
    ;//出区完成

    private Integer value;

    private String desc;

    ExportOrderStatus(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static ExportOrderStatus getEnum(Integer value){
        for (ExportOrderStatus exportOrderStatus : ExportOrderStatus.values()) {
            if (exportOrderStatus.value == value){
                return exportOrderStatus;
            }
        }
        return NULL;
    }

    public static String getDesc(Integer value) {
        for (ExportOrderStatus exportOrderStatus : ExportOrderStatus.values()) {
            if (Objects.equals(exportOrderStatus.value, value)) {
                return exportOrderStatus.getDesc();
            }
        }
        return NULL.getDesc();
    }
}
