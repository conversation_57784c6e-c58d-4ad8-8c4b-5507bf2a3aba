package com.danding.cds.invenorder.api.dto;

import com.danding.cds.common.annotations.UcRoleAccountBookIdList;
import com.danding.logistics.api.common.page.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class InventoryOrderSearchV2 extends Page implements Serializable {
    private static final long serialVersionUID = -9037018796933098497L;


    @ApiModelProperty("清关单号")
    private String inveOrderSn;
    @ApiModelProperty("提单号")
    private String pickUpNo;
    @ApiModelProperty("清关状态")
    private String status;
    @ApiModelProperty("预录入核注清单编号")
    private String refCheckOrderNo;
    @ApiModelProperty("业务类型")
    private String bussinessType;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("创建开始时间")
    private Long beginCreateTime;
    @ApiModelProperty("创建结束时间")
    private Long endCreateTime;
    //    @ApiModelProperty("完成开始时间")
//    private Long beginCompleteTime;
//    @ApiModelProperty("完成结束时间")
//    private Long endCompleteTime;
    @ApiModelProperty("服务完成开始时间")
    private Long beginFinishTime;
    @ApiModelProperty("服务完成结束时间")
    private Long endFinishTime;
    @ApiModelProperty("账册id")
    private Long customsBookId;
    @ApiModelProperty("角色账册ID列表")
    @UcRoleAccountBookIdList
    private List<Long> roleAccountBookIdList;
    @ApiModelProperty("清关企业")
    private Long inveCompanyId;
    @ApiModelProperty("备货/配货单号")
    private String channelBusinessSn;
    @ApiModelProperty("核注单号")
    private String endorsementSn;
    @ApiModelProperty("是否主单  1:是 2:否")
    private Integer isMaster;
    @ApiModelProperty("实体仓名称")
    private String entityWarehouseCode;
    @ApiModelProperty("货主名称")
    private String ownerCode;
    @ApiModelProperty("出/入库单号")
    private String inOutOrderNo;
    @ApiModelProperty("外部单号")
    private String upstreamNo;
    @ApiModelProperty("是否约车")
    private Boolean isYc;

    /**
     * 单据来源|渠道
     */
    private Integer channel;

    @ApiModelProperty("核注清单编号")
    private String endorsementRealOrderNos;

    @ApiModelProperty("清关单标记")
    private Integer orderTag;

    /**
     * 清关单待办标记
     */
    private Integer orderTodoTag;

    /**
     * 是和否
     */
    @ApiModelProperty("上游是否取消")
    private Integer upstreamCancel;

    /**
     * 是否理货完成
     */
    private Boolean tallyComplete;
    /**
     * 统一料号
     */
    private String originProductId;

    /**
     * 清关回传状态
     */
    private Integer callbackStatus;

    /**
     * 关联核注清单编号
     */
    private String associatedEndorsementNo;

    /**
     * 企业核放单流水号
     */
    private String fbChecklistSn;

    /**
     * 操作人
     */
    private String operatorName;

    /**
     * 申报表编号
     */
    private String declareFormNo;
}
