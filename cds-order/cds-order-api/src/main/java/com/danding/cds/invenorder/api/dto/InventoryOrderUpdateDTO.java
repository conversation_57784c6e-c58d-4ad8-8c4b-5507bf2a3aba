package com.danding.cds.invenorder.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: yousx
 * @Date: 2024/01/05
 * @Description:
 */
@Data
@ApiModel
public class InventoryOrderUpdateDTO implements Serializable {

    @ApiModelProperty("ids, ','隔开")
    private String ids;

    @ApiModelProperty("状态")
    private String status;

}
