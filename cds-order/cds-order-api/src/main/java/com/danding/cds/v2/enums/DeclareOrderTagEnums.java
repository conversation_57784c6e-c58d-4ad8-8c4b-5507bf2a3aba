package com.danding.cds.v2.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.*;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum DeclareOrderTagEnums {
    DISCARD(1 << 0, "作废", DeclareOrderTagTypeEnum.GENERAL),
    HANG_UP(1 << 1, "挂起", DeclareOrderTagTypeEnum.TO_DO),
    BYTE_DANCE_WMS(1 << 2, "字节WMS", DeclareOrderTagTypeEnum.GENERAL),
    JIEZHOU_ENCRYPT(1 << 3, "淘分销加密", DeclareOrderTagTypeEnum.GENERAL),
    CAINIAO_WMS(1 << 4, "菜鸟WMS", DeclareOrderTagTypeEnum.GENERAL),
    CHANGE_BOOK(1 << 5, "变更账册", DeclareOrderTagTypeEnum.GENERAL),
    ;

    private final Integer code;

    private final String desc;
    /**
     * 标签类型 0:申报单标记, 1:待办标记
     */
    private final DeclareOrderTagTypeEnum tagType;

    @Getter
    @AllArgsConstructor
    public enum DeclareOrderTagTypeEnum {

        GENERAL(0, "普通"),
        TO_DO(1, "待办");

        private final Integer code;
        private final String desc;
    }

    public static List<Integer> getOrderTag(Integer useTag) {
        List<Integer> orderTagList = new ArrayList<>();
        if (useTag == null || useTag == 0) {
            return orderTagList;
        }
        for (DeclareOrderTagEnums value : DeclareOrderTagEnums.values()) {
            // 判断下orderTag是否包含有数据
            Integer code = value.getCode();
            // 排除下没有用途的占位
            if (Objects.equals(code, 0)) {
                continue;
            }
            if ((code & useTag) == code) {
                orderTagList.add(code);
            }
        }
        return orderTagList;
    }

    public static List<String> getOrderTagDescList(Integer useTag) {
        List<String> orderTagList = new ArrayList<>();
        if (useTag == null || useTag == 0) {
            return orderTagList;
        }
        for (DeclareOrderTagEnums value : DeclareOrderTagEnums.values()) {
            // 判断下orderTag是否包含有数据
            Integer code = value.getCode();
            // 排除下没有用途的占位
            if (Objects.equals(code, 0)) {
                continue;
            }
            if ((code & useTag) == code) {
                orderTagList.add(value.getDesc());
            }
        }
        return orderTagList;
    }

    /**
     * 区分标记类型
     *
     * @param useTag
     * @return
     */
    public static Map<DeclareOrderTagTypeEnum, List<String>> getOrderTagDescMap(Integer useTag) {
        Map<DeclareOrderTagTypeEnum, List<String>> orderTagMap = new HashMap<>();
        if (useTag == null || useTag == 0) {
            return orderTagMap;
        }
        for (DeclareOrderTagTypeEnum tagType : DeclareOrderTagTypeEnum.values()) {
            List<String> orderTagList = new ArrayList<>();
            for (DeclareOrderTagEnums value : DeclareOrderTagEnums.valuesByType(tagType)) {
                // 判断下orderTag是否包含有数据
                Integer code = value.getCode();
                // 排除下没有用途的占位
                if (Objects.equals(code, 0)) {
                    continue;
                }
                if ((code & useTag) == code) {
                    orderTagList.add(value.getDesc());
                }
            }
            orderTagMap.put(tagType, orderTagList);
        }
        return orderTagMap;
    }

    public static List<DeclareOrderTagEnums> valuesByType(DeclareOrderTagTypeEnum tagType) {
        return Arrays.stream(DeclareOrderTagEnums.values()).filter(e -> Objects.equals(tagType, e.getTagType())).collect(Collectors.toList());
    }

    public static Integer remove(Integer orderTag, DeclareOrderTagEnums enums) {
        if (Objects.isNull(orderTag)) {
            orderTag = 0;
        }
        Integer code = enums.getCode();
        if ((code & orderTag) == code) {
            orderTag -= code;
        }
        return orderTag;
    }

    public static Integer add(Integer orderTag, DeclareOrderTagEnums enums) {
        if (Objects.isNull(orderTag)) {
            orderTag = 0;
        }
        Integer code = enums.getCode();
        if ((code & orderTag) == code) {
            return orderTag;
        }
        return (orderTag + enums.getCode());
    }

}
