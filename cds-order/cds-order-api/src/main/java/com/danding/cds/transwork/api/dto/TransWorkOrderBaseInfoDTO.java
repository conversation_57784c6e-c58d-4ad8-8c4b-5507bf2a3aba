package com.danding.cds.transwork.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel
public class TransWorkOrderBaseInfoDTO implements Serializable {


    /**
     * 资源编码
     */
    @ApiModelProperty(value = "港到仓资源编码")
    private String resourceCode;

    @ApiModelProperty(value = "干线运输资源编码")
    private String mainLineResourceCode;

    /**
     * 起运国/地区
     */
    @ApiModelProperty(value = "起运国/地区")
    private String fromRegion;

    /**
     * 起运港
     */
    @ApiModelProperty(value = "起运港")
    private String fromLocation;

    /**
     * 起始地址
     */
    @ApiModelProperty(value = "起始地址")
    private String mainLineFromLocation;

    /**
     * 起运仓
     */
    @ApiModelProperty(value = "起运仓")
    private String fromWarehouse;

    /**
     * 目的国/地区
     */
    @ApiModelProperty(value = "目的国/地区")
    private String toRegion;

    /**
     * 目的港
     */
    @ApiModelProperty(value = "目的港")
    private String toLocation;

    /**
     * 目的地址
     */
    @ApiModelProperty(value = "目的地址")
    private String mainLineToLocation;

    /**
     * 目的仓
     */
    @ApiModelProperty(value = "目的仓")
    private String toWarehouse;

    /**
     * 港到仓装车方式
     */
    @ApiModelProperty(value = "港到仓装车方式")
    private String loadingTypePortToWh;

    /**
     * 进口口岸
     */
    @ApiModelProperty(value = "进口口岸")
    private String entryPort;
}