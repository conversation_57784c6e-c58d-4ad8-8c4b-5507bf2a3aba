package com.danding.cds.v2.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 对账订单-异常类型
 */
public interface ReconciliationItemException {

    public static final String JD_DIRECT = "JD_DIRECT";
    public static final String JD_POP = "JD_POP";


    @Getter
    @AllArgsConstructor
    public enum ExceptionTypeEnums {
        ORDER_EXCEPTION("ORDER_EXCEPTION", "订单异常"),
        DIFFERENT_PRODUCT_ID("DIFFERENT_PRODUCT_ID", "料号不一致"),
        DIFFERENT_COUNT("DIFFERENT_COUNT", "数量不一致"),
        EMG_MAPPING_EXCEPTION("EMG_MAPPING_EXCEPTION", "EMG映射异常"),
        EMG_NOT_MAPPING("EMG_NOT_MAPPING", "EMG未映射"),
        ;

        private final String code;
        private final String desc;

        public static ExceptionTypeEnums getEnums(String code) {
            for (ExceptionTypeEnums enums : ExceptionTypeEnums.values()) {
                if (enums.getCode().equals(code)) {
                    return enums;
                }
            }
            return null;
        }
    }

    @Getter
    @AllArgsConstructor
    public enum ExceptionDescEnums {
        JD_DIRECT_OUT_ORDER_NOT_EXIST("JD_DIRECT_OUT_ORDER_NOT_EXIST", "出库订单号不存在清单",
                JD_DIRECT, ExceptionTypeEnums.ORDER_EXCEPTION),
        JD_DIRECT_DIFFERENT_PRODUCT_ID("JD_DIRECT_DIFFERENT_PRODUCT_ID", "商品编码与料号不一致",
                JD_DIRECT, ExceptionTypeEnums.DIFFERENT_PRODUCT_ID),
        JD_DIRECT_DIFFERENT_COUNT("JD_DIRECT_DIFFERENT_COUNT", "出库数量和申报数量不一致",
                JD_DIRECT, ExceptionTypeEnums.DIFFERENT_COUNT),
        JD_POP_ORDER_OUT_ORDER_NOT_EXIST("JD_POP_ORDER_OUT_ORDER_NOT_EXIST", "出库订单号不存在清单",
                JD_POP, ExceptionTypeEnums.ORDER_EXCEPTION),
        JD_POP_EMG_MAPPING_EXCEPTION("JD_POP_EMG_MAPPING_EXCEPTION", "EMG编码关联多条京东备案",
                JD_POP, ExceptionTypeEnums.EMG_MAPPING_EXCEPTION),
        JD_POP_EMG_NOT_MAPPING("JD_POP_EMG_NOT_MAPPING", "EMG编码未关联京东备案",
                JD_POP, ExceptionTypeEnums.EMG_NOT_MAPPING),
        JD_POP_DIFFERENT_PRODUCT_ID("JD_POP_DIFFERENT_PRODUCT_ID", "出库单的商品货号与清单的料号不一致",
                JD_POP, ExceptionTypeEnums.DIFFERENT_PRODUCT_ID),
        JD_POP_DIFFERENT_COUNT("JD_POP_DIFFERENT_COUNT", "出库数量和申报数量不一致",
                JD_POP, ExceptionTypeEnums.DIFFERENT_COUNT),
        ;

        private final String code;
        private final String desc;
        private final String businessType;
        private final ExceptionTypeEnums typeEnums;

        public static ExceptionDescEnums getEnums(String code) {
            for (ExceptionDescEnums enums : ExceptionDescEnums.values()) {
                if (enums.getCode().equals(code)) {
                    return enums;
                }
            }
            return null;
        }

        public static List<ExceptionDescEnums> listUnmatched() {
            return Arrays.asList(JD_DIRECT_OUT_ORDER_NOT_EXIST, JD_POP_ORDER_OUT_ORDER_NOT_EXIST);
        }

        public static List<ExceptionDescEnums> getDescLike(String desc) {
            List<ExceptionDescEnums> result = new ArrayList<>();
            for (ExceptionDescEnums enums : ExceptionDescEnums.values()) {
                if (enums.getDesc().contains(desc)) {
                    result.add(enums);
                }
            }
            return result;
        }
    }
}
