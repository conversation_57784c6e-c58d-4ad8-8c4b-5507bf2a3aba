package com.danding.cds.customs.logistics.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class CustomsLogisticsDTO implements Serializable {

    private Long id;
    /**
     * 所属用户id
     */
    private Long userId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 系统编号
     */
    private String sn;

    /**
     * 申报单系统编号
     */
    private String orderSn;

    /**
     * 上游单号
     */
    private String outOrderNo;

    /**
     * 订单编号 申报单号
     */
    private String declareOrderNo;

    /**
     * 申报项状态
     */
    private Integer status;

    /**
     * 运单状态
     */
    private Integer logisticsStatus;

    /**
     * 快递标识
     */
    private String expressCode;

    /**
     * 快递方式ID
     */
    private Long expressId;

    /**
     * 物流企业
     */
    private Long logisticsCompanyId;


    /**
     * 报文传输企业
     */
    private Long agentCompanyId;

    /**
     * 关区口岸
     */
    private String customs;

    /**
     * 海关状态
     */
    private String customsStatus;
    /**
     * 清关回执描述
     */
    private String customsDetail;
    /**
     * 最后一次回执时间
     */
    private Date lastCustomsTime;

    /**
     * 最后一次申报时间
     */
    private Date lastDeclareTime;

    /**
     * 完成时间
     */
    private Date finishTime;

    /**
     * 收件人姓名
     */
    private String consigneeName;

    /**
     * 收件人省
     */
    private String consigneeProvince;

    /**
     * 收件人市
     */
    private String consigneeCity;

    /**
     * 收件人区
     */
    private String consigneeDistrict;

    /**
     * 收件人街道(四级地址)
     */
    private String consigneeStreet;

    /**
     * 收件人地址
     */
    private String consigneeAddress;

    /**
     * 收件人电话
     */
    private String consigneeTel;

    /**
     * 物流运单编号
     */
    private String logisticsNo;

    /**
     * 货值
     */
    private BigDecimal goodsTotalAmount;
    /**
     * 运费
     */
    private BigDecimal feeAmount;

    /**
     * 毛重（公斤）
     */
    private BigDecimal grossWeight;

    /**
     * json储存的商品信息
     */
    private String itemJson;

    /**
     * 订单标签
     */
    private String tagsJson;

    /**
     * 租户ID
     */
    private String tenantId;

    private Date createTime;

    /**
     * 已申报的次数
     */
    private Integer declareFrequency;

    /**
     * 净重
     */
    private BigDecimal netWeight;

    /**
     * 保费
     */
    private BigDecimal insureAmount;
}
