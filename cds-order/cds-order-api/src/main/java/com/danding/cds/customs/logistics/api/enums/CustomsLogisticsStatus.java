package com.danding.cds.customs.logistics.api.enums;

public enum CustomsLogisticsStatus {
    NULL("","空"),
    SEND_ING("2","电子口岸申报中"),
    SEND_SUCCESS("3","发送海关成功"),
    SEND_FAIL("4","发送海关失败"),
    CUSTOMS_RECEIVE("120","海关入库"),
    DEAL_EXCEPTION("-1","处理异常"),
    MANUAL_CHARGEBACK("6","人工退单"),
    INVENTORY_ORDER_NO("7","清单号不存在系统中"),
    CANNOT_WITHDRAW_ORDER("8","缴款书已生成的清单不能撤单"),
    REPORTED_NUCLEAR_BET("9","该撤销申请单对应清单已报核注清单，不允许撤单");

    private String value;

    private String desc;

    CustomsLogisticsStatus(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static CustomsLogisticsStatus getEnum(String value){
        if (Integer.parseInt(value) < 0){
            return DEAL_EXCEPTION;
        }
        for (CustomsLogisticsStatus orderStatus : CustomsLogisticsStatus.values()) {
            if (orderStatus.getValue().equals(value)){
                return orderStatus;
            }
        }
        return NULL;
    }
}
