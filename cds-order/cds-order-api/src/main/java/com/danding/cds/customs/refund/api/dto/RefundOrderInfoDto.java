package com.danding.cds.customs.refund.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class RefundOrderInfoDto implements Serializable {
    private Long id;

    private List<Long> ids;
    /**
     * 公司退货单号
     */
    private String refundNo;

    /**
     * 账册ID
     */
    private Long accountBookId;

    /**
     * 相关清单据ID(清单号)
     */
    private Long refListBillId;

    /**
     * 相关清单据号(清单号)
     */
    private String refListBillSn;

    /**
     * 申报单号ID
     */
    private Long refDeclareId;
    /**
     * 申报单号(申报单表,为了后续查询，其实也可以不要这字段)
     */

    private String refDeclareNo;
    /**
     * 退货快递公司
     */

    private String refundExpressCode;

    private String refundExpressName;
    /**
     * 渠道订单号
     */
    private String channelOrderNo;
    /**
     * 退货状态[海关审核状态]
     */
    private String refundCheckStatus;

    /**
     * 退货审核时间
     */
    private Date refundCheckTime;

    /**
     * 退货入区清关单号
     */
    private String refundInCustomsSn;

    /**
     * 清关单状态
     */
    private String customsStatus;


    /**
     * 总署回执明细
     */
    private String refundCustomCheckDetail;
    /**
     * 总署回执状态
     */
    private String refundCustomStatus;
    /**
     * 总署回执时间
     */
    private Date refundCustomTime;
    /**
     * 退货订单完成状态[初始化，待退货,退货完成,退货关闭]
     */
    private Integer refundStatus;
    /**
     * 退货订单完成时间
     */
    private Date refundStatusTime;
    /**
     * 物流运单
     */
    private String  mailNo;
    /**
     * 担保企业代码
     */
    private String  assureNo;
    /**
     * 担保企业名称
     */
    private String  assureName;
    /**
     * 退货跟踪号
     */
    private String refundMailNo;
    /**
     * 申报海关代码
     */
    private String customsCode;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 电商平台代码
     */
    private String ebpCode;
    /**
     * 电商平台名称
     */
    private String ebpName;

    /**
     * 电商企业代码
     */
    private String ebcCode;

    /**
     * 电商企业名称
     */
    private String ebcName;
    /**
     * 物流运单编号
     */

    private String logisticsNo;

    /**
     * 物流企业代码
     */

    private String logisticsCode;

    /**
     * 物流企业名称
     */

    private String logisticsName;

    /**
     * 企业内部编号
     */

    private String copNo;

    /**
     * 预录入编号
     */

    private String preNo;

    /**
     * 清单编号
     */

    private String invtNo;

    /**
     * 订购人证件类型
     */

    private String buyerIdType;

    /**
     * 订购人证件号码
     */

    private String buyerIdNumber;

    /**
     * 订购人姓名
     */

    private String buyerName;

    /**
     * 订购人电话
     */

    private String buyerTelephone;

    /**
     * 申报企业代码
     */

    private String agentCode;

    /**
     * 申报企业名称
     */

    private String agentName;

    /**
     * 退货原因
     */

    private String reason;

    /**
     * 备注
     */

    private String note;

    /**
     * 手动操作原因
     */

    private Integer operateReason;

    /**
     * 手工回执
     */
    private String manualReceipt;

    /**
     * 退货申报成功时间
     */
    private Date declareSuccessTime;

    /**
     * 区内企业id
     */
    private Long areaCompanyId;

    private Date createTime;

    private Date updateTime;

    private Integer createBy;

    private Integer updateBy;
    private Boolean deleted;

    private Long tenantryId;

    /**
     * 是否部分退标志 是否部分退  0否  1是
     */
    private Integer refundPartFlag;

    /**
     * 是否跨关区 0否  1是 默认0
     */
    private Integer refundCrossCustomsFlag;


    /**
     * 是否异常 1是 0否
     */
    private Integer refundExceptionFlag;

    /**
     * 异常信息
     */
    private String refundExceptionInfo;
}