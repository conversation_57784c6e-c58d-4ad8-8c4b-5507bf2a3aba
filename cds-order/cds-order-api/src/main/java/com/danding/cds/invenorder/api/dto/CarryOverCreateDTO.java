package com.danding.cds.invenorder.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 分区结转创建明细
 * @date 2023/4/22 12:08
 */
@Data
public class CarryOverCreateDTO implements Serializable {
    /**
     * 分区结转单号
     */
    private String carryOverNo;

    /**
     * WMS实体仓编码
     */
    private String warehouseCode;

    /**
     * 出入库单号
     */
    private String inOutOrderNo;

    /**
     * 来源企业
     */
    private String originEnterprise;

    /**
     * 目标企业
     */
    private String targetEnterprise;

    /**
     * 是否自动创建核注
     * 0 否
     * 1 是
     */
    private Integer autoCreateEndorsement;

    /**
     * 结转明细
     */
    private List<CarryOverDetailDTO> carryOverDetailList;
}
