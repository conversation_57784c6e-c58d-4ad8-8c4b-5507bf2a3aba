package com.danding.cds.order.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @Author: Raymond
 * @Date: 2020/8/13 18:22
 * @Description:
 */
@Data
@ApiModel
public class OrderBaseInfoSubmit implements Serializable {
    @ApiModelProperty("申报单id")
    @NotBlank(message = "申报单id不能为空")
    private Long id;
    @ApiModelProperty("清单")
    private InventoryBaseInfoSubmit inventoryBaseInfo;
    @ApiModelProperty("订单")
    private CustomsOrderBaseInfoSubmit customsOrderBaseInfo;
    @ApiModelProperty("支付单")
    private CustomsPaymentBaseInfoSubmit customsPaymentBaseInfo;
}
