package com.danding.cds.v2.enums.taotian;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 淘天清关单 作业单类型
 */
@Getter
@AllArgsConstructor
public enum TaotianClearanceTypeEnums {

    NULL("", "", ""),
    TOB_CLEARANCE("TOB_CLEARANCE", "跨境一线进境", "ONELINE_IN"),
    P_INTER_CLEARANCE("P_INTER_CLEARANCE", "入区区间结转", "SECTION_IN"),
    P_IN_CLEARANCE("P_IN_CLEARANCE", "入区区内结转", "SECTIONINNER_IN"),
    P_UNBONDEDGIFT_CLEARANCE("P_UNBONDEDGIFT_CLEARANCE", "大贸（非保）入区", "FB_IN"),
    R_OVERSEA_CLEARANCE("R_OVERSEA_CLEARANCE", "退运出境", "ONLINE_REFUND"),
    R_INTER_CLEARANCE("R_INTER_CLEARANCE", "出区区间结转", "SECTION_OUT"),
    R_IN_CLEARANCE("R_IN_CLEARANCE", "出区区内结转", "SECTIONINNER_OUT"),
    R_SCRAP_CLEARANCE("R_SCRAP_CLEARANCE", "销毁", "DESTORY"),
    R_UNBONDEDGIFT_CLEARANCE("R_UNBONDEDGIFT_CLEARANCE", "大贸（非保）出区", "FB_OUT"),
    P_KHZ_CLEARANCE("P_KHZ_CLEARANCE", "跨货主调拨入区", "SECTIONINNER_IN"),
    R_KHZ_CLEARANCE("R_KHZ_CLEARANCE", "跨货主调拨出区", "SECTIONINNER_OUT");

    private final String code;
    private final String desc;
    private final String invOrderBusinessTypeCode;
    public static TaotianClearanceTypeEnums getEnum(String value) {
        for (TaotianClearanceTypeEnums enums : TaotianClearanceTypeEnums.values()) {
            if (enums.getCode().equals(value)) {
                return enums;
            }
        }
        return NULL;
    }
}
