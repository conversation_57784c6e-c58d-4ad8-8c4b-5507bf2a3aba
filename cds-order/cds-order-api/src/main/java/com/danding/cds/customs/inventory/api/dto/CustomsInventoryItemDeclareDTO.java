package com.danding.cds.customs.inventory.api.dto;

import com.danding.cds.item.api.dto.CustomsBookItemDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 清单申报表体
 * @date 2022/7/19 11:21
 */
@Data
public class CustomsInventoryItemDeclareDTO implements Serializable {
    /**
     * 清单申报表体
     */
    private CustomsInventoryItemDTO customsInventoryItemDTO;
    /**
     * 是否开启库存自动切换
     */
    private Boolean stockAutoSwitchEnable;
    /**
     * 是否允许负库存校验
     */
    private Boolean negativeStockCheckEnable;
    /**
     * 备用库存切换切换
     */
    private List<CustomsBookItemDTO> itemSwitchBackUpList;

}
