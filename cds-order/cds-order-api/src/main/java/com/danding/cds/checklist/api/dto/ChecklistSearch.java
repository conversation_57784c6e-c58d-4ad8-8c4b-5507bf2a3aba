package com.danding.cds.checklist.api.dto;

import com.danding.cds.common.annotations.UcRoleAccountBookIdList;
import com.danding.logistics.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel
public class ChecklistSearch extends Page implements Serializable {

    private static final long serialVersionUID = 3444033788024686848L;

    @ApiModelProperty("状态，见下拉接口")
    private String status;

    @ApiModelProperty("状态，见下拉接口")
    private Integer type;

    /**
     * {@link com.danding.cds.checklist.api.enums.ChecklistBindType}
     */
    @ApiModelProperty("绑定类型")
    private Integer bindType;

    @ApiModelProperty("创建起始时间")
    private Long createFrom;

    @ApiModelProperty("创建终止时间")
    private Long createTo;

    @ApiModelProperty("回执起始时间")
    private Long lastCustomsFrom;

    @ApiModelProperty("回执终止时间")
    private Long lastCustomsTo;

//    @ApiModelProperty("关键词查询类型 orderNo预录入编号;realOrderNo真实编号;sn企业内部编号;licensePlate车辆信息;运单号")
//    private String queryType;

//    @ApiModelProperty("关键词查询关键词")
//    private String queryInfo;

    @ApiModelProperty("角色账册ID列表")
    @UcRoleAccountBookIdList
    private List<Long> roleAccountBookIdList;

    @ApiModelProperty("核注清单编号")
    private String EndorsementSns;

    // queryType 拆开
    /**
     * 预录入编号
     */
    private String orderNo;
    /**
     * 真实编号
     */
    private String realOrderNo;
    /**
     * 企业内部编号
     */
    private String sn;
    /**
     * 车辆信息
     */
    private String licensePlate;

    /**
     * 清关企业ID
     */
    private Long declareCompanyId;

}
