package com.danding.cds.invenorder.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class InventoryOrderRelationDTO implements Serializable {
    private static final long serialVersionUID = -2433113850499947709L;
    @ApiModelProperty("主键ID")
    private Long id;
    /**
     * 清关单ID
     */
    @ApiModelProperty("清关单ID")
    private Long refInveOrderId;
    /**
     *清关单SN
     */
    @ApiModelProperty("清关单SN")
    private String  refInveOrderSn;

    @ApiModelProperty("类型")
    private String  relType;
    @ApiModelProperty("类型描述")
    private String relTypeDesc;
    @ApiModelProperty("编号")
    private String relNo;

    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("更新时间")
    private Date updateTime;
    @ApiModelProperty("创建人ID")
    private Integer createBy;
    @ApiModelProperty("更新人ID")
    private Integer updateBy;
    @ApiModelProperty("逻辑删除")
    private Boolean deleted = false;


    @ApiModelProperty("错误信息")
    private String errorMsg;
}
