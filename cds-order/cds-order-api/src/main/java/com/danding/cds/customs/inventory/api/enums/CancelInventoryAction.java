package com.danding.cds.customs.inventory.api.enums;


public enum CancelInventoryAction {
    INVENTORY_CANCEL_APPALY(0,"清单撤销"),
    INVENTORY_DELETE_APPALY(1,"删除撤销");
    private Integer value;
    private String desc;

    CancelInventoryAction(Integer value, String desc) {
        this.value = value;
        this.desc = desc;

    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }


}
