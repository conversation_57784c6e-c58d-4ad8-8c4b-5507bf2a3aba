package com.danding.cds.collaborateorder.api.dto;

import com.danding.logistics.api.common.page.Page;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 协同单对象
 * @date 2022/3/30
 */
@Data
public class CollaborateOrderParam extends Page {
    /**
     * 协同单号
     */
    private String collaborateSn;

    private List<String> collaborateSns;
    /**
     * 协同类型
     */
    private String collaborateType;

    /**
     * 协同单状态
     */
    private String collaborateStatus;

    /**
     * 清关单号
     */
    private String inveCustomsSn;

    /**
     * 业务单号
     */
    private String inOutOrderNo;
    private List<String> inOutOrderNos;

    /**
     * 核注清单编号
     */
    private String endorsementSn;


    /**
     * 申报企业
     */
    private String agentCompanyId;

    /**
     * 实体仓id
     */
    private String warehouseName;

    /**
     * 创建开始时间
     */
    private Long createStaTime;

    /**
     * 创建结束时间
     */
    private Long createEndTime;

    /**
     * 完成开始时间
     */
    private Long finishStaTime;

    /**
     * 完成结束时间
     */
    private Long finishEndTime;

    /**
     * 清关完成时间
     */
    private Long invStaFinishTime;

    /**
     * 清关结束时间
     */
    private Long invEndFinishTime;

    /**
     * 理货完成时间
     */
    private Long tallyStaTime;

    /**
     * 理货结束时间
     */
    private Long tallyEndTime;

    /**
     * 搜索范围
     * 1.全部
     * 2.一线入境
     * 3.非一线入境
     */
    private Integer searchType;
}
