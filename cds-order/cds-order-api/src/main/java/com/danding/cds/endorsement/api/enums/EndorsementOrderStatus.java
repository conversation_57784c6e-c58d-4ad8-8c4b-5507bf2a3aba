package com.danding.cds.endorsement.api.enums;

public enum
EndorsementOrderStatus {
    NULL("", "空"),
    INIT("INIT", "已创建"),
    DECALRING("DECALRING", "申报中"),
    STORAGING("STORAGING", "暂存中"),
    STORAGED("STORAGED", "已暂存"),
    EXAMINE("EXAMINE", "已审核"),
    EXCEPTION("EXCEPTION", "核注异常"),
    STORAGE_EXCEPTION("STORAGE_EXCEPTION", "暂存异常"),
    DISCARD("DISCARD", "已作废"),
    DELETED("DELETED", "已删除"),
    FINISH("FINISH", "清关完成");


    private String code;

    private String desc;

    EndorsementOrderStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static EndorsementOrderStatus getEnum(String code){
        for (EndorsementOrderStatus value : EndorsementOrderStatus.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return NULL;
    }
}
