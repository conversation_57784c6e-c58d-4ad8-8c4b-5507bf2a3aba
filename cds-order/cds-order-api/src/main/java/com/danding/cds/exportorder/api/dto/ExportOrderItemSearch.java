package com.danding.cds.exportorder.api.dto;

import com.danding.cds.common.annotations.UcRoleAccountBookIdList;
import com.danding.logistics.api.common.page.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class ExportOrderItemSearch extends Page implements Serializable {

    /**
     * 出库单id
     */
    @NotNull(message = "出库单id不能为空")
    private Long exportOrderId;

    /**
     * 账册编号
     */
    private String accountBookNo;

    @ApiModelProperty("角色账册ID列表")
    @UcRoleAccountBookIdList
    private List<Long> roleAccountBookIdList;
}
