package com.danding.cds.customs.inventory.api.enums;

import com.google.common.base.Objects;

/**
 * 超时时间（天）
 */
public enum TimeOutDayEnum {
    HOUR_12(-12, "12h"),
    HOUR_24(-24, "24h"),
    <PERSON><PERSON><PERSON>_36(-36, "36h"),
    DAY_3(-72, "3d"),
    DAY_7(-168, "7d"),
    DAY_10(-240, "10d"),
    DAY_14(-336, "14d"),
    DAY_21(-504, "21d"),
    DAY_28(-672, "28d"),
    DAY_35(-840, "35d");

    //定义单位改为小时
    private Integer value;
    private String desc;

    TimeOutDayEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;

    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static TimeOutDayEnum getEnum(Integer value) {
        for (TimeOutDayEnum item : TimeOutDayEnum.values()) {
            if (Objects.equal(item.value, value)) {
                return item;
            }
        }
        return null;
    }

}
