package com.danding.cds.v2.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 清单-电子税单状态
 */
@Getter
@AllArgsConstructor
public enum TaxBillStatusEnums {

    NOT_SEND(0, "未下发"),
    SENT(1, "已下发");

    private final Integer code;
    private final String desc;

    public static TaxBillStatusEnums getEnums(Integer code) {
        if (code == null) {
            return null;
        }
        for (TaxBillStatusEnums enums : TaxBillStatusEnums.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

}
