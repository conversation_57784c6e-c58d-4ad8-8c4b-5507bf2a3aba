package com.danding.cds.customs.refund.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel
public class MailNoInfoRecord implements Serializable {
    private static final long serialVersionUID = -8592623245709946888L;
    @ApiModelProperty("运单号")
    private String mailNo;
    @ApiModelProperty("退货运单号")
    private  String refundMailNo;
    @ApiModelProperty("退货快递名称")
    private String  refundExpressName;
    @ApiModelProperty("错误信息")
    private String errorMsg;
}
