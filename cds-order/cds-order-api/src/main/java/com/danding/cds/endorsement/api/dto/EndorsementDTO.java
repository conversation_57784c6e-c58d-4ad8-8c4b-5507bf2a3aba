package com.danding.cds.endorsement.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

@Data
public class EndorsementDTO implements Serializable {
    private static final long serialVersionUID = 45426560238422277L;

    private Long id;
    /**
     * 出区单ID
     */
    private Long exportOrderId;

    /**
     * 清关单ID
     */
    private Long inventoryOrderId;

    /**
     * 出入区标志
     */
    private Integer ieFlag;

    /**
     * 一票多车标识
     */
    private Boolean checklistsFlag;

    /**
     * 业务类型
     */
    private String bussinessType;
    /**
     * 企业内部核注清单编号
     */
    private String sn;

    /**
     * 预录入核注单号
     */
    private String preOrderNo;

    /**
     * 真实核注清单编号
     */
    private String realOrderNo;

    /**
     * 账册ID
     */
    private Long accountBookId;

    /**
     * 清关企业ID
     */
    private Long declareCompanyId;

    /**
     * 核注单状态
     */
    private String status;
    /**
     * 海关回执状态
     */
    private String customsStatus;
    /**
     * 海关回执描述
     */
    private String informationDesc;
    /**
     * 完成时间
     */
    private Date finishTime;

    private String extraJson;

    /**
     * 是否核扣库存
     */
    private Boolean stockChangeEnable;

    private Date createTime;

    /**
     * 获取是否核扣库存
     * 是 或 null 都返回为true， 否则返回 false
     *
     * @return
     */
    public Boolean getStockChangeEnableLogic() {
        if (Objects.isNull(this.stockChangeEnable)) {
            return true;
        }
        return this.stockChangeEnable;
    }

    /**
     * 出库单
     */
    private String outBond;

    /**
     * 备注
     */
    private String remark;

    /**
     * 监管方式
     */
    private String supvMode;

    /**
     * 录入员IC卡号
     */
    private String icCardNo;

    /**
     * 清单类型
     */
    private String invtType;

    /**
     * 报关单统一编号
     */
    private String customsEntrySeqNo;

    /**
     * 报关单生成状态
     */
    private String generateDeclareStatus;

    /**
     * 报关单生成失败原因
     */
    private String generateDeclareReason;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private Integer createBy;

    /**
     * 单据类型
     */
    private String orderType;

    /**
     * 运输方式
     */
    private String transportMode;
}
