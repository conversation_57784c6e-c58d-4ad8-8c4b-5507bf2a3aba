package com.danding.cds.exception.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 查询返回对象
 * @date 2022/5/9
 */
@Data
public class ExceptionDTO implements Serializable {

    private Long id;


    /**
     * 异常名称
     */
    private String exceptionName;


    /**
     * 异常分类 1.业务异常2.系统异常3.海关异常
     */
    private Integer exceptionClassify;

    /**
     * 处理建议
     */
    private String handlePropose;

    /**
     * 申报单能否重推：0否;1是
     */
    private Integer repushEnable;

    /**
     * 用途标签(二进制运算)：1:海关回执文本匹配
     * ExceptionUseTagEnum
     */
    private Integer useTag;

    /**
     * 异常描述
     */
    private String exceptionDescribe;

    private Date updateTime;

}
