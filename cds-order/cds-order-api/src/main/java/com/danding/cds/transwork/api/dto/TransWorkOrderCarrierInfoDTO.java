package com.danding.cds.transwork.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel
public class TransWorkOrderCarrierInfoDTO implements Serializable {

    /**
     * 运输方式（AIR=空运、OCEAN=海运、RAILWAY=铁路、TRUCK=陆运）
     */
    @ApiModelProperty(value = "运输方式（AIR=空运、OCEAN=海运、RAILWAY=铁路、TRUCK=陆运）")
    private String transportModeDesc;

    private String transportMode;

    /**
     * 主提单号
     */
    @ApiModelProperty(value = "主提单号")
    private String mblNo;

    /**
     * 分提单号
     */
    @ApiModelProperty(value = "分提单号")
    private String hblNo;

    /**
     * 承运商
     */
    @ApiModelProperty(value = "承运商")
    private String carrierName;

    /**
     * 船名
     */
    @ApiModelProperty(value = "船名")
    private String shippingName;

    /**
     * 航次
     */
    @ApiModelProperty(value = "航次")
    private String flightNo;

    /**
     * 航班日期（YYYY-DD-MM）
     */
    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    /**
     * 期望到港时间（YYYY-MM-DD）
     */
    @ApiModelProperty(value = "期望到港时间")
    private String expectArrivePortDate;

    /**
     * 集装箱清单
     */
    @ApiModelProperty(value = "集装箱清单")
    private List<String> containerCodes;

}