package com.danding.cds.transwork.api.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: yousx
 * @Date: 2024/03/12
 * @Description:
 */
@Data
@ApiModel
public class TransWorkTimeEditVO implements Serializable {

    private String id;

    @ApiModelProperty(value = "实际到达时间")
    private Date actArrTime;

    @ApiModelProperty(value = "实际发车时间")
    private Date actDepTime;

    @ApiModelProperty(value = "实际签收时间")
    private Date actSignTime;
}
