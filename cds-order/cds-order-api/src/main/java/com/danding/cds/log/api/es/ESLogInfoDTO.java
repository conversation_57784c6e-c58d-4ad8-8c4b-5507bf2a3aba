package com.danding.cds.log.api.es;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ESLogInfoDTO implements java.io.Serializable{
    private String id;
    /**
     * 申报单号
     */
    private String declareNo;
    /**
     * 单据类型【清单，退货，撤单，运单】
     */
    private String sn;

    /**
     * 相关的数据库日志Id
     */
    private String traceId;

    private Long logTime;
    /**
     * 请求日志【如果是申报，就是请求报文 | 如果是回执，就是回执报文】
     *
     */
    private String xmlRequest;
    /**
     * 客户端日志【如果是申报，就是webservice返回结果 | 如果是回执，就是解析后返回结果json】
     */
    private String xmlResponse;
    /**
     * 创建时间
     */
    private long createTime;

}
