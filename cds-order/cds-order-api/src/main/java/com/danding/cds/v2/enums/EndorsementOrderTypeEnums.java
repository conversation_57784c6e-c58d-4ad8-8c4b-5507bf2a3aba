package com.danding.cds.v2.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum EndorsementOrderTypeEnums {

    INVENTORY_ORDER("1", "清关单"),
    EXPORT_ORDER("2", "申报出库单"),
    EXCEL_IMPORT("3", "导入");

    private final String code;
    private final String desc;

    public static EndorsementOrderTypeEnums getEnums(String code) {
        for (EndorsementOrderTypeEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    public static String getDesc(String code) {
        for (EndorsementOrderTypeEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums.getDesc();
            }
        }
        return "";
    }

}
