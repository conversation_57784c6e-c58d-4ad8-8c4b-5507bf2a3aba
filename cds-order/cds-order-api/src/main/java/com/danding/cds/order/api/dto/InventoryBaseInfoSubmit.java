package com.danding.cds.order.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * @Author: Raymond
 * @Date: 2020/7/29 10:08
 * @Description:
 */
@Data
@ApiModel
public class InventoryBaseInfoSubmit implements Serializable {
    @ApiModelProperty("清单Id")
    private String id;
    /**
     * 清单： 订购人证件号码
     */
    @ApiModelProperty("清单：订购人证件号码")
    @NotBlank(message = "订购人证件号码不能为空")
    private String buyerIdNumber;

    /**
     * 清单：订购人姓名
     */
    @ApiModelProperty("清单：订购人姓名")
    @NotBlank(message = "订购人姓名不能为空")
    private String buyerName;

    /**
     * 清单：收件人省
     */
    @ApiModelProperty("清单：收件人省")
    @NotBlank(message = "收件人省不能为空")
    private String consigneeProvince;

    /**
     * 清单：收件人市
     */
    @ApiModelProperty("清单：收件人市")
    @NotBlank(message = "收件人市不能为空")
    private String consigneeCity;

    /**
     * 清单：收件人区
     */
    @ApiModelProperty("清单：收件人区")
    private String consigneeDistrict;

    /**
     * 清单：收件人地址
     */
    @ApiModelProperty("清单：收件人地址")
    @NotBlank(message = "收件人地址不能为空")
    private String consigneeAddress;

    /**
     * 清单：商品项
     */
    @ApiModelProperty("清单：商品项")
    private List<InventoryItemInfoSubmit> itemList;

}
