package com.danding.cds.invenorder.api.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class InvBusinessCountDTO implements Serializable {

    /**
     * 一线入境总数
     */
    private Integer onelineInCount;
    /**
     * 区间流转(入)
     */
    private Integer sectionInCount;
    /**
     * 区内流转(入)
     */
    private Integer sectionInnerInCount;

    /**
     * 区间流转(出)
     */
    private Integer sectionOutCount;

    /**
     * 区内流转(出)
     */
    private Integer sectionInnerOutCount;

    /**
     * 退货入区
     */
    private Integer refundInAreaCount;

    /**
     * 销毁
     */
    private Integer destroyCount;

    /**
     * 一线退运
     */
    private Integer oneLineRefund;

    /**
     * 保税物流转大贸
     */
    private Integer bondedToTrade;

    /**
     * 后续补税
     */
    private Integer subsequentTax;
}
