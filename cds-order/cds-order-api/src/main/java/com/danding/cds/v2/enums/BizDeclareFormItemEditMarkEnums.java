package com.danding.cds.v2.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BizDeclareFormItemEditMarkEnums {

    //0-未修改、1-修改、2-删除、3-增加
    UNCHANGED("0", "未修改"),
    CHANGED("1", "修改"),
    DELETED("2", "删除"),
    ADDED("3", "增加");

    private final String code;
    private final String desc;

    public static String getDesc(String code) {
        for (BizDeclareFormItemEditMarkEnums value : BizDeclareFormItemEditMarkEnums.values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value.getDesc();
            }
        }
        return "";
    }
}
