package com.danding.cds.invenorder.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: Raymond
 * @Date: 2020/11/30 9:17
 * @Description:
 */
@Data
public class InvenorderStatusDTO implements Serializable {
    @ApiModelProperty("sn")
    private String inveCustomsSn;

    @ApiModelProperty("业务类型")
    private String inveBusinessType;


    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("出库单号")
    private List<String> outBoundList = new ArrayList<>();

    /**
     * 完成时间
     */
    private Long finishTime;

}
