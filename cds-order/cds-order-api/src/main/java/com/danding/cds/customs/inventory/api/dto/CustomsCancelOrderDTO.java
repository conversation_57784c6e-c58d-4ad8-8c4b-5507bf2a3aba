package com.danding.cds.customs.inventory.api.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2022/1/4
 */
@Data
public class CustomsCancelOrderDTO implements Serializable {

    /**
     * 任务Id
     */
    private String taskId;


    /**
     * 状态类型
     */
    private String statusStr;
    private Integer status;

    /**
     * 开始时间
     */
    private String staCreateTime;

    /**
     * 结束时间
     */
    private String endCreateTime;

    //查询类型 1 - 申报单号查询，2 - 时间查询
    private Integer queryType;

    /**
     * 备注
     */
    private String remark;
}
