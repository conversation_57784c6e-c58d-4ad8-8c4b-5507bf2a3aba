package com.danding.cds.jieztech;

import lombok.Data;

import java.io.Serializable;


@Data
public class JieztechDeclareInventoryItem implements Serializable {
    /**
     * 排序
     */
    public String gnum;
    /**
     * 商品淘宝 sku
     */
    public String itemSku;
    /**
     * 账册备案料号（金二账册序号）
     */
    public String itemRecordNo;
    /**
     * 货号
     */
    public String itemNo;
    /**
     * 商品编码
     */
    public String gcode;
    /**
     * 商品备案名称
     */
    public String gname;
    /**
     * 规格
     */
    public String gmodel;
    /**
     * 条码
     */
    public String barCode;
    /**
     * 原产国
     */
    public String country;
    /**
     * 币制
     */
    public String currency = "142";

    public String qty;

    public String unit;

    public String qty1;

    public String unit1;

    public String qty2;

    public String unit2;

    public String price;

    public String totalPrice;

    public String hsCode;
}
