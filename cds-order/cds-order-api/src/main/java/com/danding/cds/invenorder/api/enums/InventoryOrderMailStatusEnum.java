package com.danding.cds.invenorder.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 质押货主-邮件状态
 */
@Getter
@AllArgsConstructor
public enum InventoryOrderMailStatusEnum {

    EMPTY(0, "空"),
    WAIT_SEND(1, "待发送"),
    WAIT_REPLY(2, "待回复"),
    PASS(3, "通过"),
    REJECT(4, "驳回");

    private final Integer code;
    private final String desc;

    public static InventoryOrderMailStatusEnum getEnum(Integer code) {
        for (InventoryOrderMailStatusEnum value : InventoryOrderMailStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return EMPTY;
    }
}
