package com.danding.cds.transwork.api.rpc;

import com.danding.cds.transwork.api.vo.*;
import com.danding.cds.v2.bean.dto.TransWorkOrderDTO;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;

import java.util.List;

/**
 * @Author: yousx
 * @Date: 2024/03/12
 * @Description:
 */
public interface TransWorkRpc {

    /**
     * 分页
     * @param search
     * @return
     */
    ListVO<TransWorkPageVO> paging(TransWorkSearch search) throws ArgsErrorException;

    TransWorkDetailVO detail(Long id);

    /**
     * 商品分页
     * @param id
     * @return
     */
    ListVO<TransWorkItemVO> itemPaging(Long id);

    /**
     * 商品编辑
     * @param param
     * @return
     */
    void itemEdit(TransWorkItemEditVO param) throws ArgsErrorException;

    /**
     * 时间编辑
     * @param param
     * @return
     */
    void editTimeInfo(TransWorkTimeEditVO param) throws ArgsErrorException;

    /**
     * 添加车辆信息
     * @param param
     */
    void addTruckInfo(TransTruckInfoVO param) throws ArgsErrorException;

    /**
     * 完成运输作业单
     * @param id
     */
    void finishTransWork(Long id) throws ArgsErrorException;

    List<TransWorkOrderDTO> findByTruckResourceCode(String vehicleCode);

    List<TransWorkOrderDTO> findByTruckResourceCode(String vehicleCode, String truckNo);

    List<TransWorkOrderPackagingItemsVO> getPackagingItems(Long id);

    void packagingItemEdit(TransWorkItemEditVO param) throws ArgsErrorException;

    void addTruckNo(TransAddTruckNoVO param) throws ArgsErrorException;
}
