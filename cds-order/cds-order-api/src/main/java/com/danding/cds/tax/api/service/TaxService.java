package com.danding.cds.tax.api.service;

import com.danding.cds.order.api.dto.OrderSimpleSubmitItem;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: Raymond
 * @Date: 2020/9/8 16:14
 * @Description:
 */
public interface TaxService {
    /**
     * 根据HS编码计算税费
     * @param customsHsCode hs编码
     * @param price 价格
     * @param goodsCustomsHsPar 商品计税规格
     * @return
     */
    BigDecimal calculateByHsCode(String customsHsCode, BigDecimal price, Integer count, BigDecimal goodsCustomsHsPar);

    /**
     * 根据sku编码和仓库编码计算税费
     * @param skuCode sku
     * @param wareCode 仓库编码
     * @param price 商品价格
     * @param count 商品个数
     * @param goodsCustomsHsPar 商品计税规格
     * @return
     */
    BigDecimal calculateBySku(String skuCode, String wareCode, BigDecimal price, Integer count, BigDecimal goodsCustomsHsPar);


    /**
     * 计算商品项的总税费
     * @param itemList 商品项
     * @param feeAmount 运费
     * @param wareCode 仓库编码
     * @param goodsCustomsHsPar 商品计税规格
     * @return
     */
    BigDecimal calculateItemsTotalTax(List<OrderSimpleSubmitItem> itemList, BigDecimal feeAmount, String wareCode, BigDecimal goodsCustomsHsPar);
}
