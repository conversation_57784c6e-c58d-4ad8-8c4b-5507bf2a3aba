package com.danding.cds.customs.inventory.api.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2022/1/4
 */
@Data
public class CustomsCancelOrderDetailsDTO implements Serializable {

    /**
     * 子任务Id
     */
    private String subtasksId;


    /**
     * 订单编号 申报单号
     */
    private String declareOrderNo;

    /**
     * 状态类型
     */
    private String statusStr;

    //查询类型 1 - 申报单号查询，2 - 时间查询
    private Integer queryType;

    /**
     * 处理总数
     */
    private Integer count;


}
