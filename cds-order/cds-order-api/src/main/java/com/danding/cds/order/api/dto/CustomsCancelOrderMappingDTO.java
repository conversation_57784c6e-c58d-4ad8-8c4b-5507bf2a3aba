package com.danding.cds.order.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Create 2021/7/29  14:06
 * @Describe 供BMS调用
 **/
@Data
@Accessors(chain = true)
public class CustomsCancelOrderMappingDTO implements Serializable {
    @ApiModelProperty("id")
    private Long id;
    @ApiModelProperty("退货单类型:退货/撤单")
    private String type;
    @ApiModelProperty("外部单号")
    private String outOrderNo;
    @ApiModelProperty("申报单号")
    private String declareOrderNo;
    @ApiModelProperty("运单号")
    private String logisticsNo;
    @ApiModelProperty("清单编号")
    private String inventoryNo;
    @ApiModelProperty("售后创建时间")
    private Date createTime;
    @ApiModelProperty("售后取消时间")
    private Date calloffTime;
    @ApiModelProperty("实体仓编码")
    private String entityWarehouseCode;
    @ApiModelProperty("实体仓名称")
    private String entityWarehouseName;
    @ApiModelProperty("货主编码/名称")
    private String ownerCode;
    @ApiModelProperty("货主编码/名称")
    private String ownerName;
    @ApiModelProperty("商品")
    private List<CalloffOrderItemDTO> calloffOrderItemDTOList;
}
