package com.danding.cds.order.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * @Author: Raymond
 * @Date: 2020/8/13 18:27
 * @Description:
 */
@Data
@ApiModel
public class CustomsOrderBaseInfoSubmit implements Serializable {

    private String id;

    /**
     * 订购人证件号码
     */
    @ApiModelProperty("订购人证件号码")
    @NotBlank(message = "订购人证件号码不能为空")
    private String buyerIdNumber;

    /**
     * 订购人姓名
     */
    @ApiModelProperty("订购人姓名")
    @NotBlank(message = "订购人姓名不能为空")
    private String buyerName;

    @ApiModelProperty(value = "收货省")
    private String consigneeProvince;

    @ApiModelProperty(value = "收货市")
    private String consigneeCity;

    @ApiModelProperty(value = "收货区")
    private String consigneeDistrict;

    @ApiModelProperty(value = "详细地址")
    private String consigneeAddress;

    @ApiModelProperty(value = "商品项")
    List<CustomsOrderItemBaseInfo> itemList;
}
