package com.danding.cds.jieztech;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public class JieztechInventoryHead implements Serializable {
    /**
     * 提交时间 20200617020101
     */
    private String commitTime;
    /**
     * 操作类型 1-新增 2-变更 3-删除
     */
    private String appType = "1";
    /**
     * 业务状态 业务状态:1-暂 存,2-申报,默
     * 认为 2。
     */
    private String appStatus = "2";
    /**
     * 报关类别B：B 类快件，个人行
     * 邮
     * 9610：跨境直邮
     * 1210：保税进口
     */
    private String entryType = "1210";
    /**
     * 订单编号
     * 交易平台的订单编
     * 号，同一交易平台
     * 的订单编号应唯一。
     * 订单编号长
     * 度不能超过 60 位。
     */
    private String orderNo;
    /**
     * 电商平台代码
     */
    private String ebpCode;
    /**
     * 电商平台名称
     */
    private String ebpName;
    /**
     * 电商企业代码
     */
    private String ebcCode;
    /**
     * 电商企业名称
     */
    private String ebcName;
    /**
     * 支付企业代码
     */
    private String payCode;
    /**
     * 支付企业名称
     */
    private String payName;
    /**
     * 物流运单编号
     */
    private String logisticsNo;
    /**
     * 物流企业代码
     */
    private String logisticsCode;
    /**
     * 物流企业名称
     */
    private String logisticsName;
    /**
     * 企业内部编号
     */
    private String copNo;
    /**
     * 预录入编号
     */
    private String preNo;
    /**
     * 担保企业编号
     */
    private String assureCode;
    /**
     * 账册编号
     */
    private String emsNo;
    /**
     * 清单编号
     */
    private String invtNo;
    /**
     * 进出口标记
     * I-进口,E-出口
     */
    private String ieFlag = "I";
    /**
     * 申报日期 YYYYMMDD
     */
    private String declTime;
    /**
     * 申报海关代码
     */
    private String customsCode;
    /**
     * 口岸海关代码
     */
    private String portCode;
    /**
     * 进口日期 YYYYMMDD
     */
    private String ieDate;
    /**
     * 订购人证件类型
     */
    private String buyerIdType = "1";
    /**
     * 订购人证件号码
     */
    private String buyerIdNumber;
    /**
     * 订购人姓名
     */
    private String buyerName;
    /**
     * 订购人电话
     */
    private String buyerTelephone;
    /**
     * 收货人姓名
     */
    private String consignee;
    /**
     * 收货人电话
     */
    private String consigneeTelephone;
    /**
     * 收货人省
     */
    private String consigneePrvince;
    /**
     * 收货人市
     */
    private String consigneeCity;
    /**
     * 收货人区
     */
    private String consigneeCounty;
    /**
     * 收件地址
     */
    private String consigneeAddress;
    /**
     * 申报企业代码
     */
    private String agentCode;
    /**
     * 申报企业名称
     */
    private String agentName;
    /**
     * 区内企业代码
     */
    private String areaCode;
    /**
     * 区内企业名称
     */
    private String areaName;
    /**
     * 贸易方式
     */
    private String tradeMode = "1210";
    /**
     * 运输方式
     */
    private String trafMode = "7";
    /**
     * 运输工具编号
     */
    private String trafNo;
    /**
     * 航班航次号
     */
    private String voyageNo;
    /**
     * 提运单号
     */
    private String billNo;
    /**
     * 监管场所代码
     */
    private String loctNo;
    /**
     * 许可证件号
     */
    private String licenseNo;
    /**
     * 起运国（地区）
     */
    private String country = "142";
    /**
     * 申报金额
     */
    private BigDecimal acturalPaid;
    /**
     * 商品金额
     */
    private BigDecimal goodsValue;
    /**
     * 非现金抵扣金额
     */
    private BigDecimal discount;
    /**
     * 税费
     */
    private BigDecimal taxTotal;
    /**
     * 运费
     */
    private BigDecimal freight;
    /**
     * 保费
     */
    private BigDecimal insuredFee;
    /**
     * 币制
     */
    private String currency = "142";
    /**
     * 支付交易申报编号
     */
    private String payTransactionId;
    /**
     * 包装种类代码
     */
    private String wrapType;
    /**
     * 件数
     */
    private String packNo = "1";
    /**
     * 毛重（公斤）
     */
    private String grossWeight;
    /**
     * 净重（公斤）
     */
    private String netWeight;
    private String note;
    private List<JieztechInventoryItem> list = new ArrayList<>();
}
