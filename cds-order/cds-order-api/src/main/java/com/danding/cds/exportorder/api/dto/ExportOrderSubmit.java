package com.danding.cds.exportorder.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel
public class ExportOrderSubmit implements Serializable {

    private Long id;

    @ApiModelProperty("快递公司ID")
    private List<Long> expressIdList;

    @ApiModelProperty("账册ID")
    @Deprecated
    private Long accountBookId;

    @Deprecated
    @ApiModelProperty("清关企业ID")
    private Long declareCompanyId;

    @ApiModelProperty("实体仓编码")
    private String entityWarehouseCode;
}
