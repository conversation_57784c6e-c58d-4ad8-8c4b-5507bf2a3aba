package com.danding.cds.order.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class FilterSpecialConfigDTO implements Serializable {

    /**
     * 企业微信通知webHook   0 不开启，1开启
     */
    public String isFilterSpecialChar;
    /**
     * 企业微信通知webHook
     */
    public String webHook;
    /**
     * 通知人电话
     */
    public List<String> phoneList;

    /**
     * 电商平台的过滤，不需要过滤特殊字符
     */
    public List<String> notFilterPlatformCodeList;
}
