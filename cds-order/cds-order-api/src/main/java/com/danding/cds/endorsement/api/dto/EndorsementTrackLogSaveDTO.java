package com.danding.cds.endorsement.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: 核注单日志保存dto
 * @date 2023/6/25 17:04
 */
@Data
public class EndorsementTrackLogSaveDTO implements Serializable {

    /**
     * 核注单id
     */
    private Long endorsementId;

    /**
     * 核注单状态
     */
    private String status;

    /**
     * 日志描述
     */
    private String logInfo;

    /**
     * 回执详情
     */
    private String callbackDetail;

    /**
     * 操作时间
     */
    private Date createTime;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 压缩方式
     */
    private String compressType;
}
