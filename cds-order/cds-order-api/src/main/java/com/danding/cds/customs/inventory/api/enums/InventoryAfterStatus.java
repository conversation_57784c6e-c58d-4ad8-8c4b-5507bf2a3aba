package com.danding.cds.customs.inventory.api.enums;

public enum InventoryAfterStatus {
    NO(1, "暂无"),
    YES(2, "发起售后成功"),

    CANCEL_SUCCESS(3, "撤单完成"),

    REFUND_SUCCESS(4, "退货完成");

    private Integer value;

    private String desc;

    InventoryAfterStatus(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static InventoryAfterStatus getEnum(Integer value){
        for (InventoryAfterStatus afterStatus : InventoryAfterStatus.values()) {
            if (afterStatus.getValue() == value){
                return afterStatus;
            }
        }
        return NO;
    }
}
