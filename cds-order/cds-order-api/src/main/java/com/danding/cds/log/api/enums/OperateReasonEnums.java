package com.danding.cds.log.api.enums;

import lombok.Getter;

@Getter
public enum OperateReasonEnums {

    OTHER(0,"其他"),
    PUSH_EXCEPTION(1,"报文推送异常"),
    CUSTOMS_NO_RECEIPT(3,"海关无回执"),
    TIME_OUT(5,"清单放行超30天"),
    NEED_NOT_REFUND(7,"无需退货/撤单");


    private Integer value;

    private String desc;

    OperateReasonEnums(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static OperateReasonEnums getEnum(Integer value){
        for (OperateReasonEnums operateReasonEnums : OperateReasonEnums.values()) {
            if (operateReasonEnums.getValue().equals(value)){
                return operateReasonEnums;
            }
        }
        return OTHER;
    }
}
