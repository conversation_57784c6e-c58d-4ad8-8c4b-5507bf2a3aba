package com.danding.cds.exportorder.api.service;

import com.danding.cds.endorsement.api.dto.EndorsementDTO;
import com.danding.cds.exportorder.api.dto.*;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 出区单
 */
public interface ExportOrderService {

    Set<Long> listIdsByExpress(Long expressId);

    // Module::出区单操作
    Long create(ExportOrderSubmit submit);

    ListVO<ExportOrderDTOV2> paging(ExportOrderSearch search);

    ExportOrderDTO findById(Long id);

    List<ExportOrderDTO> findById(List<Long> id);

    ExportOrderDTO findBySn(String sn);

    List<ExportOrderDTO> findBySn(List<String> sn);

    List<ExportOrderDTO> listByStatus(Integer status);

    /**
     * 删除
     * @param id
     */
    void deleteById(Long id);

    /**
     * 作废
     * @param id
     */
    void discardById(Long id);

    /**
     * 运单录入 做到一起是因为，编辑核注清单的订单项，是集合最复杂的一种情况，只要实现了这种，其他的就是控制其部分功能生效
     * @param id 出区单ID
     * @param importList 导入列表
     * @param save 是否持久化
     * @return
     * @throws ArgsErrorException
     */
    ExportItemWritingReport writing(Long id, List<ExportItemRecord> importList , Boolean save) throws ArgsErrorException;
    void writingByItem(Long id, ExportItemWritingReport report) throws ArgsErrorException;

    /**
     * 分块写入表体
     *
     * @param id
     * @param report
     */
    void writingBlockByItem(Long id, ExportItemWritingReport report);

    /**
     * 运单绑定（提供给交接单）
     *
     * @param id
     * @param report
     */
    void bindWaybill(Long id, ExportItemWritingReport report);

    ExportItemEditReport editByEndorsement(Long endorsementId,
                                                 List<ExportItemRecord> addList,
                                                 List<ExportItemRecord> delList,
                                                 Boolean save) throws ArgsErrorException;

    // Module::出区订单项操作
    List<ExportItemDTO> listItemByMailNos(Set<String> mainNoSet);

    /**
     * 根据运单号查询单个
     *
     * @param mainNo
     * @return
     */
    ExportOrderDTO itemByMailNos(String mainNo);

    ExportItemDTO findByItemMailNo(String mainNo);

    Map<String, String> listExportOrderByMailNo(List<String> mailNoList);

    List<ExportItemDTO> listItemById(Long id);

    List<ExportItemDTO> listItemByIdAndBookId(Long id, Long accountBookId);

    List<ExportItemDTO> listItemByEndorsementId(Long endorsementId);

    List<ExportItemDTO> listItemByEndorsementIds(Set<Long> endorsementIds);

    ExportItemDTO findItemByCustomInventory(String customsInventorySn);

    /**
     * 获取清关单号 - 出库单表体 对应map
     *
     * @param customsInventorySnList 清关单号List
     * @return
     */
    Map<String, ExportItemDTO> getInvetSnItemMapByCustomInventoryList(List<String> customsInventorySnList);

    List<ExportItemDTO> listItemByCustomInventorySAndEndorsementId(List<String> customsInventorySns, Long endorsementOrderId);

    /**
     * 按照清单规则对出库订单进行分组
     * key: 账册id_组号
     * @param id
     * @return
     */
    Map<String, List<ExportItemDTO>> distributionForEndorsement(Long id) throws ArgsErrorException;

    /**
     * 根据清单编号删除出库单表体
     *
     * @param customsInventoryList
     */
    void deleteByCustomsInventorySn(List<String> customsInventoryList);

    /**
     * 根据清单sn获取出库单绑定的核注单号
     *
     * @param customsInventorySnList
     * @return
     */
    Map<String, EndorsementDTO> getEndorsementByCustomsInventorySnList(List<String> customsInventorySnList);

    Map<String, Long> findEndorsementIdListByLogisticsNo(List<String> logisticsNo);

    /**
     * 删除出库单表体 并解除关联关系
     */
    void deleteExportItemByEndorsement(Long endorsementId, Long exportOrderId);

    ListVO<ExportItemDTO> itemPaging(ExportOrderItemSearch search);
}
