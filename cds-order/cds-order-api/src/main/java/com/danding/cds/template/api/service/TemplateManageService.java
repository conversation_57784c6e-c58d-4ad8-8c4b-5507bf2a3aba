package com.danding.cds.template.api.service;

import com.danding.cds.template.api.dto.TemplateManageDTO;
import com.danding.cds.template.api.dto.TemplateManageSearch;
import com.danding.cds.template.api.dto.TemplateManageSubmit;
import com.danding.logistics.api.common.response.ListVO;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 模板管理服务接口
 * @date 2025/7/31
 */
public interface TemplateManageService {

    /**
     * 分页查询模板
     *
     * @param search 查询参数
     * @return 分页结果
     */
    ListVO<TemplateManageDTO> paging(TemplateManageSearch search);

    /**
     * 上传模板
     *
     * @param submit 提交参数
     * @return 模板ID
     */
    Long upload(TemplateManageSubmit submit);

    /**
     * 编辑模板
     *
     * @param submit 提交参数
     * @return 模板ID
     */
    Long edit(TemplateManageSubmit submit);

    /**
     * 根据ID查询模板详情
     *
     * @param id 模板ID
     * @return 模板详情
     */
    TemplateManageDTO getById(Long id);

    /**
     * 删除模板
     *
     * @param id 模板ID
     * @return 是否删除成功
     */
    Boolean delete(Long id);

    /**
     * 批量删除模板
     *
     * @param ids 模板ID列表
     * @return 删除成功的数量
     */
    Integer batchDelete(List<Long> ids);
}
