package com.danding.cds.promotion.api.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


@Data
public class TTPromotionDetailVO implements Serializable {

    /**
     * 货主编码
     */
    private String ownerName;


    /**
     * 场景类型
     */
    private String scenarioType;

    /**
     * 报备单号
     */
    private String orderCode;

    /**
     * 单据来源
     */
    private String orderSource;

    /**
     * 商家的卖家昵称
     */
    private String sellerNick;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 促销类型
     */
    private String marketingType;

    /**
     * 主营行业
     */
    private String categoryName;

    /**
     * 活动类型
     */
    private String activityType;

    /**
     * 活动开始时间
     */
    private Date activityStartDate;

    /**
     * 活动结束日期
     */
    private Date activityEndDate;

    /**
     * 活动创建时间
     */
    private Date activityCreateDate;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 预估订单金额（单元：元）
     */
    private String predictAmount;

    /**
     * 商家姓名
     */
    private String sellerContactName;

    /**
     * 商家电话
     */
    private String sellerContactPhone;

    /**
     * 状态
     */
    private String status;
    private String statusDesc;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;


    /**
     * 主品列表
     */
    private List<TTPromotionItemVO> mainItemVOList;
    /**
     * 赠品列表
     */
    private List<TTPromotionItemVO> presentItemVOList;

    /**
     * 操作日志
     */
    private List<TTPromotionTrackLogVO> trackLogVOList;


    @Data
    public static class TTPromotionItemVO implements Serializable {

        /**
         * id
         */
        private Long id;

        /**
         * 促销订单id
         */
        private Long refOrderId;

        /**
         * 商品编码
         */
        private String itemCode;

        /**
         * 商品名称
         */
        private String itemName;

        /**
         * 商品宝贝链接
         */
        private String itemUrl;

        /**
         * 原价（单位：元）
         */
        private String originalPrice;

        /**
         * 入区价（单位：元）
         */
        private String registerPrice;

        /**
         * 促销价（单位：元）/ 赠品日销售价（单位：元）
         */
        private String activityPrice;

        /**
         * 促销数量  / 赠品数量
         */
        private String activityQuantity;

        /**
         * 海关备案序号 / 赠品海关备案序号
         */
        private String recordNumber;

        /**
         * 条码（多个条码用;分隔）
         */
        private String barCode;

    }

    @Data
    public static class TTPromotionTrackLogVO implements Serializable {
        /**
         * id
         */
        private Long id;

        /**
         * 促销单id
         */
        private Long promotionId;

        /**
         * 操作类型
         */
        private String operateType;

        /**
         * 日志描述
         */
        private String logInfo;

        /**
         * 日志描述
         */
        private String callbackDetail;

        /**
         * 操作人
         */
        private String operator;

        /**
         * 操作时间
         */
        private Date creatTime;
    }
}
