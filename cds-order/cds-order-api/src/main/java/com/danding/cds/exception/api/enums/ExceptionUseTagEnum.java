package com.danding.cds.exception.api.enums;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 异常用途标签(二进制运算)
 * 二进制位  --->  数值
 * ------1  --->   1
 * -----10  --->   2
 * ----100  --->   4
 * ---1000  --->   8
 * ...............等
 */
public enum ExceptionUseTagEnum {

    NULL(0, "无"),
    CUSTOMS_CALLBACK_MATCH(1, "重推文本匹配");

    private Integer code;

    private String desc;

    ExceptionUseTagEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }


    public static List<Integer> getUseTags(Integer useTag) {

        List<Integer> useTagList = new ArrayList<>();
        if (useTag == null || useTag == 0) {
            return useTagList;
        }
        for (ExceptionUseTagEnum value : ExceptionUseTagEnum.values()) {
            // 判断下useTag是否包含有数据
            Integer code = value.code;
            // 排除下没有用途的占位
            if (Objects.equals(code, 0)) {
                continue;
            }
            if ((code & useTag) == code) {
                useTagList.add(code);
            }
        }
        return useTagList;
    }

    public static void main(String[] args) {
        System.out.println(getUseTags(1));
        System.out.println(getUseTags(0));
    }
}
