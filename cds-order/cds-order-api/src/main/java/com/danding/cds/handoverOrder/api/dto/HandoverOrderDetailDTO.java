package com.danding.cds.handoverOrder.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 交接单明细
 * @date 2021/12/14
 */
@Data
public class HandoverOrderDetailDTO implements Serializable {

    Long id;

    /**
     * 包裹号
     */
    private String packageSn;

    /**
     * 包裹重量（kg）
     */
    private String packageWeight;


    /**
     * 运单编号
     */
    private String wayBillSn;

    /**
     * 交接单
     */
    private String handoverSn;

    /**
     * 清单sn
     */
    private String customsInventorySn;

    /**
     * 申报单号
     */
    private String declareOrderNo;

    /**
     * 快递编码
     */
    private String expressCode;

    /**
     * 快递公司
     */
    private String expressName;

    /**
     * 出库时间
     */
    private Date outHouseTime;

    /**
     * 出库单
     */
    private String outboundOrder;

    /**
     * 仓库名称
     */
    private String storeHouseName;

    /**
     * 仓库编码
     */
    private String storeHouseSn;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 创建人
     */
    private Integer createBy;
    private Integer updateBy;

    /**
     * 是否联系出库0-未关联  1-已关联
     */
    private Integer associateOutboundStatus;

    /**
     * 是否有异常 0 -否 -1 是
     */
    private Integer unusualStatus;

    /**
     * unusualMsg
     */
    private String unusualMsg;

}
