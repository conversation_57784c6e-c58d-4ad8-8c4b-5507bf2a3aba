package com.danding.cds.cull.api.enums;


public enum CullStatusEnums {
    NULL(0,null),
    PENDING(1,"待处理"),
    PROCESSED(2,"已处理"),
    DO_NOT_HANDLE(3,"不处理");


    private Integer code;

    private String desc;

    CullStatusEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static CullStatusEnums getEnum(Integer code){
        for (CullStatusEnums value : CullStatusEnums.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        throw new IllegalArgumentException("unknown StatusEnums.code: " + code);
    }
}
