package com.danding.cds.collaborateorder.api.enums;

public enum DiffType {
    NULL(0,null),
    MULTI_PRODUCT(1,"多品"),
    FEW_PRODUCT(2,"少品"),
    MULTIPLE_PIECES(3,"多件"),
    FEW_PIECES(4,"少件"),
    NORMAL(5,"正常");


    private Integer code;

    private String desc;

    DiffType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static DiffType getEnum(Integer code){
        for (DiffType value : DiffType.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        throw new IllegalArgumentException("unknown BondedType.code: " + code);
    }
}
