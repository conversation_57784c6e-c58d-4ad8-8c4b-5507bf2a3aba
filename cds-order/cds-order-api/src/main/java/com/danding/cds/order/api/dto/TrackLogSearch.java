package com.danding.cds.order.api.dto;

import com.danding.logistics.api.common.page.Page;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 日志轨迹检索条件
 * @date 2021/11/16
 */
@Data
public class TrackLogSearch extends Page implements Serializable {
    /**
     * 申报单id
     */
    Long orderId;

    String orderSn;
    /**
     * 申报单号
     */
    String declareOrderNo;

    /**
     * 订单类型
     */
    Integer orderType;
}
