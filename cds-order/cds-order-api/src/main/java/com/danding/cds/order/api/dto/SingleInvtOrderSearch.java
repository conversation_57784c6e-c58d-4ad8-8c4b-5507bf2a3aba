package com.danding.cds.order.api.dto;

import com.danding.cds.common.annotations.UcRoleAccountBookIdList;
import com.danding.logistics.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel
public class SingleInvtOrderSearch extends Page implements Serializable {
    private static final long serialVersionUID = -2385666350094314664L;

    @ApiModelProperty("清单状态")
    private Integer status;

    @ApiModelProperty("清单海关状态")
    private String customsStatus;

    @ApiModelProperty("出区状态")
    private Integer exitRegionStatus;

    @ApiModelProperty("申报成功时间From")
    private Long customsPassFrom;

    @ApiModelProperty("申报成功时间To")
    private Long customsPassTo;

    @ApiModelProperty("创建时间From")
    private Long createFrom;

    @ApiModelProperty("创建时间To")
    private Long createTo;

    @ApiModelProperty("申报时间From")
    private Long lastDeclareFrom;

    @ApiModelProperty("申报时间To")
    private Long lastDeclareTo;

    @ApiModelProperty("回执时间From")
    private Long lastReceiveFrom;

    @ApiModelProperty("回执时间To")
    private Long lastReceiveTo;

    @ApiModelProperty("清单节点: 0/可撤清单，1/可退清单")
    private String inveOrderStep;

    @ApiModelProperty("关键词查询类型 declareOrderNo,inventoryNo,logisticsNo,productId")
    private String queryType;

    @ApiModelProperty("关键词查询关键词")
    private String queryInfo;

    @ApiModelProperty("电商企业")
    private Long ebcId;

    @ApiModelProperty("电商平台")
    private Long ebpId;

    @ApiModelProperty("申报企业")
    private Long agentCompanyId;

    @ApiModelProperty("区内企业")
    private Long areaCompanyId;

    @ApiModelProperty("账册id")
    private Long accountBookId;

    @ApiModelProperty("角色账册ID列表")
    @UcRoleAccountBookIdList
    private List<Long> roleAccountBookIdList;

    @ApiModelProperty("用户")
    private List<String> user;

    @ApiModelProperty("售后状态")
    private Integer afterStatus;

    @ApiModelProperty("核注状态")
    private Integer reviwStatus;

    @ApiModelProperty("是否关联交接单")
    private Integer handoverStatus;

    /**
     * ERP实体仓编码
     */
    @ApiModelProperty("ERP实体仓编码")
    private String erpPhyWarehouseSn;

    private List<Long> accountBookIdList;

    /**
     * 撤单状态
     */
    private String cancelStatus;

    /**
     * 撤单创建时间
     */
    private Long cancelCreateTimeFrom;
    private Long cancelCreateTimeTo;

    /**
     * 退货状态
     */
    private String refundStatus;

    /**
     * 退货创建时间
     */
    private Long refundCreateTimeFrom;
    private Long refundCreateTimeTo;

    /**
     * 清单sn
     */
    private List<String> customsInventorySnList;

    /**
     * 担保企业
     */
    private Long assureCompanyId;

    /**
     * 电子税单状态
     */
    private Integer taxBillStatus;

    /**
     * 出区状态
     */
    private Integer checkOutStatus;

    /**
     * 出区时间
     */
    private Long checkOutTimeFrom;
    private Long checkOutTimeTo;

    /**
     * 备注
     */
    private String note;

    /**
     * 是否包含非保赠品 是:true
     */
    private Boolean containFbGifts;
}
