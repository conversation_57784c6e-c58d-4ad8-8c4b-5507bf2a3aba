package com.danding.cds.endorsement.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 核注单-修改标志
 */
@Getter
@AllArgsConstructor
public enum EndorsementModfMarkEnums {

    NOT_MODIFY("0", "未修改"),
    MODIFY("1", "修改"),
    DELETE("2", "删除"),
    ADD("3", "增加");


    private final String code;
    private final String desc;

    public static EndorsementModfMarkEnums getEnums(String code) {
        for (EndorsementModfMarkEnums value : EndorsementModfMarkEnums.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        //默认没有值就是 未修改 兼容之前的
        return NOT_MODIFY;
    }

    public static String getDesc(String code) {
        for (EndorsementModfMarkEnums value : EndorsementModfMarkEnums.values()) {
            if (value.code.equals(code)) {
                return value.getDesc();
            }
        }
        return "";
    }
}
