package com.danding.cds.tax.api.enums;

public enum ConsumptionFlag {
    TAX_NO(-1,"不征"),
    TAX_PRICE(10,"从价"),
    TAX_NUM(5,"从量");

    private Integer value;

    private String desc;

    ConsumptionFlag(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    /*
    public static ConsumptionFlag getEnum(int value) {
        for (ConsumptionFlag item: ConsumptionFlag.values()) {
            if (item.value == value)
                return item;
        }
        return null;
    }

     */
}
