package com.danding.cds.exportorder.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel
public class ExportOrderResult implements Serializable {

    @ApiModelProperty("ID")
    private Long id;

    @ApiModelProperty("申报出库单号")
    private String sn;

    @ApiModelProperty("快递公司")
    private List<String> expressNameList;

    @ApiModelProperty("关联单证号")
    private String endorsementSns;

    @ApiModelProperty("账册编号")
    private String bookNo;

    @ApiModelProperty("创建时间")
    private String createAt;

    @ApiModelProperty("完成时间")
    private String finishAt;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("允许导入")
    private Boolean allowImport = false;

    @ApiModelProperty("允许作废")
    private Boolean allowDiscard = false;

    @ApiModelProperty("允许查看运单")
    private Boolean allowLoadMailNo = false;

    @ApiModelProperty("允许删除")
    private Boolean allowDelete = false;

    @ApiModelProperty("操作人")
    private String createBy;

    /**
     * 实体仓编码
     */
    private String entityWarehouseCode;

    /**
     * 实体仓名称
     */
    private String entityWarehouseName;

    /**
     * 包裹数
     */
    private Integer packageCount;

    /**
     * 允许生成核注单
     */
    private Boolean allowGenerateEndorsement = false;
}
