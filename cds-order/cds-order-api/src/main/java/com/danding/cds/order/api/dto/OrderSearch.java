package com.danding.cds.order.api.dto;

import com.danding.cds.common.annotations.UcRoleAccountBookIdList;
import com.danding.logistics.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Date: 2020/05/08 15:29
 * @Auth: Dante-gxj
 * @Description: 申报单查询条件类
 */
@Data
@ApiModel
public class OrderSearch extends Page implements Serializable {

    private static final long serialVersionUID = -2433522850497347709L;

    @ApiModelProperty("申报超时时长")
    private String declareTimeOut;

    @ApiModelProperty("履约超时时长")
    private String performanceTimeOut;

    @ApiModelProperty("申报状态列表")
    private List<String> manyStatus;

    @ApiModelProperty("内部流转状态")
    private List<String> internalStatus;

    @ApiModelProperty("清单状态")
    private Integer inventoryOrderStatus;

    @ApiModelProperty("关键词查询类型 outOrderNo,declareOrderNo,inventoryNo,logisticsNo")
    private String queryType;

    @ApiModelProperty("关键词查询关键词")
    private String queryInfo;

    @ApiModelProperty("路径代码")
    private String routeCode;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("电商平台")
    private String ebpCode;

    @ApiModelProperty("电商企业")
    private String ebcCode;

    @ApiModelProperty("担保企业")
    private String assureCompanyCode;

    @ApiModelProperty("电商平台 可不填")
    private Long ebpId;

    @ApiModelProperty("电商企业 可不填")
    private Long ebcId;

    @ApiModelProperty("担保企业 可不填")
    private Long assureCompanyId;

    @ApiModelProperty("物流企业 可不填")
    private Long logisticsCompanyId;

    @ApiModelProperty("快递方式ID")
    private Long expressId;

    /**
     * 申报成功 --> 指放行
     */
    @ApiModelProperty("申报成功时间From")
    private Long customsPassFrom;

    @ApiModelProperty("申报成功时间To")
    private Long customsPassTo;

    @ApiModelProperty("创建时间From")
    private Long createFrom;

    @ApiModelProperty("创建时间To")
    private Long createTo;

    @ApiModelProperty("更新时间From")
    private Long updateFrom;

    @ApiModelProperty("更新时间To")
    private Long updateTo;

    @ApiModelProperty("是否异常 -1 非异常；0 不筛选；1 异常")
    private Integer exceptionFlag;

    @ApiModelProperty("异常类型")
    private Integer exceptionType;
    private List<Integer> exceptionTypes;

    @ApiModelProperty("异常描述")
    private String exceptionDetail;

    @ApiModelProperty("申报项")
    private String actionItem;

    @ApiModelProperty("角色账册ID列表")
    @UcRoleAccountBookIdList
    private List<Long> roleAccountBookIdList;
    @ApiModelProperty("租户id")
    private String tenantId;

    /**
     * 账册ID
     */
    @ApiModelProperty("账册ID")
    private Long accountBookId;

    /**
     * 海关状态
     */
    @ApiModelProperty("海关状态")
    private List<String> customsStatus;

    /**
     * 异常描述
     */
    @ApiModelProperty("异常描述")
    private List<Long> receiptDescribe;

    /**
     * 异常管理id
     */
    @ApiModelProperty("异常管理id")
    private List<Long> exceptionTypeId;

    @ApiModelProperty("挂起状态")
    private List<Integer> hangUpStatus;

    /**
     * 申报单标记
     */
    private List<Integer> orderTags;

    /**
     * ERP实体仓编码
     */
    private String erpPhyWarehouseSn;
}