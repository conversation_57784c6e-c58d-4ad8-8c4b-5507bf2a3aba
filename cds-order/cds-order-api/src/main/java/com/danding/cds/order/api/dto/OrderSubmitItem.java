package com.danding.cds.order.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Auther: Dante-Gxj
 * @Date: 2020/5/9 11:33
 * @Description: 申报单提交商品项
 */
@Data
public class OrderSubmitItem implements Serializable {
    private static final long serialVersionUID = -9072934736782574171L;

    // --- 清单商品信息 ---
    /**
     * 商品备案料号
     */
    @ApiModelProperty("商品备案料号")
    @NotBlank(message = "商品备案料号不能为空")
    private String recordNo;

    /**
     * 商品备案序号 非必填，未填时随机分配序号
     */
    @ApiModelProperty("商品备案序号 非必填")
    private String recordGnum;

    /**
     * 商品货号
     */
    @ApiModelProperty("商品货号")
    @NotBlank(message = "商品货号不能为空")
    private String itemNo;

    /**
     * 商品品名
     */
    @ApiModelProperty("商品品名")
    @NotBlank(message = "商品品名不能为空")
    private String itemName;

    /**
     * 数量
     */
    @ApiModelProperty("数量")
    @NotNull(message = "数量不能为空")
    @Min(value = 1, message = "数量最小为1")
    private Integer count;

    /**
     * 单价
     */
    @ApiModelProperty("单价")
    @NotNull(message = "单价不能为空")
    private BigDecimal unitPrice;

    /**
     * 海关备案料号
     */
    private String customsRecordProductId;

    /**
     * 指定原产国
     */
    private String assignOriginCountry;

    /**
     * 表体标记
     */
    private Integer itemTag;
}
