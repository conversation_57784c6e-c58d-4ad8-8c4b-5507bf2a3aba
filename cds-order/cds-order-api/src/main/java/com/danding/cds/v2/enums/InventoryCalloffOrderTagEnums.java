package com.danding.cds.v2.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description: 通过位运算计算tag
 * @date 2022/8/10 09:26
 */
@Getter
@AllArgsConstructor
public enum InventoryCalloffOrderTagEnums {
    NULL(2 >> 2, "无"),
    XIAOJUREN(2 >> 1, "小巨人"),
    REFUND_WAREHOUSE(2, "退货仓"),
    JD_REFUND(2 << 1, "京东退货"),
    ;
    private Integer code;

    private String desc;

    public static List<Integer> getOrderTags(Integer useTag) {
        List<Integer> orderTagList = new ArrayList<>();
        if (useTag == null || useTag == 0) {
            return orderTagList;
        }
        for (InventoryCalloffOrderTagEnums value : InventoryCalloffOrderTagEnums.values()) {
            // 判断下orderTag是否包含有数据
            Integer code = value.getCode();
            // 排除下没有用途的占位
            if (Objects.equals(code, 0)) {
                continue;
            }
            if ((code & useTag) == code) {
                orderTagList.add(code);
            }
        }
        return orderTagList;
    }

    public static List<String> getOrderTagsDesc(Integer useTag) {
        List<String> orderTagList = new ArrayList<>();
        if (useTag == null || useTag == 0) {
            return orderTagList;
        }
        for (InventoryCalloffOrderTagEnums value : InventoryCalloffOrderTagEnums.values()) {
            // 判断下orderTag是否包含有数据
            Integer code = value.getCode();
            // 排除下没有用途的占位
            if (Objects.equals(code, 0)) {
                continue;
            }
            if ((code & useTag) == code) {
                orderTagList.add(value.getDesc());
            }
        }
        return orderTagList;
    }

    public static Integer removeOrderTag(Integer orderTag, InventoryCalloffOrderTagEnums enums) {
        if (Objects.isNull(orderTag)) {
            orderTag = 0;
        }
        Integer code = enums.getCode();
        if ((code & orderTag) == code) {
            orderTag -= code;
        }
        return orderTag;
    }


    public static Integer add(Integer orderTag, InventoryCalloffOrderTagEnums enums) {
        if (Objects.isNull(orderTag)) {
            orderTag = 0;
        }
        Integer code = enums.getCode();
        if ((code & orderTag) != code) {
            orderTag += enums.getCode();
        }
        return orderTag;
    }

    public static Boolean contains(Integer orderTags, InventoryCalloffOrderTagEnums enums) {
        if (Objects.isNull(orderTags) || orderTags.equals(0)) {
            return false;
        }
        return Objects.equals(enums.getCode(), orderTags & enums.getCode());
    }
}
