package com.danding.cds.suningFP.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class SuNingFPLoadInfoDTO implements Serializable {
    private Long id;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 流水号
     */
    private String serialNo;

    /**
     * 装载单号
     */
    private String loadOrderNo;

    /**
     * 总件数
     */
    private Integer totalCount;

    /**
     * 总重量
     */
    private BigDecimal totalWeight;

    /**
     * 总托数
     */
    private Integer totalToryNum;

    /**
     * 车牌号
     */
    private String licensePlate;

    /**
     * 审核状态
     */
    private Integer auditStatus;

    private Date createTime;

    private Date updateTime;

    private Date auditTime;
}
