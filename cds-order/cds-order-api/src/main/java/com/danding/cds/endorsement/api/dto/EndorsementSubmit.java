package com.danding.cds.endorsement.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel
public class EndorsementSubmit implements Serializable {

    @ApiModelProperty("出入区标志 1出区 2入区")
    private Integer ieFlag;

    @ApiModelProperty("业务类型")
    private String bussinessType;

    @Deprecated
    @ApiModelProperty("是否一票多车")
    private Boolean checklistsFlag;

    @ApiModelProperty("申请出库单ID，出区必填")
    private Long exportOrderId;

    @ApiModelProperty("清关单ID，入区必填")
    private Long inventoryOrderId;

    /**
     * 0 不核扣
     * 1 核扣
     */
    @ApiModelProperty("是否核扣账册")
    private Integer stockChangeEnable;
}
