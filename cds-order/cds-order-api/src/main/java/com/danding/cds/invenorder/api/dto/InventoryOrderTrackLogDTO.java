package com.danding.cds.invenorder.api.dto;

import com.danding.cds.invenorder.api.enums.InventoryOrderEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/8/5 16:48
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryOrderTrackLogDTO implements Serializable {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 清关单id
     */
    private Long inventoryOrderId;

    /**
     * 清关单sn
     */
    private String inventoryOrderSn;

    /**
     * 清关单状态
     * {@link InventoryOrderEnum}
     */
    private String inventoryStatus;

    /**
     * 日志描述
     */
    private String logDesc;

    /**
     * 操作时间
     */
    private Date operateTime;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 请求报文
     */
    private String requestMessage;

    /**
     * 返回报文
     */
    private String returnMessage;

    private Date createTime;

    private Date updateTime;

    private Integer createBy;

    private Integer updateBy;

    private Boolean deleted = false;


}
