package com.danding.cds.v2.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 底账料件成品标志枚举
 */
@Getter
@AllArgsConstructor
public enum BizDeclareFormBookMtpckEndprdTypeEnums {

    //I-料件E-成品
    MATERIAL_PACKAGE("I", "料件"),
    END_PRODUCT("E", "成品");

    private final String code;
    private final String desc;

    public static BizDeclareFormBookMtpckEndprdTypeEnums getEnums(String code) {
        for (BizDeclareFormBookMtpckEndprdTypeEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    public static String getDesc(String code) {
        for (BizDeclareFormBookMtpckEndprdTypeEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums.getDesc();
            }
        }
        return "";
    }

}
