package com.danding.cds.promotion.api.vo;

import com.danding.logistics.api.common.page.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Author: yousx
 * @Date: 2024/03/14
 * @Description:
 */
@Data
public class TTPromotionOrderSearch extends Page implements Serializable {


    /**
     * 报备单号
     */
    @ApiModelProperty(value = "报备单号")
    private List<String> orderCode;

    /**
     * 活动类型
     */
    @ApiModelProperty(value = "活动类型")
    private String activityType;

    /**
     * 促销类型
     */
    @ApiModelProperty(value = "促销类型")
    private String marketingType;

    /**
     * 活动名称
     */
    @ApiModelProperty(value = "活动名称")
    private String activityName;

    /**
     * 卖家昵称
     */
    @ApiModelProperty(value = "卖家昵称")
    private String sellerNick;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private List<String> ownerCode;

    /**
     * 实体仓
     */
    @ApiModelProperty(value = "实体仓")
    private String warehouseName;

    /**
     * 单据来源
     */
    @ApiModelProperty(value = "单据来源")
    private String orderSource;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String status;

    /**
     * 活动创建时间
     */
    @ApiModelProperty(value = "活动创建时间")
    private Date activityCreateDateFrom;

    /**
     * 活动创建时间
     */
    @ApiModelProperty(value = "活动创建时间")
    private Date activityCreateDateTo;

    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "活动结束时间")
    private Date activityEndDateFrom;

    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "活动结束时间")
    private Date activityEndDateTo;

    /**
     * 活动开始时间
     */
    @ApiModelProperty(value = "活动开始时间")
    private Date activityStartDateFrom;

    /**
     * 活动开始时间
     */
    @ApiModelProperty(value = "活动开始时间")
    private Date activityStartDateTo;

    /**
     * 活动报备截止时间
     */
    @ApiModelProperty(value = "活动报备截止时间")
    private Date activityReportEndDateFrom;

    /**
     * 活动报备截止时间
     */
    @ApiModelProperty(value = "活动报备截止时间")
    private Date activityReportEndDateTo;
}
