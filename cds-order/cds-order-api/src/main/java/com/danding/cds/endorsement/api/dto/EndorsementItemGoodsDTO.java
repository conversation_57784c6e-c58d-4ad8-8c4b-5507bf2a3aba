package com.danding.cds.endorsement.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 料件表体
 */
@Data
public class EndorsementItemGoodsDTO implements Serializable {

    private Long id;

    /**
     * 核放单ID
     */
    private Long checklistId;

    /**
     * 核注清单ID
     */
    private Long endorsementId;

    /**
     * 商品表体序号
     */
    private Integer serialNumber;

    /**
     * 商品料号
     */
    private String productId;

    /**
     * 账册项号
     */
    private String goodsSeqNo;

    /**
     * 料号名称
     */
    private String goodsName;

    /**
     * 备案名称
     */
    private String recordProductName;

    /**
     * HS代码
     */
    private String hsCode;

    /**
     * 申报单位数量
     */
    private BigDecimal declareUnitQfy;

    /**
     * 剩余申报单位
     */
    private BigDecimal remainDeclareUnitQfy;

    /**
     * 毛重（公斤）
     */
    private BigDecimal grossWeight;

    /**
     * 净重（公斤）
     */
    private BigDecimal netWeight;

    /**
     * 额外其他属性
     */
    private String extraJson;

    /**
     * 修改标志
     */
    private String modfMark;

    /**
     * 记账金二序号
     */
    private String customsCallBackSeqNo;

    /**
     * 表体标签
     * {@link com.danding.cds.c.api.bean.enums.OrderItemTagEnum}
     */
    private Integer itemTag;

    /**
     * 申报表序号
     */
    private Integer declareFormItemSeqNo;

    /**
     * 来源标识
     */
    private String goodsSource;

    private Date createTime;

    private Date updateTime;

    private Integer createBy;

    private Integer updateBy;

    private Boolean deleted;
}
