package com.danding.cds.v2.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 清关单待办标记枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum JdPurchaseOrderTagEnums {

    NULL(1 >> 1, "无"),
    VIRTUAL(1, "虚拟采购");

    private final Integer code;
    private final String desc;

    public static List<Integer> getOrderTags(Integer useTag) {
        List<Integer> orderTagList = new ArrayList<>();
        if (useTag == null || useTag == 0) {
            return orderTagList;
        }
        for (JdPurchaseOrderTagEnums value : JdPurchaseOrderTagEnums.values()) {
            // 判断下orderTag是否包含有数据
            Integer code = value.getCode();
            // 排除下没有用途的占位
            if (Objects.equals(code, 0)) {
                continue;
            }
            if ((code & useTag) == code) {
                orderTagList.add(code);
            }
        }
        return orderTagList;
    }

    public static List<String> getOrderTagsDesc(Integer useTag) {
        List<String> orderTagList = new ArrayList<>();
        if (useTag == null || useTag == 0) {
            return orderTagList;
        }
        for (JdPurchaseOrderTagEnums value : JdPurchaseOrderTagEnums.values()) {
            // 判断下orderTag是否包含有数据
            Integer code = value.getCode();
            // 排除下没有用途的占位
            if (Objects.equals(code, 0)) {
                continue;
            }
            if ((code & useTag) == code) {
                orderTagList.add(value.getDesc());
            }
        }
        return orderTagList;
    }

    public static Integer remove(Integer orderTag, JdPurchaseOrderTagEnums enums) {
        if (Objects.isNull(orderTag)) {
            orderTag = 0;
        }
        Integer code = enums.getCode();
        if ((code & orderTag) == code) {
            orderTag -= code;
        }
        return orderTag;
    }

    public static Integer add(Integer orderTag, JdPurchaseOrderTagEnums enums) {
        if (Objects.isNull(orderTag)) {
            orderTag = 0;
        }
        Integer code = enums.getCode();
        if ((code & orderTag) != code) {
            orderTag += enums.getCode();
        }
        return orderTag;
    }

    public static Boolean contains(Integer orderTags, JdPurchaseOrderTagEnums enums) {
        if (Objects.isNull(orderTags) || orderTags.equals(0)) {
            return false;
        }
        return Objects.equals(enums.getCode(), orderTags & enums.getCode());
    }

    public static List<JdPurchaseOrderTagEnums> valuesV2() {
        return Arrays.stream(JdPurchaseOrderTagEnums.values()).filter(i ->
                !i.equals(JdPurchaseOrderTagEnums.NULL)
        ).collect(Collectors.toList());
    }
}
