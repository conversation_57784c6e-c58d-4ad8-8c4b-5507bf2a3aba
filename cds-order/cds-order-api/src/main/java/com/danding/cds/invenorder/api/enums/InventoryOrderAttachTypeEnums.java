package com.danding.cds.invenorder.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: houwen<PERSON>e
 * @Date: 2025/05/21
 * @Description: 清关单附件类型
 */
@Getter
@AllArgsConstructor
public enum InventoryOrderAttachTypeEnums {

    OTHER("0", "其他"),
    BOX_INVOICE_CONTRACT("1", "箱单/发票/合同"),
    LADING_BILL("2", "提货单"),
    ;


    private String value;

    private String desc;

    public static InventoryOrderAttachTypeEnums getEnum(String value) {
        for (InventoryOrderAttachTypeEnums enums : InventoryOrderAttachTypeEnums.values()) {
            if (enums.getValue().equals(value)) {
                return enums;
            }
        }
        return null;
    }

    public static String getDesc(String value) {
        for (InventoryOrderAttachTypeEnums enums : InventoryOrderAttachTypeEnums.values()) {
            if (enums.getValue().equals(value)) {
                return enums.getDesc();
            }
        }
        return "";
    }
}