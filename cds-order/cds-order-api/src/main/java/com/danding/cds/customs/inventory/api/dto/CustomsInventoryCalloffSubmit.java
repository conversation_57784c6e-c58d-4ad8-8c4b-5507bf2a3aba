package com.danding.cds.customs.inventory.api.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2022/1/4
 */
@Data
public class CustomsInventoryCalloffSubmit implements Serializable {

    /**
     * 开始时间-取消
     */
    private Long beginCalloffTime;

    /**
     * 结束时间-取消
     */
    private Long endCalloffTime;

    /**
     * 清关企业ID
     */
    private Long agentCompanyId;

    /**
     * 账册
     */
    private Long bookId;

    /**
     * 订单编号 申报单号
     */
    private String declareOrderNo;

    /**
     * 任务Id
     */
    private String taskId;

    /**
     * 子任务Id
     */
    private String subtasksId;


}
