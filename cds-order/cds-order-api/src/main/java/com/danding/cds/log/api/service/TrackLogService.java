package com.danding.cds.log.api.service;

import com.danding.cds.log.api.dto.TrackLogDTO;
import com.danding.cds.order.api.dto.TrackLogSearch;
import com.danding.logistics.api.common.response.ListVO;

import java.util.List;

/**
 * <AUTHOR>
 * @Create 2021/6/8  16:07
 * @Describe
 * @link com.danding.cds.c.api.service.TrackLogService
 **/
@Deprecated
public interface TrackLogService {
    /**
     * 用于异步保存轨迹日志
     * @param trackLog
     */
    void submit(TrackLogDTO trackLog);

    List<TrackLogDTO> getTrackLogs(String declareNo);

    List<TrackLogDTO> getByKeyWord(String declaredOrderNoKeyWord,String operateType, String beginDate, String endDate);

    ListVO<TrackLogDTO> selectTrackLogList(TrackLogSearch search);
}
