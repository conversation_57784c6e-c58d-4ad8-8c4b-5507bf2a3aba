package com.danding.cds.invenorder.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class InventoryOrderItemExtra implements java.io.Serializable{
    private static final long serialVersionUID = -2433500850499944500L;
    @ApiModelProperty("计量单位")
    private String unit;
    @ApiModelProperty("第一单位")
    private String firstUnit;
    @ApiModelProperty("第一单位数量")
    private BigDecimal firstUnitQfy;
    @ApiModelProperty("第二单位")
    private String secondUnit;
    @ApiModelProperty("第二单位数量")
    private BigDecimal secondUnitQfy;
    @ApiModelProperty("毛重")
    private BigDecimal grossWeight;
    private BigDecimal totalGrossWeight;
    @ApiModelProperty("净重")
    private BigDecimal netweight;
    private BigDecimal totalNetWeight;
    @ApiModelProperty("申报要素")
    private String declareFactor;
    @ApiModelProperty("原产国")
    private String originCountry;
    @ApiModelProperty("商品条码")
    private String goodsBar;
    @ApiModelProperty("生产企业")
    private String productCompany;
    @ApiModelProperty("国检备案号")
    private String countryRecordNo;

    @ApiModelProperty("报关单商品序号")
    private  String declareCustomsGoodsSeqNo;//add
    @ApiModelProperty("规格型号")//add
    private String goodsModel;
    @ApiModelProperty("最终目的国")
    private String destinationCountry;//add
    @ApiModelProperty("币制")//add
    private String currency;
    @ApiModelProperty("免征方式")//add
    private String avoidTaxMethod="3";
    @ApiModelProperty("单号版本号")//add
    private String orderVersion;


}
