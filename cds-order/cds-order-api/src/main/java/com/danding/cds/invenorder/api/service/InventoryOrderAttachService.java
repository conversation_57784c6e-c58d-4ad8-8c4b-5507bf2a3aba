package com.danding.cds.invenorder.api.service;

import com.danding.cds.invenorder.api.dto.InventoryOrderAttachDTO;
import com.danding.cds.invenorder.api.dto.InventoryOrderInfoDTO;

import java.util.List;

public interface InventoryOrderAttachService {

    InventoryOrderAttachDTO findById(Long id);
    List<InventoryOrderAttachDTO> findList(Long inveOrderId);
    void createInventoryOrderAttachDTO(InventoryOrderAttachDTO inventoryOrderAttachDTO);
    void deleteById(Long id);
    void traceLogDownload(Long id);

    void saveAttachUrl(InventoryOrderInfoDTO dto, String url, String fileName, String attachType);

    String download(Long id, Integer isUpdateCompanyInfo);
}
