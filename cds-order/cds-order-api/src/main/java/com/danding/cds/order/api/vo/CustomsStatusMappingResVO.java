package com.danding.cds.order.api.vo;

import com.danding.cds.order.api.dto.CustomsStatusMappingDTO;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class CustomsStatusMappingResVO implements Serializable {

    private String id;
    /**
     * 申报项，用于区分模块
     */
    private String action;
    private String actionStr;

    /**
     * 标识，各类场景根据特定规则拼凑成的唯一标识码
     */
    private String code;

    /**
     * 海关状态码
     */
    private String customsStatusCode;
    private String customsStatusCodeStr;


    /**
     * 详情状态码
     */
    private String detailCode;
    private String detailCodeStr;

    /**
     * 申报状态
     * PS::对应着申报单据的status
     * <p>
     * DEC_ING(20,"等待回执"),
     * DEC_SUCCESS(100,"已放行"),
     * DEC_FAIL(-1,"申报失败");
     */
    private Integer status;
    private String statusStr;

    /**
     * 备注，可填写异常处理方式(海关回执)
     */
    private String note;
    /**
     * 是否属异常
     */
    @Column(name = "exception_flag")
    private Boolean exceptionFlag;

    /**
     * 异常id
     */
    private Long exceptionId;
    private String exceptionName;

    /**
     * 映射状态
     */
    private Integer mapStatus;
    private String mapStatusStr;

    /**
     * 回执说明
     */
    private String receiptExplain;

    /**
     * 启用状态：0停用 1启用
     */
    private Integer enabledState;
    private String enabledStateStr;


    /**
     * 备注
     */
    private String remark;

    private Date createTime;


    /**
     * 是否终态回执（判断是否走正则表达式）
     */
    private Boolean finalReceiptFlag;

    /**
     * 正则表达式校验
     */
    private List<CustomsStatusMappingDTO.RegexCheck> regexCheckList;
}
