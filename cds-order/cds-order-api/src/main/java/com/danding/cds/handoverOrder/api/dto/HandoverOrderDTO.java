package com.danding.cds.handoverOrder.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2021/12/14
 */
@Data
public class HandoverOrderDTO implements Serializable {

    private Long id;

    /**
     * 交接单号
     */
    private String handoverSn;

    /**
     * 包裹数
     */
    private Integer packageNum;

    /**
     * 总重量(Kg)
     */
    private String totalWeight;

    /**
     *总托数
     */
    private Integer totalPalletsNum;

    /**
     * 快递公司名称(wms系统数据)
     */
    private String wmsExpressName;

    /**
     * 快递公司ID(wms系统数据)
     */
    private String wmsExpressCode;

    /**
     * 仓库名称
     */
    private String storeHouseName;

    /**
     * 仓库编码
     */
    private String storeHouseSn;

    /**
     * 车辆信息
     */
    private String vehicleInfo;

    /**
     * 出库状态0-待生成出库单 1-已生成出库单
     */
    private Integer status;
    /**
     * 创建人
     */
    private Integer createBy;
    private Integer updateBy;
    /**
     * 开始时间
     */
    private Long createTime;

    /**
     * 开始时间
     */
    private Long staTime;

    /**
     * 结束时间
     */
    private Long endTime;

    /**
     * 交接单明细
     */
    private List<HandoverOrderDetailDTO> handoverOrderDetailDTO;


}
