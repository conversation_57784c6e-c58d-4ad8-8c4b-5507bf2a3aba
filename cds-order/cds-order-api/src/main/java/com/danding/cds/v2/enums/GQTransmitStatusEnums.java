package com.danding.cds.v2.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * 关企传输状态枚举
 */
@AllArgsConstructor
@Getter
public enum GQTransmitStatusEnums {

    CREATED("CREATED", "已创建"),
    PUSHED("PUSHED", "已推送"),
    EXCEPTION("EXCEPTION", "异常"),
    FINISH("FINISH", "已完成"),
    DISCARD("DISCARD", "已作废"),
    ;

    private final String code;

    private final String desc;

    public static GQTransmitStatusEnums getByCode(String code) {
        for (GQTransmitStatusEnums status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    public static boolean allowPush(String code) {
        List<GQTransmitStatusEnums> allowPushStatus = Arrays.asList(CREATED, PUSHED, EXCEPTION);
        GQTransmitStatusEnums enums = getByCode(code);
        return enums != null && allowPushStatus.contains(enums);
    }

    public static boolean allowDiscard(String code) {
        List<GQTransmitStatusEnums> allowDiscardStatus = Arrays.asList(CREATED, PUSHED, EXCEPTION);
        GQTransmitStatusEnums enums = getByCode(code);
        return enums != null && allowDiscardStatus.contains(enums);
    }
}
