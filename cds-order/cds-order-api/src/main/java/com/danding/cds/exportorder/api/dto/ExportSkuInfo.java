package com.danding.cds.exportorder.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ExportSkuInfo implements Serializable {

    /**
     * 账册库存ID
     */
    private Long bookItemId;

    /**
     * 统一料号
     */
    private String unifiedProductId;

    /**
     * 海关备案料号
     */
    private String productId;

    /**
     * 数量
     */
    private Integer count;

    /**
     * 表体标签
     * {@link com.danding.cds.c.api.bean.enums.OrderItemTagEnum}
     */
    private Integer itemTag;

    /**
     * 用于非保赠品的商品名称
     */
    private String goodsName;


    /**
     * 净重
     */
    private BigDecimal netWeight;

    /**
     * 毛重
     */
    private BigDecimal grossWeight;

}
