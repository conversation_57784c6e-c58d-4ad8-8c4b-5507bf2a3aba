package com.danding.cds.customs.inventory.api.enums;

public enum ModifyDeclareStat {

    /**
     * 状态;10:待申报;20:申报中;30:待总署审核;40:总署驳回;50:申报失败;100:审核通过;-10:取消变更
     */

    WAITING(10, "待申报"),
    PENDING(20, "申报中"),
    ZS_VERIFY_PENDING(30, "待总署审核"),
    ZS_REJECT(40, "总署驳回"),
    FAIL(50, "申报失败"),
    SUCCESS(100, "审核通过"),
    CANCEL(-10, "取消变更");

    private Integer value;

    private String desc;

    ModifyDeclareStat(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(Integer value) {

        for (ModifyDeclareStat status : ModifyDeclareStat.values()) {
            if (status.getValue().equals(value)) {
                return status.desc;
            }
        }
        return null;
    }

}
