package com.danding.cds.v2.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 出区对账标记
 * 采用位运算
 */
@Getter
@AllArgsConstructor
public enum ReconciliationTagEnums {

    //处理中
    PROCESSING(1, "处理中"),
    ;
    private final Integer code;
    private final String desc;

    public static ReconciliationTagEnums getEnums(Integer code) {
        for (ReconciliationTagEnums enums : ReconciliationTagEnums.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    public static List<Integer> getOrderTags(Integer useTag) {
        List<Integer> orderTagList = new ArrayList<>();
        if (useTag == null || useTag == 0) {
            return orderTagList;
        }
        for (ReconciliationTagEnums value : ReconciliationTagEnums.values()) {
            // 判断下orderTag是否包含有数据
            Integer code = value.getCode();
            // 排除下没有用途的占位
            if (Objects.equals(code, 0)) {
                continue;
            }
            if ((code & useTag) == code) {
                orderTagList.add(code);
            }
        }
        return orderTagList;
    }

    public static List<String> getOrderTagsDesc(Integer useTag) {
        List<String> orderTagList = new ArrayList<>();
        if (useTag == null || useTag == 0) {
            return orderTagList;
        }
        for (ReconciliationTagEnums value : ReconciliationTagEnums.values()) {
            // 判断下orderTag是否包含有数据
            Integer code = value.getCode();
            // 排除下没有用途的占位
            if (Objects.equals(code, 0)) {
                continue;
            }
            if ((code & useTag) == code) {
                orderTagList.add(value.getDesc());
            }
        }
        return orderTagList;
    }

    public static Integer remove(Integer orderTag, ReconciliationTagEnums enums) {
        if (Objects.isNull(orderTag)) {
            orderTag = 0;
        }
        Integer code = enums.getCode();
        if ((code & orderTag) == code) {
            orderTag -= code;
        }
        return orderTag;
    }

    public static Integer add(Integer orderTag, ReconciliationTagEnums enums) {
        if (Objects.isNull(orderTag)) {
            orderTag = 0;
        }
        Integer code = enums.getCode();
        if ((code & orderTag) != code) {
            orderTag += enums.getCode();
        }
        return orderTag;
    }

    public static Boolean contains(Integer orderTags, ReconciliationTagEnums enums) {
        if (Objects.isNull(orderTags) || orderTags.equals(0)) {
            return false;
        }
        return Objects.equals(enums.getCode(), orderTags & enums.getCode());
    }
}
