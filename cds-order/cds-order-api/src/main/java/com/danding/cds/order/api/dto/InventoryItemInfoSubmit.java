package com.danding.cds.order.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: Raymond
 * @Date: 2020/7/29 11:05
 * @Description:
 */

@Data
@ApiModel
public class InventoryItemInfoSubmit implements Serializable {
    @ApiModelProperty("商品信息id")
    @NotBlank(message = "商品信息id不能为空")
    private String id;

    @ApiModelProperty("外部编码")
    private String itemNo;

    @ApiModelProperty("商品备案名称")
    private String goodsName;

    @ApiModelProperty("商品料号")
    private String productId;

    @ApiModelProperty("条码")
    private String barCode;

    @ApiModelProperty("HS")
    private String hsCode;

    @ApiModelProperty("毛重") // TODO:暂无
    private BigDecimal grossWeight;

    @ApiModelProperty("净重") // TODO:暂无
    private BigDecimal netWeight;

    @ApiModelProperty("原产国")
    private String originCountry;

    @ApiModelProperty("第一计量单位")
    private String firstUnit;

    @ApiModelProperty("第二计量单位")
    private String secondUnit;

    @ApiModelProperty("法定第一计量单位数量")
    private BigDecimal firstUnitAmount;

    @ApiModelProperty("法定第二计量单位数量")
    private BigDecimal secondUnitAmount;
}
