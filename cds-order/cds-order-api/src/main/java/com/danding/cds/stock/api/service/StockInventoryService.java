package com.danding.cds.stock.api.service;

import com.danding.cds.common.model.excel.UrlParam;
import com.danding.cds.inventory.api.dto.ItemLogsIdDTO;
import com.danding.cds.inventory.api.dto.UpdateInventoryDTO;
import com.danding.soul.client.common.result.RpcResult;

import java.util.List;

/**
 * <AUTHOR>
 * @Create 2021/7/20  10:06
 * @Describe
 * @link com.danding.cds.c.api.service.StockInventoryService
 **/
@Deprecated
public interface StockInventoryService {
    void updateInventory(UpdateInventoryDTO updateInventoryDTO);

    List<ItemLogsIdDTO> updateInventory(List<UpdateInventoryDTO> list);
}
