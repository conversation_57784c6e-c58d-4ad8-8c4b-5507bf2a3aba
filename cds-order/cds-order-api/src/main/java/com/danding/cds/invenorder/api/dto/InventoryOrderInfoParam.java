package com.danding.cds.invenorder.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Author: Raymond
 * @Date: 2020/11/19 17:13
 * @Description:
 */
@Data
public class InventoryOrderInfoParam implements Serializable {
    private static final long serialVersionUID = -2433523850499947709L;
    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("清关单SN")
    private String inveCustomsSn;
    /**
     * 清关企业
     */
    @ApiModelProperty("清关企业(第一步骤)")
    private Long inveCompanyId;

    @ApiModelProperty("清关企业编码")
    private String inveCompanyCode;
    /*
    单据类型

    @ApiModelProperty("单据类型")
    private String inveOrderType;*/
    @ApiModelProperty("业务类型")
    private String inveBusinessType;

    @ApiModelProperty("选择区间转出，区内转出的业务类型时 ->关联转入账册字段")
    private String inAccountBook;

    @ApiModelProperty("选择区间转入，区内转入的业务类型时 ->关联转出账册")
    private String outAccountBook;

    @ApiModelProperty("核放单编号")
    private String refCheckOrderNo;


    @ApiModelProperty("仓库")
    private String warehouseId;
    /**
     * 核注清单编号
     */
    @ApiModelProperty("核注清单编号")
    private String refHzInveNo;
    /**
     * 提取号
     */
    @ApiModelProperty("提取号(第一步骤) 用作关联核注清单编号")
    private String pickUpNo;
    /**
     * 进出境关别
     */
    @ApiModelProperty("进出境关别")
    private String entryExitCustoms;
    /**
     * 运输方式
     */
    @ApiModelProperty("运输方式")
    private String transportMode;
    /**
     * 启运国
     */
    @ApiModelProperty("启运国")
    private String shipmentCountry;
    /**
     * 租户
     */
    @ApiModelProperty("租户")
    private String rentPerson;

    /**
     * 账册ID
     */
    @ApiModelProperty("账册ID(第一步骤)")
    private Long bookId;

    @ApiModelProperty("账册号")
    private String bookNo;
    /**
     * 申请人
     */
    @ApiModelProperty("申请人(第一步骤)")
    private String applyPerson;
    /**
     * 备注
     */
    @ApiModelProperty("备注(第一步骤)")
    private String remark;
    /**
     * 清关单状态
     */
    @ApiModelProperty("清关单状态")
    private String status;

    /**
     * 清关单状态对应时间
     */
    @ApiModelProperty("清关单状态对应时间")
    private Date statusTime;

    @ApiModelProperty("预计出区时间")
    private Date expectedOutAreaTime;

    @ApiModelProperty("预计到港时间")
    private Date expectedToPortTime;
    /**
     * 状态:0.停用;1.启用(默认)
     */
    @ApiModelProperty("状态:0.停用;1.启用(默认)")
    private Integer enable;
    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("更新时间")
    private Date updateTime;
    @ApiModelProperty("创建人ID")
    private Integer createBy;
    @ApiModelProperty("更新人ID")
    private Integer updateBy;
    @ApiModelProperty("逻辑删除")
    private Boolean deleted = false;
    @ApiModelProperty("清关单对应的分录信息")
    private List<InventoryOrderItemParam> listItems;

    @ApiModelProperty("上游单据类型: ReadyOrder(备货) Distribution(配货)")
    private String channelBusinessType;

    @ApiModelProperty("上游单据编号")
    private String channelBusinessSn;

    @ApiModelProperty("实体仓编码")
    private String entityWarehouseCode;

    @ApiModelProperty("实体仓名称")
    private String entityWarehouseName;

    @ApiModelProperty("WMS仓编码")
    private String wmsWarehouseCode;

    @ApiModelProperty("货主编码")
    private String ownerCode;

    @ApiModelProperty("货主名称")
    private String ownerName;

    /**
     * 用户id
     * ERP下发(上游商家用户中心id)
     */
    @ApiModelProperty("用户id")
    private Long userId;

    /**
     * 用户名称
     * ERP下发(上游商家用户中心name)
     */
    @ApiModelProperty("用户名称")
    private String userName;

    @ApiModelProperty("外部单号")
    private String upstreamNo;

    @ApiModelProperty("出入库单号")
    private String inOutOrderNo;

    @ApiModelProperty("是否关仓协同")
    private Boolean collaborateFlag;

    /**
     * 质押货主标志
     */
    @ApiModelProperty("质押货主标志")
    private Boolean pledgeOwnerFlag;

    /**
     * 邮箱地址
     */
    private String mailAddrList;

    @ApiModelProperty("附件")
    private List<InventoryOrderAttachDTO> attachList;

    @ApiModelProperty("货主是否自备车辆")
    private Boolean selfOwnedVehicle;

    @ApiModelProperty("车牌号")
    private String licensePlate;

    /**
     * 非保标记 (1:是非保清关单, 0:不是非保清关单)
     */
    private Integer fbFlag;

    /**
     * 是否同用户调拨标记
     */
    private Boolean sameUserTransferFlag;

    /**
     * 来源
     * 1-ERP 2-CCS 3-WMS 4-淘天
     */
    private Integer channel;
}