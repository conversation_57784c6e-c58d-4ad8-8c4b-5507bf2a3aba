package com.danding.cds.customs.order.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class CustomsOrderSubmitItem implements Serializable {

    private String recordNo; // 料号

    private String recordGnum; // 序号

    private String sku; // SKU

    private Integer count; // 数量

    private BigDecimal unitPrice; // 单价

    /**
     * 表体标记
     */
    private Integer itemTag;
}
