package com.danding.cds.invenorder.api.dto;

import com.danding.cds.invenorder.api.enums.InventoryOrderChannel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: Raymond
 * @Date: 2020/11/19 19:38
 * @Description:
 */
@Data
public class InvenorderDeclareDTO implements Serializable {
    @ApiModelProperty("sn")
    private String inveCustomsSn;

    @ApiModelProperty("成功:1/失败:0")
    private Integer opinion;

    private InventoryOrderChannel channel;
}
