package com.danding.cds.invenorder.api.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @program: cds-center
 * @description: 清单税金统计
 * @author: 潘本乐（Belep）
 * @create: 2022-11-30 11:25
 **/
@Data
public class InventoryTaxStatisticsDTO implements Serializable {
    /**
     * 海关总署回执总税金
     */
    private Double totalTax;
    /**
     * 系统计算的税金
     */
    private Double calculateTaxFee;
    /**
     * 商品总价
     */
    private Double totalFee;
}
