package com.danding.cds.handoverOrder.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 交接单分页查询结果VO
 * @date 2021/12/14
 */
@Data
public class HandoverOrderVO implements Serializable {
    private Long id;

    /**
     * 交接单号
     */
    private String handoverSn;

    /**
     * 包裹数
     */
    private Integer packageNum;

    /**
     * 总重量(Kg)
     */
    private BigDecimal totalWeight;

    /**
     * 未关联出库包裹数
     */
    private Integer notAssociateCount;

    /**
     * 已关联出库包裹数
     */
    private Integer associateCount;

    /**
     * 快递公司名称(wms系统数据)
     */
    private String wmsExpressName;

    /**
     * 快递公司ID(wms系统数据)
     */
    private String wmsExpressCode;

    /**
     * 仓库名称
     */
    private String storeHouseName;

    /**
     * 仓库编码
     */
    private String storeHouseSn;

    /**
     * 车辆信息
     */
    private String vehicleInfo;

    /**
     * 完成状态
     */
    private String finishStatus;

    /**
     * 出库单号
     */
    private String outboundOrder;


    /**
     * 开始时间
     */
    private Date createTime;


}
