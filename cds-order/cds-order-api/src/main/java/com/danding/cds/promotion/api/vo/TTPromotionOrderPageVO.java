package com.danding.cds.promotion.api.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: yousx
 * @Date: 2024/03/14
 * @Description:
 */
@Data
public class TTPromotionOrderPageVO implements Serializable {

    private Long id;

    /**
     * 报备单号
     */
    @ApiModelProperty(value = "报备单号")
    private String orderCode;

    @ApiModelProperty(value = "主营行业")
    private String categoryName;

    @ApiModelProperty(value = "状态")
    private String status;

    /**
     * 活动类型
     */
    @ApiModelProperty(value = "活动类型")
    private String activityType;

    /**
     * 促销类型
     */
    @ApiModelProperty(value = "促销类型")
    private String marketingType;

    /**
     * 活动名称
     */
    @ApiModelProperty(value = "活动名称")
    private String activityName;

    /**
     * 商家的卖家昵称
     */
    @ApiModelProperty(value = "商家的卖家昵称")
    private String sellerNick;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String ownerCode;

    @ApiModelProperty(value = "货主名称")
    private String ownerName;

    /**
     * 单据来源
     */
    @ApiModelProperty(value = "单据来源")
    private String orderSource;

    /**
     * 活动开始时间
     */
    @ApiModelProperty(value = "活动开始时间")
    private Date activityStartDate;

    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "活动结束时间")
    private Date activityEndDate;

    /**
     * 活动创建时间
     */
    @ApiModelProperty(value = "活动创建时间")
    private Date activityCreateDate;

    /**
     * 活动报备截止时间
     */
    @ApiModelProperty(value = "活动报备截止时间")
    private Date activityReportEndDate;

    /**
     * 仓库名称
     */
    @ApiModelProperty(value = "实体仓")
    private String warehouseName;

}
