package com.danding.cds.collaborateorder.api.vo;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 协同单对象
 * @date 2022/3/30
 */
@Data
public class CollaborateOrderDetailsResVO implements Serializable {

    /**
     * 协同单表体id
     */
    private Long id;

    /**
     * 清关单表体id
     */
    private Long inveItemId;

    /**
     * 协同单id
     */
    private Long collaborateOrderId;

    /**
     * 货品ID
     */
    private String goodsId;

    /**
     * 货品名称
     */
    private String goodsName;

    /**
     * sku
     */
    private String sku;

    /**
     * 条形码
     */
    private String barCode;

    /**
     * 申报料号
     */
    private String productId;

    /**
     * 净重
     */
    private BigDecimal netWeight;

    /**
     * 毛重
     */
    private BigDecimal grossWeight;

    /**
     * 申报数量
     */
    private Integer declareQty;

    /**
     * 理货数量
     */
    private Integer tallyQty;

    /**
     * 差异类型,1:多品；2:少品；3:多件；4:少件
     */
    private Integer diffType;

    /**
     * 差异数量
     */
    private Integer diffQty;

    /**
     * 商品序号
     */
    private String goodsSeqNo;
    /**
     * 标识
     */
    private String label;


}
