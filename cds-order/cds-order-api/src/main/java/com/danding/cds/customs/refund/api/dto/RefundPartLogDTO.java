package com.danding.cds.customs.refund.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: yousx
 * @Date: 2023/12/05
 * @Description:
 */
@Data
public class RefundPartLogDTO implements Serializable {

    /**
     * 退货单号
     */
    private String refundNo;

    /**
     * 退货单id
     */
    private Long refundId;

    /**
     * 申报单号
     */
    private String declareNo;

    /**
     * 清单编号
     */
    private String inventoryNo;

    /**
     * 账册id
     */
    private Long accountBookId;

    /**
     * 清单sn
     */
    private String sn;

    /**
     * 变更列表
     */
    private List<RefundPartDetailLogDTO> changeList;

}
