package com.danding.cds.endorsement.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class EndorsementTrackLogDTO implements Serializable {

    /**
     * 核注单id
     */
    private Long endorsementId;

    /**
     * 核注单状态
     */
    private String status;

    /**
     * 日志描述
     */
    private String logInfo;

    /**
     * 回执详情
     */
    private String callbackDetail;

    /**
     * 操作时间
     */
    private Date createTime;

    /**
     * 操作人
     */
    private String operator;
}
