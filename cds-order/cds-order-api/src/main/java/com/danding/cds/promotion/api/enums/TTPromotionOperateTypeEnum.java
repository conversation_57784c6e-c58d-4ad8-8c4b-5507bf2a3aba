package com.danding.cds.promotion.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TTPromotionOperateTypeEnum {

    CREATED("CREATE", "创建"),
    UPDATED("UPDATED", "更新"),
    AUDITED_PASS("AUDITED_PASS", "通过"),
    AUDITED_REJECT("AUDITED_REJECT", "不通过"),
    ;

    private final String code;
    private final String desc;

    public static TTPromotionOperateTypeEnum getEnum(String code) {
        for (TTPromotionOperateTypeEnum type : TTPromotionOperateTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
