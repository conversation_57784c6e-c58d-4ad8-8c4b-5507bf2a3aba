package com.danding.cds.customs.inventory.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description: 退货回调状态 （退货仓）
 * @create: 2019/12/12 14:32
 */
@Getter
@AllArgsConstructor
public enum RefundCallbackMessageStatus {

    UNKNOWN(0, ""),
    CALLOFF_REJECT(3, "取消单驳回"),
    DECLARE_START(4, "申报开始"),
    DECLARE_FAILED(5, "申报失败"),
    DECLARE_SUCCESS(6, "申报成功");
    private final Integer code;
    private final String desc;

    public static RefundCallbackMessageStatus getEnum(Integer value) {
        for (RefundCallbackMessageStatus step : RefundCallbackMessageStatus.values()) {
            if (step.getCode().equals(value)) {
                return step;
            }
        }
        return UNKNOWN;
    }
}
