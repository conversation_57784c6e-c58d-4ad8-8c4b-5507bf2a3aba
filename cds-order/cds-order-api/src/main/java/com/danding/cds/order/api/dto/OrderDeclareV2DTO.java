package com.danding.cds.order.api.dto;

import lombok.Data;

import java.util.List;

@Data
public class OrderDeclareV2DTO extends OrderDeclareDTO {
    private List<Long> ebpIdList;

    private List<Long> excludeEbpIdList;

    private Integer inventoryDeclareMax;
    private Integer orderDeclareMax;
    private Integer logisticDeclareMax;
    private Integer paymentDeclareMax;
    /**
     * 最大限制是否启用，控制达到最大限制时是否执行限制
     */
    private Boolean maxLimitedEnable = true;
    /**
     * 企业微信通知webHook
     */
    private String webHook;
    /**
     * 通知人电话
     */
    private List<String> phoneList;
}
