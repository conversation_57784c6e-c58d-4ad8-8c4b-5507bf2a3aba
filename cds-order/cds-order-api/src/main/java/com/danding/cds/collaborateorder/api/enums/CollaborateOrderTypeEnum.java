package com.danding.cds.collaborateorder.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CollaborateOrderTypeEnum {
    BUSSINESS_SECTION_OUT("SECTION_OUT", "区间流转(出)"),
    BUSSINESS_SECTION_IN("SECTION_IN", "区间流转(入)"),
    BUSSINESS_SECTIONINNER_OUT("SECTIONINNER_OUT", "区内流转(出)"),
    BUSSINESS_SECTIONINNER_IN("SECTIONINNER_IN", "区内流转(入)"),
    BUSSINESS_ONELINE_IN("ONELINE_IN", "一线入境");

    private String code;
    private String desc;

    public static CollaborateOrderTypeEnum getEnum(String value) {
        for (CollaborateOrderTypeEnum orderStatus : CollaborateOrderTypeEnum.values()) {
            if (orderStatus.getCode().equals(value)) {
                return orderStatus;
            }
        }
        throw new IllegalArgumentException("unknown BondedType.code: " + value);
    }


}
