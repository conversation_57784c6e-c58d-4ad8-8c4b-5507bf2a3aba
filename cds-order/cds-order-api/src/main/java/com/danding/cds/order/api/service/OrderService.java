package com.danding.cds.order.api.service;

import com.danding.cds.common.enums.DeclareEnum;
import com.danding.cds.message.api.process.OrderDeliverMessage;
import com.danding.cds.order.api.dto.*;
import com.danding.cds.payinfo.api.dto.ExtraPayInfoSubmit;
import com.danding.cds.route.api.enums.RouteActionEnum;
import com.danding.cds.v2.bean.dto.OrderTotalTaxEsDTO;
import com.danding.cds.v2.bean.enums.GoodsRecordMappingWayEnums;
import com.danding.cds.v2.bean.vo.req.CustomsLinkCancelReqVo;
import com.danding.cds.v2.bean.vo.res.JdFopDetailVO;
import com.danding.component.common.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;

import javax.validation.Valid;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * @Date: 2020/05/08 15:29
 * @Auth: Dante-gxj
 * @Description: 申报单服务
 * @link com.danding.cds.c.api.service.OrderService
 */
@Deprecated
public interface OrderService {

    /**
     * 提交创建订单
     *
     * @param submit
     * @return
     */
    @Deprecated
    Long submit(OrderSubmit submit) throws ArgsErrorException;

    /**
     * 支付信息上传
     *
     * @param userId
     * @param submit
     * @return
     */
    Long expInfoSubmit(Long userId, ExtraPayInfoSubmit submit);

    /**
     * 从海关子客户端接收上报动作
     *
     * @param data
     */
    void acceptCustomsSupport(String data);

    /**
     * 根据ID查找
     *
     * @param id
     * @return 不建议使用
     */
    OrderDTO findByIdFull(Long id);

    OrderDTO findByDeclareOrderNoFull(String declareOrderNo);

    OrderDTO findByGlobalSnFull(String systemGlobalSn);

    OrderDTO findByOutOrderSn(String outOrderSn);

    OrderDTO findByIdSection(Long id, Date sectionDate);

    /**
     * 根据给定时间表，获取对应ID数据
     *
     * @param idList
     * @param sectionDate
     * @return
     */
    List<OrderDTO> findByIdSection(List<Long> idList, Date sectionDate);

    OrderDTO findBySnSection(String sn);

    List<OrderDTO> findBySnSection(List<String> snList);

    /**
     * 查询统一电商平台下，是否已存在生效版本的订单
     *
     * @param ebpId
     * @param declareOrderNo
     * @param version
     * @return
     */
    OrderDTO findByEbpAndNoAndVersionFull(Long ebpId, String declareOrderNo, Long version);

    /**
     * 申报单号查询
     *
     * @param declareOrderNo
     * @return
     */
    OrderDTO findByDeclareOrderNo(String declareOrderNo);

    List<OrderDTO> listByNoAndVersionFull(String declareOrderNo, Long version);

    List<OrderDTO> listByOutOrderNo90Days(String outOrderNo);

    List<OrderDTO> listByOutOrderNo90Days(List<String> outOrderNoList);

    List<OrderDTO> listByEbpAndStatus14days(Long ebpId, Integer status);

    List<OrderDTO> listByUpdateTimeSection(Date updateFrom, Date updateTo, Date section);

    List<OrderDTO> listByUpdateTimeSection(Date updateFrom, Date updateTo);

    List<OrderDTO> listByCreateTimeSection(Date createFrom, Date createTo);

    /**
     * 根据ID更新
     *
     * @param id
     * @param status
     * @param sectionDate
     */
    void updateStatusSection(Long id, Integer status, Date sectionDate);

    void updateStatusSection(Long id, String orderSn, Integer status, Date sectionDate);

    void updateStatusSection(Long id, Integer status, String internalStatus, Date sectionDate);

    void updateStatusSection(Long id, String orderSn, Integer status, String internalStatus, Date sectionDate);

    void updateByFinish(Long id, Integer status, Date sectionDate);

    void updateByFinishClearException(Long id, Integer status, Date sectionDate);

    List<OrderDTO> findByNo90Days(String declareOrderNo);

    void updateSnWithFix(Long id, String customOrderSn, String customsInventorySn, Date sectionDate);

    /**
     * 添加清关异常标识
     *
     * @param id
     * @param typeId
     * @param detail
     */
    void addExceptionSection(Long id, Long typeId, String detail, Date sectionDate);

    void addExceptionSection(Long id, String orderSn, Long typeId, String detail, Date sectionDate);

    /**
     * 移除清关异常标识
     *
     * @param id
     * @param sectionDate
     */
    void clearExceptionSection(Long id, Date sectionDate);


    /**
     * 出入库系统提交创建
     *
     * @param submit
     * @return
     */
    Long syncLogsticsOrder(OrderSimpleSubmit submit) throws ArgsErrorException;


    /**
     * 出入库系统申报单状态回调同步
     *
     * @param orderCallbackSubmit
     * @return
     */
    void syncOrderCallback(OrderCallbackSubmit orderCallbackSubmit);

    void syncDeliver(OrderDeliverMessage message);

    void syncTaxCallback(TaxCallbackSubmit taxCallbackSubmit);

    List<TaxCallbackSubmit> queryTaxByDeclareNo(List<String> declareNoList);

    /**
     * 同步上报订单
     *
     * @param submit DockerOrderSubmit
     * @return
     */
    Integer syncDockerOrder(@Valid DockerOrderSubmit submit) throws ArgsErrorException;

    /**
     * 审核
     *
     * @param sn
     */
    void examine(String sn);

    /**
     * 申报
     *
     * @param sn
     */
    void declare(String sn) throws ArgsErrorException;

    /**
     * 申报实现
     *
     * @param sn          申报单sn
     * @param declareEnum 指定的申报类型
     * @throws ArgsErrorException
     */
    void declare(String sn, DeclareEnum declareEnum) throws ArgsErrorException;

    /**
     * 异常单重推
     *
     * @param id
     * @throws ArgsErrorException
     */
    void exceptionOrderDeclare(Long id) throws ArgsErrorException;

    /**
     * 重置申报项申报状态
     *
     * @param id
     * @param action
     */
    void resetDeclare(Long id, RouteActionEnum action) throws ArgsErrorException;

    String checkCompanyEnable(Long id, RouteActionEnum action) throws ArgsErrorException;

    /**
     * 更新支付人信息
     *
     * @param id
     * @param createTime
     * @param playerId
     * @param playerName
     */
    void payerInfoUpdate(OrderDTO orderDTO, String payerId, String payerName);

    /**
     * 更新申报单基础信息
     *
     * @param submit OrderBaseInfoSubmit
     */
    void orderBaseInfoUpdate(OrderBaseInfoSubmit submit);

    void customsOrderInfoUpdate(CustomsOrderBaseInfoSubmit customsOrderBaseInfoSubmit);

    void customsInventoryInfoUpdate(InventoryBaseInfoSubmit inventoryBaseInfoSubmit) throws ArgsErrorException;

    /**
     * 更新商品基础信息
     *
     * @param submit OrderItemInfoSubmit
     */
    void orderItemInfoUpdate(InventoryItemInfoSubmit submit);


    /**
     * 订单直接关闭申报
     *
     * @param id
     */
    void cancel(Long id) throws ArgsErrorException;

    /**
     * 订单挂起
     *
     * @param id
     */
    void operateHangUpOrder(Long id, Integer hungUpStatus) throws ArgsErrorException;

    /**
     * 菜鸟逆向指令取消
     *
     * @param reqVo
     */
    void cancelByLink(CustomsLinkCancelReqVo reqVo) throws ArgsErrorException;

    void cancelOrderPushOMS(Long id, String reason) throws ArgsInvalidException;

    Boolean cancelByGlobalSn(String systemGlobalSn, String picJson, String refundLogisticsNo);

    Boolean cancelByGlobalSnV2(CancelOrderSubmit cancelOrderSubmit) throws ArgsInvalidException;

    /**
     * 逐步废弃
     *
     * @param SystemGlobalSn
     */
    void cacelByGlobalSn(String SystemGlobalSn);

    /**
     * 接收清单申报结果回执
     *
     * @param receive
     * @return
     */
    String receiveInventory(CustomsReceive receive);

    CustomsStatusMappingDTO getCustomsStatusMappingDTO(CustomsReceive receive);

    /**
     * 分页查询
     *
     * @param search
     * @return
     */
    ListVO<OrderDTO> pagingES(OrderSearch search);

    List<OrderDTO> listOrder(Long ebpId, List<Integer> statusList, Integer page, Integer queryDays);

    /**
     * 根据平台查询
     *
     * @param ebpId
     * @param status
     * @param page
     * @return
     */
    List<OrderDTO> listOrder(Long ebpId, Integer status, Integer page, Integer queryDays);

    List<OrderDTO> listOrder(List<Long> ebpId, Integer status, Integer page, Integer queryDays);

    List<OrderDTO> listOrder(List<Long> ebpId, List<Integer> status, Integer page, Integer queryDays);

    List<OrderDTO> listOrderExcludeEbpId(List<Long> ebpIdList, Integer status, Integer page, Integer queryDays);

    /**
     * 订单监控监控
     *
     * @param search
     * @return
     */
    void orderMonitor(JdMonitorParamDto jdMonitorParamDto);

    /**
     * ES单个更新
     *
     * @param sn
     */
    void dumpBySn(String sn);

    /**
     * ES批量更新
     *
     * @param sn
     */
    void dumpBySn(List<String> snList);

    void deliver(OrderDTO orderDTO, String logisticsNo, String depotOrderSn, BigDecimal weight, Long shipmentTime);

    /**
     * es账册id处理
     * 维护账册id，如果为空，就维护进去
     */
    void esAccountBookIdDeal();

    List<CustomsCancelOrderMappingDTO> getRefundOrderByOwnerCode(String ownerCode, String entityWarehouseCode);

    List<CustomsCancelOrderMappingDTO> getRefundOrderByOwnerCode(String ownerCode, String entityWarehouseCode, Long timeBegin, Long timeEnd);

    List<CustomsCancelOrderMappingDTO> getCallBackOrderByOwnerCode(String ownerCode, String entityWarehouseCode);

    List<CustomsCancelOrderMappingDTO> getCallBackOrderByOwnerCode(String ownerCode, String entityWarehouseCode, Long timeBegin, Long timeEnd);

    String getOrderSubmit(Long id) throws ArgsErrorException;

    /**
     * 获取多个申报单提交原始数据
     *
     * @param idList 申报单ID列表
     * @return
     */
    List<String> getOrderSubmitOrginMsg(List<Long> idList);

    void dumpEsTaxPrice(OrderTotalTaxEsDTO totalTaxEsDTO) throws IOException;

    void updateLastDeclareTime(String snList, String action);

    void syncEsData(String sn);

    void updateOrderInternalStatus(Long orderId, String internalStatus);

    void updateOrderInternalStatus(Long orderId, String orderSn, String internalStatus);

    void updateOrderInternalStatus(OrderDTO orderDTO, String internalStatus);

    void updateOrderInternalStatusAndDeclareRecord(OrderDTO orderDTO, String internalStatus);

    void updateOrderInternalStatusBySnList(List<String> snList, String internalStatus);

    Map<String, String> pddDecrypt(String orderSn, List<String> data);

    Map<String, String> pddEncrypt(String orderSn, List<String> data, String type);

    ConsigneeInfoRes getConsigneeInfo(String orderSn);

    void updateConsigneeAndBuyerInfo(ConsigneeInfoParam param);

    JdFopDetailVO getJdFopDetailByDeclareOrderNo(String declareOrderNo);

    GoodsRecordMappingWayEnums getGoodsRecordMappingWayEnum(List<String> declareOrderTypeList);

    Long findOrderIdByDeclareOrderNo(String declareOrderNo);

//    void logicDeleteBySn(String orderSn);

    void updateBySn(OrderDTO orderDTO, String sn);

    void deletedById(Long id) throws ArgsErrorException;

    boolean isByteDanceDeclareWithinCloud(String code, String extraJson);
}