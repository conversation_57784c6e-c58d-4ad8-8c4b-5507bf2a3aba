package com.danding.cds.promotion.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TTPromotionItemTypeEnum {

    NULL("", ""),
    MAIN_ITEM("1", "主商品"),
    PRESENT_ITEM("2", "赠品");

    private final String code;
    private final String desc;


    public static TTPromotionItemTypeEnum getEnum(String code) {
        for (TTPromotionItemTypeEnum item : TTPromotionItemTypeEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return NULL;
    }
}
