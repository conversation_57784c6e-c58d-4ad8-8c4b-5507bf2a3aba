package com.danding.cds.order.api.service;

import com.danding.cds.exception.api.dto.ExceptionDTO;
import com.danding.cds.order.api.dto.CustomsStatusMappingDTO;
import com.danding.cds.order.api.vo.CustomsStatusMappingAddReqVO;
import com.danding.cds.order.api.vo.CustomsStatusMappingReqVO;
import com.danding.cds.order.api.vo.CustomsStatusMappingResVO;
import com.danding.cds.order.api.vo.CustomsStatusMappingUpdReqVO;
import com.danding.cds.route.api.enums.RouteActionEnum;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;

import java.util.List;

/**
 * @link com.danding.cds.c.api.service.CustomsStatusMappingService
 */
@Deprecated
public interface CustomsStatusMappingService {

    ExceptionDTO findExceptionByCustomsDetail(String customsStatus, String customsDetail, RouteActionEnum routeActionEnum);

    CustomsStatusMappingDTO findByCustomsCallback(String customsStatus, String customsDetail, RouteActionEnum routeActionEnum);

    CustomsStatusMappingDTO findOnlyByCustomsCallback(String customsStatus, String customsDetail, RouteActionEnum routeActionEnum);

    List<CustomsStatusMappingDTO> listByException(Boolean exceptionFlag);

    CustomsStatusMappingDTO findByCode(String code);

    CustomsStatusMappingDTO findById(Long id);

    List<CustomsStatusMappingDTO> findByIds(List<Long> idList);

    Integer findPendingCount();

    Long create(CustomsStatusMappingDTO customsStatusMappingDTO) throws ArgsErrorException;

    void edit(CustomsStatusMappingUpdReqVO reqVO) throws ArgsErrorException;

    Long update(CustomsStatusMappingDTO customsStatusMappingDTO);

    ListVO<CustomsStatusMappingResVO> paging(CustomsStatusMappingReqVO reqVO);

    void save(CustomsStatusMappingAddReqVO reqVO) throws ArgsErrorException;

    void delete(CustomsStatusMappingUpdReqVO reqVO);

    List<CustomsStatusMappingDTO> getCustomsReceipt();

    List<CustomsStatusMappingDTO> findByCustomsCodeAndException(String code, Long exception);

    List<CustomsStatusMappingDTO> findByCustomsCodeAndException(List<String> code, List<Long> exception);

    CustomsStatusMappingDTO getFinalMappingCode(CustomsStatusMappingDTO originDTO, String customsDetail);

    List<CustomsStatusMappingDTO> listAll();
}
