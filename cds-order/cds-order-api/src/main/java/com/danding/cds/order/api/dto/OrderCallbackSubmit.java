package com.danding.cds.order.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: Raymond
 * @Date: 2020/8/13 13:35
 * @Description:
 */
@Data
public class OrderCallbackSubmit implements Serializable {
    @ApiModelProperty("申报单id")
    private Long orderId;
    /**
     * 状态枚举值，详见订单状态枚举, 支付状态枚举以及结算状态枚举
     */
    @ApiModelProperty("状态枚举值")
    private String status;

    @ApiModelProperty("上游订单号")
    private String  orderNo;

    @ApiModelProperty("申报单号")
    private String declareOrderNo;

    /**
     * 状态描述 标识订单的状态描述，详见订单状态, 支付状态枚举以及结算状态枚举
     */
    @ApiModelProperty("状态描述")
    private String description;

    @ApiModelProperty("标识订单的状态变迁时间")
    private String datetime;

    @ApiModelProperty("系统全局单号")
    private String systemGlobalSn;

    @ApiModelProperty("运单号")
    private String logisticsNo;

    @ApiModelProperty("清单号")
    private String inventoryNo;

    /**
     * 电商平台
     */
    public String ebpCode;
    public String ebpName;

    /**
     * 电商企业
     */
    public String ebcCode;
    public String ebcName;

    /**
     * 物流企业
     */
    public String logisticsCode;
    public String logisticsName;

    /**
     * 担保企业
     */
    public String assureCode;
    public String assureName;

    /**
     * 支付企业
     */
    public String payCompanyCode;
    public String payCompanyName;
}
