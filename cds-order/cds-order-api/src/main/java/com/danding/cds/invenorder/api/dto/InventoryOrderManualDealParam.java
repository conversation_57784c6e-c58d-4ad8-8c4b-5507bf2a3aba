package com.danding.cds.invenorder.api.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: 清关单手动清关提交参数
 * @date 2022/4/13 14:06
 */
@Data
public class InventoryOrderManualDealParam implements Serializable {
    /**
     * 手动清关单id
     */
    @NotNull(message = "清关单id为空")
    private Long id;
    /**
     * 关联清关单sn
     */
    @NotBlank(message = "关联清关单sn为空")
    private String associatedInventorySn;
}
