package com.danding.cds.customs.inventory.api.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 业务中台工作总览 - 保税售后 - 退货和撤单
 */
@Data
public class CancelAndRefundAfterSaleCount implements Serializable {

    /**
     * 用户名
     */
    private String userName;
    private String userId;

    /**
     * 需申报
     * <p>
     * 撤单 - 初始化+时间段
     * 退货 - 待总署审核（审核状态）+时间段
     */
    private Integer initDeclare;

    /**
     * 处理申报
     * <p>
     * 撤单 - 申报中+待总署审核+时间段
     * 退货 - 审核通过（审核状态）+时间段
     */
    private Integer processingDeclare;

    /**
     * 申报成功
     * <p>
     * 撤单 - 审核通过+时间段
     * 退货 - 待总署审核（审核状态）+时间段
     */
    private Integer successfulDeclare;

    /**
     * 申报失败
     * <p>
     * 撤单 - 申报失败+总署驳回+时间段
     * 退货 - 待总署审核（审核状态）+时间段
     */
    private Integer failedDeclare;

    /**
     * 取消退货/撤单
     * <p>
     * 撤单 - 取消撤单+时间段
     * 退货 - 待总署审核（审核状态）+时间段
     */
    private Integer cancel;


}
