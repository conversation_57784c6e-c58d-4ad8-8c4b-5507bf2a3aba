package com.danding.cds.invenorder.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import static com.danding.cds.invenorder.api.enums.InventoryOrderBusinessEnum.*;

/**
 * @program: cds-center
 * @description: 作业单和清关单业务类型映射
 * @author: 潘本乐（Belep）
 * @create: 2024-03-16 14:48
 **/
@Getter
@AllArgsConstructor
public enum WorkOrderStatueEnum {

    CUSTOMS_CLEAR_ORDER_SUBMIT_CHECK("CUSTOMS_CLEAR_ORDER_SUBMIT_CHECK", "提交资料（资料待审核）",
            new InventoryOrderBusinessEnum[]{BUSSINESS_ONELINE_IN, BUSSINESS_SECTION_IN, BUSSINESS_SECTIONINNER_IN, BUSINESS_ONELINE_REFUND, BUSSINESS_SECTION_OUT, BUSSINESS_SECTIONINNER_OUT, BUSSINESS_DESTORY, BUSINESS_FB_IN, BUSINESS_FB_OUT}),
    CUSTOMS_CLEAR_ORDER_PASS("CUSTOMS_CLEAR_ORDER_PASS", "审核通过",
            new InventoryOrderBusinessEnum[]{BUSSINESS_ONELINE_IN, BUSSINESS_SECTION_IN, BUSSINESS_SECTIONINNER_IN, BUSINESS_ONELINE_REFUND, BUSSINESS_SECTION_OUT, BUSSINESS_SECTIONINNER_OUT, BUSSINESS_DESTORY, BUSINESS_FB_IN, BUSINESS_FB_OUT}),
    CARGO_ARRIVED("4", "货物到港", new InventoryOrderBusinessEnum[]{InventoryOrderBusinessEnum.BUSSINESS_ONELINE_IN}),
    M_ICUS_START("M_ICUS_START", "清关开始",
            new InventoryOrderBusinessEnum[]{BUSSINESS_ONELINE_IN, BUSINESS_FB_IN}),
    PORT_CHECK("PORT_CHECK", "口岸查验", new InventoryOrderBusinessEnum[]{BUSSINESS_ONELINE_IN}),
    PORT_CHECK_START("PORT_CHECK_START", "口岸查验开始", new InventoryOrderBusinessEnum[]{BUSSINESS_ONELINE_IN}),
    PORT_CHECK_FINISH("PORT_CHECK_FINISH", "口岸查验结束", new InventoryOrderBusinessEnum[]{BUSSINESS_ONELINE_IN}),
    PORT_LEAVE("PORT_LEAVE", "口岸放行", new InventoryOrderBusinessEnum[]{BUSSINESS_ONELINE_IN}),
    PORT_LEAVE_FINISH("PORT_LEAVE_FINISH", "清关完成（海关放行）",
            new InventoryOrderBusinessEnum[]{BUSSINESS_ONELINE_IN, BUSSINESS_SECTION_IN, BUSSINESS_SECTIONINNER_IN, BUSINESS_ONELINE_REFUND, BUSSINESS_SECTION_OUT, BUSSINESS_SECTIONINNER_OUT, BUSSINESS_DESTORY}),
    M_TRANSPORT_ARRIVED("M_TRANSPORT_ARRIVED", "服务完成",
            new InventoryOrderBusinessEnum[]{BUSSINESS_ONELINE_IN, BUSSINESS_SECTION_IN, BUSSINESS_SECTIONINNER_IN, BUSINESS_FB_IN}),
    EX_TRANSPORT_ARRIVED("EX_TRANSPORT_ARRIVED", "服务完成",
            new InventoryOrderBusinessEnum[]{BUSINESS_ONELINE_REFUND, BUSINESS_FB_OUT}),
    CUSTOMS_CLEAR_ENTRY_DECLARE_START("CUSTOMS_CLEAR_ENTRY_DECLARE_START", "入区申报开始",
            new InventoryOrderBusinessEnum[]{BUSSINESS_SECTION_IN, BUSSINESS_SECTIONINNER_IN}),
    CUSTOMS_CLEAR_ENTRY_DECLARE_FINISH("CUSTOMS_CLEAR_ENTRY_DECLARE_FINISH", "入区申报结束",
            new InventoryOrderBusinessEnum[]{BUSSINESS_SECTION_IN, BUSSINESS_SECTIONINNER_IN}),
    EX_CUS_START("EX_CUS_START", "清关开始",
            new InventoryOrderBusinessEnum[]{BUSINESS_ONELINE_REFUND, BUSSINESS_SECTION_OUT, BUSSINESS_SECTIONINNER_OUT, BUSSINESS_DESTORY, BUSINESS_FB_OUT}),
    OUT_APPLY_START("OUT_APPLY_START", "出区申报开始",
            new InventoryOrderBusinessEnum[]{BUSINESS_ONELINE_REFUND, BUSSINESS_SECTION_OUT, BUSSINESS_SECTIONINNER_OUT, BUSSINESS_DESTORY}),
    OUT_APPLY_END("OUT_APPLY_END", "出区申报结束",
            new InventoryOrderBusinessEnum[]{BUSINESS_ONELINE_REFUND, BUSSINESS_SECTION_OUT, BUSSINESS_SECTIONINNER_OUT, BUSSINESS_DESTORY});

    private String code;
    private String desc;
    private InventoryOrderBusinessEnum[] allowInventoryOrderTypes;

    public static boolean checkBusinessReportStatusMatch(InventoryOrderBusinessEnum businessEnum, WorkOrderStatueEnum statueEnum) {
        if (Objects.isNull(statueEnum) || Objects.isNull(businessEnum)) {
            return false;
        }
        List<InventoryOrderBusinessEnum> list = Arrays.asList(statueEnum.allowInventoryOrderTypes);
        return list.contains(businessEnum);
    }

    public static WorkOrderStatueEnum getEnumsByCode(String code) {
        for (WorkOrderStatueEnum enums : WorkOrderStatueEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    public static void main(String[] args) {
        System.out.println(checkBusinessReportStatusMatch(BUSSINESS_DESTORY, WorkOrderStatueEnum.CARGO_ARRIVED));
    }

}
