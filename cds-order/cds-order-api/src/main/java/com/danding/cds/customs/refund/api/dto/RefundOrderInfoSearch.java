package com.danding.cds.customs.refund.api.dto;

import com.danding.cds.common.annotations.UcRoleAccountBookIdList;
import com.danding.logistics.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@ApiModel
public class RefundOrderInfoSearch extends Page {

    @ApiModelProperty("物流企业代码")
    private String logisticsCode;
    ;

    /**
     * 退货订单完成状态[初始化，待退货,退货完成,退货关闭]
     **/
    @ApiModelProperty("审核状态[0：初始化],[1:待退货],[2:退货完成],[3:退货关闭]")
    private Integer refundStatus;

    @ApiModelProperty("审核状态[INT：初始化],[AUDITING:待总署审核],[AUDIT_PASS:审核通过],[AUDIT_REJECT:总署驳回]")
    private List<String> refundCheckStatusList;

    @ApiModelProperty("创建时间 - > 开始")
    private Long beginCreateTime;

    @ApiModelProperty("创建时间 - > 结束")
    private Long endCreateTime;

    @ApiModelProperty("完成时间 - > 开始")
    private Long beginCompleteTime;

    @ApiModelProperty("完成时间 - > 结束")
    private Long endCompleteTime;

    @ApiModelProperty("关键词查询类型 outOrderNo,declareOrderNo,inventoryNo,logisticsNo")
    private String queryType;

    @ApiModelProperty("关键词查询关键词")
    private String queryInfo;

    @ApiModelProperty("导入，导出勾选字段")
    private String ids;

    @ApiModelProperty("角色账册ID列表")
    @UcRoleAccountBookIdList
    private List<Long> roleAccountBookIdList;

    /**
     * "电商企业编号"
     */
    private String ebcCode;

    /**
     * "担保企业编号"
     */
    private String assureCompanyCode;

    /**
     * 清关企业
     */
    private String agentCompanyCode;

    /**
     * 电商平台
     */
    private String ebpCode;

    /**
     * 区内企业id
     */
    private Long areaCompanyId;

    @ApiModelProperty("申报成功时间")
    private Long declareSuccessTimeFrom;

    @ApiModelProperty("申报成功时间")
    private Long declareSuccessTimeTo;

    /**
     * 是否部分退标志
     */
    @ApiModelProperty("是否部分退  0否  1是")
    private Integer refundPartFlag;

    /**
     * 是否跨关区 0否  1是 默认0
     */
    private Integer refundCrossCustomsFlag;

    /**
     * 是否异常 0否  1是
     */
    private Integer refundExceptionFlag;
}