package com.danding.cds.customs.order.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class CustomsOrderDTO implements Serializable {

    private static final long serialVersionUID = -8592623245709946322L;
    private Long id;

    /**
     * 订单系统编号
     */
    private String sn;

    /**
     * 申报单系统编号
     */
    private String orderSn;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单编号 申报单号
     */
    private String declareOrderNo;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 关区口岸
     */
    private String customs;

    /**
     * 电商平台
     */
    private Long ebpId;

    /**
     * 电商企业
     */
    private Long ebcId;

    /**
     * 支付方式ID
     */
    private Long payChannelId;

    /**
     * 支付企业
     */
    private Long payCompanyId;

    /**
     * 报文传输企业
     */
    private Long agentCompanyId;

    /**
     * 支付申报流水号
     */
    private String declarePayNo;

    /**
     * 支付交易流水号
     */
    private String tradePayNo;

    /**
     * 运费
     */
    private BigDecimal freight;

    /**
     * 税费
     */
    private BigDecimal tax;

    /**
     * 折扣 正数
     */
    private BigDecimal discount;

    /**
     * 交易时间
     */
    private Date tradeTime;

    /**
     * 发件人名称
     */
    private String senderName;

    /**
     * 快递方式ID
     */
    private Long expressId;

    /**
     * 物流企业
     */
    private Long logisticsCompanyId;

    /**
     * 订购人手机号
     */
    private String buyerTelNumber;
    /**
     * 订购人证件号码
     */
    private String buyerIdNumber;

    /**
     * 订购人姓名
     */
    private String buyerName;

    /**
     * 收件人姓名
     */
    private String consigneeName;

    /**
     * 收件人地址
     */
    private String consigneeAddress;

    /**
     * 收件人电话
     */
    private String consigneeTel;

    /**
     * 收件人邮箱
     */
    private String consigneeEmail;

    /**
     * 海关状态
     */
    private String customsStatus;

    /**
     * 最后一次清关回执时间
     */
    private Date lastCustomsTime;

    /**
     * 最后一次申报时间
     */
    private Date lastDeclareTime;

    /**
     * 清关回执描述
     */
    private String customsDetail;

    /**
     * json储存的商品信息
     */
    private String itemJson;

    /**
     * 已申报的次数
     */
    private Integer declareFrequency;

    /**
     * 备注
     */
    private String note;

    /**
     * json储存的其他属性键值对
     */
    private String extraJson;

    private Date createTime;
    private Date updateTime;

}
