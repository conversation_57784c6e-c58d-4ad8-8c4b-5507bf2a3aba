package com.danding.cds.handoverOrder.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 交接单明细实体对象
 * @date 2021/12/14
 */
@Data
public class HandoverOrderDetailVO implements Serializable {

    /**
     * 包裹号
     */
    private String packageSn;

    /**
     * 包裹重量（kg）
     */
    private String packageWeight;


    /**
     * 运单编号
     */
    private String wayBillSn;

    /**
     * 交接单
     */
    private String handoverSn;

    /**
     * 申报单号
     */
    private String declareOrderNo;

    /**
     * 快递公司
     */
    private String expressName;

    /**
     * 出库时间
     */
    private Date outHouseTime;

    /**
     * 出库单号
     */
    private String outboundOrder;


    /**
     * 仓库名称
     */
    private String storeHouseName;






}
