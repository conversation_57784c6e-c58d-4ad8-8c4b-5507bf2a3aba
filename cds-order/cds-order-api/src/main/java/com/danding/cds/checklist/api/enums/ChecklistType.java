package com.danding.cds.checklist.api.enums;

import com.danding.cds.endorsement.api.enums.EndorsementBussiness;
import com.danding.cds.endorsement.api.enums.IEType;

import java.util.HashMap;
import java.util.Map;

public enum ChecklistType {
    NULL(0, "空"),
    FIRST(1, "一线入区"),
    SECOND(2, "二线入区"),
    EMPTY(3, "空车入区"),
    SECOND_OUT(4, "二线出区"),
    EMPTY_OUT(5, "空车出区"),
    TWO_STEP(6, "两步申报"),
    ONE_OUT(7, "一线出区");
    private static Map<Integer, EndorsementBussiness[]> checkListTypeMapEndorsementBussiness = new HashMap<Integer, EndorsementBussiness[]>();
    private static Map<Integer, IEType> ieTypeMap = new HashMap<Integer, IEType>();

    static {
        checkListTypeMapEndorsementBussiness.put(FIRST.getCode(), new EndorsementBussiness[]{EndorsementBussiness.BUSSINESS_ONELINE_IN,
                EndorsementBussiness.BUSINESS_BONDED_ONELINE_IN,
                EndorsementBussiness.BUSSINESS_SECTION_IN,
                EndorsementBussiness.BUSINESS_BONDED_PROCESSING_ONELINE_IN
        });
        checkListTypeMapEndorsementBussiness.put(SECOND.getCode(), new EndorsementBussiness[]{EndorsementBussiness.BUSSINESS_SECTION_IN,
                EndorsementBussiness.BUSSINESS_DESTORY});
        checkListTypeMapEndorsementBussiness.put(SECOND_OUT.getCode(), new EndorsementBussiness[]{EndorsementBussiness.BUSSINESS_SECTION_OUT,
                EndorsementBussiness.BUSSINESS_SECONDE_OUT,
                EndorsementBussiness.BUSINESS_SIMPLE_PROCESSING,
                EndorsementBussiness.BUSINESS_BONDED_TO_TRADE
        });
        checkListTypeMapEndorsementBussiness.put(ONE_OUT.getCode(), new EndorsementBussiness[]{EndorsementBussiness.BUSINESS_ONELINE_REFUND,
                EndorsementBussiness.BUSINESS_BONDED_PROCESSING_ONELINE_OUT
        });

        ieTypeMap.put(FIRST.getCode(), IEType.IMPORT);
        ieTypeMap.put(SECOND.getCode(), IEType.IMPORT);
        ieTypeMap.put(EMPTY.getCode(), IEType.IMPORT);
        ieTypeMap.put(SECOND_OUT.getCode(), IEType.EXPORT);
        ieTypeMap.put(EMPTY_OUT.getCode(), IEType.EXPORT);
        ieTypeMap.put(TWO_STEP.getCode(), IEType.IMPORT);
        ieTypeMap.put(ONE_OUT.getCode(), IEType.EXPORT);
    }
    private Integer code;

    private String desc;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    ChecklistType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static IEType getIEType(Integer code)
    {

       return  ieTypeMap.get(code);

    }
    public static ChecklistType getEnum(Integer code){
        for (ChecklistType value : ChecklistType.values()) {
            if (code == value.code){
                return value;
            }
        }
        return NULL;
    }
    public static Map<Integer, EndorsementBussiness[]> getCheckListTypeMapEndorsementBussiness()
    {
        return  checkListTypeMapEndorsementBussiness;
    }
}
