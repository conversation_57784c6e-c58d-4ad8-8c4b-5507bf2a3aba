package com.danding.cds.handoverOrder.api.enums;

public enum HandoverOrderFinishStatus {

    TREAT_OUTBOUND(0, "未完成"),
    ALREADY_OUTBOUND(1, "已完成");


    private Integer value;

    private String desc;

    HandoverOrderFinishStatus(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static HandoverOrderFinishStatus getEnum(Integer value) {
        for (HandoverOrderFinishStatus exportOrderStatus : HandoverOrderFinishStatus.values()) {
            if (exportOrderStatus.value == value) {
                return exportOrderStatus;
            }
        }
        return TREAT_OUTBOUND;
    }
}
