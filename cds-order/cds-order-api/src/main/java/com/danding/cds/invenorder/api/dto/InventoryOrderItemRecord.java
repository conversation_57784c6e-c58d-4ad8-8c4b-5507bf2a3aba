package com.danding.cds.invenorder.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ApiModel
public class InventoryOrderItemRecord implements Serializable {
    private static final long serialVersionUID = 7091343962031431654L;

    @ApiModelProperty("行号")
    private int idx;
    // 导入时必须提交
    @ApiModelProperty("账册备案序号")
    private String goodsSeqNo;

    @ApiModelProperty("*统一料号")
    private String productId;

    @ApiModelProperty("海关备案料号")
    private String customsRecordProductId;

    @ApiModelProperty("*申报数量")
    private BigDecimal declareQty;

    @ApiModelProperty("申报单价")
    private BigDecimal declarePrice;

    @ApiModelProperty("币制【编码】")
    private String currency;

    @ApiModelProperty("规格型号")
    private String goodsModel;

    @ApiModelProperty("错误信息")
    private String errorMsg;
}
