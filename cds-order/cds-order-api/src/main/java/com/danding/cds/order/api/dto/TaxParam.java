package com.danding.cds.order.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: Raymond
 * @Date: 2020/11/4 15:32
 * @Description:
 */
@Data
@ApiModel
public class TaxParam implements Serializable {
    @ApiModelProperty("sku编码")
    private String skuCode;
    @ApiModelProperty("仓库编码")
    private String wareCode;
    @ApiModelProperty("价格")
    private BigDecimal price;
    @ApiModelProperty("个数")
    private Integer count;
    @ApiModelProperty("商品计税规格")
    private BigDecimal goodsCustomsHsPar;
}
