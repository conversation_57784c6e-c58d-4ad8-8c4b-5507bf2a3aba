package com.danding.cds.order.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class OrderSensitivePlainText implements Serializable {

    /**
     * 购买人电话
     */
    @ApiModelProperty("购买人电话")
    private String buyerTelNumber;

    /**
     * 订购人证件号码
     */
    @ApiModelProperty("订购人证件号码")
    private String buyerIdNumber;

    /**
     * 订购人姓名
     */
    @ApiModelProperty("订购人姓名")
    private String buyerName;

    @ApiModelProperty("收件人地址")
    private String consigneeAddress;

    @ApiModelProperty("收件人电话")
    private String consigneeTel;

    @ApiModelProperty("收件人姓名")
    private String consigneeName;
}
