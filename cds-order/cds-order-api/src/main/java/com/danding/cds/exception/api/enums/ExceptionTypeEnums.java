package com.danding.cds.exception.api.enums;

public enum ExceptionTypeEnums {
    BUSINESS_EXCEPTION(1,"业务异常"),
    SYSTEM_EXCEPTION(2,"系统异常"),
    CUSTOMS_EXCEPTION(3,"海关异常");


    private Integer code;

    private String desc;

    ExceptionTypeEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ExceptionTypeEnums getEnum(Integer code){
        for (ExceptionTypeEnums value : ExceptionTypeEnums.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        throw new IllegalArgumentException("unknown BondedType.code: " + code);
    }
}
