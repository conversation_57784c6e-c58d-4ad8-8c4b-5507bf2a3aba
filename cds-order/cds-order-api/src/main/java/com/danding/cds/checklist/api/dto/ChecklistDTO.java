package com.danding.cds.checklist.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class ChecklistDTO implements Serializable {

    private static final long serialVersionUID = 4494367742761823694L;
    private Long id;
    /**
     * 企业内部核放单号
     */
    private String sn;

    /**
     * 预录入核放单号
     */
    private String preOrderNo;

    /**
     * 真实核放单号
     */
    private String realOrderNo;

    /**
     * 清关企业ID
     */
    private Long declareCompanyId;

    /**
     * 报关单号
     */
    private String declareOrderNo;

    /**
     * IC卡号
     */
    private String vehicleIcNo;

    /**
     * 车牌号
     */
    private String licensePlate;

    /**
     * 车架号
     */
    private String licenseFrame;

    /**
     * 车辆自重
     */
    private BigDecimal carWeight;

    /**
     * 车架重
     */
    private BigDecimal frameWeight;

    /**
     * 账册ID
     */
    private Long accountBookId;

    /**
     * 核放单状态
     */
    private String status;
    /**
     * 海关回执状态
     */
    private String customsStatus;
    /**
     * 海关回执描述
     */
    private String informationDesc;
    /**
     * 核放单类型
     */
    private Integer type;

    private String endorsementType;

    private Integer bindType;

    private BigDecimal totalNetWeight;

    private BigDecimal totalGrossWeight;

    private BigDecimal totalWeight;


    /**
     * 出入区标志
     */
    private Integer ieFlag;

    /**
     * 申请人
     */
    private String applicant;

    private String extraJson;

    private Date createTime;

    /**
     * 备注
     */
    private String remark;

    private String emptyCarPicUrl;

    private String emptyCarPicName;
}
