package com.danding.cds.customs.inventory.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class CustomsInventoryExtra implements Serializable {
    /**
     * 快递方式Code 这个快照是为了可以追溯最原始传入的快递代码
     */
    private String expressCode;

    private String customsBookNo;

    private String routeCode;

    private String consigneeProvince;

    private String consigneeCity;

    private String consigneeDistrict;

    /**
     * 收件人街道（四级地址）
     */
    private String consigneeStreet;
    /**
     * 支付流水号
     */
    private String payTransactionId;
    /**
     * 支付企业
     */
    private String payCompanyCode;

    /**
     * 税费
     */
    private BigDecimal taxFee;
    /**
     * 折扣
     */
    private BigDecimal discountFee;

    private String tenantOuterId;

    private String tenantName;

    private Boolean notItem;
}
