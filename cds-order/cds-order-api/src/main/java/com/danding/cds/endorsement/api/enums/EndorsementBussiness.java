package com.danding.cds.endorsement.api.enums;

import com.danding.cds.invenorder.api.enums.InventoryOrderBusinessEnum;

import java.util.Arrays;
import java.util.List;

public enum EndorsementBussiness {
    BUSSINESS_EMPTY(InventoryOrderBusinessEnum.BUSSINESS_EMPTY.getCode(), InventoryOrderBusinessEnum.BUSSINESS_EMPTY.getDesc(), false),
    BUSSINESS_REFUND_INAREA(InventoryOrderBusinessEnum.BUSSINESS_REFUND_INAREA.getCode(), InventoryOrderBusinessEnum.BUSSINESS_REFUND_INAREA.getDesc(), false),
    BUSSINESS_SECTION_OUT(InventoryOrderBusinessEnum.BUSSINESS_SECTION_OUT.getCode(), InventoryOrderBusinessEnum.BUSSINESS_SECTION_OUT.getDesc(), true),
    BUSSINESS_SECTION_IN(InventoryOrderBusinessEnum.BUSSINESS_SECTION_IN.getCode(), InventoryOrderBusinessEnum.BUSSINESS_SECTION_IN.getDesc(), true),
    BUSSINESS_SECTIONINNER_OUT(InventoryOrderBusinessEnum.BUSSINESS_SECTIONINNER_OUT.getCode(), InventoryOrderBusinessEnum.BUSSINESS_SECTIONINNER_OUT.getDesc(), false),
    BUSSINESS_SECTIONINNER_IN(InventoryOrderBusinessEnum.BUSSINESS_SECTIONINNER_IN.getCode(), InventoryOrderBusinessEnum.BUSSINESS_SECTIONINNER_IN.getDesc(), false),
    BUSSINESS_ONELINE_IN(InventoryOrderBusinessEnum.BUSSINESS_ONELINE_IN.getCode(), InventoryOrderBusinessEnum.BUSSINESS_ONELINE_IN.getDesc(), true),
    BUSSINESS_SECONDE_OUT("SECONDE_OUT", "二线出区", true),
    BUSSINESS_DESTORY(InventoryOrderBusinessEnum.BUSSINESS_DESTORY.getCode(), InventoryOrderBusinessEnum.BUSSINESS_DESTORY.getDesc(), false),
    BUSINESS_ONELINE_REFUND(InventoryOrderBusinessEnum.BUSINESS_ONELINE_REFUND.getCode(), InventoryOrderBusinessEnum.BUSINESS_ONELINE_REFUND.getDesc(), true),
    BUSINESS_BONDED_TO_TRADE(InventoryOrderBusinessEnum.BUSINESS_BONDED_TO_TRADE.getCode(), InventoryOrderBusinessEnum.BUSINESS_BONDED_TO_TRADE.getDesc(), true),
    BUSINESS_SUBSEQUENT_TAX(InventoryOrderBusinessEnum.BUSINESS_SUBSEQUENT_TAX.getCode(), InventoryOrderBusinessEnum.BUSINESS_SUBSEQUENT_TAX.getDesc(), false),
    BUSINESS_BONDED_ONELINE_IN(InventoryOrderBusinessEnum.BUSINESS_BONDED_ONELINE_IN.getCode(), InventoryOrderBusinessEnum.BUSINESS_BONDED_ONELINE_IN.getDesc(), true),
    BUSINESS_INVENTORY_PROFIT(InventoryOrderBusinessEnum.BUSINESS_INVENTORY_PROFIT.getCode(), InventoryOrderBusinessEnum.BUSINESS_INVENTORY_PROFIT.getDesc(), false),
    BUSINESS_RANDOM_INSPECTION_DECLARATION(InventoryOrderBusinessEnum.BUSINESS_RANDOM_INSPECTION_DECLARATION.getCode(), InventoryOrderBusinessEnum.BUSINESS_RANDOM_INSPECTION_DECLARATION.getDesc(), false),
    BUSINESS_SIMPLE_PROCESSING(InventoryOrderBusinessEnum.BUSINESS_SIMPLE_PROCESSING.getCode(), InventoryOrderBusinessEnum.BUSINESS_SIMPLE_PROCESSING.getDesc(), false),
    BUSINESS_BONDED_PROCESSING_ONELINE_IN(InventoryOrderBusinessEnum.BUSINESS_BONDED_PROCESSING_ONELINE_IN.getCode(), InventoryOrderBusinessEnum.BUSINESS_BONDED_PROCESSING_ONELINE_IN.getDesc(), false),
    BUSINESS_BONDED_PROCESSING_ONELINE_OUT(InventoryOrderBusinessEnum.BUSINESS_BONDED_PROCESSING_ONELINE_OUT.getCode(), InventoryOrderBusinessEnum.BUSINESS_BONDED_PROCESSING_ONELINE_OUT.getDesc(), false);
    private String code;
    private String desc;
    private Boolean showInChecklist;

    private EndorsementBussiness(String code, String desc, Boolean showInChecklist) {
        this.code = code;
        this.desc = desc;
        this.showInChecklist = showInChecklist;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public void setShowInChecklist(Boolean showInChecklist) {
        this.showInChecklist = showInChecklist;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public Boolean getShowInChecklist() {
        return showInChecklist;
    }

    public static EndorsementBussiness getEnum(String value) {
        for (EndorsementBussiness orderStatus : EndorsementBussiness.values()) {
            if (orderStatus.getCode().equals(value)) {
                return orderStatus;
            }
        }
        return BUSSINESS_EMPTY;
    }

    public static IEType getIEType(String bussinessType) {
        EndorsementBussiness endorsementBussiness = getEnum(bussinessType);
        if (endorsementBussiness.equals(BUSSINESS_EMPTY)) {
            return null;
        } else if (endorsementBussiness.equals(BUSSINESS_SECONDE_OUT) ||
                endorsementBussiness.equals(BUSSINESS_SECTIONINNER_OUT) ||
                endorsementBussiness.equals(BUSSINESS_SECTION_OUT) ||
                endorsementBussiness.equals(BUSSINESS_DESTORY) ||
                endorsementBussiness.equals(BUSINESS_BONDED_TO_TRADE) ||
                endorsementBussiness.equals(BUSINESS_ONELINE_REFUND) ||
                endorsementBussiness.equals(BUSINESS_SUBSEQUENT_TAX) ||
                endorsementBussiness.equals(BUSINESS_RANDOM_INSPECTION_DECLARATION)
        ) {
            return IEType.EXPORT;
        } else {
            return IEType.IMPORT;
        }
    }

    public static List<EndorsementBussiness> getOneLineBusinessList() {
        return Arrays.asList(EndorsementBussiness.BUSSINESS_ONELINE_IN,
                EndorsementBussiness.BUSINESS_ONELINE_REFUND, EndorsementBussiness.BUSINESS_BONDED_ONELINE_IN,
                EndorsementBussiness.BUSINESS_BONDED_PROCESSING_ONELINE_IN, EndorsementBussiness.BUSINESS_BONDED_PROCESSING_ONELINE_OUT);
    }

    public static List<EndorsementBussiness> getAllowImportBusinessList() {
        return Arrays.asList(EndorsementBussiness.BUSSINESS_SECONDE_OUT, EndorsementBussiness.BUSSINESS_REFUND_INAREA);
    }
}
