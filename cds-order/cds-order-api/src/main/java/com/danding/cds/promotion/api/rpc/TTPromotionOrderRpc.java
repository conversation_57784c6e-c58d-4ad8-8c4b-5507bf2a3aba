package com.danding.cds.promotion.api.rpc;

import com.danding.cds.promotion.api.dto.TTPromotionExportDTO;
import com.danding.cds.promotion.api.vo.TTPromotionDetailVO;
import com.danding.cds.promotion.api.vo.TTPromotionOrderPageVO;
import com.danding.cds.promotion.api.vo.TTPromotionOrderSearch;
import com.danding.cds.v2.bean.dto.TTActiveRegistrationCreateDTO;
import com.danding.cds.v2.bean.vo.req.TTPromotionAuditReqVo;
import com.danding.logistics.api.common.response.ListVO;

import java.util.List;

/**
 * @Author: yousx
 * @Date: 2024/03/14
 * @Description:
 */
public interface TTPromotionOrderRpc {
    void createByTaotian(TTActiveRegistrationCreateDTO ttActiveRegistrationCreateDTO);

    /**
     * 分页
     * @param search
     * @return
     */
    ListVO<TTPromotionOrderPageVO> paging(TTPromotionOrderSearch search);

    void audit(TTPromotionAuditReqVo reqVo);

    TTPromotionDetailVO detail(Long id);

    List<TTPromotionExportDTO> exportItemExcel(TTPromotionOrderSearch search);

    List<String> activityTypeList();

    List<String> marketingTypeList();

    List<String> sellerNickList();

    List<String> orderSourceList();
}
