package com.danding.cds.checklist.api.enums;

public enum ChecklistBindType {
    NULL(1, "无"),
    MULTI_TICKET_ONE_CAR(2, "一车多票"),
    ONE_TICKET_ONE_CAR(3, "一票一车"),
    ONE_TICKET_MULTI_CAR(4, "一票多车");

    private Integer code;

    private String desc;

    ChecklistBindType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static ChecklistBindType getEnum(Integer code) {
        for (ChecklistBindType value : ChecklistBindType.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return NULL;
    }
}
