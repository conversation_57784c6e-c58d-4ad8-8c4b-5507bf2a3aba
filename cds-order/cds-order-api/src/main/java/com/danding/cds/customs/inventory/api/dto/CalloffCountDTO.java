package com.danding.cds.customs.inventory.api.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @Author: hou<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020/10/14 13:15
 * @Description:
 */
@Data
public class CalloffCountDTO implements Serializable {
    /**
     * 取消单总数
     */
    public int cancelCount = 0;
    /**
     * 撤销单总数
     */
    public int cancelInfoCount = 0;
    /**
     * 退货单总数
     */
    public int refundOrder = 0;
    /**
     * 退货入仓
     */
    public int refundInWarehouse = 0;

}
