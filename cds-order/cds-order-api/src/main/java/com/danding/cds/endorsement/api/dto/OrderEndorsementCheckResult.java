package com.danding.cds.endorsement.api.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class OrderEndorsementCheckResult implements Serializable {

    //任务Id
    public String jobId;
    public String startDate;
    public String endDate;
    public List<EndorsementReceiveData> endorsementReceiveDataList;
    @Data
    public static class EndorsementReceiveData implements Serializable {

        //企业内部编号
        public String snNo;
        //核注清单编号
        public String realOrderNo;
        //海关系统状态
        public String customsStatus;
        //区内名称
        public String companyName;
        //区内code
        public String companyCode;
    }


    @Data
    public static class OrderEndorsementCheckDTO implements Serializable {
        //企业内部编号
        public String sn;
        //核注清单编号
        public String realOrderNo;
        //海关系统状态
        public String customsStatus;
        //清关系统的状态
        public String status;
        //区内名称
        public String companyName;
        //区内code
        public String companyCode;
        //业务类型
        public String bussinessType;
    }


    @Data
    @HeadRowHeight(80)
    public static class AbnormalEndorsementCheckDTO implements Serializable {
        //企业内部编号
        @ColumnWidth(30)
        @ExcelProperty({"清关系统无对应核注清单编号：单一窗口有清单编号，但清关系统未查到清单编号\n" +
                "清关系统核注清单未完成：单一窗口有清单编号，但清关系统查询到的核注单未完成\n" +
                "海关系统无对应核注清单编号：清关系统查询到核注清单编号，但单一窗口未查到清单编号\n" +
                "海关系统核注清单未核扣：清关系统查询到核注清单编号，但单一窗口查询到的核注清单的核扣标志不是【已核扣】", "企业内部编号"})

        public String snNo;
        //核注清单编号
        @ColumnWidth(30)
        @ExcelProperty({"清关系统无对应核注清单编号：单一窗口有清单编号，但清关系统未查到清单编号\n" +
                "清关系统核注清单未完成：单一窗口有清单编号，但清关系统查询到的核注单未完成\n" +
                "海关系统无对应核注清单编号：清关系统查询到核注清单编号，但单一窗口未查到清单编号\n" +
                "海关系统核注清单未核扣：清关系统查询到核注清单编号，但单一窗口查询到的核注清单的核扣标志不是【已核扣】", "核注清单编号"})
        public String preOrderNo;
        //区内企业
        @ColumnWidth(30)
        @ExcelProperty({"清关系统无对应核注清单编号：单一窗口有清单编号，但清关系统未查到清单编号\n" +
                "清关系统核注清单未完成：单一窗口有清单编号，但清关系统查询到的核注单未完成\n" +
                "海关系统无对应核注清单编号：清关系统查询到核注清单编号，但单一窗口未查到清单编号\n" +
                "海关系统核注清单未核扣：清关系统查询到核注清单编号，但单一窗口查询到的核注清单的核扣标志不是【已核扣】", "区内企业"})
        public String companyName;
        //异常类型
        @ColumnWidth(40)
        @ExcelProperty({"清关系统无对应核注清单编号：单一窗口有清单编号，但清关系统未查到清单编号\n" +
                "清关系统核注清单未完成：单一窗口有清单编号，但清关系统查询到的核注单未完成\n" +
                "海关系统无对应核注清单编号：清关系统查询到核注清单编号，但单一窗口未查到清单编号\n" +
                "海关系统核注清单未核扣：清关系统查询到核注清单编号，但单一窗口查询到的核注清单的核扣标志不是【已核扣】", "异常类型"})
        public String abnormalType;

        //区内企业编号
        @ExcelIgnore
        public String companyCode;
    }
}
