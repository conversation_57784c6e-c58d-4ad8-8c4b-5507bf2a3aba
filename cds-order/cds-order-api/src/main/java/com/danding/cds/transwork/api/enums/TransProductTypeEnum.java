package com.danding.cds.transwork.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: yousx
 * @Date: 2024/03/12
 * @Description:
 */
@AllArgsConstructor
@Getter
public enum TransProductTypeEnum {

    NORMAL("NORMAL", "普通品"),
    DANGER("DANGER", "危险品"),
    TEMPERATURE("TEMPERATURE", "温控品"),
    NULL("", "");

    private final String code;
    private final String desc;

    public static TransProductTypeEnum getEnum(String code) {
        for (TransProductTypeEnum type : TransProductTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return NULL;
    }
}
