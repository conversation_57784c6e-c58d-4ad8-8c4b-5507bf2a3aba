package com.danding.cds.transwork.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: yousx
 * @Date: 2024/03/12
 * @Description:
 */
@AllArgsConstructor
@Getter
public enum TransOrderTypeEnum {

    TOB_PORT_TO_WAREHOUSE("TOB_PORT_TO_WAREHOUSE", "港到仓运输"),
    TOB_TRANSPORT("TOB_TRANSPORT", "干线运输"),
    ;

    private final String code;

    private final String desc;

    public static TransOrderTypeEnum getEnum(String code) {
        for (TransOrderTypeEnum transOrderTypeEnum : TransOrderTypeEnum.values()) {
            if (transOrderTypeEnum.getCode().equals(code)) {
                return transOrderTypeEnum;
            }
        }
        return null;
    }

    public static TransOrderTypeEnum getEnumByDesc(String desc) {
        for (TransOrderTypeEnum transOrderTypeEnum : TransOrderTypeEnum.values()) {
            if (transOrderTypeEnum.getDesc().equals(desc)) {
                return transOrderTypeEnum;
            }
        }
        return null;
    }
}
