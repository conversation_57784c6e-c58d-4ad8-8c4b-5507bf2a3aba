package com.danding.cds.customs.logistics.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel
public class LogisticsReceive implements Serializable {
    /**
     * 清关状态
     */
    @ApiModelProperty("清关状态")
    private String customsStatus;
    /**
     * 清关描述
     */
    @ApiModelProperty("清关描述")
    private String customsDetail;

    /**
     * 回执信息
     */
    @ApiModelProperty("回执信息")
    private String responseMsg;

    /**
     * 回执发生时间
     */
    @ApiModelProperty("回执发生时间")
    private Long customsTime;
    /**
     * 运单号
     */
    @ApiModelProperty("运单号")
    private String logisticsNo;

    /**
     * 发送方
     */
    @ApiModelProperty("发送方")
    private String sender;

    /**
     * 物流企业代码
     */
    @ApiModelProperty("物流企业代码")
    private String logisticsCode;

}
