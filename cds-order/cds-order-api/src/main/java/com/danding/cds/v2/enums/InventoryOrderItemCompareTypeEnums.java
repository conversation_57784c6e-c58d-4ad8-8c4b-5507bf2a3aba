package com.danding.cds.v2.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum InventoryOrderItemCompareTypeEnums {

    COMPARE_ATTACH(1, "箱单/发票/合同");

    private final Integer code;
    private final String desc;


    public static String getDesc(Integer code) {
        for (InventoryOrderItemCompareTypeEnums item : InventoryOrderItemCompareTypeEnums.values()) {
            if (item.getCode().equals(code)) {
                return item.getDesc();
            }
        }
        return "";
    }

    public static InventoryOrderItemCompareTypeEnums getEnums(Integer code) {
        for (InventoryOrderItemCompareTypeEnums item : InventoryOrderItemCompareTypeEnums.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }

}
