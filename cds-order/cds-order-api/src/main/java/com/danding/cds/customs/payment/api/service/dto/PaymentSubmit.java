package com.danding.cds.customs.payment.api.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel
public class PaymentSubmit implements Serializable {

    @ApiModelProperty("申报路径编码")
    private String routeCode;

    @ApiModelProperty("商户编号")
    private String merchantCode;

    @ApiModelProperty("付款渠道")
    private String payChannel;

    @ApiModelProperty("外部交易流水号")
    private String outTradeNo;

    @ApiModelProperty("支付交易流水号")
    private String tradePayNo;

    @ApiModelProperty("渠道单号|外部单号")
    private String outOrderNo;

    @ApiModelProperty("申报单号")
    private String declareOrderNo;

    @ApiModelProperty("订购人证件号码")
    private String buyerIdNumber;

    @ApiModelProperty("订购人姓名")
    private String buyerName;

    @ApiModelProperty("交易时间|付款时间")
    private Long tradeTime;

    @ApiModelProperty("税费")
    private BigDecimal taxAmount = BigDecimal.ZERO;

    @ApiModelProperty("折扣")
    private BigDecimal discount = BigDecimal.ZERO;

    @ApiModelProperty("商品总价")
    private BigDecimal goodsSumAmount;

    @ApiModelProperty("运费 非必填")
    private BigDecimal feeAmount = BigDecimal.ZERO;

    @ApiModelProperty("支付金额")
    private BigDecimal payAmount;

    @ApiModelProperty("申报口岸编码")
    private String customsCode = "";

    @ApiModelProperty("租户ID")
    private String tenantOuterId;

    /**
     * 支付流水号关联申报单号
     * 通联拆单申报
     */
    private List<String> relDeclareOrderNoList;

    /**
     * 支付商户号(外部)
     */
    private String payMerchantOutNo;

}
