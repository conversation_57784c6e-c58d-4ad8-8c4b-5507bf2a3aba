package com.danding.cds.invenorder.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class InventoryOrderItemParamV2 implements Serializable {
    private static final long serialVersionUID = -8240481619569672819L;

    @ApiModelProperty("主键ID")
    private Long id;
    /**
     * 清关单ID
     */
    @ApiModelProperty("清关单ID")
    private Long refInveOrderId;
    /**
     *清关单SN
     */
    @ApiModelProperty("清关单SN")
    private String  refInveOrderSn;
    /**
     * 是否是新的(0:旧,1:新)
     */
    @ApiModelProperty("是否是新的(old:旧,new:新)")
    private String oldOrNew;
    /**
     * SKU
     */
    @ApiModelProperty("sku")
    private String skuId;

    /**
     * 原始料号
     */
    @ApiModelProperty("原始料号")
    private String originProductId;

    /**
     * 商品料号
     */
    @ApiModelProperty("商品料号")
    private String productId;
    /**
     * 账册项号
     */
    @ApiModelProperty("商品序号")
    private String goodsSeqNo;

    @ApiModelProperty("商品名称")
    private String goodsName;

    @ApiModelProperty("商品备案名称")
    private String recordProductName;
    /**
     * HS代码
     */
    @ApiModelProperty("HS代码")
    private String hsCode;
    /**
     * 计划申报数量
     */
    @ApiModelProperty("计划申报数量")
    private BigDecimal planDeclareQty;
    /**
     * 出库单号
     */
    @ApiModelProperty("出库单号，多个用/分开")
    private String outBoundNo;
    /**
     * 实际理货数量
     */
    @ApiModelProperty("实际理货数量")
    private BigDecimal actualTallyQty;
    /**
     * 申报单位数量
     */
    @ApiModelProperty("申报单位数量")
    private Double declareUnitQfy;

    @ApiModelProperty("申报单价")
    private Double declarePrice; //add

    /**
     * json 属性
     */
    @ApiModelProperty("计量单位")
    private String unit;
    @ApiModelProperty("第一单位")
    private String firstUnit;
    @ApiModelProperty("第一单位数量")
    private Double firstUnitQfy;
    @ApiModelProperty("第二单位")
    private String secondUnit;
    @ApiModelProperty("第二单位数量")
    private Double secondUnitQfy;
    @ApiModelProperty("毛重")
    private Double grossWeight;
    @ApiModelProperty("净重")
    private Double netweight;
    @ApiModelProperty("申报要素")
    private String declareFactor;
    @ApiModelProperty("原产国")
    private String originCountry;
    @ApiModelProperty("商品条码")
    private String goodsBar;
    @ApiModelProperty("生产企业")
    private String productCompany;
    @ApiModelProperty("国检备案号")
    private String countryRecordNo;

    //增加的字段
    @ApiModelProperty("报关单商品序号")
    private  String declareCustomsGoodsSeqNo;//add
    @ApiModelProperty("规格型号")//add
    private String goodsModel;
    @ApiModelProperty("最终目的国")
    private String destinationCountry;//add
    @ApiModelProperty("币制")//add
    private String currency;
    @ApiModelProperty("免征方式")//add
    private String avoidTaxMethod = "3";
    @ApiModelProperty("单号版本号")//add
    private String orderVersion;
    @ApiModelProperty("关联核注单id")
    private Long refEndorsementId;
    @ApiModelProperty("关联核注单号")
    private String refEndorsementSn;
    @ApiModelProperty("物种证明附件名称")
    private String speciesCertificateAttachmentName;
    @ApiModelProperty("物种证明附件URL")
    private String speciesCertificateAttachmentUrl;
    @ApiModelProperty("生化品标志  1是  0否")
    private String dangerousFlag;
    /**
     * 申报表序号
     */
    private Integer declareFormItemSeqNo;
    /**
     * 来源标识
     */
    @ApiModelProperty("来源标识")
    private String goodsSource;
    @ApiModelProperty("总净重")
    private BigDecimal totalNetWeight;
    @ApiModelProperty("总毛重")
    private BigDecimal totalGrossWeight;
}
