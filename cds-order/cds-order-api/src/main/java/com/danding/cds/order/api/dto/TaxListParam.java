package com.danding.cds.order.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: Raymond
 * @Date: 2020/11/4 17:47
 * @Description:
 */
@Data
@ApiModel
public class TaxListParam implements Serializable {
    @ApiModelProperty("商品列表")
    List<OrderSimpleSubmitItem> itemList;
    @ApiModelProperty("运费")
    private BigDecimal feeAmount;
    @ApiModelProperty("仓库编码")
    private String wareCode;
    @ApiModelProperty("商品计税规格")
    private BigDecimal goodsCustomsHsPar;
}
