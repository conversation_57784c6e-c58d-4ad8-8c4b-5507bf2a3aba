package com.danding.cds.customs.refund.api.enums;

import java.io.Serializable;

public class RefundOrderEnum implements Serializable {
    public enum STATUS_ENUM {
        STATUS_INIT(0, "初始化"),
        STATUS_WAIT(1, "退货申报中"),
        STATUS_COMPLETE(2, "退货完成"),
        STATUS_CLOSE(3, "退货关闭"),
        STATUS_INVENTORY_WAIT_CREATE(4, "清关待创建"),
        STATUS_ENDORSEMENT_WAIT_CREATE(5, "核注待创建"),
        STATUS_ENDORSEMENT_WAIT_FINISH(6, "核注待完成"),
        STATUS_FAIL(7, "申报失败");
        private Integer value;
        private String desc;

        private STATUS_ENUM(Integer value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        public void setValue(Integer value) {
            this.value = value;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }

        public Integer getValue() {
            return value;
        }

        public String getDesc() {
            return desc;
        }

        public static STATUS_ENUM getEnum(Integer value) {
            for (RefundOrderEnum.STATUS_ENUM status : RefundOrderEnum.STATUS_ENUM.values()) {
                if (status.getValue().equals(value)) {
                    return status;
                }
            }
            return null;
        }
    }

    public enum CHECK_STATUS_ENUM {
        CHECK_STATUS_INIT("INT", "初始化"),
        CHECK_STATUS_WAITING("DECALRING_AUDITING", "申报中", ""),
        CHECK_STATUS_WAIT("AUDITING", "待总署审核"), CHECK_STATUS_PASS("AUDIT_PASS", "审核通过"),
        CHECK_STATUS_REJECT("AUDIT_REJECT", "总署驳回");//,
        // CHECK_STATUS_FAIL("FAIL","申报失败");
        private String value;
        private String desc;
        private String corporationDesc;

        private CHECK_STATUS_ENUM(String value, String desc) {
            this.value = value;
            this.desc = desc;
            this.corporationDesc = desc;
        }

        private CHECK_STATUS_ENUM(String value, String desc, String corporationDesc) {
            this.value = value;
            this.desc = desc;
            this.corporationDesc = corporationDesc;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }

        public String getValue() {
            return value;
        }

        public String getDesc() {
            return desc;
        }

        public String getCorporationDesc() {
            return corporationDesc;
        }

        public static CHECK_STATUS_ENUM getEnum(String value) {
            for (RefundOrderEnum.CHECK_STATUS_ENUM status : RefundOrderEnum.CHECK_STATUS_ENUM.values()) {
                if (status.getValue().equals(value)) {
                    return status;
                }
            }
            return null;
        }
    }

    public enum refundOrder {
        /**
         * 取消
         */
        REFUND0(RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_INIT, RefundOrderEnum.STATUS_ENUM.STATUS_INIT, RefundOrderAction.REFUND_CANCEL),
        REFUND1(RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_REJECT, RefundOrderEnum.STATUS_ENUM.STATUS_WAIT, RefundOrderAction.REFUND_CANCEL),
        REFUND2(RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_WAIT, RefundOrderEnum.STATUS_ENUM.STATUS_WAIT, RefundOrderAction.REFUND_CANCEL),
        REFUND3(RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_WAITING, RefundOrderEnum.STATUS_ENUM.STATUS_WAIT, RefundOrderAction.REFUND_CANCEL),
        // REFUND4(RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_FAIL, RefundOrderEnum.STATUS_ENUM.STATUS_WAIT,RefundOrderAction.REFUND_CANCEL),

        /**
         * 回执
         */
        REFUND5(RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_WAIT, RefundOrderEnum.STATUS_ENUM.STATUS_WAIT, RefundOrderAction.REFUND_CALLBACK),
        REFUND6(RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_WAITING, RefundOrderEnum.STATUS_ENUM.STATUS_WAIT, RefundOrderAction.REFUND_CALLBACK),
        REFUND7(RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_WAIT, RefundOrderEnum.STATUS_ENUM.STATUS_WAIT, RefundOrderAction.REFUND_CALLBACK),
        REFUND11(RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_REJECT, STATUS_ENUM.STATUS_FAIL, RefundOrderAction.REFUND_CALLBACK),
        //兼容上个线程还在申报中未将init变成申报中的事物提交，海关回执就回来了不处理报错的的情况
        REFUND13(RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_INIT, STATUS_ENUM.STATUS_INIT, RefundOrderAction.REFUND_CALLBACK),


        /**
         * 申报
         */
        REFUND8(RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_INIT, RefundOrderEnum.STATUS_ENUM.STATUS_INIT, RefundOrderAction.REFUND_APPALY),
        REFUND9(RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_REJECT, RefundOrderEnum.STATUS_ENUM.STATUS_WAIT, RefundOrderAction.REFUND_APPALY),
        //申报中允许重推了
        REFUND10(RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_WAITING, RefundOrderEnum.STATUS_ENUM.STATUS_WAIT, RefundOrderAction.REFUND_APPALY),
        // REFUND10(RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_FAIL, RefundOrderEnum.STATUS_ENUM.STATUS_WAIT, RefundOrderAction.REFUND_APPALY);
        REFUND12(RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_REJECT, STATUS_ENUM.STATUS_FAIL, RefundOrderAction.REFUND_APPALY);


        private STATUS_ENUM statusEnum;
        private CHECK_STATUS_ENUM checkStatusEnum;
        private RefundOrderAction operation;

        private refundOrder(CHECK_STATUS_ENUM checkStatusEnum, STATUS_ENUM statusEnum, RefundOrderAction operation) {
            this.statusEnum = statusEnum;
            this.checkStatusEnum = checkStatusEnum;
            this.operation = operation;
        }

        public static boolean contains(String check_status_enum, Integer status_enum, RefundOrderAction operation) {
            refundOrder[] arrays = refundOrder.values();
            for (int i = 0; i < arrays.length; i++) {
                if (arrays[i].operation.equals(operation)) {
                    if (arrays[i].checkStatusEnum.getValue().equals(check_status_enum) &&
                            arrays[i].statusEnum.getValue().equals(status_enum)) {
                        return true;
                    }
                }
            }
            return false;
        }
    }
}