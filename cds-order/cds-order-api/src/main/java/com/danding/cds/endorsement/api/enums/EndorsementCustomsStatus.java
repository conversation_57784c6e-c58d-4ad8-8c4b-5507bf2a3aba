package com.danding.cds.endorsement.api.enums;

public enum  EndorsementCustomsStatus {
    //自定义状态
    NULL("", "空"),
    DECLARE("DECLARE", "发送海关成功"),
    ERROR("ERROR", "申报异常"),
    //核注清单回执状态
    INV201_YIHEKOU("INV201_1", "通过（已核扣）"),
    INV201_RENGONG("INV201_2", "转人工"),
    INV201_TUIDAN("INV201_3", "退单"),
    INV201_YUKEKOU("INV201_4", "预核扣"),
    INV201_WEIHEKOU("INV201_5", "通过（未核扣）"),
    INV201_RUKU_SUCCESS("INV201_Y", "入库成功"),
    INV201_RUKU_FAIL("INV201_Z", "入库失败"),
    INV201_DELETED_NOT_VERIFIED("INV201_6_0", "删除（未核扣）"),
    INV201_DELETED_REVERSE_VERIFIED("INV201_6_4", "删除（反核扣）"),
    ;

    private String code;

    private String desc;

    EndorsementCustomsStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static EndorsementCustomsStatus getEnum(String code){
        for (EndorsementCustomsStatus value : EndorsementCustomsStatus.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return NULL;
    }
}
