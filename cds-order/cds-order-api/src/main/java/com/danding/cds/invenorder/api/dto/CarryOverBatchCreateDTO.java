package com.danding.cds.invenorder.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 分区结转创建明细
 * @date 2023/4/22 12:08
 */
@Data
public class CarryOverBatchCreateDTO implements Serializable {
    /**
     * WMS实体仓编码
     */
    private String warehouseCode;

    /**
     * 出入库单号
     */
    private String inOutOrderNo;

    /**
     * 是否自动创建核注
     * 0 否
     * 1 是
     */
    private Integer autoCreateEndorsement;

    private List<CarryOverCreateDTO> carryOverCreateDTOList;
}
