package com.danding.cds.customs.inventory.api.enums;

public enum CustomsCancelOrderStatus {

    PROCESSING(1,"处理中"),
    PROCESSING_COMPLETE(2,"处理完成");
    private Integer code;
    private String desc;
    private CustomsCancelOrderStatus(Integer code, String desc)
    {
        this.code = code;
        this.desc = desc;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }
    public String getDesc() {
        return desc;
    }
    public static CustomsCancelOrderStatus getEnum(String value){
        for (CustomsCancelOrderStatus step : CustomsCancelOrderStatus.values()) {
            if (step.getCode().equals(value)){
                return step;
            }
        }
        return PROCESSING;
    }
}
