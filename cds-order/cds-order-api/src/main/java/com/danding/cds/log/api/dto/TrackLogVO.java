package com.danding.cds.log.api.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Create 2021/6/8  16:10
 * @Describe
 **/
@Data
@Accessors(chain = true)
public class TrackLogVO implements Serializable {
    private Long id;
    private String declareOrderNo;
    private String oldStatusStr;
    private String newStatusStr;
    //操作描述
    private String operateDesStr;
    //日志描述
    private String logDes;
    private String content;
    private Integer hasXmlMessage = 0;
    private Date createTime;
    private Date updateTime;
    private String createByName;
    private Boolean deleted = false;
    private String orderTypeStr;
    private String inventoryNo;
    private String auditStatus;
}
