package com.danding.cds.suningFP.api.dto;

import com.danding.logistics.api.common.page.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class SuNingFPLoadSearch extends Page implements Serializable {
    private static final long serialVersionUID = -1956310765495140518L;

    @ApiModelProperty("装载单号")
    private String loadOrderNo;

    @ApiModelProperty("车牌号")
    private String licensePlate;

    @ApiModelProperty("创建时间From")
    private Long createFrom;

    @ApiModelProperty("创建时间To")
    private Long createTo;

    @ApiModelProperty("修改时间From")
    private Long updateFrom;

    @ApiModelProperty("修改时间To")
    private Long updateTo;
}
