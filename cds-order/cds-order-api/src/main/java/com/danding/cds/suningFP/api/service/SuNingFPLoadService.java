package com.danding.cds.suningFP.api.service;

import com.danding.cds.suningFP.api.dto.*;
import com.danding.logistics.api.common.response.ListVO;

public interface SuNingFPLoadService {
    /**
     * 苏宁装载单分页查询
     * @param search
     * @return
     */
    ListVO<SuNingFPLoadInfoDTO> paging(SuNingFPLoadSearch search);

    /**
     * 苏宁装载单明细分页查询
     * @param search
     * @return
     */
    ListVO<SuNingFPLoadInfoItemDTO> detailPaging(SuNingFPLoadDetailSearch search);

    /**
     * 创建装载单
     * @param submit
     * @return
     */
    void submit(SuNingFPLoadInfoSubmit submit);

    /**
     * 根据装载单号查询
     * @param loadOrderNo
     * @return
     */
    SuNingFPLoadInfoDTO findByLoadOrderNo(String loadOrderNo);

    /**
     * 根据id查询
     * @param id
     * @return
     */
    SuNingFPLoadInfoDTO findById(Long id);

    /**
     * 更新审核状态
     * @param id
     * @param auditStatus
     */
    void updateAuditStatus(Long id,Integer auditStatus);
}
