package com.danding.cds.suningFP.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public class SuNingFPLoadInfoSubmit implements Serializable {
    private Long id;

    private String serialNo;

    private String loadOrderNo;

    private Integer totalCount;

    private BigDecimal totalWeight;

    private Integer totalToryNum;

    private String licensePlate;

    private List<SuNingFPLoadInfoItemSubmit> infoItemSubmitList = new ArrayList<>();
}
