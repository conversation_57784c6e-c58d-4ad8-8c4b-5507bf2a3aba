package com.danding.cds.order.api.dto;

public enum OrderExceptionEnum {
    ERROR_EXCEPTION0(0,"异常1"),
    ERROR_EXCEPTION1(1,"异常1"),
    ERROR_EXCEPTION2(2,"异常1");
    private Integer status;
    private String desc;
    private OrderExceptionEnum(Integer status,String desc)
    {
        this.status = status;
        this.desc = desc;
    }
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}