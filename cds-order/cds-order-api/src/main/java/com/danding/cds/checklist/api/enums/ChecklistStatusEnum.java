package com.danding.cds.checklist.api.enums;

public enum ChecklistStatusEnum {
    // 新增-【已创建】-录入运单号-【已录入】-生成核注清单-推送核注清单-推送核放单-【申报中】-【已审批】/【异常】/【申报失败】-【已完成（过卡）】-作废-【作废申报中】-【已作废】
    NULL("","空"),
    CREATED("CREATED","已创建"),

    DECALRING("DECALRING","申报中"),
    AUDITING("AUDITING","已审批"),
//    AUDIT_PASS("AUDIT_PASS","已完成"),

    FINISH("FINISH","已完成"),
    EXCEPTION("EXCEPTION","申报失败"),

    DELETING("DELETING","作废申报中"),
    DELETE("DELETE","已作废"),
    STORING("STORING","暂存中"),
    STORED("STORED","已暂存");

    private String code;

    private String desc;

    ChecklistStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ChecklistStatusEnum getEnum(String code){
        for (ChecklistStatusEnum value : ChecklistStatusEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return NULL;
    }
}
