package com.danding.cds.handoverOrder.api.enums;

/**
 * <AUTHOR>
 * @description 交接单表体是否关联出库单状态
 * @date 2021/12/20
 */
public enum HandoverDetailOutBoundStatus {

    NO(0, "否"),
    YES(1, "是");

    private Integer value;

    private String desc;

    HandoverDetailOutBoundStatus(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static HandoverDetailOutBoundStatus getEnum(Integer value) {
        for (HandoverDetailOutBoundStatus exportOrderStatus : HandoverDetailOutBoundStatus.values()) {
            if (exportOrderStatus.value == value) {
                return exportOrderStatus;
            }
        }
        return NO;
    }
}
