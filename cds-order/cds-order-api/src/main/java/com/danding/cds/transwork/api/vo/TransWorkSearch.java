package com.danding.cds.transwork.api.vo;

import com.danding.logistics.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * @Author: yousx
 * @Date: 2024/03/12
 * @Description:
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
public class TransWorkSearch extends Page {

    @ApiModelProperty(value = "运输单作业号")
    private List<String> transNos;

    @ApiModelProperty(value = "作业单类型")
    private String transType;

    @ApiModelProperty(value = "创建时间")
    private Date createTimeFrom;

    @ApiModelProperty(value = "创建时间")
    private Date createTimeTo;

    @ApiModelProperty(value = "外部单号")
    private List<String> outOrderNos;

    @ApiModelProperty(value = "车牌号")
    private List<String> truckNos;

    @ApiModelProperty(value = "运次编号")
    private String shipmentNo;

    @ApiModelProperty(value = "货主编码")
    private List<String> ownerCode;

    @ApiModelProperty(value = "仓库编码")
    private List<String> warehouseCode;

    @ApiModelProperty(value = "实际到达时间")
    private Date actArrTimeFrom;

    @ApiModelProperty(value = "实际到达时间")
    private Date actArrTimeTo;

    @ApiModelProperty(value = "实际发车时间")
    private Date actDepTimeFrom;

    @ApiModelProperty(value = "实际发车时间")
    private Date actDepTimeTo;

    @ApiModelProperty(value = "实际签收时间")
    private Date actSignTimeFrom;

    @ApiModelProperty(value = "实际签收时间")
    private Date actSignTimeTo;

    @ApiModelProperty(value = "完成时间")
    private Date finishTimeFrom;

    @ApiModelProperty(value = "完成时间")
    private Date finishTimeTo;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "车辆资源编码")
    private String truckResourceNo;
}
