package com.danding.cds.v2.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum PassGateStatusEnums {

    NULL(null, ""),
    NO(0, "未过卡"),
    YES(1, "已过卡");

    private final Integer code;
    private final String desc;

    public static PassGateStatusEnums getEnums(Integer code) {
        for (PassGateStatusEnums e : PassGateStatusEnums.values()) {
            if (Objects.equals(e.getCode(), code)) {
                return e;
            }
        }
        return NULL;
    }
}
