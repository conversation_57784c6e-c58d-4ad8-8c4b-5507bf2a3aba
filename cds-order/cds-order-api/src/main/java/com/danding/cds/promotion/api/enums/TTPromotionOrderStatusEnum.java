package com.danding.cds.promotion.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: yousx
 * @Date: 2024/03/14
 * @Description:
 */
@AllArgsConstructor
@Getter
public enum TTPromotionOrderStatusEnum {

    PENDING("100", "待报备"),
    PASS("200", "海关审核通过"),
    REJECT("300", "海关审核不通过"),
    WITHDRAW("400", "报备撤回"),
    LESS_THAN_2DAYS("500", "报备截止低于48h"),
    ;

    private final String code;
    private final String desc;

    public static TTPromotionOrderStatusEnum getEnum(String code) {
        for (TTPromotionOrderStatusEnum statusEnum : TTPromotionOrderStatusEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }
}
