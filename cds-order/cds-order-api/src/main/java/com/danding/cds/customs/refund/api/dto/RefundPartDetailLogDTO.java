package com.danding.cds.customs.refund.api.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @Author: yousx
 * @Date: 2023/12/05
 * @Description:
 */
@Data
public class RefundPartDetailLogDTO implements Serializable {

    private Long itemId;

    private String itemNo;

    /**
     * 原逆向申报数量
     */
    private Integer oldRefundDeclareQty;

    /**
     * 更新的逆向申报数量
     */
    private Integer newRefundDeclareQty;
}
