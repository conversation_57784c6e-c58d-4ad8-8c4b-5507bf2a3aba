package com.danding.cds.customs.inventory.api.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.google.common.base.Objects;
import lombok.Getter;

@Getter
public enum InventoryOrderType {

    //取消单
    CANCEL_ORDER(1, "取消单"),

    //退货单
    RETURNED_PURCHASE_ORDER(2, "退货单"),

    //撤单
    CANCEL_AN_ORDER(3, "撤单"),

    //清单
    INVENTORY_ORDER(4,"清单"),

    //申报单
    DECLARE_ORDER(5,"申报单"),

    //订单
    ORDER(6,"订单"),

    //税单
    TAX_ORDER(7,"税单");




    InventoryOrderType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * @EnumValue 标记数据库存的值是code
     */
    @EnumValue
    private final int code;

    /**
     * @JsonValue 可以对外使用的描述 标记响应json值
     */
    @JsonValue
    private final String desc;


    public static InventoryOrderType fromInt(Integer code) {
        for (InventoryOrderType from : InventoryOrderType.values()) {
            if (Objects.equal(from.code, code)) {
                return from;
            }
        }
        throw new IllegalArgumentException("unknown BondedType.code: " + code);
    }
}
