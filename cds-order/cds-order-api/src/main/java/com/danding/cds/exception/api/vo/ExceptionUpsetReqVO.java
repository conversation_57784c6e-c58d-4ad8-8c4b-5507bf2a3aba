package com.danding.cds.exception.api.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description 查询请求对象
 * @date 2022/5/9
 */
@Data
public class ExceptionUpsetReqVO implements Serializable {

    private String id;

    /**
     * 异常名称
     */
    private String exceptionName;


    /**
     * 异常分类 1.业务异常2.系统异常3.海关异常
     */
    private Integer exceptionClassify;

    /**
     * 异常描述
     */
    private String exceptionDescribe;

    /**
     * 处理建议
     */
    private String handlePropose;

    /**
     * 申报单能否重推：0否;1是
     */
    private Integer repushEnable;
    /**
     * 异常用途标签
     */
    private List<Integer> useTagList;
}
