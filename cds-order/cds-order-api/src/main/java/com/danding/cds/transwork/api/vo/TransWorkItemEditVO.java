package com.danding.cds.transwork.api.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: yousx
 * @Date: 2024/03/12
 * @Description:
 */
@Data
public class TransWorkItemEditVO implements Serializable {

    private List<Detail> items;

    @Data
    public static class Detail implements Serializable {

        private Long id;

        /**
         * 实际发货数量
         */
        @ApiModelProperty(value = "实际发货数量")
        private Long actDeliveryQty;


        /**
         * 实际签收数量
         */
        @ApiModelProperty(value = "实际签收数量")
        private Long actSignQty;
    }
}
