package com.danding.cds.customs.inventory.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 清单商品项快照
 */
@Data
public class CustomsInventoryItemExtra implements Serializable {

    /**
     * 预估税费
     */
    private BigDecimal taxPrice;
    /**
     * 商品序号
     */
    private String goodsSeqNo;

    /**
     * 商品料号(通过统一料号获取的最终料号或者是上游指定的海关备案料号)
     */
    private String productId;

    /**
     * 统一料号(保存上游下发的统一料号)
     */
    private String unifiedProductId;

    /**
     * HS编码
     */
    private String hsCode;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 申报币制
     */
    private String currCode;

    /**
     * 申报单价金额
     */
    private BigDecimal declarePrice;

    /**
     * 规格型号
     */
    private String goodsModel;

    /**
     * 原产国
     */
    private String originCountry;

    /**
     * 指定原产国
     */
    private String assignOriginCountry;

    /**
     * 申报单位
     */
    private String goodsUnit;

    /**
     * 第一计量单位
     */
    private String firstUnit;

    /**
     * 第二计量单位
     */
    private String secondUnit;

    /**
     * 条码
     */
    private String barCode;
    /**
     * 净重
     */
    private BigDecimal netWeight;

    /**
     * 毛重
     */
    private BigDecimal grossWeight;

    /**
     * 品牌（中文）
     */
    private String brand;

    /**
     * 品牌（英文）
     */
    private String brandEn;

    /**
     * 租户
     */
    private String lesseeNo;

    /**
     * 增值税率
     */
    private Integer vatRate;

    /**
     * 消费税率
     */
    private Integer taxRate;
    /**
     * 成分
     */
    private String composition;

    /**
     * 海关申报要素
     */
    private String hgsbys;

    /**
     * 功能
     */
    private String recordFunction;

    /**
     * 用途
     */
    private String recordUsage;

    /**
     * 法定第一计量单位数量
     */
    private BigDecimal firstUnitAmount;

    /**
     * 法定第二计量单位数量
     */
    private BigDecimal secondUnitAmount;
    /**
     * 商品备案号（天津）
     */
    private String goodsRegNo;
    /**
     * 进口入区申报号（天津）
     */
    private String declIINo;
    /**
     * 出区进口商品流水号（天津）
     */
    private String ioGoodsSerialNo;
    /**
     * 原产国代码（天津国检）
     */
    private String originCiqCountry;
}
