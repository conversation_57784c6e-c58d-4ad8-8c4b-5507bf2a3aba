package com.danding.cds.invenorder.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel("接收清关单分录明细")
public class InventoryOrderItemsParam implements Serializable {
    @ApiModelProperty("清关单ID")
    private Long invenOrderId;
    @ApiModelProperty("分录明细")
    private List<InventoryOrderItemParam> listOrderItems;
}
