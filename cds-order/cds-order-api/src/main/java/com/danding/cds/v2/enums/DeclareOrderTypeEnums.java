package com.danding.cds.v2.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 申报单类型
 */
@Getter
@AllArgsConstructor
public enum DeclareOrderTypeEnums {

    BYTE_DANCE_WMS("byteDance_wms", "字节wms", DeclareOrderTagEnums.BYTE_DANCE_WMS),
    JIEZHOU_ENCRYPT("jieZhou_encrypt", "淘分销加密", DeclareOrderTagEnums.JIEZHOU_ENCRYPT),
    CAINIAO_WMS("cainiao_wms", "菜鸟WMS", DeclareOrderTagEnums.CAINIAO_WMS);

    private final String code;
    private final String desc;
    private final DeclareOrderTagEnums orderTag;
}
