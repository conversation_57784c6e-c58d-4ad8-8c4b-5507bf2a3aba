package com.danding.cds.transwork.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel
public class TransWorkOrderLoadInfoDTO implements Serializable {


    /**
     * 货品类型（NORMAL=普通品、DANGER=危险品、TEMPERATURE=温控品）
     */
    @ApiModelProperty(value = "货品类型（NORMAL=普通品、DANGER=危险品、TEMPERATURE=温控品）")
    private String productTypeDesc;

    private String productType;

    /**
     * 货品描述
     */
    @ApiModelProperty(value = "货品描述")
    private String productDesc;

    /**
     * 装箱方式（FCL=整箱、LCL=拼箱）
     */
    @ApiModelProperty(value = "装箱方式（FCL=整箱、LCL=拼箱）")
    private String loadingTypeDesc;

    private String loadingType;

    /**
     * 温度
     */
    @ApiModelProperty(value = "温度")
    private String temperature;

    /**
     * 将柜型与数量，以组合形式生成，例如：40GP-1。多个组合以“，”分隔
     */
    @ApiModelProperty(value = "将柜型与数量，以组合形式生成，例如：40GP-1。多个组合以“，”分隔")
    private String containerTypes;

}