package com.danding.cds.checklist.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel
public class ChecklistSubmit implements Serializable {
    private static final long serialVersionUID = 1595681388118223069L;

    @ApiModelProperty("ID 更新时必填")
    private Long id;

    @ApiModelProperty("预录入核放单号")
    private String orderNo;

    @ApiModelProperty("核放单号")
    private String realOrderNo;

    @ApiModelProperty("车牌号")
    private String licensePlate;

    @ApiModelProperty("车辆自重")
    private BigDecimal carWeight;

    @ApiModelProperty("车架号")
    private String licenseFrame;

    @ApiModelProperty("车架重")
    private BigDecimal frameWeight;

    @ApiModelProperty("总重量")
    private BigDecimal totalWeight;

    @ApiModelProperty("核放单类型")
    private Integer type;

    @ApiModelProperty("绑定类型")
    private Integer bindType;

    @ApiModelProperty("出入区标志 2入 1出")
    private Integer ieFlag;

    @ApiModelProperty("清关企业ID")
    private Long declareCompanyID;

    @ApiModelProperty("申请人")
    private String applicant;

    @ApiModelProperty("报关单号")
    private String declareOrderNo;

    @ApiModelProperty("IC卡号")
    private String vehicleIcNo;

    @ApiModelProperty("核注单类型")
    private String endorsementType;

    @ApiModelProperty("核注单ID")
    private List<Long> endorsementIdList;

    @ApiModelProperty("货物总毛重")
    private BigDecimal totalGrossWeight;

    @ApiModelProperty("货物总净重")
    private BigDecimal totalNetWeight;

    @ApiModelProperty("空车照片/磅单URL")
    private String emptyCarPicUrl;

    @ApiModelProperty("空车照片/磅单照片")
    private String emptyCarPicName;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("集装箱箱型")
    private String containerType;

    @ApiModelProperty("集装箱重（千克）")
    private BigDecimal containerWt;
}
