package com.danding.cds.order.api.enums;

public enum MapStatusEnums {
    PENDING(0,"待处理"),
    FINISH(1,"映射完成");


    private Integer value;

    private String desc;

    MapStatusEnums(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static MapStatusEnums getEnum(Integer value){
        for (MapStatusEnums orderChannel : MapStatusEnums.values()) {
            if (orderChannel.getValue().equals(value)){
                return orderChannel;
            }
        }
        return PENDING;
    }
}
