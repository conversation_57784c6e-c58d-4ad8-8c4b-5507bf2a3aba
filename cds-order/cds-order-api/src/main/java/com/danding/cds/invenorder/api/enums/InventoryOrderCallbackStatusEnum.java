package com.danding.cds.invenorder.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum InventoryOrderCallbackStatusEnum {


    EMPTY(0, ""),
    WAIT(1, "待回传"),
    SUCCESS(2, "成功"),
    FAILED(3, "失败");

    private Integer code;
    private String desc;

    public static InventoryOrderCallbackStatusEnum getEnum(Integer value) {
        for (InventoryOrderCallbackStatusEnum status : InventoryOrderCallbackStatusEnum.values()) {
            if (status.getCode().equals(value)) {
                return status;
            }
        }
        return EMPTY;
    }
}
