package com.danding.cds.v2.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 关企传输 报文类型枚举
 */
@AllArgsConstructor
@Getter
public enum GQTransmitMsgTypeEnums {

    CRKS0001("CRKS0001", "出入库申请", GQTransmitBizTypeEnums.IN_OUT_WAREHOUSE),
    KNZH0001("KNZH0001", "库内转化申请", GQTransmitBizTypeEnums.WAREHOUSE_IN_TRANSFORM),
    YKSQ0001("YKSQ0001", "移库申请", null),
    TZSQ0001("TZSQ0001", "异常调账申请", null),
    PKJH0001("PKJH0001", "盘库计划", null),

    CRKS0002("CRKS0002", "出入库申请回执", GQTransmitBizTypeEnums.IN_OUT_WAREHOUSE),
    KNZH0002("KNZH0002", "库内转化申请回执", GQTransmitBizTypeEnums.WAREHOUSE_IN_TRANSFORM),
    YKSQ0002("YKSQ0002", "移库申请回执", null),
    TZSQ0002("TZSQ0002", "异常调账申请回执", null),
    PKJH0002("PKJH0002", "盘库计划回执", null),
    ;

    private final String code;

    private final String desc;

    private final GQTransmitBizTypeEnums bizType;

    public static GQTransmitMsgTypeEnums getByCode(String code) {
        for (GQTransmitMsgTypeEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }
}
