package com.danding.cds.v2.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BizDeclareFormCustomsStatusEnums {
    //1-正常执行、2-暂停、3-结案
    NORMAL("1", "正常执行"),
    PAUSE("2", "暂停"),
    FINISH("3", "结案");

    private final String code;
    private final String desc;

    public static BizDeclareFormCustomsStatusEnums getEnums(String code) {
        for (BizDeclareFormCustomsStatusEnums enums : BizDeclareFormCustomsStatusEnums.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }
}
