package com.danding.cds.customs.inventory.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class SupervisionNodeCommonDTOOld implements Serializable {
    @ApiModelProperty(value = "全局单号")
    private String globalNo;

    @ApiModelProperty(value = "所属系统")
    private String businessSystem;


    @ApiModelProperty(value = "所属节点  交接单：ccs_handover_order 核注出区：ccs_clear_order")
    private String businessNode;

    //节点状态 出区  1出区    已交接1

    @ApiModelProperty(value = "节点状态")
    private String node;

    @ApiModelProperty(value = "节点时间")
    private Long nodeTime;

    @ApiModelProperty(value = "租户ID")
    private Long tenantId;

}
