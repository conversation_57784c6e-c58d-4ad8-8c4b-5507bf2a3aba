package com.danding.cds.v2.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 通过位运算计算tag (清关单标记)
 * @date 2022/8/10 09:26
 */
@Getter
@AllArgsConstructor
public enum InventoryOrderTagEnums {
    NULL(2 >> 2, "无"),
    @Deprecated
    ENDORSEMENT_NOT_DELETED(2 >> 1, "核注未删除"),
    TRANSIT_MASTER_ORDER(2, "中转主", "中转主"),
    @Deprecated
    TRANSIT_IN_NOT_GENERATE(2 << 1, "调入未生成"),
    @Deprecated
    TRANSIT_OUT_NOT_GENERATE(2 << 2, "调出未生成"),
    TRANSIT_IN_ORDER(2 << 3, "中转调入", "中转入"),
    TRANSIT_OUT_ORDER(2 << 4, "中转调出", "中转出"),
    PLEDGE_OWNER(2 << 5, "质押货主", "质押"),
    // 自动分区结转单
    AUTO_PART_CARRYOVER(2 << 6, "分区自动结转", "分区"),
    // 关仓单要加的标记
    RELATE(2 << 7, "关联分区结转", "关联分区"),
    // 2B的结转单
    TO_B_PART_CARRYOVER(2 << 8, "B单分区结转", "B单分区"),
    CW_INVENTORY(2 << 9, "CW清关", "CW"),
    ASSOCIATE_TRANSFER(2 << 10, "关联调拨", "关联"),
    DAITA_WAREHOUSE_TRANSFER(2 << 11, "代塔仓间", "仓间"),
    MASTER_ORDER(2 << 12, "主单", "主单"),
    SUB_ORDER(2 << 13, "子单", "子单"),
    SAME_USER_TRANSFER(2 << 14, "同用户调拨", "同用户"),
    AUTO_TRANSFER(2 << 15, "全自动调拨", "自动"),
    SAME_BOOK_NO_TRANSFER(2 << 16, "同账册调拨", "同账册调拨"),
    WAITING_TAKE_ORDER(2 << 17, "待接单", "待接单"),
    TAKEN_ORDER(2 << 18, "已接单", "接单"),
    URGENT_PROCESS(2 << 19, "加急", "加急"),
    ACROSS_CUSTOMS_AREA(2 << 20, "跨关区", "跨关区")
    ;

    private final Integer code;

    private final String desc;

    private String shortName;

    InventoryOrderTagEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static List<Integer> getOrderTags(Integer useTag) {
        List<Integer> orderTagList = new ArrayList<>();
        if (useTag == null || useTag == 0) {
            return orderTagList;
        }
        for (InventoryOrderTagEnums value : InventoryOrderTagEnums.values()) {
            // 判断下orderTag是否包含有数据
            Integer code = value.getCode();
            // 排除下没有用途的占位
            if (Objects.equals(code, 0)) {
                continue;
            }
            if ((code & useTag) == code) {
                orderTagList.add(code);
            }
        }
        return orderTagList;
    }

    public static List<String> getOrderTagsDesc(Integer useTag) {
        List<String> orderTagList = new ArrayList<>();
        if (useTag == null || useTag == 0) {
            return orderTagList;
        }
        for (InventoryOrderTagEnums value : InventoryOrderTagEnums.values()) {
            // 判断下orderTag是否包含有数据
            Integer code = value.getCode();
            // 排除下没有用途的占位
            if (Objects.equals(code, 0)) {
                continue;
            }
            if ((code & useTag) == code) {
                orderTagList.add(value.getDesc());
            }
        }
        return orderTagList;
    }

    public static List<String> getOrderTagsShortName(Integer useTag) {
        List<String> orderTagList = new ArrayList<>();
        if (useTag == null || useTag == 0) {
            return orderTagList;
        }
        for (InventoryOrderTagEnums value : InventoryOrderTagEnums.values()) {
            // 判断下orderTag是否包含有数据
            Integer code = value.getCode();
            // 排除下没有用途的占位
            if (Objects.equals(code, 0)) {
                continue;
            }
            if ((code & useTag) == code) {
                orderTagList.add(value.getShortName());
            }
        }
        return orderTagList;
    }

    public static Integer addOrderTag(Integer orderTag, InventoryOrderTagEnums enums) {
        if (Objects.isNull(orderTag)) {
            orderTag = 0;
        }
        Integer code = enums.getCode();
        if ((code & orderTag) != code) {
            orderTag += code;
        }
        return orderTag;
    }


    public static Integer removeOrderTag(Integer orderTag, InventoryOrderTagEnums enums) {
        if (Objects.isNull(orderTag)) {
            orderTag = 0;
        }
        Integer code = enums.getCode();
        if ((code & orderTag) == code) {
            orderTag -= code;
        }
        return orderTag;
    }

    public static Boolean contains(Integer orderTags, InventoryOrderTagEnums enums) {
        if (Objects.isNull(orderTags) || orderTags.equals(0)) {
            return false;
        }
        return Objects.equals(enums.getCode(), orderTags & enums.getCode());
    }

    public static List<InventoryOrderTagEnums> valuesV2() {
        return Arrays.stream(InventoryOrderTagEnums.values()).filter(i ->
                !i.equals(InventoryOrderTagEnums.NULL)
                        && !i.equals(InventoryOrderTagEnums.TRANSIT_OUT_NOT_GENERATE)
                        && !i.equals(InventoryOrderTagEnums.TRANSIT_IN_NOT_GENERATE)
                        && !i.equals(InventoryOrderTagEnums.ENDORSEMENT_NOT_DELETED)
        ).collect(Collectors.toList());
    }
}
