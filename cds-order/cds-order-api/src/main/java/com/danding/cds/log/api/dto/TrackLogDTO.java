package com.danding.cds.log.api.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Create 2021/6/8  16:10
 * @Describe
 **/
@Data
@Accessors(chain = true)
public class TrackLogDTO implements Serializable {
    private Long id;
    private String declareOrderNo;
    private Integer oldStatus;
    private String oldStatusStr;
    private Integer newStatus;
    private String newStatusStr;
    //操作描述
    private Integer operateDes;
    private String operateDesStr;
    //日志描述
    private String logDes;
    private String content;
    private Integer hasXmlMessage = 0;
    private Date createTime;
    private Date updateTime;
    private Integer createBy;
    private String createByName;
    private Integer updateBy;
    private Boolean deleted = false;
    private Integer orderType;
    private String orderTypeStr;
    private String inventoryNo;
    private String auditStatus;
}
