package com.danding.cds.customs.logistics.api.service;

import com.danding.cds.customs.inventory.api.enums.CustomsActionStatus;
import com.danding.cds.customs.logistics.api.dto.CustomsLogisticsDTO;
import com.danding.cds.customs.logistics.api.dto.CustomsLogisticsSubmit;
import com.danding.cds.customs.logistics.api.dto.LogisticsReceive;
import com.danding.cds.customs.logistics.api.dto.LogisticsSearch;
import com.danding.cds.customs.logistics.api.enums.LogisticsStatus;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;

import java.util.Date;
import java.util.List;

/**
 * @link com.danding.cds.c.api.service.CustomsLogisticsService
 */
@Deprecated
public interface CustomsLogisticsService {
    CustomsLogisticsDTO findByOrder(Long id, String sn);

    /**
     * 创建申报运单
     *
     * @param userId
     * @param orderDTO
     * @param submit
     */
    void submit(Long userId, OrderDTO orderDTO, CustomsLogisticsSubmit submit) throws ArgsErrorException;

    /**
     * 发起运单获取
     *
     * @param logisticsDTO
     * @throws ArgsErrorException
     */
    void obtainLogisticsNo(CustomsLogisticsDTO logisticsDTO) throws ArgsErrorException;

    /**
     * 写入运单号
     *
     * @param id
     * @param logisticsNo
     * @throws ArgsErrorException
     */
    void recordLogisticsNo(Long id, String logisticsNo) throws ArgsErrorException;

    void customsDeclare(CustomsLogisticsDTO logisticsDTO);

    void customsReceiver(CustomsLogisticsDTO logisticsDTO);

    /**
     * 更新运单状态
     *
     * @param id
     * @param logisticsStatus
     */
    void updateLogisticsStatus(Long id, LogisticsStatus logisticsStatus);

    void updateStatus(Long id, CustomsActionStatus status);

    void updateStatusResetDeclareTime(Long id, CustomsActionStatus status);

    void updateByPush(Long id, CustomsActionStatus status);

    void updateByPush(Long id, CustomsActionStatus status, Integer declareFrequency);

    /**
     * 更新回执
     *
     * @param id
     * @param customsStatus
     * @param customsDetail
     * @param lastCustomsTime
     */
    void updateByCustomsActive(Long id, String customsStatus, String customsDetail, Date lastCustomsTime);

    CustomsLogisticsDTO findById(Long id);

    CustomsLogisticsDTO findBySn(String sn);

    CustomsLogisticsDTO findByLogisticsNo(String logisticsNo);

    List<CustomsLogisticsDTO> listByLogisticsStatus(CustomsActionStatus status, LogisticsStatus logisticsStatus);

    ListVO<CustomsLogisticsDTO> paging(LogisticsSearch search);

    void rePush(String sn, Boolean sendNow);

    /**
     * 接收清单申报结果回执
     *
     * @param receive
     * @return
     */
    String receiveLogistics(LogisticsReceive receive);


    /**
     * 修改最后一次申报时间
     *
     * @param orderSn
     */
    void updateBylastDeclareTime(String orderSn);

    /**
     * 重置最近申报时间
     *
     * @param sn 申报单系统编号
     */
    void updateLastDeclareTime(String sn);

//    void logicDeleteBySn(String sn);

    void updateLogisticsNo(Long orderId, String newLogisticsNo);

    void updateAgentCompanyId(Long id, Long logisticsCompanyId);
}
