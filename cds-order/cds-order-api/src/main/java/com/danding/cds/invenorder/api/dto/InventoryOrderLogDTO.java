package com.danding.cds.invenorder.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
@Data
public class InventoryOrderLogDTO implements Serializable {
    private static final long serialVersionUID = -2433523850499947700L;
    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("清关单ID")
    private Long refInveOrderId;
    /**
     *清关单SN
     */
    @ApiModelProperty("清关单SN")
    private String  refInveOrderSn;
    /**
     * 日志名称
     */
    @ApiModelProperty("日志名称")
    private String logName;
    /**
     * 日志明细
     */
    @ApiModelProperty("日志明细")
    private String logDetail;
    /**
     * 备注说明
     */
    @ApiModelProperty("备注说明")
    private String logContent;



    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("更新时间")
    private Date updateTime;
    @ApiModelProperty("创建人ID")
    private Integer createBy;
    @ApiModelProperty("更新人ID")
    private Integer updateBy;
    @ApiModelProperty("逻辑删除")
    private Boolean deleted = false;
}
