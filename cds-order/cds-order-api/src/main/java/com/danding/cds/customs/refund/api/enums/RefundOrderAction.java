package com.danding.cds.customs.refund.api.enums;


public enum RefundOrderAction {
    REFUND_APPALY(20,"申请退货"),
    REFUND_CANCEL(30,"取消退货"),
    REFUND_CALLBACK(40,"退货单回执");
    private Integer value;

    private String desc;

    RefundOrderAction(Integer value, String desc) {
        this.value = value;
        this.desc = desc;

    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

}
