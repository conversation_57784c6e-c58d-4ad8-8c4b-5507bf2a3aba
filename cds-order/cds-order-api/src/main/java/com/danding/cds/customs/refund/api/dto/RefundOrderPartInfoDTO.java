package com.danding.cds.customs.refund.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class RefundOrderPartInfoDTO implements Serializable {
    @ApiModelProperty("运单")
    private String mailNo;
    @ApiModelProperty("原订单号")
    private String originOrderNo;
    @ApiModelProperty("料号")
    private String itemNo;
    @ApiModelProperty("正向申报数量")
    private Integer count;
    @ApiModelProperty("逆向申报数量")
    private Integer declareRefundQty;
    @ApiModelProperty("快递名称")
    private String refundExpressName;
}
