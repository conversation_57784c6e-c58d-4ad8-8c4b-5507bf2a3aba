package com.danding.cds.order.api.dto;

import com.danding.cds.payinfo.api.dto.PayInfoGoodsInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Auther: Dante-Gxj
 * @Date: 2020/5/8 15:36
 * @Description: 申报单提交对象
 */
@Data
@ApiModel
public class OrderSubmit implements Serializable {
    private static final long serialVersionUID = 6677018036727898308L;
    /**
     * 1.基础信息
     * -1.1通用基础
     * -1.2四单基础
     * 2.商品信息
     * -2.1四单商品
     * 3.收发货信息
     * -3.1 四单订购人信息
     * -3.2 四单发件人信息
     * -3.3 四单收件人信息
     */
    @ApiModelProperty("申报结果回传地址")
    private String notifyUrl;

    @ApiModelProperty("申报单流入渠道（1:出入库系统 2:pangu 3:京东 4:老支付系统）")
    private Integer channel;

    /**
     * 外部单号 基础
     */
    @ApiModelProperty("渠道单号|外部单号")
    @NotBlank(message = "外部单号不能为空")
    private String outOrderNo;

    @ApiModelProperty("外部交易流水号")
    private String outTradeNo;

    @ApiModelProperty("全局单号")
    private String systemGlobalSn;

    /**
     * 申报单号 基础
     */
    @NotBlank(message = "申报单号不能为空")
    @ApiModelProperty("申报单号")
    private String declareOrderNo;

    /**
     * 申报路径 基础 A1 必填二选一
     */
    @ApiModelProperty("申报路径编码")
    private String routeCode;

    @ApiModelProperty("申报路由标识1")
    private String firstIdentify;

    @ApiModelProperty("申报路由标识2")
    private String secondIdentify;

    @ApiModelProperty("申报路由标识3")
    private String thirdIdentify;

    // --- 清单基础 ---
    /**
     * 物流快递代码
     */
    @ApiModelProperty("物流快递代码")
    @NotBlank(message = "物流快递代码不能为空")
    private String expressCode;

    /**
     * 物流运单编号 不申报运单时必填
     */
    @ApiModelProperty("物流运单编号")
    @NotBlank(message = "物流运单编号不能为空")
    private String logisticsNo;

    /**
     * 运费
     */
    @ApiModelProperty("运费 非必填")
    private BigDecimal feeAmount = BigDecimal.ZERO;

    /**
     * 保费
     */
    @ApiModelProperty("保费 非必填")
    private BigDecimal insureAmount = BigDecimal.ZERO;

    /**
     * 毛重（公斤）
     */
    @ApiModelProperty("毛重（公斤）非必填")
    private BigDecimal grossWeight;

    /**
     * 净重（公斤） 若未填，则会去备案信息找
     */
    @ApiModelProperty("净重（公斤）非必填")
    private BigDecimal netWeight;

    // --- 商品 ---
    /**
     * 商品项
     */
    @ApiModelProperty("商品项")
    @NotEmpty(message = "商品项不能为空")
    private List<OrderSubmitItem> itemList;

    // --- 清单订购人 ---
    /**
     * 购买人电话
     */
    @ApiModelProperty("购买人电话 不传时取收件人电话")
    private String buyerTelNumber;

    /**
     * 订购人证件号码
     */
    @ApiModelProperty("订购人证件号码")
    @NotBlank(message = "订购人证件号码不能为空")
    private String buyerIdNumber;

    /**
     * 订购人姓名
     */
    @ApiModelProperty("订购人姓名")
    @NotBlank(message = "订购人姓名不能为空")
    private String buyerName;

    // --- 清单订购人 ---

    @ApiModelProperty("收件人省")
    @NotBlank(message = "收件人省不能为空")
    private String consigneeProvince;

    @ApiModelProperty("收件人市")
    @NotBlank(message = "收件人市不能为空")
    private String consigneeCity;

    @ApiModelProperty("收件人区")
    private String consigneeDistrict;

    /**
     * 收件人街道（四级地址）
     */
    private String consigneeStreet;

    @ApiModelProperty("收件人地址")
    @NotBlank(message = "收件人地址不能为空")
    private String consigneeAddress;

    // --- 订单基础 ---

    @ApiModelProperty("付款方式")
    private String payChannel;

    @ApiModelProperty("支付申报流水号")
    private String declarePayNo;

    @ApiModelProperty("税费")
    private BigDecimal taxAmount = BigDecimal.ZERO;

    @ApiModelProperty("折扣")
    private BigDecimal discount;

    @ApiModelProperty("交易时间|付款时间")
    private Long tradeTime;

    @ApiModelProperty("收件人邮箱 非必填")
    private String consigneeEmail;

    @ApiModelProperty("收件人电话")
    @NotBlank(message = "收件人电话不能为空")
    private String consigneeTel;

    @ApiModelProperty("收件人姓名")
    @NotBlank(message = "收件人姓名不能为空")
    private String consigneeName;

    @ApiModelProperty("海关发件人姓名")
    private String senderName;

    // --- 海关179公告对接 ---
    @ApiModelProperty("是否开启海关179备查")
    private Boolean payInfoDataCheckFlag = false;

    @ApiModelProperty("支付交易流水号")
    private String tradePayNo; // 订单里存一份，以防万一需要查

    @ApiModelProperty("验核机构交易流水号")
    private String payTransactionId;

    @ApiModelProperty("验核机构名称")
    private String verDept;

    @ApiModelProperty("海关订单支付方式")
    private String payWay;

    @ApiModelProperty("支付单总金额")
    private BigDecimal payTransactionAmount = BigDecimal.ZERO;

    @ApiModelProperty("收款企业社会信用代码")
    private String recpCode;

    @ApiModelProperty("收款企业工商备案名称")
    private String recpName;

    @ApiModelProperty("收款渠道下的账号")
    private String recpAccount;

    @ApiModelProperty("支付请求原始数据")
    private String payRequestMessage;

    @ApiModelProperty("支付返回原始数据")
    private String payResponseMessage;

    @ApiModelProperty("原始商品信息 一个原始商品可能对应多个发货SKU")
    private List<PayInfoGoodsInfo> origGoodsInfoList;
    // --- 租户定制化功能 ----
    @ApiModelProperty("租户ID")
    private String tenantOuterId;

    @ApiModelProperty("租户名称")
    private String tenantName;


    /**
     * 新增
     */
    @ApiModelProperty("申报口岸编码")
    private String customsCode;

    @ApiModelProperty("商户编号")
    private String merchantCode;

    /**
     * ERP实体仓编码
     * 如：JHS342775
     */
    private String erpPhyWarehouseSn;

    /**
     * ERP实体仓名称
     */
    private String erpPhyWarehouseName;

    /**
     * 订单下单时间，毫秒
     */
    private Long orderTime;

    /**
     * 物流企业Id
     */
    private Long logisticsEnterpriseId;

    /**
     * 物流企业编码
     */
    private String logisticsEnterpriseCode;

    /**
     * 物流企业名称
     */
    private String logisticsEnterpriseName;


    /**
     * 申报单类型
     * eg；”字节WMS“ - byteDance_wms
     */
    private List<String> declareOrderTypes;

    /**
     * 敏感信息是否加密
     */
    private Boolean isEncrypt;


    /**
     * 敏感信息明文, 用于字节云内申报
     */
    private OrderSensitivePlainText sensitivePlainText;

    /**
     * 支付流水号关联申报单号
     * 通联拆单申报
     */
    private List<String> payNoReldeclareOrderNoList;

    /**
     * 支付商户号(外部)
     */
    private String payMerchantOutNo;

    private String note;

    /**
     * 外部关务单号 （eg:CB..）
     */
    private String outerCustomsNo;

    /**
     * 外部物流单号 (eg:LP..)
     */
    private String outerLogisticsNo;
}
