package com.danding.cds.invenorder.api.service;

import com.danding.cds.invenorder.api.dto.InventoryOrderTallyReportDTO;
import com.danding.cds.invenorder.api.dto.InventoryOrderTallyReportSubmit;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;

import java.util.List;

public interface InventoryOrderTallyReportService {
    /**
     * 根据清关单找理货报告
     * @param inveOrderId
     * @return
     */
    List<InventoryOrderTallyReportDTO> findListByInveOrderId(Long inveOrderId);

    /**
     * 根据id查询理货报告
     * @param id
     * @return
     */
    InventoryOrderTallyReportDTO findById(Long id);

    /**
     * 保存理货报告
     * @param submit
     * @return
     * @throws ArgsErrorException
     */
    Long upset(InventoryOrderTallyReportSubmit submit) throws ArgsErrorException;
}
