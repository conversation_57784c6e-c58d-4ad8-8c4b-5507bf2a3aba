package com.danding.cds.log.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */

@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum TrackLogEnums {
    NULL(0, "空"),
    INVENTORY_CREATE(10, "清单创建成功"),
    INVENTORY_DECLARE(20, "清单申报"),
    INVENTORY_RECEIPT(30, "清单回执已返回"),
    TAX_RECEIPT(40, "税金已回传"),
    WAREHOUSE_NOTIFIED(50, "已通知仓库"),

    REFUND_CREATE(60, "退货单创建成功"),
    REFUND_DECLARE_PUSH_SUCCESS(70, "退货单申报推送成功"),
    REFUND_DECLARE_OVERRULE(80, "退货单申报驳回"),
    REFUND_DECLARE_SUCCESS(71, "退货单申报成功"),
    REFUND_DECLARE_SUCCESS_INIT(90, "退货单修改成功：初始化"),
    REFUND_DECLARE_SUCCESS_PENDING_REVIEW(100, "退货单修改成功：待总署审核"),


    CANCEL_CREATE(110, "撤单创建成功"),
    CANCEL_DECLARING(120, "撤单申报中"),
    CANCEL_DECLARE_PUSH_SUCCESS(130, "撤单申报推送成功"),
    CANCEL_DECLARE_PUSH_FAIL(140, "撤单申报失败"),
    CANCEL_DECLARE_OVERRULE(150, "撤单申报驳回"),
    CANCEL_DECLARE_SUCCESS(160, "撤单申报成功"),
    CANCEL_SUCCESS(170, "撤单取消成功"),
    CANCEL_DECLARE_SUCCESS_INIT(180, "撤单修改成功：初始化");


    private Integer code;
    private String desc;

    public static TrackLogEnums getEnum(Integer code) {
        for (TrackLogEnums value : TrackLogEnums.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return NULL;
    }

}
