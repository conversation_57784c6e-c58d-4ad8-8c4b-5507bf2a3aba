package com.danding.cds.v2.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum InventoryOrderDraftCompareTypeEnums {

    COMPARE_HS_CODE(1, "HS编码"),
    COMPARE_PRODUCT_ID(2, "统一料号");

    private final Integer code;
    private final String desc;


    public static String getDesc(Integer code) {
        for (InventoryOrderDraftCompareTypeEnums item : InventoryOrderDraftCompareTypeEnums.values()) {
            if (item.getCode().equals(code)) {
                return item.getDesc();
            }
        }
        return "";
    }

    public static InventoryOrderDraftCompareTypeEnums getEnums(Integer code) {
        for (InventoryOrderDraftCompareTypeEnums item : InventoryOrderDraftCompareTypeEnums.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }

}
