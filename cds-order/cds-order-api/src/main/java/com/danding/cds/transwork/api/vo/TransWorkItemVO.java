package com.danding.cds.transwork.api.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: yousx
 * @Date: 2024/03/12
 * @Description:
 */
@ApiModel
@Data
public class TransWorkItemVO implements Serializable {

    private Long id;

    /**
     * 明细行号
     */
    @ApiModelProperty(value = "明细行号")
    private String orderLineNo;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String itemCode;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String itemName;

    /**
     * 数量
     */
    @ApiModelProperty(value = "货品数量")
    private Long qty;

    /**
     * 实际发货数量
     */
    @ApiModelProperty(value = "实际发货数量")
    private Long actDeliveryQty;


    /**
     * 实际签收数量
     */
    @ApiModelProperty(value = "实际签收数量")
    private Long actSignQty;


    /**
     * 原产国
     */
    @ApiModelProperty(value = "原产国")
    private String originRegion;

    /**
     * 采购单价
     */
    @ApiModelProperty(value = "采购单价")
    private String purchasePrice;

    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    private String currency;

    /**
     * 总价
     */
    @ApiModelProperty(value = "总价")
    private String totalPrice;

    /**
     * 合计体积
     */
    @ApiModelProperty(value = "合计体积")
    private String volume;

    /**
     * 合计重量
     */
    @ApiModelProperty(value = "合计重量")
    private String weight;

    /**
     * 生产批号
     */
    @ApiModelProperty(value = "生产批号")
    private String produceCode;

    /**
     * 生产日期
     */
    @ApiModelProperty(value = "生产日期")
    private String productDate;

    /**
     * 过期日期
     */
    @ApiModelProperty(value = "过期日期")
    private String expiryDate;
}
