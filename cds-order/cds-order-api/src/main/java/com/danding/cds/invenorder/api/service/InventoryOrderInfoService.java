package com.danding.cds.invenorder.api.service;

import com.danding.cds.collaborateorder.api.dto.TallyReportDetailDTO;
import com.danding.cds.common.model.excel.ImportResultResVo;
import com.danding.cds.endorsement.api.dto.EndorsementDTO;
import com.danding.cds.invenorder.api.dto.*;
import com.danding.cds.invenorder.api.enums.InventoryOrderBusinessEnum;
import com.danding.cds.invenorder.api.vo.req.InventoryOrderTransitReqVO;
import com.danding.cds.invenorder.api.vo.res.InventoryOrderRelationResVO;
import com.danding.cds.invenorder.api.vo.res.InventoryOrderStatusCountResVO;
import com.danding.cds.item.api.dto.CustomsGoodsItemInfoDTO;
import com.danding.cds.v2.bean.dto.*;
import com.danding.cds.v2.bean.vo.req.*;
import com.danding.cds.v2.bean.vo.res.InventoryOrderInfoTransferorListResVO;
import com.danding.cds.v2.bean.vo.res.InventoryUrgentProcessResVO;
import com.danding.cds.v2.enums.InvOrderItemGenTypeEnums;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.Response;
import com.danding.logistics.api.common.response.SelectItemVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface InventoryOrderInfoService {


    /**
     * 清关单统计
     *
     * @return
     */
    InvBusinessCountDTO selectInventoryOrderInfoCount(String areaCompanyId);

    Map<String, CustomsCompleteCountDTO> inventoryOrderInfoCountDetail(List<String> handlerIdList, Long beginTime, Long endTime);

    /**
     * 更新清关单状态，通过核注状态
     *
     * @param inventoryOrderId      清关单ID
     * @param endorsementStatusCode 核注状态CODE
     */
    void updateStatusByEndorsementStatus(Long inventoryOrderId, String endorsementStatusCode);

    void updatePushMsg(Long inventoryOrderId, Integer pushStatus, String pushMsgId, String pushMsg);

    InventoryOrderInfoDTO findByInOutOrderNo(String inOutOrderNo);

    List<InventoryOrderInfoDTO> findByInOutOrderNo(List<String> inOutOrderNoList);

    List<InventoryOrderInfoDTO> findByInOutOrderNoAll(String inOutOrderNo);

    List<InventoryOrderInfoDTO> findByInOutOrderNos(String inOutOrderNo, Integer limit);

    InventoryOrderInfoDTO findById(Long id);

    List<InventoryOrderInfoDTO> findById(List<Long> idList);

    InventoryOrderInfoDTO findBySn(String inveCustomsSn);

    InventoryOrderInfoDTO findByEndorsementSn(String endorsementSn);

    List<InventoryOrderInfoDTO> findBySn(List<String> inveCustomsSn);


    InventoryOrderRelationDTO findInventoryOrderRelationDtoById(Long id);

    List<InventoryOrderItemDTO> findListByInvenOrderId(Long id);

    List<InventoryOrderItemDTO> findListByInvenOrderSn(String sn);

    List<InventoryOrderItemDTO> listItemDTOByItemIdS(List<Long> itemIdList);

    /**
     * 根据清关单id和核注清单id查询清关单表体
     *
     * @param invenOrderId
     * @param endorsementOrderId
     * @return
     */
    List<InventoryOrderItemDTO> findListByInvenOrderIdAndEndorsementId(Long invenOrderId, Long endorsementOrderId);

    List<InventoryOrderRelationDTO> findInventoryOrderRelationListByInvenOrderId(Long id);

    ListVO<InventoryOrderInfoDTO> paging(InventoryOrderSearch search);

    List<InventoryOrderInfoDTO> findList(InventoryOrderSearch search);

    List<InventoryOrderInfoDTO> findCanRefEndorsement();

    List<InventoryOrderInfoDTO> findCanRefEndorsement(List<Long> accountBookList);

    String createInventoryOrder(InventoryOrderInfoParam infoParam);

    String createInventoryOrderSelf(InventoryOrderInfoCreateDTO createDTO) throws Exception;

    InventoryOrderInfoDTO createInventoryOrderInfoDTO(InventoryOrderInfoDTO inventoryOrderInfoDTO);

    InventoryOrderInfoDTO updateInventoryOrderInfoDTO(InventoryOrderInfoDTO inventoryOrderInfoDTO);

    InventoryOrderInfoDTO updateInventoryOrderInfoDTO(InventoryOrderInfoDTO inventoryOrderInfoDTO, Boolean statusFlag);

    InventoryOrderInfoDTO updateInventoryOrderInfoDTOAllArgs(InventoryOrderInfoDTO inventoryOrderInfoDTO);

    InventoryOrderInfoDTO updateInventoryOrderInfoDTOAllArgs(InventoryOrderInfoDTO inventoryOrderInfoDTO, Boolean statusFlag);

    InventoryOrderInfoDTO updateInventoryOrderInfoDTO(InventoryOrderInfoDTO inventoryOrderInfoDTO, List<InventoryOrderItemDTO> listItemOrder);

    List<CustomsGoodsItemInfoDTO> deleteInventoryOrderRelation(List<Long> ids);

    public void update(InventoryOrderRelationDTO inventoryOrderRelationDTO);

    public void upset(List<InventoryOrderRelationDTO> inventoryOrderRelationDTOList);

    InventoryOrderInfoDTO correlation(InventoryOrderInfoDTO inventoryOrderInfoDTO, String status);

    void updateInventoryOrderInfoStatus(Long id, String status);

    void discard(Long id, EndorsementDTO endorsementDTO);

    void discard(Long id);

    void updateSubOrderStatusByList(List<String> subSnList, String status, Boolean isDiscard);

    void discardInventoryOrderAndEndorsement(Long inventoryOrderId);

    ResultDTO audit(Long id);

    /**
     * 清关单审核重试回调ERP
     *
     * @param sn 清关单号
     */
    void auditCallBackToErpRetry(String sn);

    void manualCallBackErp(InventoryOrderInfoDTO infoDTO, String action);

    public List<Long> findListInventoryOrderIds(String logisticsNo);

    public InventoryOrderRelationEditReport editByEndorsement(Long inventoryOrderId, List<InventoryOrderRelationDTO> addList, List<InventoryOrderRelationDTO> delList, Boolean save) throws ArgsErrorException;

    List<SelectItemVO> listTransport();

    List<InventoryOrderInfoDTO> findByChannelBusinessTypeAndSn(String channelBusinessType, String channelBusinessSn);

    /**
     * 上游发起清关单取消（仅当清关失败状态）
     *
     * @param inveCustomsSn
     * @return
     */
    boolean discard(String inveCustomsSn) throws ArgsErrorException;

    /**
     * 清关单核注作废
     *
     * @param inventoryOrderInfoDTO 清关单
     */
    void inventoryOrderAndEndorsementDiscard(InventoryOrderInfoDTO inventoryOrderInfoDTO);


    /**
     * 清关单核注作废同步wms
     *
     * @param inventoryOrderInfoDTO 清关单
     */
    void inventoryOrderAndEndorsementDiscardSyncWms(InventoryOrderInfoDTO inventoryOrderInfoDTO, String wmsCancelJson);

    /**
     * 清关单核注作废同步wms(出库)
     *
     * @param inventoryOrderInfoDTO 清关单ID
     */
    void inventoryOrderAndEndorsementDiscardSyncWmsOut(InventoryOrderInfoDTO inventoryOrderInfoDTO, String wmsCancelJson);

    Response submitTallyReport(String inveCustomsSn, String tallyReportJson) throws ArgsErrorException;

    void updateInveOrderDraftById(Long inventoryOrderId, String attachmentName, String attachmentUrl);

    void updateInventoryOrderItemTallyQtyById(Long inventoryOrderItemId, Integer actualTallyQty, String outBoundNos);

    void updateInventoryOrderActualTallyQtyById(Long inventoryOrderItemId, Integer actualTallyQty, String outBoundNo);

    ListVO<InventoryOrderInfoDTO> pagingV2(InventoryOrderSearchV2 search);

    List<InventoryOrderStatusCountResVO> getStatusCount(InventoryOrderSearchV2 search);

    List<List<InventoryVerifyResult>> itemBatchVerify(InventoryOrderInfoDTO inventoryOrderInfoDTO, List<InventoryOrderItemDTO> itemDTOList, Long customsBookId);

    List<InventoryVerifyResult> verifyInventoryItem(InventoryItemVerifyParamDTO paramDTO);

    Response<String> mergeCheck(List<String> idList);

    Response<InventoryOrderInfoDTO> mergeInventoryOrder(InventoryOrderMergeParam param) throws Exception;

    void unMerge(Long id);

    void regenerateItem(Long inventoryOrderId);

    String generateEndorsement(Long inventoryOrderId);

    void endorsementGenerateTallyReportCheck(InventoryOrderInfoDTO infoDTO);

    InventoryFlowStateDTO getFlowState(Long inventoryOrderId);

    int sumPlanDeclareQty(String inventoryOrderInfoId);

    int sumActualDeclareQty(String inventoryOrderInfoId);

    List<InventoryOrderBusinessEnum> listBusinessTypeById(Long inventoryOrderId);

    Map<String, String> listErpOwner();

    Map<String, Integer> inventoryStatusCount();

    void endorsementDiscardPostProcess(EndorsementDTO inventoryOrderId);

    InventoryOrderItemDTO findSkuAndProductIdGoodsSeqNo(String sku, String productId, String goodsSeqNo, String inveSn);

    void manualDeal(Long id, String associatedInventorySn);

    void manualFinish(Long id);

    InventoryOrderItemDTO findByEndorsementIdAndProductId(Long endorsementId, String productId);

    void updateInventoryOrderInfoHead(InventoryOrderEditReqVo editReqVo);

    List<WmsTallyDetailDTO> getWmsTallyDetail(Long id);

    List<InventoryOrderAttachDTO> viewInventoryOrderAttach(Long inventoryOrderId);

    void updateItemSeqCallbackAndAutoCreate(String productId, String goodsSeqNo, String realOrderNo, String invtGNo);

    void updateInventoryOrderItemSeqCallback(String productId, String goodsSeqNo, String realOrderNo, String invtGNo);

    /**
     * 检查并自动创建中转调入调出清关单
     *
     * @param inventoryOrderId
     * @throws Exception
     */
    void checkAndAutoCreateTransitInventoryOrder(Long inventoryOrderId) throws Exception;

    void updateTransitOutOrderEndorsementSn(EndorsementDTO endorsementDTO, InventoryOrderInfoDTO inventoryOrderInfoDTO);

    /**
     * 编辑导入关联单号
     * controller 方法挪到 service 中
     *
     * @param id
     * @param relNo
     * @return
     */
    Response updateInventoryOrderRelation(Long id, String relNo);

    /**
     * 导入关联单号
     *
     * @param param
     * @return
     */
    Response buildInventoryOrderRelation(InventoryOrderRelationParam param);


    /**
     * 接受理货报告 - 多品，创建清关单表体
     *
     * @param infoDTO     清关单
     * @param detailReqVo 理货报告详情
     */
    InventoryOrderItemDTO multiProductCreateItem(InventoryOrderInfoDTO infoDTO, TallyReportDetailDTO detailReqVo);

    void retryCallBack(List<Long> idList);

    void startTransit(InventoryOrderTransitReqVO reqVO);

    void returnTransit(Long id);

    void returnTransit(InventoryOrderInfoDTO infoDTO, Boolean isUpStreamCancel);

    void resendMail(Long id);

    void updateMailStatus(Long id, Integer mailStatus);

    void auditMail(String inveOrderSn, Integer auditStatus, String rejectReason);

    InventoryOrderInfoTransferorListResVO getMailDetailByOrderNo(String inveCustomsSn);

    void retryCallbackRejectOrder(Long id);

    void updateCallbackErpRejectOrderErrMsg(Long id, String errorMsg);

    Boolean judgeExist30DaysInByProductId(String productId);

    /**
     * 根据出入库单号和结转单号切换核出企业
     *
     * @param inOutOrderNo         出入库单号
     * @param carryOverNo          结转单号
     * @param actualOutCompanyName 实际核出企业
     */
    void changeCompanyByInOutOrderNo(String inOutOrderNo, String carryOverNo, String actualOutCompanyName);

    List<ReceiveCarryOverResDTO> createCarryOverOrder(CarryOverCreateDTO carryOverCreateDTO);

    List<ReceiveCarryOverResDTO> createCarryOverOrderBatch(CarryOverBatchCreateDTO carryOverBatchCreateDTO);

    List<InventoryOrderInfoDTO> findByCarryOverNo(String carryOverNo);

    List<InventoryOrderInfoDTO> findByUpstreamNo(String upstreamNo);

    List<InventoryOrderInfoDTO> findByUpstreamNo(List<String> upstreamNo);

    Map<String, String> findCompanyByInOutOrderNo(List<String> inOutOrderNoList);

    String checkCarryOverRelateOrder(String carryOverNo);

    InventoryOrderRelationResVO preBuildInventoryOrderRelation(InventoryOrderRelationParam param);

    void updateInventoryCompanyAndBook(Long id, Long companyId, Long bookId, Integer needCarryOver);

    /**
     * 更新车辆信息
     *
     * @param inventoryOrderInfoDTO 清单主体信息
     */
    void updateInventoryOrderCarInformation(InventoryOrderInfoDTO inventoryOrderInfoDTO);

    String createInventoryByCw(InventoryOrderInfoCwSubmit inventoryOrderInfoCwSubmit);

    String finishTallyByInOutOrderNo(String inOutOrderNo);

    List<InventoryOrderInfoDTO> listFbInventorySn(String businessType, Long bookId);

    /**
     * 非保核放单作废关联清关单作废
     *
     * @param inventoryOrderId
     */
    void discardByFbChecklist(Long inventoryOrderId);

    InventoryOrderItemDTO getInventoryOrderItemByItemIdV2(Long bookItemId, String originProductId, Long bookId, String oldOrNew);

    Map<String, String> getInvCompanyNameByMailNo(List<String> mailNoList);

    void carryOverDiscardSyncInvOrder(String carryOrderNo);

    void cancelTallyReport(String inOutOrderNo);

    void updateStatusById(Long id, String status) throws ArgsErrorException;

    void callWmsGenerateCarryOver();

    String createInventoryByTaotian(TTClearanceEntryorderCreateDTO createDTO);

    TaotianLogisticsTraceInfoDTO getTaotianLogisticsTraceInfo(String itemCode);

    void cancelByTaotian(TTGeneralCancelDTO ttGeneralCancelDTO);

    boolean checkTaotian2050Exist(InventoryOrderInfoDTO infoDTO);

    void updateTaotian2050Success(InventoryOrderInfoDTO infoDTO);

    List<TaotianLogisticsTraceInfoDTO> getTaotianLogisticsTraceInfoV2(String orderNo);

    ImportResultResVo importInventoryItemLogisticsInfoExcel(Long inventoryOrderId, InventoryItemLogisticsInfoDTO logisticsInfoDTO);

    void updateInventoryItemLogisticsInfo(Long inventoryOrderId, InventoryItemLogisticsInfoDTO logisticsInfoDTO);

    InventoryOrderItemDTO findItemById(Long id);

    void boundYcNo(String invCustomsSn, String ycNo);

    List<InventoryOrderInfoDTO> getExportData(InventoryOrderSearchV2 searchV2);

    void associateTransfer(String inveOrderSn, String associatedOrderSn);

    void checkAssociateTransfer(List<InventoryOrderInfoDTO> infoDTOList);

    void disassociateTransfer(Long id);

    List<ImportResultResVo> importExcelBatchUpdItem(Long inventoryOrderId, List<InventoryOrderItemDTO> importItemDTOList);

    List<DraftItemCompareDTO> compareDraftItemList(Long id);

    List<DraftItemCompareDTO> compareDraftItemList(Long id, Integer type);

    void updateItemListByDraftCompare(List<InventoryOrderItemDTO> itemDTOList, Integer compareType);

    void saveInventoryOrderLog(Long inventoryOrderId, String logDesc);

    List<InventoryOrderTrackLogDTO> inventoryOrderInfoTrackLogs(Long trackLogId);

    void doEndorsementDeletedPostProcess(Long inventoryOrderId);

    void confirmDraft(Long id, Integer type);

    List<InventoryOrderItemDTO> buildItemsByAssociatedTransitId(Long id, Long transitId);

    List<InventoryOrderItemDTO> buildItemsByAssociatedTransit(Long id);

    void takeOrder(Long id);

    void updateCompleteTime(List<Long> needUpdateCompleteTimeIds, Date completeTime);

    void updateFinishTime(List<Long> needUpdateFinishTimeIds, Date finishTime);

    void updateOrderTag(Long id, Integer orderTag);

    void updateOrderTodoTag(Long id, Integer orderTodoTag);

    List<InventoryUrgentProcessResVO> listUrgentProcess();

    void saveUrgentSort(InventoryUrgentSortSaveReqVo reqVo);

    void batchChangeUrgentProcessInventoryOrder(List<Long> inventoryOrderIdList, Boolean isUrgent);

    void batchChangeUrgentProcessWithoutCheck(List<Long> inventoryOrderIdList, Boolean isUrgent);

    void removeUrgentSort(Long inventoryOrderId);

    List<InventoryOrderInfoDTO> findByDeclareFormNo(List<String> declareFormNoList);

    List<InventoryOrderItemDTO> findItemByOrderIds(List<Long> orderIdList);

    void buildAndSaveItemGoods(InventoryOrderInfoDTO inventoryOrderInfoDTO, List<InventoryOrderItemDTO> listItems);

    List<InventoryOrderItemGoodsDTO> listItemGoodsByInventoryId(Long inventoryOrderId);

    void updateItemGoods(Long id, InventoryOrderItemGoodsDTO inventoryOrderItemGoodsDTO);

    void releaseLockStockManualById(Long id);

    void releaseLockStockById(Long id);

    ResultDTO lockStockById(Long id);

    ResultDTO lockStockIndTractionById(Long id);

    ResultDTO lockStockById(Long id, Boolean force);

    ResultDTO saveInventoryItems(InventoryOrderInfoDTO inventoryOrderInfoDTO, List<InventoryOrderItemDTO> listItems);

    List<InventoryOrderItemLockStockDTO> getLockStockDetail(Long customsBookItemId);

    String updateInventoryOrderItemOuter(InventoryOrderItemUpdateReqVO inventoryOrderInfoParam);

    List<InvOrderItemCompareDTO> compareItemList(Long id, Integer compareType);

    void updateItemListByCompare(List<InventoryOrderItemDTO> itemDTOList, Integer compareType);

    void uploadAttachListOuter(InventoryOrderAttachUploadReqVO reqVo);

    String downloadAttach(Long id, Integer isUpdateCompanyInfo);
    void importExcel(Long inventoryOrderId, List<InventoryOrderItemDTO> successList, String operator);

    InventoryOrderItemDTO buildInventoryOrderItem(InvOrderItemGenTypeEnums invOrderItemGenTypeEnums, InventoryOrderInfoDTO id, InventoryOrderItemParam inventoryOrderItemParam);

    List<InventoryOrderInfoDTO> findByEndorsementRealNo(String realOrderNo);
}
