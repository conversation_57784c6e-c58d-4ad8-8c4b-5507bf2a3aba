package com.danding.cds.order.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Create 2021/7/7  13:35
 * @Describe
 **/
@Data
public class CancelOrderSubmit implements Serializable {
    @ApiModelProperty("订单号")
    private String systemGlobalSn;
    @ApiModelProperty("取消单凭证")
    private String picJson;
    @ApiModelProperty("退货运单号")
    private String refundLogisticsNo;
    @ApiModelProperty("实体仓编码")
    private String entityWarehouseCode;
    @ApiModelProperty("实体仓名称")
    private String entityWarehouseName;
    @ApiModelProperty("货主编码")
    private String ownerCode;
    @ApiModelProperty("货主名称")
    private String ownerName;
}
