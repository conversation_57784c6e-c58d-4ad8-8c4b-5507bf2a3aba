package com.danding.cds.transwork.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: yousx
 * @Date: 2024/03/12
 * @Description:
 */
@AllArgsConstructor
@Getter
public enum TransPackageTypeEnum {

    NULL("", ""),
    TP("1", "托盘"),
    ZX("2", "装箱"),
    ;
    private final String code;
    private final String desc;

    public static TransPackageTypeEnum getEnum(String code) {
        for (TransPackageTypeEnum transPackageTypeEnum : TransPackageTypeEnum.values()) {
            if (transPackageTypeEnum.getCode().equals(code)) {
                return transPackageTypeEnum;
            }
        }
        return NULL;
    }
}
