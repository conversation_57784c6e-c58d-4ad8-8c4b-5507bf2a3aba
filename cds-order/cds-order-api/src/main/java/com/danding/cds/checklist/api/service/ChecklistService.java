package com.danding.cds.checklist.api.service;

import com.danding.cds.checklist.api.dto.*;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.endorsement.api.dto.EndorsementDTO;
import com.danding.cds.invenorder.api.dto.CustomsCompleteCountDTO;
import com.danding.cds.invenorder.api.dto.InvBusinessCountDTO;
import com.danding.cds.v2.bean.dto.ChecklistItemDTO;
import com.danding.cds.v2.bean.vo.req.ChecklistAssociateItemReqVo;
import com.danding.cds.v2.bean.vo.req.ChecklistItemSaveReqVo;
import com.danding.cds.v2.bean.vo.res.CheckListViewResVo;
import com.danding.cds.v2.bean.vo.res.ChecklistAssociateItemResVo;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;

import java.util.List;
import java.util.Map;

/**
 * 核放单
 */
public interface ChecklistService {

    InvBusinessCountDTO selectChecklistBussinessCount(String areaCompanyId);

    void push(Long id) throws ArgsErrorException;

    void pushTemporaryStorage(Long id) throws ArgsErrorException;

    Long create(ChecklistSubmit submit,Long userId) throws ArgsErrorException;

    /**
     * 手动核放
     * @param id
     * @param realNo
     */
    void finish(Long id,String realNo) throws ArgsErrorException;

    Long update(ChecklistSubmit submit) throws ArgsErrorException;

    void bindEndorsement(ChecklistSubmit submit) throws ArgsErrorException;

    Long updateStatus(Long id, String status);

    Long updateCustomsStatus(Long id, String status);

    Long updateInformationDesc(Long id, String informationDesc) throws ArgsErrorException;

    Long discard(Long id) throws ArgsErrorException;

    void postDiscard(Long id);

    ChecklistDTO findByBusinessNo(String preNo);

    ChecklistDTO findByPreNo(String preNo);

    ChecklistDTO findById(Long id);

    List<ChecklistDTO> findByIdList(List<Long> idList);

    ListVO<ChecklistDTOV2> paging(ChecklistSearch search);

    List<Long> findBoundEndorsementIdByMapping(List<Long> endorsemnetIdList);

    CheckListViewResVo viewCheckList(Long id);

    List<ChecklistItemDTO> listChecklistItemById(Long id);

    ChecklistAssociateItemResVo associateItemBySerialNumber(ChecklistAssociateItemReqVo reqVo) throws ArgsErrorException;

    ChecklistAssociateItemResVo viewChecklistItem(Long checklistItemId) throws ArgsErrorException;

    void saveChecklistItem(ChecklistItemSaveReqVo reqVo) throws ArgsErrorException;

    void deleteChecklistItem(Long id) throws ArgsErrorException;

    List<EndorsementDTO> getEndorsementListByChecklistId(Long checklistId);

    List<ChecklistDTO> checklistByEndorsementId(Long id);

    void manualUpdStatus(ChecklistDTO checklistDTO);

    /**
     * 根据二线出区核注单获取核放单
     *
     * @param endorsementIdList
     * @return
     */
    Map<Long, ChecklistDTO> getChecklistByEndorsementListSecondOut(List<Long> endorsementIdList);

    /**
     * 根据核注单Id 批量查找核注单关联的核放单DTO
     *
     * @param endorsementIdList
     * @return
     */
    Map<Long, List<ChecklistDTO>> getChecklistByEndorsementList(List<Long> endorsementIdList);

    Map<String, CustomsCompleteCountDTO> selectChecklistBussinessCountDetail(List<String> handlerIdList, Long beginTime, Long endTime);

    Map<String, Integer> countPagingStatus(ChecklistSearch search);

    void createChecklist(ChecklistSubmit submit) throws ArgsErrorException;

    List<SelectOptionVO<Integer>> listTypeByEndorsement(String endorsementType);

    List<ChecklistTrackLogDTO> listChecklistTrackLog(Long checklistId);

    List<SelectOptionVO<Integer>> listTypeByEndorsement(String endorsementType, Long endorsementId);

    void handlerCheck(List<Long> idList);
}
