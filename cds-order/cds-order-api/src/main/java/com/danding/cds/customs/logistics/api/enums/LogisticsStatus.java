package com.danding.cds.customs.logistics.api.enums;

/**
 * 运单状态
 */
public enum LogisticsStatus {
    NULL(0,"空"),
    INIT(1,"待获取"),
    WAIT(2,"获取中"),
    READY(3,"已就绪")
    ;


    private Integer value;

    private String desc;

    LogisticsStatus(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static LogisticsStatus getEnum(Integer value){
        for (LogisticsStatus logisticsStatus : LogisticsStatus.values()) {
            if (logisticsStatus.getValue().equals(value)){
                return logisticsStatus;
            }
        }
        return NULL;
    }
}
