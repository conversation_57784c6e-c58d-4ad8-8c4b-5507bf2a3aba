package com.danding.cds.ownerMapping.api.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Create 2021/7/5  10:29
 * @Describe
 **/
@Getter
@AllArgsConstructor
public enum OrderOwnerMappingTypeEnum {
    /**
     * 空
     */
    NULL("","空"),
    INVENTORY_ORDER("INVENTORYORDER","清关单"),
    INVENTORY_CALLOFF("INVENTORYCALLOFF","取消单");

    private String code;
    private String desc;

    public static OrderOwnerMappingTypeEnum getEnum(String code){
        for (OrderOwnerMappingTypeEnum type : OrderOwnerMappingTypeEnum.values()) {
            if (type.code.equals(code)){
                return type;
            }
        }
        return NULL;
    }
}
