package com.danding.cds.handoverOrder.api.dto;

import com.danding.logistics.api.common.page.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2021/12/17
 */
@Data
public class HandoverOrderDetailParam extends Page {
    @ApiModelProperty("交接单id")
    private Long id;

    /**
     * 交接单号
     */
    @ApiModelProperty("交接单号")
    private String handoverSn;

    /**
     * 类型
     */
    @ApiModelProperty("类型")
    private String queryType;

    /**
     * 运单或者申报单
     */
    @ApiModelProperty("运单或者申报单")
    private String queryInfo;

    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    private Long createFrom;

    /**
     * 结束时间
     */
    @ApiModelProperty("结束时间")
    private Long createTo;

    /**
     * 是否关联出库 0-未关联  1-已关联
     */
    @ApiModelProperty("是否关联出库")
    private Integer associateOutboundStatus;

    /**
     * 是否有异常 0 -否 -1 是
     */
    @ApiModelProperty("是否有异常")
    private Integer unusualStatus;






}
