package com.danding.cds.checklist.api.dto;

import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ChecklistOrderItemDTO implements Serializable {

    private static final long serialVersionUID = -4034429967215026132L;
    private Long id;

    /**
     * 核放单Id
     */
    private Long checklistOrderId;

    /**
     * 核放子单Id
     */
    private Long checklistOrderGroupId;

    /**
     * 核注清单ID
     */
    private Long endorsementOrderId;

    /**
     * 包裹号
     */
    private String bizId;

    /**
     * 快递标识
     */
    @Column(name = "express_code")
    private String expressCode;

    /**
     * 快递编号
     */
    @Column(name = "mail_no")
    private String mailNo;

    /**
     * 毛重
     */
    @Column(name = "gross_weight")
    private BigDecimal grossWeight;
}
