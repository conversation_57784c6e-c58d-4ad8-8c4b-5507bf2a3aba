package com.danding.cds.order.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel
public class CustomsReceive implements Serializable {
    /**
     * 清关状态
     */
    @ApiModelProperty("清关状态")
    private String customsStatus;

    /**
     * 电商平台代码
     */
    @ApiModelProperty("电商平台代码")
    private String ebpCode;

    /**
     * 担保企业
     */
    @ApiModelProperty("担保企业代码")
    private String agentCode;

    /**
     * 申报单号
     */
    @ApiModelProperty("申报单号")
    private String declareOrderNo;

    /**
     * 清关描述
     */
    @ApiModelProperty("清关描述")
    private String customsDetail;

    /**
     * 回执信息
     */
    @ApiModelProperty("回执信息")
    private String responseMsg;

    /**
     * 回执发生时间
     */
    @ApiModelProperty("回执发生时间")
    private Long customsTime;

    /**
     * 预录入编号
     */
    @ApiModelProperty("预录入编号")
    private String preNo;

    /**
     * 清单编号
     */
    @ApiModelProperty("清单编号")
    private String invtNo;

    /**
     * 发送方
     */
    @ApiModelProperty("发送方")
    private String sender;
}
