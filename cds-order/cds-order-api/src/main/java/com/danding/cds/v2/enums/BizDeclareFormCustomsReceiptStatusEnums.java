package com.danding.cds.v2.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 申报表 -海关回执状态
 */
@Getter
@AllArgsConstructor
public enum BizDeclareFormCustomsReceiptStatusEnums {

    //1-通过2-转人工3-退单Y-入库成功Z-入库失败
    PASSED("1", "通过"),
    TRANSFER("2", "转人工"),
    REFUND("3", "退单"),
    IN_WAREHOUSE_SUCCESS("Y", "入库成功"),
    IN_WAREHOUSE_FAIL("Z", "入库失败"),
    MANUAL_RECORD("MANUAL_RECORD", "手动备案"),
    MANUAL_FINISH("MANUAL_FINISH", "手动结案"),
    ;

    private final String code;
    private final String desc;

    public static BizDeclareFormCustomsReceiptStatusEnums getEnums(String code) {
        for (BizDeclareFormCustomsReceiptStatusEnums enums : BizDeclareFormCustomsReceiptStatusEnums.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }
}
