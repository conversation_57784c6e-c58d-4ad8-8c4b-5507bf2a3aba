package com.danding.cds.customs.inventory.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: Raymond
 * @Date: 2020/10/14 13:15
 * @Description:
 */
@Data
public class CustomsInventoryCalloffCountDTO implements Serializable {
    @ApiModelProperty("所有取消单")
    public int totalCount = 0;
    @ApiModelProperty("撤销取消单")
    public int cacelCount = 0;
    @ApiModelProperty("退货取消单")
    public int returnCount = 0;
    @ApiModelProperty("驳回取消单")
    public int rejectCount = 0;
}
