package com.danding.cds.suningFP.api.dto;

import com.danding.logistics.api.common.page.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class SuNingFPLoadDetailSearch extends Page implements Serializable {
    private static final long serialVersionUID = 9144768962087206680L;

    private Long loadId;

    @ApiModelProperty("申报单号")
    private String declareOrderNo;

    @ApiModelProperty("创建时间From")
    private Long createFrom;

    @ApiModelProperty("创建时间To")
    private Long createTo;

    @ApiModelProperty("修改时间From")
    private Long updateFrom;

    @ApiModelProperty("修改时间To")
    private Long updateTo;
}
