package com.danding.cds.customs.logistics.api.dto;

import com.danding.logistics.api.common.page.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class LogisticsSearch extends Page {

    @ApiModelProperty("快递")
    private Long expressId;

    @ApiModelProperty("申报状态")
    private Integer status;

    @ApiModelProperty("物流企业")
    private String logisticsCompanyId;
    @ApiModelProperty("口岸")
    private String customs;

    private String queryType;

    private String queryInfo;

    @ApiModelProperty("创建时间 - > 开始")
    private Long beginCreateTime;

    @ApiModelProperty("创建时间 - > 结束")
    private Long endCreateTime;

    @ApiModelProperty("申报处理时间 - > 开始")
    private Long beginLastDeclareTime;

    @ApiModelProperty("申报处理时间 - > 结束")
    private Long endLastDeclareTime;

    @ApiModelProperty("申报成功时间 - > 开始")
    private Long beginFinishDeclareTime;

    @ApiModelProperty("申报成功时间 - > 结束")
    private Long endFinishDeclareTime;
}
