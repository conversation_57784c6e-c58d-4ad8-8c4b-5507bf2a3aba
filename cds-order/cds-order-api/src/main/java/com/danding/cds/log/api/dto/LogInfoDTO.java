package com.danding.cds.log.api.dto;

import com.danding.cds.log.api.enums.LogCode;
import lombok.Data;

import java.util.Date;
@Data
public abstract class LogInfoDTO {
    private Long id;
    private String declareNo;
    private String sn;
    private String oldStatus;
    private String newStatus;
    private String operDetail;
    private String content;
    private Integer hasXmlMessage=0;
    private Date createTime;
    private Date updateTime;
    private Integer createBy;
    private Integer updateBy;
    private Boolean deleted = false;
    private String xmlReqeust;
    private String xmlResponse;
    public abstract LogCode getLogCode();
}
