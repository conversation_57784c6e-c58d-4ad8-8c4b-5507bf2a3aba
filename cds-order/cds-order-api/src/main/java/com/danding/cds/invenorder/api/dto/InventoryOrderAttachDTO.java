package com.danding.cds.invenorder.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class InventoryOrderAttachDTO implements Serializable {
    private static final long serialVersionUID = -2433523850499947700L;
    @ApiModelProperty("主键ID")
    private Long id;
    /**
     * 清关单ID
     */
    @ApiModelProperty("清关单ID")
    private Long refInveOrderId;
    /**
     *清关单SN
     */
    @ApiModelProperty("清关单SN")
    private String  refInveOrderSn;
    /**
     * 附件名称
     */
    @ApiModelProperty("附件名称")
    private String attachName;

    /**
     * 存储文件名
     */
    @ApiModelProperty("存储文件名")
    private String storeName;
    /**
     * 存储路径
     */
    @ApiModelProperty("存储路径")
    private String attachPath;

    @ApiModelProperty("文档类型")
    private String contentType;
    /**
     * 附件类型 0-其他 1-箱单/发票/合同
     */
    private String attachType;

    /**
     * 附件来源
     */
    private Integer source;

    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("更新时间")
    private Date updateTime;
    @ApiModelProperty("创建人ID")
    private Integer createBy;
    @ApiModelProperty("更新人ID")
    private Integer updateBy;
    @ApiModelProperty("逻辑删除")
    private Boolean deleted = false;
}
