package com.danding.cds.customs.refund.api.enums;

public enum RefundPartFlagEnum {
    REFUND_PART_NO(0,"否"),
    REFUND_PART_YES(1,"是");

    private Integer value;
    private String desc;
    RefundPartFlagEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }
    public String getDesc() {
        return desc;
    }

    public static RefundPartFlagEnum getEnum(Integer value) {
        for (RefundPartFlagEnum status : RefundPartFlagEnum.values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }
}
