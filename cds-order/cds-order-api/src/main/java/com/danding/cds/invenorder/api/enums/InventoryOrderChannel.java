package com.danding.cds.invenorder.api.enums;

/**
 * @Author: Raymond
 * @Date: 2020/11/19 18:05
 * @Description:
 */
public enum InventoryOrderChannel {
    NULL(0, "空"),
    LOGISTICS(1, "ERP"),
    CCS_SELF(2, "CCS"),
    WMS(3, "WMS"),
    TAO_TIAN(4, "淘天"),
    ;


    private Integer value;

    private String desc;

    InventoryOrderChannel(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static InventoryOrderChannel getEnum(Integer value) {
        for (InventoryOrderChannel orderChannel : InventoryOrderChannel.values()) {
            if (orderChannel.getValue().equals(value)) {
                return orderChannel;
            }
        }
        return NULL;
    }

    public static String getDesc(Integer value) {
        for (InventoryOrderChannel orderChannel : InventoryOrderChannel.values()) {
            if (orderChannel.getValue().equals(value)) {
                return orderChannel.getDesc();
            }
        }
        return NULL.getDesc();
    }
}