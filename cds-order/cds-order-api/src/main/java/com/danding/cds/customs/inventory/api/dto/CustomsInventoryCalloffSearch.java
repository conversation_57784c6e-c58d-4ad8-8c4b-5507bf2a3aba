package com.danding.cds.customs.inventory.api.dto;

import com.danding.cds.common.annotations.UcRoleAccountBookIdList;
import com.danding.logistics.api.common.page.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: Raymond
 * @Date: 2020/10/14 9:42
 * @Description:
 */
@Data
public class CustomsInventoryCalloffSearch extends Page implements Serializable {

    @ApiModelProperty("订单标记")
    private Integer orderTag;

    @ApiModelProperty("剔除标记->待办标记")
    private String label;

    @ApiModelProperty("区内企业")
    private Integer areaCompanyId;

    /**
     * 清关状态
     */
    @ApiModelProperty("清关状态")
    private String customsStatus;

    /**
     * 出区状态
     */
    @ApiModelProperty("出区状态")
    private Integer exitRegionStatus;

    /**
     * 单据状态
     * （原 取消状态）
     */
    @ApiModelProperty("单据状态")
    private String calloffStatus;

    /**
     * 取消类型
     */
    @ApiModelProperty("取消类型")
    private Integer calloffType;

    /**
     * 租户id
     */
    @ApiModelProperty("租户id")
    private String tenantId;

    /**
     * 用户名称
     */
    @ApiModelProperty("用户名称")
    private String userName;


    @Deprecated
    @ApiModelProperty("操作人")
    private String oper;


    @ApiModelProperty("清关企业ID")
    private Long agentCompanyId;

    @ApiModelProperty("电商企业ID")
    private Long ebcId;

    @ApiModelProperty("开始时间-创建")
    private Long beginCreateTime;

    @ApiModelProperty("结束时间-创建")
    private Long endCreateTime;

    //完成时间（原 取消时间）
    @ApiModelProperty("开始时间-取消")
    private Long beginCalloffTime;

    //完成时间（原 取消时间）
    @ApiModelProperty("结束时间-取消")
    private Long endCalloffTime;

    @ApiModelProperty("单号类型：0申报单号 1清单编号 2运单号")
    private Integer noType;

    @ApiModelProperty("单号")
    private String noStr;

    @ApiModelProperty("角色账册ID列表")
    @UcRoleAccountBookIdList
    private List<Long> roleAccountBookIdList;

    /**
     * 超时时间(取消)
     * 当前时间 - 创建时间 && 取消单状态 {待取消、取消中}
     */
    private Integer cancelTimeOut;

    /**
     * 超时类型
     * 撤单待取消超24h
     * cancelCalloffWaiting12hTo24h
     * 撤单待取消超24h
     * cancelCalloffWaiting24hAndMore
     * 退货待取消超7d
     * refundCalloffWaiting7dTo10d
     * 退货待取消超10d
     * refundCalloffWaiting10dAndMore
     * 取消中超24h
     * calloffing24hTo36h
     * 取消中超36h
     * calloffing36hAndMore
     */
    private String overTimeSearchType;

    /**
     * erp实体仓编码
     */
    private String erpPhyWarehouseSn;

    /**
     * 逆向实体仓
     */
    private String refundEntityWarehouseCode;

}
