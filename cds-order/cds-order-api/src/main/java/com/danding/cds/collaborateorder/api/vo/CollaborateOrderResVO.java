package com.danding.cds.collaborateorder.api.vo;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 协同单对象
 * @date 2022/3/30
 */
@Data
public class CollaborateOrderResVO implements Serializable {
    private String id;
    /**
     * 协同单号
     */
    private String collaborateSn;

    /**
     * 理货数量
     */
    private int tallyQty;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 理货完成时间
     */
    private String tallyFinishTime;

    //协同类型
    private String inveBusinessType;
    private String inveBusinessTypeDesc;
    /**
     * 协同单状态
     */
    private String collaborateStatus;

    /**
     * 清关单号
     */
    private String inveCustomsSn;

    /**
     * 关联清关单号
     */
    private String associatedInveCustomsSn;
    /**
     * 出入库单号
     */
    private String inOutOrderNo;

    /**
     * 报关单号
     */
    private String customsEntryNo;

    /**
     * 核注清单编号
     */
    private String refHzInveNo;

    /**
     * 预计数量
     */
    private int planDeclareQty;

    /**
     * 实际申报数量
     */
    private int acturalDeclareQty;

    /**
     * 差异数量
     */
    private Integer diffQty;

    /**
     * 申报企业
     */
    private String companyName;

    /**
     * 实体仓名称
     */
    private String warehouseName;

    /**
     * 清关完成时间
     */
    private String customsFinishTime;

    /**
     * 运输费
     */
    private BigDecimal shippingFee;

    /**
     * 托数
     */
    private int fightQty;

    /**
     * 理货报告号
     */
    private String tallyReportSn;

    /**
     * 到库时间
     */
    private Date arrivalTime;

    /**
     * 是否理货回传
     * eg. 是/否
     */
    private String tallyReportReceive;

    /**
     * 提单号
     */
    private String pickUpNo;

    /**
     * 启运国
     */
    private String shipmentCountry;

    /**
     * 货代公司
     */
    private String forwardingCompany;

    /**
     * 集装箱号
     */
    private String conNo;

    /**
     * 品名
     */
    private String productName;

    /**
     * 类目
     */
    private String category;

    /**
     * 预计到港时间
     */
    private Date expectedToPortTime;

    /**
     * 实际到港日期
     */
    private Date actualArrivalDate;

    /**
     * 到货港口/机场
     */
    private String arrivalPort;

    /**
     * 车辆费用备注
     */
    private String vehicleCostRemark;
}
