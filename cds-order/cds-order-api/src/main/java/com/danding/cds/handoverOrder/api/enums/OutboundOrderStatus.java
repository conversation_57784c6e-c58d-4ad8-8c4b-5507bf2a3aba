package com.danding.cds.handoverOrder.api.enums;

public enum OutboundOrderStatus {

    TREAT_OUTBOUND(1,"待出库"),
    ALREADY_OUTBOUND(2,"已出库");



    private Integer value;

    private String desc;

    OutboundOrderStatus(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static OutboundOrderStatus getEnum(Integer value){
        for (OutboundOrderStatus exportOrderStatus : OutboundOrderStatus.values()) {
            if (exportOrderStatus.value == value){
                return exportOrderStatus;
            }
        }
        return TREAT_OUTBOUND;
    }
}
