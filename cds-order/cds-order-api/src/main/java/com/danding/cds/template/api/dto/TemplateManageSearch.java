package com.danding.cds.template.api.dto;

import com.danding.logistics.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @description: 模板管理分页查询参数
 * @date 2025/7/31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("模板管理分页查询参数")
public class TemplateManageSearch extends Page {

    @ApiModelProperty("模板名称（支持模糊查询）")
    private String templateName;

    @ApiModelProperty("用途")
    private String purpose;

    @ApiModelProperty("口岸")
    private String port;

    @ApiModelProperty("上传时间开始")
    private Date uploadTimeStart;

    @ApiModelProperty("上传时间结束")
    private Date uploadTimeEnd;

    @ApiModelProperty("备注（支持模糊查询）")
    private String remark;

    @ApiModelProperty("创建者（支持模糊查询）")
    private String creatorName;
}
