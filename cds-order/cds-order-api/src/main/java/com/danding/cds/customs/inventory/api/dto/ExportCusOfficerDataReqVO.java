package com.danding.cds.customs.inventory.api.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 清单-导出关务人员数据请求参数
 *
 * <AUTHOR>
 */
@Data
public class ExportCusOfficerDataReqVO implements Serializable {

    /**
     * 创建时间
     */
    private Long createTimeFrom;
    private Long createTimeTo;

    /**
     * 账册id
     */
    private Long bookId;

    /**
     * 数据类型: 1 - 人审取消; 2 - 入区货值
     */
    private Integer dataType;
}
