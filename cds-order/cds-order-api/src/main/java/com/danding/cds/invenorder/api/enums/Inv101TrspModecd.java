package com.danding.cds.invenorder.api.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
public enum Inv101TrspModecd {
    NULL("","",""),
    WAY_WATER("2","WATER","水路运输"),
    WAY_RAIL("3","RAIL","铁路运输"),
    WAY_ROAD("4","ROAD","公路运输"),
    WAY_AIR("5","AIR","航空运输"),
    WAY_OTHER("9", "OTHER", "其他运输"),
    WAY_SPECIAL_BONDED_AREA("S", "S", "特殊综合保税区");

    // 海关运输方式编码
    private final String key;
    // 系统内运输方式编码
    private final String value;
    // 中文描述
    private final String desc;

    Inv101TrspModecd(String key, String value, String desc) {
        this.key = key;
        this.value = value;
        this.desc = desc;
    }

    public static Inv101TrspModecd getEnum(String value){
        if(StringUtils.isEmpty(value)){
            return NULL;
        }
        for (Inv101TrspModecd orderStatus : Inv101TrspModecd.values()) {
            if (orderStatus.getValue().equals(value)){
                return orderStatus;
            }
        }
        return NULL;
    }
}
