package com.danding.cds.cull.api.service;


import com.danding.cds.cull.api.dto.CullOrderCountDTO;
import com.danding.cds.cull.api.dto.CullOrderDTO;
import com.danding.cds.cull.api.enums.CullStatusEnums;
import com.danding.cds.cull.api.vo.CullOrderReqVO;
import com.danding.cds.cull.api.vo.CullOrderResVO;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;

import java.util.List;

/**
 * <AUTHOR>
 * @description 剔除单
 * @date 2022/3/29
 */
public interface CullOrderService {


    CullOrderDTO findByCustomsLogisticsSn(String customsLogisticsSn);

    CullOrderDTO findByCustomsLogisticsSnAndStatus(String customsLogisticsSn,CullStatusEnums cullStatusEnums);

    CullOrderDTO findById(Long id);

    ListVO<CullOrderResVO> paging(CullOrderReqVO search);

    void neglect(List<String> ids);

    void create(List<String> customsLogisticsSn)throws ArgsErrorException;

    String findByStatus(CullStatusEnums statusEnum);

    List<CullOrderCountDTO> findByStatusCount(CullStatusEnums statusEnum);

    void updStatus(List<String> customsLogisticsSn,CullStatusEnums cullStatusEnums);

    void updFinishStatus(String customsLogisticsSn);

}
