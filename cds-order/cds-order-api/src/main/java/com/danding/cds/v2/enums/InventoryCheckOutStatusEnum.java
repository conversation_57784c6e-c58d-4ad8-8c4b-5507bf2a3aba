package com.danding.cds.v2.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
@Getter
@AllArgsConstructor
public enum InventoryCheckOutStatusEnum {
    REGIN_NOT_NEED(0, "无需回传"),
    REGIN_WAIT(100,"待回传"),
    GEN_MSG(200,"消息创建成功"),
    REGIN_SUCCESS(300,"回传成功"),
    REGIN_FAIL(500,"回传失败");

    private Integer code;

    private  String desc;

    public static InventoryCheckOutStatusEnum getEnum(Integer code){
        for (InventoryCheckOutStatusEnum inventoryCheckOutStatusEnum : InventoryCheckOutStatusEnum.values()) {
            if (Objects.equals(inventoryCheckOutStatusEnum.getCode(), code)){
                return inventoryCheckOutStatusEnum;
            }
        }
        return null;
    }
}
