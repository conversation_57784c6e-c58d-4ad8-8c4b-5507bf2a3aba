package com.danding.cds.invenorder.api.dto;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Create 2021/6/17  15:13
 * @Describe
 **/
@Data
public class InventoryOrderMergeParam implements Serializable {

    @ApiModelProperty("逗号隔开的若干个清关单号")
    private String ids;

    @ApiModelProperty("清关单列表，与ids只要有一个填写即可")
    private List<String> idList;

    @ApiModelProperty("备注")
    private String remark;

    public List<String> getIdList() {
        if (CollectionUtils.isEmpty(idList) && !StringUtils.isEmpty(ids)){
            idList = Lists.newArrayList(ids.split(","));
        }
        return idList;
    }
}