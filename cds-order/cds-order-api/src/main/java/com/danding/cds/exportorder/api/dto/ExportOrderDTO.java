package com.danding.cds.exportorder.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class ExportOrderDTO implements Serializable {
    private Long id;

    /**
     * 出区单号
     */
    private String sn;

    /**
     * 出区单状态
     */
    private Integer status;

    /**
     * 快递方式ID
     */
    private String expressList;

    /**
     * 账册ID
     */
    private Long accountBookId;

    private Long declareCompanyId;

    private Date finishTime;

    private Date createTime;

    /**
     * 实体仓编码
     */
    private String entityWarehouseCode;

    /**
     * 创建人
     */
    private Integer createBy;

    /**
     * 账册idList
     */
    private List<Long> bookIdList;

    /**
     * 账册idListJson
     */
    private String bookIdListJson;
}
