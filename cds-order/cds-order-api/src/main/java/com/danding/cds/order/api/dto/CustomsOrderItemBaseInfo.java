package com.danding.cds.order.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: Raymond
 * @Date: 2020/8/13 18:34
 * @Description:
 */
@Data
@ApiModel
public class CustomsOrderItemBaseInfo implements Serializable {

    @ApiModelProperty(value = "SKU")
    private String goodsNo;

    @ApiModelProperty(value = "料号")
    private String recordNo;

    @ApiModelProperty("金二序号")
    private String recordGnum;

    @ApiModelProperty(value = "备案名称")
    private String name;

    @ApiModelProperty(value = "条码")
    private String barcode;

    @ApiModelProperty(value = "HS编码")
    private String hsCode;

    @ApiModelProperty(value = "产销国 原产国")
    private String originCountry;

    @ApiModelProperty(value = "申报单价")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "申报数量")
    private int goodsCount;

    @ApiModelProperty(value = "商品总价")
    private BigDecimal goodsAmount;
}
