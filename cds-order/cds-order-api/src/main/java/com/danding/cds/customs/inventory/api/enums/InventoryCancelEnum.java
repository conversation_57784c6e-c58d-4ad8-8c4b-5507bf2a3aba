package com.danding.cds.customs.inventory.api.enums;

import java.io.Serializable;

public class InventoryCancelEnum implements Serializable {
    public enum STATUS_ENUM {
        NULL("NULL", ""),
        CHECK_STATUS_INIT("INT", "初始化"),
        CHECK_STATUS_WAITING("DECALRING", "申报中"),
        CHECK_STATUS_WAIT("AUDITING", "待总署审核"),
        CHECK_STATUS_PASS("AUDIT_PASS", "审核通过"),
        CHECK_STATUS_REJECT("AUDIT_REJECT", "总署驳回"),
        CHECK_STATUS_FAIL("FAIL", "申报失败"),
        CHECK_STATUS_CANCEL("CANCEL", "取消撤单"),
        CHECK_STATUS_CANCEL_FAIL("CANCEL_FAIL", "撤单失败");
        private String value;
        private String desc;

        STATUS_ENUM(String value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        public void setValue(String value) {
            this.value = value;
        }
        public void setDesc(String desc) {
            this.desc = desc;
        }
        public String getValue() {
            return value;
        }
        public String getDesc() {
            return desc;
        }
    }

    public enum inventoryCancel
    {
        /**
         * 取消
         */
        CANCEL0(CustomsStat.CUSTOMS_FINISH.getValue(), CancelInventoryAction.INVENTORY_CANCEL_APPALY),
        CANCEL1(CustomsStat.CUSTOMS_PASS.getValue(),CancelInventoryAction.INVENTORY_CANCEL_APPALY),
        CANCEL2(CustomsStat.CLEAR_CHECK.getValue(), CancelInventoryAction.INVENTORY_CANCEL_APPALY),
        CANCEL3(CustomsStat.CLEAR_REFUSE.getValue(), CancelInventoryAction.INVENTORY_CANCEL_APPALY),
        CANCEL4(CustomsStat.CUSTOMS_HANG_UP.getValue(), CancelInventoryAction.INVENTORY_CANCEL_APPALY),

        /**
         *可删除
         */
        //CANCEL5(STATUS_ENUM.CHECK_STATUS_FAIL.getValue(),  CancelInventoryAction.INVENTORY_DELETE_APPALY),
        CANCEL6(STATUS_ENUM.CHECK_STATUS_INIT.getValue(),  CancelInventoryAction.INVENTORY_DELETE_APPALY),
        CANCEL7(STATUS_ENUM.CHECK_STATUS_CANCEL.getValue(),  CancelInventoryAction.INVENTORY_DELETE_APPALY);
         private String  statusEnum;
        private CancelInventoryAction operation;

        inventoryCancel(String statusEnum, CancelInventoryAction operation) {
            this.statusEnum = statusEnum;

            this.operation = operation;
        }

    }
    public static STATUS_ENUM getEnum(String value){
        for (STATUS_ENUM status : STATUS_ENUM.values()) {
            if (status.getValue().equals(value)){
                return status;
            }
        }
        return STATUS_ENUM.NULL;
    }
    public static boolean contains(String check_status_enum, CancelInventoryAction operation)
    {
        inventoryCancel[] arrays  =  inventoryCancel.values();
        for(int i =0;i<arrays.length;i++)
        {
            if(arrays[i].operation.equals(operation)) {
                if (arrays[i].statusEnum.equals(check_status_enum)) {
                    return true;
                }
            }
        }
        return  false;
    }
}