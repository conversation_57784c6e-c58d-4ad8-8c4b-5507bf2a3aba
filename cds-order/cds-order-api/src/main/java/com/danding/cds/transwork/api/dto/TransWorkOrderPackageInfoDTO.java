package com.danding.cds.transwork.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: yousx
 * @Date: 2024/03/11
 * @Description:
 */
@Data
@ApiModel
public class TransWorkOrderPackageInfoDTO implements Serializable {

    @ApiModelProperty(value = "总体积")
    private String totalVolume;

    @ApiModelProperty(value = "总重量")
    private String totalWeight;

    @ApiModelProperty(value = "包装明细")
    private List<PackageInfoItem> packageInfoItems;

    @Data
    public static class PackageInfoItem implements Serializable {

        /**
         * 包装单位（1=托盘、2=装箱）
         */
        @ApiModelProperty(value = "包装单位（1=托盘、2=装箱）")
        private String packageType;

        @ApiModelProperty(value = "包装单位（1=托盘、2=装箱）")
        private String packageTypeDesc;

        @ApiModelProperty(value = "包装数量")
        private Long qty;

        @ApiModelProperty(value = "宽度")
        private String width;

        @ApiModelProperty(value = "长度")
        private String length;

        @ApiModelProperty(value = "高度")
        private String height;

        /**
         * 是否叠托（1=是、0=否）
         */
        @ApiModelProperty(value = "是否叠托（1=是、0=否）")
        private String overlayTrayDesc;

        @ApiModelProperty(value = "是否叠托（1=是、0=否）")
        private String overlayTray;

        /**
         * 托盘材质（1=塑料、2=木质（IPPC）、3=木质（无IPPC）、4=纸质）
         */
        @ApiModelProperty(value = "托盘材质（1=塑料、2=木质（IPPC）、3=木质（无IPPC）、4=纸质）")
        private String trayTypeDesc;

        @ApiModelProperty(value = "托盘材质")
        private String trayType;

    }
}
