package com.danding.cds.customs.order.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel
public class CustomsOrderReceive implements Serializable {
    /**
     * 清关状态
     */
    @ApiModelProperty("清关状态")
    private String customsStatus;

    /**
     * 电商平台代码
     */
    @ApiModelProperty("电商平台代码")
    private String ebpCode;

    /**
     * 电商企业代码
     */
    @ApiModelProperty("电商企业代码")
    private String ebcCode;

    /**
     * 申报单号
     */
    @ApiModelProperty("申报单号")
    private String declareOrderNo;

    /**
     * 清关描述
     */
    @ApiModelProperty("清关描述")
    private String customsDetail;

    /**
     * 回执信息
     */
    @ApiModelProperty("回执信息")
    private String responseMsg;

    /**
     * 回执发生时间
     */
    @ApiModelProperty("回执发生时间")
    private Long customsTime;

    @ApiModelProperty("发送方")
    private String sender;
}
