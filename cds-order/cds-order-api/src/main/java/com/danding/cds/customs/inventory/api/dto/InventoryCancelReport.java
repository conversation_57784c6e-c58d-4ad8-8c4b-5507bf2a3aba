package com.danding.cds.customs.inventory.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel
public class InventoryCancelReport  implements Serializable {

    private static final long serialVersionUID = -3030005954432192976L;
    @ApiModelProperty("导入总数")
    private int totalCount;

    @ApiModelProperty("成功数量")
    private int successCount;

    @ApiModelProperty("失败数量")
    private int failCount;

    @ApiModelProperty("成功列表")
    private List<ImportExcelInventoryCancelDTO> successRecordList;

    @ApiModelProperty("失败列表")
    private List<ImportExcelInventoryCancelDTO> failRecordList;
}
