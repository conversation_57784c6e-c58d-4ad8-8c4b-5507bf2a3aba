package com.danding.cds.transwork.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: yousx
 * @Date: 2024/03/12
 * @Description:
 */
@AllArgsConstructor
@Getter
public enum TransTrayTypeEnum {

    NULL("", ""),
    PLASTIC("1", "塑料"),
    WOOD_IPPC("2", "木质（IPPC）"),
    WOOD("3", "木质（无IPPC）"),
    PAPER("4", "纸质"),
    ;
    private final String code;
    private final String desc;


    public static TransTrayTypeEnum getEnum(String code) {
        for (TransTrayTypeEnum transTrayTypeEnum : TransTrayTypeEnum.values()) {
            if (transTrayTypeEnum.getCode().equals(code)) {
                return transTrayTypeEnum;
            }
        }
        return NULL;
    }
}
