package com.danding.cds.order.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: Raymond
 * @Date: 2020/9/9 11:10
 * @Description:
 */
@Data
@ApiModel
public class OrderSimpleSubmitItem implements Serializable {
    private static final long serialVersionUID = -9072934736782574171L;

    // --- 清单商品信息 ---
    /**
     * 商品备案料号
     */
    @ApiModelProperty("商品备案料号")
    private String recordNo;


    /**
     * 商品货号
     */
    @ApiModelProperty("sku")
    @NotBlank(message = "sku不能为空")
    private String skuCode;

    /**
     * 商品品名
     */
    @ApiModelProperty("商品品名")
    private String itemName;

    /**
     * 数量
     */
    @ApiModelProperty("数量")
    @NotNull(message = "数量不能为空")
    @Min(value = 1, message = "数量最小为1")
    private Integer count;

    /**
     * 单价
     */
    @ApiModelProperty("单价")
    @NotNull(message = "单价不能为空")
    private BigDecimal unitPrice;
}
