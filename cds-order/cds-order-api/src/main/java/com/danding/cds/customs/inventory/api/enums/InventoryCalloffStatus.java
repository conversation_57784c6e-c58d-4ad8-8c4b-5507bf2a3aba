package com.danding.cds.customs.inventory.api.enums;

public enum InventoryCalloffStatus {

    START_CANCEL_ORDERS(4,"开始撤单"),
    CANCEL_FAILED(5,"撤单失败"),
    CANCEL_SUCCESS(6,"撤单成功"),
    CLOSE_SUCCESS(1000,"关单成功");
    private Integer code;
    private String desc;
    private InventoryCalloffStatus(Integer code, String desc)
    {
        this.code = code;
        this.desc = desc;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }
    public String getDesc() {
        return desc;
    }
    public static InventoryCalloffStatus getEnum(String value){
        for (InventoryCalloffStatus step : InventoryCalloffStatus.values()) {
            if (step.getCode().equals(value)){
                return step;
            }
        }
        throw new IllegalArgumentException("unknown BondedType.code: " + value);
    }
}
