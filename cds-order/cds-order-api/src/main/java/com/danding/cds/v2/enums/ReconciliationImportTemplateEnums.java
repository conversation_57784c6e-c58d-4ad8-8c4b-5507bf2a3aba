package com.danding.cds.v2.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ReconciliationImportTemplateEnums {

    JD_OUTBOUND_TEMPLATE("IMPORT_RECONCILIATION_ORDER_JD", "京东出区模板", "");

    private final String code;
    private final String desc;
    private final String templateUrl;

    public static ReconciliationImportTemplateEnums getByEnums(String code) {
        for (ReconciliationImportTemplateEnums item : ReconciliationImportTemplateEnums.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }
}
