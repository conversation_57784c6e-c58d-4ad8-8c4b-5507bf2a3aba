package com.danding.cds.invenorder.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum InventoryOrderBusinessEnum {
    BUSSINESS_EMPTY("", "空", InventoryInOutEnum.NULL),
    BUSSINESS_REFUND_INAREA("REFUND_INAREA", "退货入区", InventoryInOutEnum.IN),
    BUSSINESS_SECTION_OUT("SECTION_OUT", "区间流转(出)", InventoryInOutEnum.OUT),
    BUSSINESS_SECTION_IN("SECTION_IN", "区间流转(入)", InventoryInOutEnum.IN),
    BUSSINESS_SECTIONINNER_OUT("SECTIONINNER_OUT", "区内流转(出)", InventoryInOutEnum.OUT),
    BUSSINESS_SECTIONINNER_IN("SECTIONINNER_IN", "区内流转(入)", InventoryInOutEnum.IN),
    BUSSINESS_ONELINE_IN("ONELINE_IN", "一线入境", InventoryInOutEnum.IN),
    BUSSINESS_DESTORY("DESTORY", "销毁", InventoryInOutEnum.OUT),
    BUSINESS_ONELINE_REFUND("ONLINE_REFUND", "一线退运", InventoryInOutEnum.OUT),
    BUSINESS_BONDED_TO_TRADE("BONDED_TO_TRADE", "保税物流转大贸", InventoryInOutEnum.OUT),
    BUSINESS_SUBSEQUENT_TAX("SUBSEQUENT_TAX", "后续补税", InventoryInOutEnum.OUT),
    BUSINESS_BONDED_ONELINE_IN("BONDED_ONELINE_IN", "保税物流一线入境", InventoryInOutEnum.IN),
    BUSINESS_INVENTORY_PROFIT("INVENTORY_PROFIT", "盘盈", InventoryInOutEnum.IN),
    BUSINESS_RANDOM_INSPECTION_DECLARATION("RANDOM_INSPECTION_DECLARATION", "抽检申报", InventoryInOutEnum.OUT),
    BUSINESS_FB_IN("FB_IN", "非保入区", InventoryInOutEnum.IN),
    BUSINESS_FB_OUT("FB_OUT", "非保出区", InventoryInOutEnum.OUT),
    BUSINESS_SIMPLE_PROCESSING("SIMPLE_PROCESSING", "简单加工", InventoryInOutEnum.OUT),
    BUSINESS_BONDED_PROCESSING_ONELINE_IN("BONDED_PROCESSING_ONELINE_IN", "保税加工一线入境", InventoryInOutEnum.IN),
    BUSINESS_BONDED_PROCESSING_ONELINE_OUT("BONDED_PROCESSING_ONELINE_OUT", "保税加工一线出境", InventoryInOutEnum.OUT),
    ;

    private String code;
    private String desc;
    private InventoryInOutEnum type;

    public static InventoryOrderBusinessEnum getEnum(String value) {
        for (InventoryOrderBusinessEnum orderStatus : InventoryOrderBusinessEnum.values()) {
            if (orderStatus.getCode().equals(value)) {
                return orderStatus;
            }
        }
        return BUSSINESS_EMPTY;
    }

    public static String getDesc(String value) {
        for (InventoryOrderBusinessEnum orderStatus : InventoryOrderBusinessEnum.values()) {
            if (orderStatus.getCode().equals(value)) {
                return orderStatus.getDesc();
            }
        }
        return BUSSINESS_EMPTY.getDesc();
    }

    /**
     * 自建单业务类型
     *
     * @return
     */
    public static List<InventoryOrderBusinessEnum> listSelfBusinessType() {
        // 只支持关仓业务类型
        List<InventoryOrderBusinessEnum> onlyCollaborate = Arrays.asList(BUSINESS_FB_IN, BUSINESS_FB_OUT, BUSSINESS_EMPTY);
        return Arrays.stream(InventoryOrderBusinessEnum.values())
                .filter(i -> !onlyCollaborate.contains(i)).collect(Collectors.toList());
    }

    /**
     * 通过渠道获取 需要锁定的业务类型
     *
     * @param channel
     * @return
     */
    public static List<InventoryOrderBusinessEnum> listLockBizTypeByChannel(Integer channel) {
        InventoryOrderChannel channelEnum = InventoryOrderChannel.getEnum(channel);
        switch (channelEnum) {
            case CCS_SELF:
                //区间流转出、区内流转出、销毁、一线退运、保税物流转大贸、后续补税、抽检申报、简单加工
                return Arrays.asList(InventoryOrderBusinessEnum.BUSSINESS_SECTION_OUT,
                        InventoryOrderBusinessEnum.BUSSINESS_SECTIONINNER_OUT,
                        InventoryOrderBusinessEnum.BUSSINESS_DESTORY,
                        InventoryOrderBusinessEnum.BUSINESS_ONELINE_REFUND,
                        InventoryOrderBusinessEnum.BUSINESS_BONDED_TO_TRADE,
                        InventoryOrderBusinessEnum.BUSINESS_SUBSEQUENT_TAX,
                        InventoryOrderBusinessEnum.BUSINESS_RANDOM_INSPECTION_DECLARATION,
                        InventoryOrderBusinessEnum.BUSINESS_SIMPLE_PROCESSING
                );
            case LOGISTICS:
                //区间流转出、区内流转出
                return Arrays.asList(InventoryOrderBusinessEnum.BUSSINESS_SECTION_OUT,
                        InventoryOrderBusinessEnum.BUSSINESS_SECTIONINNER_OUT);
            case WMS:
                //区内流转出
                return Collections.singletonList(InventoryOrderBusinessEnum.BUSSINESS_SECTIONINNER_OUT);
            case TAO_TIAN:
            default:
                return new ArrayList<>();
        }
    }

}
