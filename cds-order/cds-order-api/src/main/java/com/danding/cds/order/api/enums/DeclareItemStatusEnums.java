package com.danding.cds.order.api.enums;

public enum DeclareItemStatusEnums {
    DECLARE_IN(20,"申报中"),
    DECLARE_END(-1,"申报终止"),
    DECLARE_FINISH(100,"申报完成");


    private Integer value;

    private String desc;

    DeclareItemStatusEnums(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static DeclareItemStatusEnums getEnum(Integer value){
        for (DeclareItemStatusEnums orderChannel : DeclareItemStatusEnums.values()) {
            if (orderChannel.getValue().equals(value)){
                return orderChannel;
            }
        }
        return DECLARE_IN;
    }
}
