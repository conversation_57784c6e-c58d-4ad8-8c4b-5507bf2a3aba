package com.danding.cds.transwork.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: yousx
 * @Date: 2024/03/11
 * @Description:
 */
@AllArgsConstructor
@Getter
public enum TransOrderStatusEnum {

    CREATE(100, "已创建"),
    FINISH(200, "已完成"),
    CANCEL(300, "已取消"),
    ;

    private final Integer status;

    private final String desc;

    public static TransOrderStatusEnum getEnum(Integer status) {
        for (TransOrderStatusEnum e : TransOrderStatusEnum.values()) {
            if (e.getStatus().equals(status)) {
                return e;
            }
        }
        return null;
    }

}
