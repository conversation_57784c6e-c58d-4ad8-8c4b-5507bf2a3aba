package com.danding.cds.checklist.api.dto;

import lombok.Data;

import java.util.Date;

@Data
public class ChecklistTrackLogDTO implements java.io.Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 核放单id
     */
    private Long checklistId;

    /**
     * 核放单状态
     */
    private String status;
    private String statusDesc;

    /**
     * 日志描述
     */
    private String logInfo;

    /**
     * 回执详情
     */
    private String callbackDetail;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 租户
     */
    private Long tenantryId;

    /**
     * 创建时间
     */
    private Date createTime;
}
