package com.danding.cds.invenorder.api.dto;

import com.danding.cds.invenorder.api.enums.InventoryOrderChannel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: Raymond
 * @Date: 2020/11/19 18:53
 * @Description:
 */
@Data
public class InvenorderAuditDTO implements Serializable {
    @ApiModelProperty("sn")
    private String inveCustomsSn;

    @ApiModelProperty("业务类型")
    private String inveBusinessType;


    @ApiModelProperty("审核通过:1/驳回:0")
    private Integer opinion;

    @ApiModelProperty("驳回原因")
    private String reason;

    private InventoryOrderChannel channel;
}
