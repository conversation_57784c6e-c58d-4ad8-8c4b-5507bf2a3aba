package com.danding.cds.handoverOrder.api.dto;

import com.danding.cds.exportorder.api.dto.ExportItemWritingReport;
import com.danding.logistics.api.common.page.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2021/12/17
 */
@Data
public class HandoverOrderParam extends Page {
    /**
     * 交接单号
     */
    @ApiModelProperty("交接单号")
    private String handoverSn;

    /**
     * 仓库编码
     */
    @ApiModelProperty("仓库编码")
    private String house;

    /**
     * 出库单号
     */
    @ApiModelProperty("出库单号")
    private String outboundOrder;

    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    private Long createFrom;

    /**
     * 结束时间
     */
    @ApiModelProperty("结束时间")
    private Long createTo;

    /**
     * 运单号
     */
    @ApiModelProperty("运单号")
    private String waybillSn;

    /**
     * 生成出库单状态  (1-待生成 2-已生成) 作废
     * 1-待生成2-部分出库3-全部出库
     */
    @ApiModelProperty("生成出库单状态")
    private String status;


    /**
     * 快递公司编码
     */
    @ApiModelProperty("快递公司编码")
    private String expressCode;

    /**
     * 车辆信息
     */
    @ApiModelProperty("车辆信息")
    private String vehicleInfo;

    /**
     * 账册ID
     */
    @ApiModelProperty("账册ID")
    private Long accountBookId;

    /**
     * 清关企业ID
     */
    @ApiModelProperty("清关企业ID")
    private Long declareCompanyId;

    /**
     * 出库单预览数据
     */
    @ApiModelProperty("出库单预览数据")
    ExportItemWritingReport exportItemWritingReport;

    /**
     * 实体仓编码
     */
    private String entityWarehouseCode;

}
