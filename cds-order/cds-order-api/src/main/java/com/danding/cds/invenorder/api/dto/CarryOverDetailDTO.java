package com.danding.cds.invenorder.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: 分区结转明细
 * @date 2023/4/22 12:28
 */
@Data
public class CarryOverDetailDTO implements Serializable {
    /**
     * sku
     */
    private String sku;
    /**
     * 统一料号
     */
    private String productId;
    /**
     * 条码
     */
    private String barCode;
    /**
     * 名称
     */
    private String name;
    /**
     * 结转数量
     */
    private BigDecimal changeQty;
}
