package com.danding.cds.payinfo.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: Raymond
 * @Date: 2020/8/24 18:06
 * @Description:
 */
@Data
@ApiModel
public class CustomsPaymentSubmit implements Serializable {
    /**
     * 订单ID
     */
    @ApiModelProperty("订单ID")
    @NotBlank(message = "订单ID不能为空")
    private Long orderId;

    /**
     * 申报单系统编号
     */
    @ApiModelProperty("申报单系统编号")
    @NotBlank(message = "申报单系统编号不能为空")
    private Long orderSn;

    /**
     * 订单编号 申报单号
     */
    @ApiModelProperty("订单编号 申报单号")
    @NotBlank(message = "订单编号 申报单号不能为空")
    private String declareOrderNo;


    /**
     * 海关
     */
    @ApiModelProperty("海关编码")
    private String customs;

    /**
     * 支付渠道
     */
    @ApiModelProperty("支付渠道")
    private String channel;

    /**
     * 第三方支付流水号
     */
    @ApiModelProperty("第三方支付流水号")
    private String tradePayNo;

    /**
     * 支付企业
     */
    @ApiModelProperty("支付企业")
    private String payCompany;


    /**
     * 渠道单号|外部单号
     */
    @ApiModelProperty("渠道单号|外部单号")
    private String outOrderNo;

    /**
     * 电商平台
     */
    @ApiModelProperty("电商平台")
    private String eCompany;

    /**
     * 支付订单总价格
     */
    @ApiModelProperty("支付订单总价格")
    private BigDecimal totalAmount;

    /**
     * 总价格-折后
     */
    @ApiModelProperty("总价格-折后")
    private BigDecimal amount;

    /**
     * 物流费-折后
     */
    @ApiModelProperty("物流费-折后")
    private BigDecimal transpartFee;

    /**
     * 商品费用-折后
     */
    @ApiModelProperty("商品费用-折后")
    private BigDecimal commodityFee;

    /**
     * 订购人姓名
     */
    @ApiModelProperty("订购人姓名")
    @NotBlank(message = "订单ID不能为空")
    private String buyerName;

    /**
     * 订购人证件类型
     */
    @ApiModelProperty("订购人证件类型")
    @NotBlank(message = "订单ID不能为空")
    private String buyerIdType;

    /**
     * 订购人身份证号
     */
    @ApiModelProperty("订购人身份证号")
    @NotBlank(message = "订单ID不能为空")
    private String buyerIdNo;

    /**
     * 是否拆单标记
     */
    @ApiModelProperty("是否拆单标记")
    @NotBlank(message = "是否拆单标记不能为空")
    private Integer splitFlag;

    /**
     * 商品信息JSON字符串
     */
    @ApiModelProperty("商品信息JSON字符串")
    private String goodsInfoJson;

    /**
     * 币制编码
     */
    @ApiModelProperty("币制编码")
    private String currencyCode;


    /**
     * 支付时间
     */
    @ApiModelProperty("支付时间")
    @NotBlank(message = "支付时间不能为空")
    private Date payTime;
}
