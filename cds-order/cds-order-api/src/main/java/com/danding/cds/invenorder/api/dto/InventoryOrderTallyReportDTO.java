package com.danding.cds.invenorder.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class InventoryOrderTallyReportDTO implements Serializable {
    private static final long serialVersionUID = -1717838116944999258L;

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("清关单id")
    private Long inveOrderId;

    @ApiModelProperty("清关单号")
    private String inveOrderSn;

    @ApiModelProperty("理货编号")
    private String tallyOrderNo;

    @ApiModelProperty("出库单号")
    private String outBoundNo;

    @ApiModelProperty("理货明细json")
    private String tallyJson;
}
