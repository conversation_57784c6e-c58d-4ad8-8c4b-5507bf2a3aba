package com.danding.cds.checklist.api.enums;


public enum CheckListCustomsStatusEnum {
    NULL("","空"),
    DECLARE("DECLARE","申报中"),
    ERROR("ERROR","异常"),
    //核放单回执
    SAS221_PASS("SAS221_1","通过"),
    SAS221_RENGONG("SAS221_2","转人工"),
    SAS221_TUIDAN("SAS221_3","退单"),
    SAS221_RUKU_SUCCESS("SAS221_Y","入库成功"),
    SAS221_RUKU_FAIL("SAS221_Z","入库失败"),
    //核放单过卡回执
    SAS223_GUOKA("SAS223_1","已过卡"),
    SAS223_WEIGUOKA("SAS223_2","未过卡"),
    //核放单查验处置回执
    SAS224_TUIDAN("SAS224_2","拒绝过卡"),
    SAS224_RUKU_SUCCESS("SAS224_3","卡口放行"),
    //两步申报
    SAS251_PASS("SAS251_1","通过"),
    SAS251_TUIDAN("SAS251_3","退单"),
    SAS251_RUKU_SUCCESS("SAS251_Y","入库成功"),
    SAS251_RUKU_FAIL("SAS251_Z","入库失败"),
    //两步申报过卡
    SAS253_GUOKA("SAS253_1","已过卡"),
    SAS253_WEIGUOKA("SAS253_3","未过卡");

    private String code;

    private String desc;

    CheckListCustomsStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static CheckListCustomsStatusEnum getEnum(String code){
        for (CheckListCustomsStatusEnum value : CheckListCustomsStatusEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return NULL;
    }
}
