package com.danding.cds.customs.inventory.api.service;

import com.danding.cds.customs.inventory.api.dto.*;
import com.danding.cds.order.api.dto.CustomsReceive;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.Response;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @link com.danding.cds.c.api.service.CustomsInventoryCancelService
 */
@Deprecated
public interface CustomsInventoryCancelService {


    /**
     * 获取：初始化+申报中+待总署审核总数
     *
     * @return
     */
    Integer selectCancelInfoCount(String areaCompanyId);

    /**
     * 根据ID查询
     * @param id
     * @return
     */
    CustomsInventoryCancelDTO findById(Long id);

    List<CustomsInventoryCancelDTO> findById(List<Long> idList);


    /**
     * 根据编号查询
     * @param sn
     * @return
     */
    CustomsInventoryCancelDTO findBySn(String sn);

    /**
     * 根据订单id查询对应的撤单记录
     *
     * @param orderId 订单id
     * @return 撤单记录
     */
    CustomsInventoryCancelDTO findByOrderId(Long orderId);

    List<CustomsInventoryCancelDTO> findByOrderId(List<Long> orderId);

    /**
     * 根据清单ID查询
     *
     * @param inventoryId
     * @return
     */
    CustomsInventoryCancelDTO findByInventoryId(Long inventoryId);


    /**
     * 根据invoSn查询
     * @param invoSn
     * @return
     */
    CustomsInventoryCancelDTO findByInvoSn(String invoSn);

    /**
     * 构建撤单数据（预览）
     * @param customsInventoryDTO
     * @param reason
     * @return
     * @throws ArgsErrorException
     */
    CustomsInventoryCancelDTO buildCustomsInventoryCancelDTO(CustomsInventoryDTO customsInventoryDTO,String reason)throws ArgsErrorException;

    /**
     * 批量导入取消订单（支持仅预览）
     * @param list
     * @param isSave
     * @return
     * @throws ArgsErrorException
     */
    InventoryCancelReport importExcel(List<ImportExcelInventoryCancelDTO> list,boolean isSave)throws ArgsErrorException;

    /**
     * 发起清单撤单
     * @param customsInventoryCancelDTO
     */
    void customsInventoryCancelInsert(CustomsInventoryCancelDTO customsInventoryCancelDTO);

    /**
     * 批量发起撤单
     *
     * @param ids
     */
    void declareInventoryCancel(List<Long> ids);

    void doDeclareInventoryCancel(Long id);

    /**
     * 关闭撤单
     * @param customsInventoryCancelDTO
     */
    void cancelInventoryCancelOrder(CustomsInventoryCancelDTO customsInventoryCancelDTO);

    /**
     * 删除撤单
     *
     * @param ids
     * @return
     */
    boolean deleteByIds(List<Long> ids);

    boolean deleteByOrderId(Long orderId);

    /**
     * 分页查询
     *
     * @param search
     * @return
     */
    ListVO<CustomsInventoryCancelDTO> paging(InventoryCancelSearch search);

    /**
     * 批量查询
     * @param search
     * @return
     */
    List<CustomsInventoryCancelDTO> queryList(InventoryCancelSearch search);

    /**
     * 更新状态
     * @param id
     * @param status
     * @param statusTime
     */
    void updateStatus(Long id,String status,Date statusTime);

    /**
     * 更新海关状态
     * @param id
     * @param customsStatus
     * @param customsDetail
     * @param lastCustomsTime
     */
    void updateByCustomsActive(Long id, String customsStatus, String customsDetail, Date lastCustomsTime);

    /**
     * 接受海关撤单回执
     * @param receive
     * @return
     */
    String receive(CustomsReceive receive);

    /**
     * 重新发起撤单申报
     * @param id
     */
    void retryDeclare(Long id);

    /**
     * 检查清单是否已发起过撤单
     *
     * @param orderId
     * @return
     */
    boolean checkCustomsInventoryCancelIsCreate(Long orderId);

    /**
     * 检查是否已经存在撤单
     *
     * @param orderId
     * @return
     */
    boolean checkCustomsInventoryCancelIsExist(Long orderId);

    /**
     * 统计-撤销单个数
     *
     * @param beginTime
     * @param endTime
     * @return
     */
    List<Map<String, Object>> sumInventoryCancelInfo(Date beginTime, Date endTime);

    /**
     * 批量手动审核
     * @param list
     * @return
     */
    Response<Boolean> manualReview(List<Long> list);

    List<CustomsInventoryCancelDTO> getCancelListByDeclareNoList(List<String> declareNoList);

    /**
     * 修改审核状态
     *
     * @param cancelDTO
     */
    Response<Boolean> editReviewStatus(CustomsInventoryCancelDTO cancelDTO);

    Map<String, CancelAndRefundAfterSaleCount> selectCancelDetail(List<String> handlerIdList, Long startTime, Long endTime);

}