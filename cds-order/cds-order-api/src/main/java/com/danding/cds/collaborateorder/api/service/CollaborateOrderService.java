package com.danding.cds.collaborateorder.api.service;


import com.danding.cds.collaborateorder.api.dto.CollaborateOrderCountDTO;
import com.danding.cds.collaborateorder.api.dto.CollaborateOrderDTO;
import com.danding.cds.collaborateorder.api.dto.TallyReportDTO;
import com.danding.cds.collaborateorder.api.enums.CollaborateStatus;
import com.danding.cds.collaborateorder.api.vo.CollaborateOrderReqVO;
import com.danding.cds.collaborateorder.api.vo.CollaborateOrderResVO;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description 协同单
 * @date 2022/3/29
 */
public interface CollaborateOrderService {

    /**
     * 分页查询协同单
     *
     * @param reqVO
     * @return
     */
    ListVO<CollaborateOrderResVO> paging(CollaborateOrderReqVO reqVO);

    List<CollaborateOrderCountDTO> selectCountByPaging(CollaborateOrderReqVO reqVO);

    /**
     * 生成协同单
     *
     * @param inveCustomsSn
     */
    void saveCollaborateOrder(String inveCustomsSn);

    /**
     * 修改运输费,仓库托数
     * @param fightQty
     * @param shippingFee
     * @param inveCustomsSn
     */
    void updateFightQtyAndShippingFeeByInveCustomsSn(String entityInWarehouseName,String entityOutWarehouseName,Integer fightQty, BigDecimal shippingFee, String inveCustomsSn);

    /**
     * 修改协同单状态
     *
     * @param inveCustomsSn
     * @param status
     */
    void updateCollaborateOrderStatus(String inveCustomsSn, CollaborateStatus status);

    void updateCollaborateOrderStatus(Long inventoryOrderId, CollaborateStatus status);

    /**
     * 根据清关单sn查询
     *
     * @param inveCustomsSn
     * @return
     */
    CollaborateOrderDTO selectByInveCustomsSn(String inveCustomsSn);

    /**
     * 协同单接收理货回传
     *
     * @param submit
     */
    void receiveTallyReport(TallyReportDTO submit) throws ArgsErrorException;


    void receiveTallyReportWms(TallyReportDTO submit) throws ArgsErrorException;

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    CollaborateOrderDTO selectByInveCustomsId(String id);

    /**
     * 添加关联清关单
     *
     * @param inveCustomsSn         原清关单号
     * @param associatedInventorySn 关联清关单sn
     */
    void updateAssociatedInventorySn(String inveCustomsSn, String associatedInventorySn);

    /**
     * 查看是否收到理货报告
     *
     * @param inveCustomsSn
     * @return
     */
    List<String> hasReceiveTallyReport(String inveCustomsSn);

    /**
     * 查看是否收到理货报告（批量）
     *
     * @param inveCustomsSnList
     * @return
     */
    List<String> hasAllReceiveTallyReport(List<String> inveCustomsSnList);

    /**
     * 更新协同单差异数量
     *
     * @param diffQty
     * @param collaborateId
     */
    void updateDiffQty(Long collaborateId, int diffQty);


}
