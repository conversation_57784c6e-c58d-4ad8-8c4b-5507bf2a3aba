package com.danding.cds.endorsement.api.service;

import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.endorsement.api.dto.*;
import com.danding.cds.exportorder.api.dto.ExportItemDTO;
import com.danding.cds.invenorder.api.dto.CustomsCompleteCountDTO;
import com.danding.cds.invenorder.api.dto.InvBusinessCountDTO;
import com.danding.cds.invenorder.api.dto.InventoryOrderInfoDTO;
import com.danding.cds.invenorder.api.dto.InventoryOrderItemDTO;
import com.danding.cds.v2.bean.dto.EndorsementGenerateDeclareCallbackDTO;
import com.danding.cds.v2.bean.vo.req.EndorsementEbInvAddParam;
import com.danding.cds.v2.bean.vo.req.EndorsementEbInvDeleteParam;
import com.danding.cds.v2.bean.vo.res.EndorsementEbInvEditResVO;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface EndorsementService {


    /**
     * 核注单统计
     *
     * @return
     */
    InvBusinessCountDTO selectEndorsementBussinessCount(String areaCompanyId);

    Map<String, CustomsCompleteCountDTO> selectEndorsementBussinessCountDetail(List<String> handlerIdList, Long beginTime, Long endTime);

    /**
     * 根据关联单据生成核注清单
     *
     * @param submit
     */
    List<String> generate(EndorsementSubmit submit, Long userId);

    void buildEndorsementFbNote(List<CustomsInventoryDTO> customsInventoryDTOS, Long endorsementId, Long declareCompanyId);

    List<EndorsementItemDTO> preItemView(Long id);

    Map<Long, EndorsementItemDTO> preItemDTOs(EndorsementDTO endorsementDTO, List<ExportItemDTO> itemList);

    EndorsementDTO findById(Long id);

    /**
     * 根据核注清单编号查询
     *
     * @param realOrderNo
     * @return
     */
    EndorsementDTO findByRealOrderNo(String realOrderNo);

    List<EndorsementDTO> findByRealOrderNo(List<String> realOrderNoList);

    /**
     * 通过预录入编号获取数据
     *
     * @param preOrderNo 预录入编号
     * @return
     */
    EndorsementDTO findByPreOrderNo(String preOrderNo);

    void push(Long id) throws ArgsErrorException;

    void push(String sn) throws ArgsErrorException;

    void autoCarryOverOrderPush(String sn) throws ArgsErrorException;

    void temporaryStorage(Long id) throws ArgsErrorException;

    void saveRemark(Long id, String remark) throws ArgsErrorException;

    ListVO<EndorsementDTOV2> paging(EndorsementSearch search);

    void finish(Long id, String realNo, List<String> information, Boolean isManual) throws ArgsErrorException;

    void fillItemByIdStockVerify(List<InventoryOrderItemDTO> itemDTOS, EndorsementDTO old, Long id, String realNo);

    void fillItemById(Long id, String realNo);

    EndorsementDTO findBySn(String businessNo);


    EndorsementDTO findByBusinessNo(String businessNo);

    List<EndorsementDTO> findByBtoBEndorsement(String id,Integer offset,Integer limit);
    EndorsementDTO findByPreNo(String preNo);
    void updateRealNo(Long id,String realNo) throws ArgsErrorException;
    void updatePushStatus(Long id,Integer pushStatus,String pushMsgId,String pushMsg);
    void updatePreNo(Long id,String preNo) throws ArgsErrorException;
    void updateStatus(Long id,String status) throws ArgsErrorException;

    /**
     * 更新核注的状态，同时更新下清关单状态
     *
     * @param id         核注ID
     * @param statusCode 核注状态
     * @throws ArgsErrorException
     */
    void updateEndorsementAndInvOrderStatus(Long id, String statusCode);

    void exceptionAction(Long id) throws ArgsErrorException;
    void updateCustomsStatus(Long id, String status) throws ArgsErrorException;
    void updateInformationDesc(Long id,String informationDesc) throws ArgsErrorException;
    List<EndorsementDTO> listBySns(List<String> sns);
    List<EndorsementDTO> listByExport(Long exportId);
    List<EndorsementDTO> listByInventory(Long inventoryId);

    List<EndorsementDTO> listByStatus(String status);

    /**
     * 根据账册和状态筛选
     *
     * @param status
     * @param
     * @return
     */
    List<EndorsementDTO> listByStatusAndBookIdList(String status, List<Long> accountBookIdList);

    List<EndorsementDTO> listByStatusAndBookIdList(String status, List<Long> accountBookIdList, String businessType);

    List<EndorsementDTO> listByStatusAndBookIdList(String status, List<Long> accountBookIdList, String businessType,Long companyId);

    List<EndorsementItemGoodsDTO> listItemGoodsById(Long id);

    /**
     * 判单当前录入的核注清单编号是否唯一
     * @param realNo
     * @param id
     * @return
     */
    boolean checkRealNoUnqiue(String realNo, Long id);

    List<EndorsementItemDTO> listItemById(Long id);

    List<EndorsementItemDTO> listItemByChecklist(Long checklistId);

    List<EndorsementItemDTO> listItemByItemIds(List<Long> ids);

    List<EndorsementItemDTO> listItemByEndorsementIds(List<Long> ids);

    List<EndorsementItemDTO> listItemByEndorsementSns(List<String> snList);

    List<EndorsementItemDTO> findByIdAndProductId(Long id, String productId);

    void updateItemChecklistById(Long itemId, Long checklistId);

    void discard(Long id) throws ArgsErrorException;

    void discardByInventoryOrderId(Long inventoryOrderId);

    List<EndorsementDTO> findByIdList(List<Long> endorsementIdList);

    List<String> snListByChecklistId(Long id);

    EndorsementItemDTO getEndorsementItem(Long endorsementId, Integer serialNumber) throws ArgsErrorException;

    EndorsementItemDTO findItemById(Long id);

    void synchronizeInventory(String type, String status, String createTime, String endTime);

    /**
     * 手动修改核注单状态
     *
     * @param endorsementDTO 修改后的状态DTO
     */
    void manualUpdStatus(EndorsementDTO endorsementDTO);

    List<EndorsementDTO> findByInventoryId(Long inventoryId);

    void endorsementFinishNotSecondOut(EndorsementDTO old);

    void endorsementFinishNotSecondOut(EndorsementDTO old, String realNo);

    void manualDeletedApply(Long endorsementId);

    void examinedPostProcess(EndorsementDTO endorsementDTO, List<String> information, Boolean isManual);

    /**
     * 一线统计报表查询
     *
     * @param startTime            统计起始时间
     * @param endTime              统计结束时间
     * @param declareCompanyIdList 清关企业id
     * @return
     */
    List<OneLineStatisticsResVO> statisticsOneLineReport(Long startTime, Long endTime, List<Long> declareCompanyIdList);

    List<OrderEndorsementCheckResult.OrderEndorsementCheckDTO> getOrderEndorsementCheckList(Date startTime, Date endTime);

    void retryCallBackERPStatus(String sn, String action);

    void autoCarryOverEndorsementNotify(Integer timeOut);

    List<EndorsementTrackLogDTO> listTrackLogById(Long id);

    EndorsementEbInvEditResVO addEbInvBatch(EndorsementEbInvAddParam addParam, Boolean save);

    EndorsementEbInvEditResVO deleteEbInvBatch(EndorsementEbInvDeleteParam deleteParam, Boolean save);

    void editDetail(EndorsementEditDetailReqVO reqVO);

    void deletedApply(List<Long> idList);

    void deletedApplyReceive(Long id);

    Map<String, Integer> countPagingStatus(EndorsementSearch search);

    void updatePreNoAndSyncEs(Long id, String preOrderNo);

    /**
     * Excel导入核注单
     *
     * @param businessType     业务类型
     * @param declareCompanyId 清关企业ID
     * @param accountBookId    账册ID
     * @param itemDTOList      表体(成品)列表
     * @param relationDTOList  保税电商清单列表
     */
    void importExcel(String businessType, Long declareCompanyId, Long accountBookId,
                     List<EndorsementItemDTO> itemDTOList, List<EndorsementRelationDTO> relationDTOList);

    void updateEndorsementItemSeqCallback(String productId, String goodsSeqNo, String realOrderNo, String invtGNo);

    void insertItemStockListById(Long id);

    /**
     * 批量插入库存流水
     *
     * @param endorsementItemDTOList 核注单明细列表
     * @param endorsementDTO         核注单信息
     * @param realOrderNo            真实核注单号
     */
    void itemStockListBatchInsert(List<EndorsementItemDTO> endorsementItemDTOList, EndorsementDTO endorsementDTO, String realOrderNo);

    void callbackWmsCwFinish(InventoryOrderInfoDTO infoDTO);

    void updateItemByPrimaryKeySelective(EndorsementItemDTO endorsementItemDTO);

    List<EndorsementItemDTO> getItemListSeqNoNonExistById(Long endorsementId);

    JdReceiveOutReginPushMsgDTO getReceiveOutReginPushMsg(com.danding.cds.order.api.dto.OrderDTO orderDTO);

    void createMockPushReceiveOutRegionMsg(EndorsementDTO endorsementDTO, Integer checkOutStatus);

    void receiveGenerateDeclareCallback(EndorsementGenerateDeclareCallbackDTO callbackDTO);

    void updateRemarkBySn(String endorsementRemark, String endorsementSn);

    void taotianCallBackWms(InventoryOrderInfoDTO inventoryOrderInfoDTO);

    List<EndorsementDTO> findByExportOrderId(Long exportOrderId);

    ListVO<EndorsementRelationDTO> pagingRelation(EndorsementEbInvSearch search);

    List<EndorsementRelationDTO> listRelationById(Long endorsementId);

    List<EndorsementRelationDTO> listRelationByInventoryNos(List<String> inventoryNos);

    List<EndorsementDTO> findBySn(List<String> endorsementSnList);
}
