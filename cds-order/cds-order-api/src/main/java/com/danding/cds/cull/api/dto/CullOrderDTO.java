package com.danding.cds.cull.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/19
 */
@Data
public class CullOrderDTO implements Serializable {

    private Long id;


    /**
     * 申报单号
     */
    private String declareOrderNo;

    /**
     * 海关清单编号
     */
    private String customsInventorySn;

    /**
     * 清单编码
     */
    private String inveSn;

    /**
     * 运单编号
     */
    private String customsLogisticsSn;

    /**
     * 快递
     */
    private Long expressId;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 核注状态
     * @link: InventoryReviwStatus
     */
    private Integer reviewStatus;

    /**
     * 撤单完成0否1是
     */
    private Integer finishStatus;

    /**
     * 账册id
     */
    private Long accountBookId;


    /**
     * 区内企业
     */
    private Long areaCompanyId;

    /**
     * 清单放行时间
     */
    private Long customsPassTime;

    /**
     * 剔除时间
     */
    private Date updateTime;
}
