package com.danding.cds.ownerMapping.api.service;

import com.danding.cds.customs.inventory.api.dto.CustomsInventoryCalloffDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryCalloffDTOV2;
import com.danding.cds.invenorder.api.dto.InventoryOrderInfoDTO;
import com.danding.cds.order.api.dto.CustomsCancelOrderMappingDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @Create 2021/7/5  10:29
 * @Describe
 * @link com.danding.cds.c.api.service.OrderOwnerMappingService
 **/
@Deprecated
public interface OrderOwnerMappingService {
    void saveInventoryOwnerMapping(InventoryOrderInfoDTO infoDTO);

    void saveOrderCalloffOwnerMapping(CustomsInventoryCalloffDTO infoDTO);

    List<InventoryOrderInfoDTO> getWareHouseAndOwnerInventoryOrderInfo(List<InventoryOrderInfoDTO> orderInfoDTOS);

    List<CustomsInventoryCalloffDTOV2> getWareHouseAndOwnerInventoryCallOff(List<CustomsInventoryCalloffDTOV2> orderInfoDTOS);

    InventoryOrderInfoDTO getWareHouseAndOwner(InventoryOrderInfoDTO orderInfoDTO);

    List<CustomsCancelOrderMappingDTO> getRefundOrderByOwnerCode(String ownerCode,String entityWarehouseCode, Long timeBegin, Long timeEnd);

    List<CustomsCancelOrderMappingDTO> getCallBackOrderByOwnerCode(String ownerCode,String entityWarehouseCode, Long timeBegin, Long timeEnd);

}
