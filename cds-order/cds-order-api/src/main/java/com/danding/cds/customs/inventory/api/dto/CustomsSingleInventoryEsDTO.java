package com.danding.cds.customs.inventory.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 功能描述:  申报清单列表索引
 * 创建时间:  2021/8/25 5:09 下午
 *
 * <AUTHOR>
 */
@Data
public class CustomsSingleInventoryEsDTO implements Serializable {

    private String id;
    private long createTime;
    private long updateTime;
    private int openStatus;

    @ApiModelProperty("清单编号")
    private String sn;

    @ApiModelProperty("订单ID")
    private String orderId;

    @ApiModelProperty("申报单号")
    private String declareOrderNo;

    @ApiModelProperty("运单号")
    private String logisticsNo;

    @ApiModelProperty("清单编号")
    private String inventoryNo;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty(value = "海关状态")
    private String customsStatus;

    @ApiModelProperty(value = "申报企业")
    private Long agentCompanyId;

    @ApiModelProperty(value = "账册ID")
    private Long accountBookId;

    @ApiModelProperty("账册编号")
    private String accountBookNo;

    @ApiModelProperty(value = "电商企业")
    private Long ebcId;

    @ApiModelProperty(value = "电商平台")
    private Long ebpId;

    @ApiModelProperty("区内企业")
    private Long areaCompanyId;

    @ApiModelProperty(value = "出区状态")
    private String exitRegionStatus;

    @ApiModelProperty("状态描述")
    private String statusDesc;

    @ApiModelProperty("出区状态描述")
    private String exitRegionStatusDesc;

    @ApiModelProperty(value = "清关回执")
    private String customsStatusDesc;

    @ApiModelProperty(value = "清关回执详情")
    private String customsDetail;

    @ApiModelProperty(value = "申报时间")
    private Long declareTime;

    @ApiModelProperty(value = "回执时间")
    private Long receiveTime;

    @ApiModelProperty("申报成功时间")
    private Long customsPassTime;

    @ApiModelProperty("最后一次申报时间")
    private Date lastDeclareTime;

    @ApiModelProperty("最后一次清关回执时间")
    private Date lastCustomsTime;

    @ApiModelProperty("电商企业")
    private String ebcName;

    @ApiModelProperty(value = "电商平台")
    private Long ebpName;

    @ApiModelProperty("申报企业")
    private String agentCompanyName;

    @ApiModelProperty("区内企业")
    private String areaCompanyName;

    @ApiModelProperty("用户")
    private Long userId;

    @ApiModelProperty("售后状态")
    private Integer afterSalesStatus;

    @ApiModelProperty("核注状态")
    private Integer reviewStatus;

    /**
     * 是否关联交接单
     * 0:否 1:是
     * {@link com.danding.cds.v2.enums.InventoryHandoverStatusEnums}
     */
    @ApiModelProperty(name = "是否关联交接单")
    private Integer handoverStatus;

    /**
     * 租户名称
     */
    @ApiModelProperty(name = "租户名称")
    private String tenantName;
    /**
     * 租户ID
     */
    @ApiModelProperty(name = "租户ID")
    private String tenantOuterId;

    /**
     * 快递方式ID
     */
    private Long expressId;
    private String expressName;

    /**
     * 物流企业
     */
    private Long logisticsCompanyId;
    private String logisticsCompanyName;

    /**
     * 预录入编号
     */
    private String preNo;

    /**
     * 担保企业
     */
    private Long assureCompanyId;
    private String assureCompanyName;

    /**
     * 关区口岸
     */
    private String customs;


    /**
     * 撤单审核状态
     */
    private String cancelStatus;
    /**
     * 退货审核状态
     */
    private String refundStatus;
    /**
     * 撤单创建时间
     */
    private Long cancelCreateTime;
    /**
     * 退货创建时间
     */
    private Long refundCreateTime;

    /**
     * 电子税单状态
     */
    private Integer taxBillStatus;

    /**
     * 出区状态
     */

    private Integer checkOutStatus;

    /**
     * 出区时间
     */
    private Long checkOutTime;

    /**
     * 仓库名称
     */
    private String erpPhyWarehouseName;

    private String erpPhyWarehouseSn;

    private String containFbGifts;
}
