package com.danding.cds.invenorder.api.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class CustomsCompleteCountDTO implements Serializable {

    /**
     * 用户名
     */
    private String userName;

    /**
     * 一线入境
     */
    private Integer oneLineIn;

    /**
     * 区间流转入
     */
    private Integer sectionIn;

    /**
     * 区间流转出
     */
    private Integer sectionOut;

    /**
     * 区内流转入
     */
    private Integer sectionInnerIn;

    /**
     * 区内流转出
     */
    private Integer sectionInnerOut;

    /**
     * 退货入区
     */
    private Integer refundInArea;

    /**
     * 销毁
     */
    private Integer destroy;
}
