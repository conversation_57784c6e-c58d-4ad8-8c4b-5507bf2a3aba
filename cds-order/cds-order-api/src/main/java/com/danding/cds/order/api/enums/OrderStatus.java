package com.danding.cds.order.api.enums;

/**
 * @Author: gxj
 * @Date: 2019/06/21 17:30
 * @Description:
 */
public enum OrderStatus {
    NULL(0,"空"),
    DEC_WAIT(10,"待申报"),
    DEC_ING(20,"申报中"),
    DEC_SUCCESS(30,"申报成功"), // 保税：海关审核中 完税：仓库推送中
    DEC_FAIL(-40,"申报失败"),
    CANCEL(-10,"订单取消"),
    DISCARD(-20,"作废");

    private Integer value;

    private String desc;

    OrderStatus(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static OrderStatus getEnum(Integer value){
        for (OrderStatus orderStatus : OrderStatus.values()) {
            if (orderStatus.getValue() == value){
                return orderStatus;
            }
        }
        return NULL;
    }
}
