package com.danding.cds.customs.refund.api.enums;

public enum CustomsRefundOrderStatus {
    NULL("","空"),
    SEND_WAIT("1","电子口岸已暂存"),
    SEND_ING("2","电子口岸申报中"),
    SEND_SUCCESS("3","发送海关成功"),
    SEND_FAIL("4","发送海关失败"),
    CUSTOMS_PASS("800", "放行"),
    CUSTOMS_REFUSE("100", "海关退单"),
    CUSTOMS_RECEIVE("120", "海关入库"),
    CUSTOMS_PERSON("300", "人工审核"),
    CUSTOMS_FINISH("399", "海关审结"),
    PAYMENT_BOOK("400", "原清单已经生成缴款书"),
    NOT_ENTERED("410", "货未到区内"),
    NOT_ARRIVED("420", "未运抵"),
    UNKNOWN_ADDRESS("430", "收件地址不详"),
    DECLARATION_ERROR("440", "企业申报错误"),
    DAMAGED("450", "影响二次销售"),
    DEAL_EXCEPTION("-1", "处理异常");

    private String value;

    private String desc;

    CustomsRefundOrderStatus(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static CustomsRefundOrderStatus getEnum(String value){
        if (Integer.parseInt(value) < 0){
            return DEAL_EXCEPTION;
        }
        for (CustomsRefundOrderStatus orderStatus : CustomsRefundOrderStatus.values()) {
            if (orderStatus.getValue().equals(value)){
                return orderStatus;
            }
        }
        return NULL;
    }
}
