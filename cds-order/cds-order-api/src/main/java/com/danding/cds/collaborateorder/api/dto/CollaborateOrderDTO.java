package com.danding.cds.collaborateorder.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * <AUTHOR>
 * @description 协同单对象
 * @date 2022/3/30
 */
@Data
public class CollaborateOrderDTO implements Serializable {

    private Long id;
    /**
     * 协同单号
     */
    private String collaborateSn;

    /**
     * 理货数量
     */
    private int tallyQty;

    /**
     * 差异数量(总)
     */
    private int diffQty;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 理货完成时间
     */
    private Date tallyFinishTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    //协同类型
    private String inveBusinessType;
    /**
     * 协同单状态
     */
    private String collaborateStatus;

    /**
     * 清关单号
     */
    private String inveCustomsSn;

    /**
     * 关联清关单号
     */
    private String associatedInveCustomsSn;

    /**
     * 业务单号
     */
    private String inOutOrderNo;

    /**
     * 外部单号
     */
    private String upstreamNo;

    /**
     * 报关单号
     */
    private String customsEntryNo;

    /**
     * 核注清单编号
     */
    private String refHzInveNo;

    /**
     * 清关单Id
     */
    private String inveId;

    /**
     * 预计数量
     */
    private int planDeclareQty;

    /**
     * 申报企业
     */
    private String companyName;

    /**
     * 申报企业id
     */
    private Long inveCompanyId;

    /**
     * 实体仓名称
     */
    private String warehouseName;

    /**
     * 备注
     */
    private String remark;

    private Date inveFinishTime;


    /**
     * 运输费
     */
    private BigDecimal shippingFee;

    /**
     * 托数
     */
    private int fightQty;

    /**
     * 理货报告号
     */
    private String tallyReportSn;

    /**
     * 到库时间
     */
    private Date arrivalTime;


    /**
     * 状态流 JSON格式保存
     */
    private String stateFlow;

    /**
     * 清关完成时间
     */
    private Date inventoryFinishTime;

    /**
     * 转入实体仓
     */
    private String entityInWarehouseName;

    /**
     * 转出实体仓
     */
    private String entityOutWarehouseName;
}
