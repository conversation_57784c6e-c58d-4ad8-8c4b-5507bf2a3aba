package com.danding.cds.order.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Create 2021/4/20  14:22
 * @Describe
 **/
@AllArgsConstructor
@NoArgsConstructor
public enum OrderDeclareEntryType {
    /**
     * 空枚举
     */
    NULL(0, "空"),
    IMPORT_DECLARE(10, "进口报关单"),
    ENTRY_DECLARE(20, "进境备案清单"),
    IMPORT_TWO_STEP_DECLARE(30, "进口两步申报报关单"),
    IMPORT_TWO_STEP_RECORD_DECLARE(40, "进口两步申报备案清单"),
    EXIT_RECORD_DECLARE(50, "出境备案清单"),
    EXPORT_DECLARE(60, "出口报关单"),
    ;

    @Getter
    private Integer value;
    @Getter
    private String desc;

    public static OrderDeclareEntryType getEnums(Integer value) {
        for (OrderDeclareEntryType o : OrderDeclareEntryType.values()) {
            if (o.getValue().equals(value)) {
                return o;
            }
        }
        return NULL;
    }
}
