package com.danding.cds.v2.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 核放单授权状态枚举
 */
@Getter
@AllArgsConstructor
public enum ChecklistAuthCancelStatusEnum {

    NULL(null, ""),
    NOT_SEND(0, "未发起"),
    SENT(1, "已发起"),
    FAIL(2, "取消失败");


    private final Integer code;
    private final String desc;

    public static ChecklistAuthCancelStatusEnum getEnum(Integer code) {
        for (ChecklistAuthCancelStatusEnum enums : ChecklistAuthCancelStatusEnum.values()) {
            if (Objects.equals(enums.getCode(), code)) {
                return enums;
            }
        }
        return NULL;
    }
}
