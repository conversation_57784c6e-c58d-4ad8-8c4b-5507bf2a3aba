package com.danding.cds.jieztech;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class JieztechDeclareInventoryReq extends JieztechCommonReq {
    /**
     * 账册号
     */
    private String emsNo;
    /**
     * 清关订单号
     */
    private String orderNo;
    /**
     * 物流订单号
     */
    private String logisticsNo;
    /**
     * 物流企业代码
     */
    private String logisticsCode;
    /**
     * 物流进境申报企业编(上海必填)
     * 码
     */
    private String customDeclco;
    /**
     * 物流企业名称
     */
    private String logisticsName;
    /**
     * 口岸代码
     */
    private String portCode;
    /**
     * 企业内部编号
     */
    private String copNo;
    /**
     * 担保企业编号
     */
    private String assureCode;
    /**
     * 申报企业代码
     */
    private String agentCode;
    /**
     * 申报企业名称
     */
    private String agentName;
    /**
     * 区内企业代码
     */
    private String areaCode;
    /**
     * 区内企业名称
     */
    private String areaName;
    /**
     * 运输方式
     */
    private String trafMode;
    /**
     * 起运国
     */
    private String country = "142";
    /**
     * 保费
     */
    private String insuredFee;
    /**
     * 毛重
     */
    private String grossWeight;
    /**
     * 净重
     */
    private String netWeight;
    /**
     * 清单商品项
     */
    private List<JieztechDeclareInventoryItem> inventoryList = new ArrayList<>();
}
