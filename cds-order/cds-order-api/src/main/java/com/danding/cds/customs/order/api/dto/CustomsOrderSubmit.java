package com.danding.cds.customs.order.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


@Data
public class CustomsOrderSubmit implements Serializable {

    private String routeCode;

    private String expressCode;

    private String payChannel;

    private String declareOrderNo;

    private String declarePayNo;

    private String tradePayNo;

    private BigDecimal feeAmount = BigDecimal.ZERO;

    private BigDecimal taxAmount = BigDecimal.ZERO;

    private BigDecimal discount = BigDecimal.ZERO;

    private Long tradeTime;

    private String senderName;

    private String buyerIdNumber;

    private String buyerName;

    private String buyerTelNumber;

    private String consigneeProvince;

    private String consigneeCity;

    private String consigneeDistrict;

    private String consigneeStreet;

    private String consigneeAddress;

    private String consigneeEmail;

    private String consigneeTel;

    private String consigneeName;

    private List<CustomsOrderSubmitItem> itemList;

    /**
     * 申报单类型
     * eg；”字节WMS“
     */
    private List<String> declareOrderTypes;


    /**
     * 全局单号
     */
    private String systemGlobalSn;

    /**
     * 外部物流单号 (eg:LP..)
     */
    private String outerLogisticsNo;

    /**
     * wms实体仓编码
     */
    private String wmsEntityWarehouseCode;
}
