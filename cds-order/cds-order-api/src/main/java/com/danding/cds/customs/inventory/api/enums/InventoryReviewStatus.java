package com.danding.cds.customs.inventory.api.enums;

/**
 * 清关完成或交接时：未锁定
 * 出库单创建：已汇总
 * 核注单创建：已锁定
 * 核注终审中（申报中）：终审中
 * 核注终审通过：未核扣
 * 核注出区：已核扣
 */
public enum InventoryReviewStatus {
    UNLINKED_ENDORSEMENT(0, "未锁定"),
    LINKED_REVIEW(1, "已锁定"),
    //    ANOMALY(2,"核注异常"),
    VERIFICATION_COMPLETED(3, "已核扣"),
    PASS_NOT_COMPLETED(4, "未核扣"),

    DECLARING(5, "终审中"),
    LINKED_EXPORT_ORDER(6, "已汇总");

    private Integer value;

    private String desc;

    InventoryReviewStatus(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static InventoryReviewStatus getEnum(Integer value) {
        for (InventoryReviewStatus reviewStatus : InventoryReviewStatus.values()) {
            if (reviewStatus.getValue() == value) {
                return reviewStatus;
            }
        }
        return null;
    }
}
