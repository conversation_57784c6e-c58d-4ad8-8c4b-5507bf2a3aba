package com.danding.cds.transwork.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: yousx
 * @Date: 2024/03/12
 * @Description:
 */
@AllArgsConstructor
@Getter
public enum TransTransportModeEnum {

    NULL("", ""),
    AIR("AIR", "空运"),
    OCEAN("OCEAN", "海运"),
    RAILWAY("RAILWAY", "铁路"),
    TRUCK("TRUCK", "陆运"),
    ;
    private final String code;
    private final String desc;

    public static TransTransportModeEnum getEnum(String code) {
        for (TransTransportModeEnum e : TransTransportModeEnum.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return NULL;
    }
}
