package com.danding.cds.endorsement.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class OneLineStatisticsResVO implements Serializable {

    /**
     * 真实核注清单编号
     */
    private String realOrderNo;

    /**
     * 报关单号
     */
    private String customsEntryNo;

    /**
     * 清关企业ID
     */
    private Long declareCompanyId;

    /**
     * 清关企业名称
     */
    private String declareCompanyName;

    /**
     * 货品明细
     */
    private List<OneLineItemDetail> itemDetails;

    /**
     * 核注完成时间
     */
    private Date finishTime;

    /**
     * 核注创建时间
     */
    private Date createTime;

    @Data
    public static class OneLineItemDetail implements Serializable {
        /**
         * 备案序号
         */
        private String goodsSeqNo;

        /**
         * 商品料号
         */
        private String productId;

        /**
         * 币制code
         */
        private String currency;

        /**
         * 币制名称
         */
        private String currencyDesc;

        /**
         * 申报单价
         */
        private BigDecimal declarePrice;

        /**
         * 最终申报数量
         */
        private BigDecimal declareUnitQfy;

        /**
         * 申报总价
         */
        private BigDecimal declareTotalPrice;

        /**
         * 原产国code
         */
        private String originCountryCode;

        /**
         * 原产国名称
         */
        private String originCountryDesc;
    }

}
