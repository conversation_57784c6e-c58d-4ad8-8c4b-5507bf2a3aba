package com.danding.cds.v2.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BizDeclareFormStatusEnums {

    CREATED("CREATED", "已创建"),

    RECORDING("RECORDING", "备案中"),

    RECORDED("RECORDED", "已备案"),

    FINISHING("FINISHING", "结案中"),

    FINISHED("FINISHED", "已结案"),

    PAUSED("PAUSED", "已暂停"),

    RECORD_EXCEPTION("RECORD_EXCEPTION", "备案异常"),

    FINISH_EXCEPTION("FINISH_EXCEPTION", "结案异常"),

    DISCARD("DISCARD", "已作废"),
    ;

    private final String code;
    private final String desc;

    public static BizDeclareFormStatusEnums getEnums(String code) {
        for (BizDeclareFormStatusEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    public static String getDesc(String code) {
        for (BizDeclareFormStatusEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums.getDesc();
            }
        }
        return "";
    }
}
