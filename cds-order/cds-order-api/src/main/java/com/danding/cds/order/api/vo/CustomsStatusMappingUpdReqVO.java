package com.danding.cds.order.api.vo;

import com.danding.cds.order.api.dto.CustomsStatusMappingDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CustomsStatusMappingUpdReqVO implements Serializable {

    /**
     * id
     */
    private String id;

    /**
     * 申报项，用于区分模块
     */
    private String action;

    /**
     * 海关状态码
     */
    private String customsStatusCode;

    /**
     * 详情状态码
     */
    private String detailCode;


    /**
     * 申报状态
     * PS::对应着申报单据的status
     *
     DEC_ING(20,"等待回执"),
     DEC_SUCCESS(100,"已放行"),
     DEC_FAIL(-1,"申报失败");
     */
    private Integer status;

    /**
     * 回执说明
     */
    private String receiptExplain;

    /**
     * 是否属异常
     */
    private Boolean exceptionFlag;

    /**
     * 备注
     */
    private String remark;

    private Long exceptionId;

    /**
     * 是否终态回执
     */
    private Boolean finalReceiptFlag;

    /**
     * 映射 正则校验List
     */
    private List<CustomsStatusMappingDTO.RegexCheck> regexCheckList;
}
