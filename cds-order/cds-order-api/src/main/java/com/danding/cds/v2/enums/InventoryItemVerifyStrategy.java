package com.danding.cds.v2.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @description: 清关单表体校验策略
 * @date 2022/7/27 17:52
 */
@AllArgsConstructor
@Getter
public enum InventoryItemVerifyStrategy {

    ENDANGERED_STRATEGY("涉濒危", "ENDANGERED", "EndangeredVerifyStrategyService", "涉濒危",
            "此料号的备案系统判定符合备案规则，所以涉濒危", true, new HashSet<>(Arrays.asList("normal", "bondedProcess"))),
    NO_CUSTOMS_BOOK_ITEM("无账册库存", "NO_CUSTOMS_BOOK_ITEM", "", "无账册库存",
            "无账册库存，判定为属于新品的表体", true, new HashSet<>(Collections.singletonList("normal"))),
    MULTI_SEQ_NO("料号多序号", "MULTI_SEQ_NO", "", "料号多序号",
            "存在账册库存，且【账册+海关备案料号】维度下存在多个金二序号", true, new HashSet<>(Collections.singletonList("normal"))),
    MULTI_ORIGIN_COUNTRY("料号多原产国", "MULTI_ORIGIN_COUNTRY", "", "料号多原产国",
            "存在账册库存，且【账册+海关备案料号】维度下存在多个原产国", true, new HashSet<>(Collections.singletonList("normal"))),
    SHARE_GOODS_RECORD("共享备案", "SHARE_GOODS_RECORD", "", "共享备案",
            "商品备案无对应实体仓信息时，仅通过统一料号锁定备案，适用于一线中转", true, new HashSet<>(Arrays.asList("normal", "bondedProcess"))),
    OLD_GOODS_RECORD("旧备案", "OLD_GOODS_RECORD", "", "旧备案",
            "锁定商品备案，且备案的新旧标识是old", true, new HashSet<>(Arrays.asList("normal", "bondedProcess"))),
    NOT_MAIN_SKU_JD_RECORD("非主sku京东备案", "NOT_MAIN_SKU_JD_RECORD", "", "非主sku京东备案",
            "锁定京东备案，依据输入的料号搜京东sku查询备案，当主sku存在时即回填主sku", true, new HashSet<>(Collections.singletonList("jdGoodsRecord"))),
    NO_MERGE_RELATION("无归并关系", "NO_MERGE_RELATION", "", "无归并关系",
            "无归并关系", true, new HashSet<>(Collections.singletonList("mergeRelation"))),
    DIFF_DECLARE_UNIT("申报单位不符", "DIFF_DECLARE_UNIT", "", "申报单位不符",
            "申报单位不符", true, new HashSet<>(Arrays.asList("jdGoodsRecord", "normal"))),
    TRANSPORT_CN_LIMIT_STRATEGY("输华限制", "TRANSPORT_CN_LIMIT", "TransportCNLimitStrategyService", "输华限制",
            "此料号的备案系统判定符合备案规则，所以输华限制", true, new HashSet<>(Collections.singletonList("normal"))),
    INGREDIENT_DEFICIENCY_STRATEGY("成分错误", "INGREDIENT_DEFICIENCY", "IngredientDeficiencyStrategyService", "成分错误",
            "缺少成分字样或成分总和不足100%", true, new HashSet<>(Arrays.asList("normal", "bondedProcess"))),
    DRUG_FACTORS_STRATEGY("涉药成分", "DRUG_FACTORS", "DrugFactorsStrategyService", "涉药成分",
            "涉药成分", true, new HashSet<>(Arrays.asList("normal", "bondedProcess"))),
    NO_PROCESS_BOOK_STRATEGY("无加贸账册", "NO_PROCESS_BOOK", "NoProcessBookStrategyService", "无加贸账册",
            "无加贸账册", true, new HashSet<>(Collections.singletonList("bondedProcess"))),
    FOUR_CATEGORY_GOODS("四类措施商品", "FOUR_CATEGORY_GOODS", "FourCategoryGoodsStrategyService", "四类措施商品",
            "四类措施", true, new HashSet<>(Arrays.asList("normal")))
    ;

    private String name;
    private String code;
    /**
     * springBean对象
     */
    private String beanName;
    private String desc;
    private String note;
    private Boolean enable;
    private Set<String> source;
}
