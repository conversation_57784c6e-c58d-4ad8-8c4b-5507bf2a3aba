package com.danding.cds.promotion.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Author: yousx
 * @Date: 2024/03/14
 * @Description:
 */
@Data
public class TTPromotionExportDTO implements Serializable {

    private Long id;

    private String status;

    private String orderCode;

    private String ownerCode;

    private String ownerName;

    private String warehouseName;

    private String activityType;

    /**
     * 单据来源
     */
    private String orderSource;

    /**
     * 商家的卖家昵称
     */
    private String sellerNick;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 促销类型
     */
    private String marketingType;

    /**
     * 主营行业
     */
    private String categoryName;

    /**
     * 活动开始时间
     */
    private Date activityStartDate;

    /**
     * 活动结束时间
     */
    private Date activityEndDate;

    /**
     * 活动创建时间
     */
    private Date activityCreateDate;


    /**
     * 预估订单金额（单元：元）
     */
    private String predictAmount;

    /**
     * 商家联系人名称
     */
    private String sellerContactName;

    /**
     * 商家联系人电话
     */
    private String sellerContactPhone;

    /**
     * 备注
     */
    private String remark;

    private List<TTPromotionItem> items;

    @Data
    public static class TTPromotionItem implements Serializable {


        private Long refOrderId;

        /**
         * 类型 1-报备主品, 2-报备赠品
         */
        private String type;

        /**
         * 商品编码
         */
        private String itemCode;

        /**
         * 商品名称
         */
        private String itemName;

        /**
         * 商品宝贝链接
         */
        private String itemUrl;

        /**
         * 原价（单位：元）
         */
        private String originalPrice;

        /**
         * 入区价（单位：元）
         */
        private String registerPrice;

        /**
         * 促销价（单位：元）
         */
        private String activityPrice;

        /**
         * 促销数量
         */
        private String activityQuantity;

        /**
         * 海关备案序号
         */
        private String recordNumber;

        /**
         * 条码（多个条码用;分隔）
         */
        private String barCode;
    }


}
