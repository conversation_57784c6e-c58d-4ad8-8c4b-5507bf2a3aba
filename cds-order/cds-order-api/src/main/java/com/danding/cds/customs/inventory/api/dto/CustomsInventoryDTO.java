package com.danding.cds.customs.inventory.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Auther: Dante-Gxj
 * @Date: 2020/5/9 11:42
 * @Description: 海关清单表
 */
@Data
public class CustomsInventoryDTO implements Serializable {

    private static final long serialVersionUID = -3037375954449192976L;
    private Long id;
    private Long orderId;
    private String orderSn;
    private String sn;
    /**
     * 订单编号 申报单号
     */
    private String declareOrderNo;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 清关状态
     */
    private String customsStatus;

    /**
     * 出区状态
     */
    private Integer exitRegionStatus;

    /**
     * 清关回执描述
     */

    private String customsDetail;

    /**
     * 最后一次清关回执时间
     */

    private Date lastCustomsTime;

    /**
     * 最后一次申报时间
     */
    private Date lastDeclareTime;

    /**
     * 电商平台
     */
    private Long ebpId;

    /**
     * 电商企业
     */
    private Long ebcId;

    /**
     * 快递ID
     */
    private Long expressId;

    /**
     * 物流运单编号
     */
    private String logisticsNo;

    /**
     * 物流企业
     */
    private Long logisticsCompanyId;

    /**
     * 预录入编号
     */
    private String preNo;

    /**
     * 担保企业
     */
    private Long assureCompanyId;

    /**
     * 清关企业
     */
    private Long declareCompanyId;

    /**
     * 账册ID
     */
    private Long accountBookId;

    /**
     * 清单编号 申请单编号
     */
    private String inventoryNo;

    /**
     * 关区口岸
     */
    private String customs;

    /**
     * 购买人电话
     */
    private String buyerTelNumber;

    /**
     * 订购人证件类型
     */
    private String buyerIdType;

    /**
     * 订购人证件号码
     */
    private String buyerIdNumber;

    /**
     * 订购人姓名
     */
    private String buyerName;

    /**
     * 收件人地址
     */
    private String consigneeAddress;

    /**
     * 申报企业
     */
    private Long agentCompanyId;

    /**
     * 区内企业
     */
    private Long areaCompanyId;

    /**
     * 运费
     */
    private BigDecimal feeAmount;

    /**
     * 保费
     */
    private BigDecimal insureAmount;

    /**
     * 毛重（公斤）
     */
    private BigDecimal grossWeight;

    /**
     * 净重（公斤）
     */
    private BigDecimal netWeight;

    /**
     * 备注
     */
    private String note;

    /**
     * 海关放行时间
     */
    private Date customsPassTime;
    private String extraJson;

    private Date createTime;
    private Date updateTime;

    private String inveOrderStep;

    private List<CustomsInventoryItemExtraEsDTO> itemExtras;

    /**
     * 用于区分清单申报类型 ADD/EDIT/DELETE
     */
    private String type;

    /**
     * 已申报的次数
     */
    private Integer declareFrequency;
    /**
     * 售后状态:1-暂无,2-发起售后成功 默认状态1
     */
    private Integer afterSalesStatus;

    /**
     * 核注状态:0：未关联核注；1：已关联核注；2：核注异常；3：核注完成 默认为0
     */
    private Integer reviewStatus;

    /**
     * 是否关联交接单
     * 0:否 1:是
     */
    private Integer handoverStatus;

    /**
     * 总税金 单位为分
     */
    private BigDecimal totalTax;

    /**
     * 存在库存占用
     * true 库存占用， false 库存未占用
     */
    private Boolean isOccupiedStock;

    /**
     * 租户id
     */
    private Long tenantryId;

//    /**
//     * 逻辑删除
//     */
//    private Boolean deleted;

    /**
     * 电子税单状态
     */
    private Integer taxBillStatus;

    /**
     * 出区时间
     */
    private Date checkOutTime;

    /**
     * 出区回传状态
     */
    private Integer checkOutStatus;

    /**
     * 总税金 单位为分
     */
    private BigDecimal totalRefundTax;

}
