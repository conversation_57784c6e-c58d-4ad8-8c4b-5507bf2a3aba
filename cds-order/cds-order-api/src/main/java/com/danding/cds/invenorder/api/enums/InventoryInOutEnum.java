package com.danding.cds.invenorder.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description:
 * @date 2022/3/31 13:55
 */
@Getter
@AllArgsConstructor
public enum InventoryInOutEnum {
    NULL("", "空"),
    IN("in", "入区"),
    OUT("out", "出区");

    private String code;
    private String desc;

    public static InventoryInOutEnum getEnum(String code) {
        for (InventoryInOutEnum inventoryInOutEnum : InventoryInOutEnum.values()) {
            if (inventoryInOutEnum.getCode().equals(code)) {
                return inventoryInOutEnum;
            }
        }
        return NULL;
    }
}
