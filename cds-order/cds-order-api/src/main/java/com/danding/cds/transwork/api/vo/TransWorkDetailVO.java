package com.danding.cds.transwork.api.vo;

import com.danding.cds.transwork.api.dto.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Author: yousx
 * @Date: 2024/03/12
 * @Description:
 */
@Data
public class TransWorkDetailVO implements Serializable {

    private Long id;

    /**
     * 运输作业单号
     */
    @ApiModelProperty(value = "运输作业单号")
    private String transNo;

    /**
     * 作业单类型
     */
    @ApiModelProperty(value = "作业单类型")
    private String orderType;


    @ApiModelProperty(value = "作业单类型desc")
    private String orderTypeDesc;

    @ApiModelProperty(value = "作业单状态")
    private Integer status;

    @ApiModelProperty(value = "作业单状态desc")
    private String statusDesc;

    @ApiModelProperty(value = "外部单号")
    private String outOrderNo;

    /**
     * 车辆资源编号
     */
    @ApiModelProperty(value = "车辆资源编号")
    private String vehicleResourceCode;

    /**
     * 车牌号
     */
    @ApiModelProperty(value = "车牌号")
    private String vehiclePlate;

    @ApiModelProperty(value = "车型名称")
    private String vehicleTypeName;

    @ApiModelProperty(value = "可装载集装箱类型")
    private String containerType;

    @ApiModelProperty(value = "可装载集装箱类型")
    private String containerTypeDesc;

    @ApiModelProperty(value = "历史运输次数")
    private Integer historyShipmentsNum;


    /**
     * 运次编号
     */
    @ApiModelProperty(value = "运次编号")
    private String shipmentNo;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String ownerCode;

    @ApiModelProperty(value = "货主名称")
    private String ownerName;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    /**
     * 单据来源
     */
    @ApiModelProperty(value = "单据来源")
    private String orderOrigin;

    @ApiModelProperty(value = "单据来源desc")
    private String orderOriginDesc;
    /**
     * 完成时间
     */
    @ApiModelProperty(value = "完成时间")
    private Date finishTime;

    /**
     * 实际到达时间
     */
    @ApiModelProperty(value = "实际到达时间")
    private Date actArrTime;

    /**
     * 实际发车时间
     */
    @ApiModelProperty(value = "实际发车时间")
    private Date actDepTime;

    /**
     * 实际签收时间
     */
    @ApiModelProperty(value = "实际签收时间")
    private Date actSignTime;


    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "部分基础信息")
    private TransWorkOrderBaseInfoDTO baseInfo;

    @ApiModelProperty(value = "发货人")
    private TransWorkOrderContactsDTO shipperInfo;

    @ApiModelProperty(value = "收货人")
    private TransWorkOrderContactsDTO consigneeInfo;

    @ApiModelProperty(value = "通知人")
    private TransWorkOrderContactsDTO notifyPersonInfo;

    @ApiModelProperty(value = "供应商")
    private TransWorkOrderContactsDTO supplierInfo;

    @ApiModelProperty(value = "商家联系人")
    private TransWorkOrderContactsDTO bookingPartyInfo;

    @ApiModelProperty(value = "包裹信息")
    private TransWorkOrderPackageInfoDTO packageInfo;

    @ApiModelProperty(value = "装箱信息")
    private TransWorkOrderLoadInfoDTO loadInfo;

    @ApiModelProperty(value = "头程承运信息")
    private TransWorkOrderCarrierInfoDTO carrierInfo;

    @ApiModelProperty(value = "装箱明细")
    private List<TransWorkOrderPackagingItemDTO> packagingItems;

    @ApiModelProperty(value = "文件信息")
    private List<TransWorkOrderAttachmentsDTO> attachments;

}
