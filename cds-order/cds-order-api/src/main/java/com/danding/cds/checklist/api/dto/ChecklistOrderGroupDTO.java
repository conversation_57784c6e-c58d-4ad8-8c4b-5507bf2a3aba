package com.danding.cds.checklist.api.dto;

import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
public class ChecklistOrderGroupDTO implements Serializable {
    private static final long serialVersionUID = -3019595318460685957L;

    private Long id;

    /**
     * 核放单Id
     */
    private Long checklistOrderId;

    /**
     * 托盘号
     */
    private String trayNo;

    /**
     * 操作员工号
     */
    private String operatorNo;

    /**
     * 工作台编号
     */
    private String stationNo;
}
