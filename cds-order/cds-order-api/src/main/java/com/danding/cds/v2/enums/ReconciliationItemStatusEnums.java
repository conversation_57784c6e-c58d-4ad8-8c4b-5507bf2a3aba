package com.danding.cds.v2.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

@Getter
@AllArgsConstructor
public enum ReconciliationItemStatusEnums {

    INIT("INIT", "初始化"),
    NORMAL("NORMAL", "正常"),
    EXCEPTION("EXCEPTION", "异常"),
    DEAL("DEAL", "已解决"),
    DISCARD("DISCARD", "作废");

    private final String code;
    private final String desc;

    public static List<ReconciliationItemStatusEnums> listPagingStatus() {
        return Arrays.asList(EXCEPTION, DEAL, DISCARD);
    }

    public static ReconciliationItemStatusEnums getEnums(String code) {
        for (ReconciliationItemStatusEnums status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
