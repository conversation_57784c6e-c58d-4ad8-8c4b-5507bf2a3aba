package com.danding.cds.handoverOrder.api.enums;

/**
 * <AUTHOR>
 * @description
 * @date 2021/12/20
 */
public enum HandoverDetailStatus {

    NO(0,"否"),
    YES(1,"是");



    private Integer value;

    private String desc;

    HandoverDetailStatus(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static HandoverDetailStatus getEnum(Integer value){
        for (HandoverDetailStatus exportOrderStatus : HandoverDetailStatus.values()) {
            if (exportOrderStatus.value == value){
                return exportOrderStatus;
            }
        }
        return NO;
    }
}
