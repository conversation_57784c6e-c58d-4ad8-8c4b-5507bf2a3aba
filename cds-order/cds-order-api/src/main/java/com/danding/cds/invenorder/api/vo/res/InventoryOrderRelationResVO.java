package com.danding.cds.invenorder.api.vo.res;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class InventoryOrderRelationResVO implements Serializable {

    /**
     * 单号总数量
     */
    private Integer totalCount;
    /**
     * 成功数量
     */
    private Integer successCount;
    /**
     * 失败数量
     */
    private Integer errorCount;

    /**
     * 正确单号List
     */
    private List<ResultObj> successList;

    /**
     * 错误单号List
     */
    private List<ResultObj> errorList;

    @Data
    public static class ResultObj implements Serializable {

        /**
         * 关联单号
         */
        private String relNo;

        /**
         * 错误信息
         */
        private String errorMsg;
    }
}
