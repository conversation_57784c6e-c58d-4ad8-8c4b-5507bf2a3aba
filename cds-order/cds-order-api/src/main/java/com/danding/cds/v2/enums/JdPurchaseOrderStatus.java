package com.danding.cds.v2.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description:
 * @date 2022/11/8 10:16
 */
@Getter
@AllArgsConstructor
public enum JdPurchaseOrderStatus {
    NULL("", "空"),
    WAIT_AUDIT("waitAudit", "待关务审核"),
    AUDIT_REJECT("auditReject", "审核驳回"),
    START_PRE_DECLARE("startPreDeclare", "开始清关预申报"),
    WAIT_ENTER("waitEnter", "待入区"),
    WAIT_TALLY("waitTally", "待理货"),
    WAIT_DEFINITE("waitDefinite", "待确报"),
    WAIT_IMPROVE("waitImprove", "待改单"),
    WAIT_RETURN("waitReturn", "待退运"),
    DECLARE_FINISH("DeclareFinish", "清关完成"),
    CANCELED("canceled", "已取消");

    private String code;
    private String desc;

    public static JdPurchaseOrderStatus getEnums(String code) {
        for (JdPurchaseOrderStatus j : JdPurchaseOrderStatus.values()) {
            if (Objects.equals(j.getCode(), code)) {
                return j;
            }
        }
        return NULL;
    }
}
