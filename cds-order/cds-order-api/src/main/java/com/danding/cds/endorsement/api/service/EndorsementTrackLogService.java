package com.danding.cds.endorsement.api.service;

import com.danding.cds.endorsement.api.dto.EndorsementTrackLogDTO;
import com.danding.cds.endorsement.api.dto.EndorsementTrackLogSaveDTO;
import com.danding.cds.endorsement.api.enums.EndorsementOrderStatus;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 核注单日志
 * @date 2023/6/25 16:54
 */
public interface EndorsementTrackLogService {
    void save(EndorsementTrackLogSaveDTO endorsementTrackLogSaveDTO);

    List<EndorsementTrackLogDTO> listTrackLogById(Long id);


    void buildStatusAndInfoLog(Long endorsementId, EndorsementOrderStatus status, String logInfo);

    void buildFullLog(Long endorsementId, EndorsementOrderStatus status, String logInfo, String detail);
}
