package com.danding.cds.invenorder.api.dto;

import com.danding.cds.common.annotations.TrackLogCompare;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 清关单表体 - 草单比对
 */
@Data
public class DraftItemCompareDTO implements Serializable {

    /**
     * 是否存在差异
     */
    private Boolean exitsDiff = false;

    /**
     * 是否存在比对失败
     */
    private Boolean exitsFail = false;

    /**
     * 统一料号（同用户调拨比较维度）
     */
    private String productId;

    /**
     * HS编码 （非同用户调拨比较维度）
     */
    private String hsCode;

    /**
     * 转入表体
     */
    private List<CompareItem> transmitInItem;

    /**
     * 转出表体
     */
    private List<CompareItem> transmitOutItem;

    @Data
    public static class CompareItem implements Serializable {

        /**
         * id
         */
        private Long id;

        /**
         * HS编码
         */
        @TrackLogCompare(aliasName = "HS编码")
        private String hsCode;

        /**
         * 原产国（地区）
         */
        @TrackLogCompare(aliasName = "原产国（地区）")
        private String originCountry;

        /**
         * 商品名称
         */
        private String goodsName;

        /**
         * 申报单位
         */
        @TrackLogCompare(aliasName = "申报单位")
        private String unit;

        /**
         * 申报数量
         */
        @TrackLogCompare(aliasName = "申报数量")
        private BigDecimal declareUnitQfy;

        /**
         * 法定数量（总）
         */
        @TrackLogCompare(aliasName = "法定数量（总）")
        private BigDecimal firstUnitQfy;

        /**
         * 法定计量单位
         */
        private String firstUnit;

        /**
         * 第二法定数量（总）
         */
        @TrackLogCompare(aliasName = "第二法定数量（总）")
        private BigDecimal secondUnitQfy;

        /**
         * 法定第二计量单位
         */
        private String secondUnit;

        /**
         * 币制
         */
        @TrackLogCompare(aliasName = "币制")
        private String currency;

        /**
         * 申报单价
         */
        @TrackLogCompare(aliasName = "申报单价")
        private BigDecimal declarePrice;

        /**
         * 申报总价
         */
        private BigDecimal declareTotalPrice;

        /**
         * 单据来源
         */
        private Integer channel;
    }

}
