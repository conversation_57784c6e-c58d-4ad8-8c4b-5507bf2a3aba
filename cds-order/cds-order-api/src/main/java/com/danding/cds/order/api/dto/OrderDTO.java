package com.danding.cds.order.api.dto;

import com.danding.cds.exception.api.dto.ExceptionTypeDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Date: 2020/05/08 15:29
 * @Auth: Dante-gxj
 * @Description: 申报单DTO
 */
@Data
public class OrderDTO implements Serializable {

    private Long id;

    /**
     * 电商平台
     */
    private Long ebpId;

    private String sn;

    private String customsPaymentSn;

    private String customsOrderSn;

    private String customsLogisticsSn;

    private String customsInventorySn;

    private String systemGlobalSn;

    /**
     * 租户ID
     */
    private String tenantOuterId;

    /**
     * 上游单号
     */
    private String outOrderNo;

    /**
     * 申报单号
     */
    private String declareOrderNo;

    /**
     * 申报订单状态
     */
    private Integer status;

    /**
     * 内部流转状态
     */
    private String internalStatus;

    /**
     * 1 清关异常标识
     */
    private Boolean exceptionFlag;

    /**
     * 清关异常类型
     */
    private Integer exceptionType;

    /**
     * 清关异常描述
     */
    private String exceptionDetail;

    /**
     * 记录版本 当前生效版本0
     */
    private Long version;

    /**
     * 申报项
     */
    private String actionJson;

    /**
     * json储存的其他属性键值对
     */
    private String extraJson;

    private Date createTime;

    private Date updateTime;
    private Date finishTime;

    /**
     * 回执描述
     */
    private String receiptDescribe;

    /**
     * 异常类型
     */
    private ExceptionTypeDTO exceptionTypeStr;

    /**
     * 海关回执
     */
    private String customsReceipt;

    /**
     * 申报方式记录
     * com.danding.cds.declare.sdk.model.route.RouteDeclareConfig
     */
    private String declareWayRecord;


    /**
     * 0不挂起，1挂起
     */
    private Integer hangUpStatus;

    /**
     * 申报单标记
     */
    private Integer orderTags;

    /**
     * 租户id
     */
    private Long tenantryId;

//    /**
//     * 逻辑删除
//     */
//    private Boolean deleted;

    /**
     * 敏感信息是否加密
     */
    private Boolean isEncrypt;

    /**
     * 回传平台回执标记
     * {@link com.danding.cds.c.api.bean.enums.OrderCallbackPlatformReceiptEnums}
     */
    private Long callbackPlatformReceiptFlag;

    /**
     * 回传平台回执标记
     */
    private Long callbackPlatformReceiptTypeFlag;
}