package com.danding.cds.v2.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 核放单授权状态枚举
 */
@Getter
@AllArgsConstructor
public enum ChecklistAuthStatusEnum {

    NULL(null, ""),
    CREATED("CREATED", "已创建"),
    AUTHORIZING("AUTHORIZING", "授权中"),
    AUTHORIZED_FAIL("AUTHORIZED_FAIL", "授权失败"),
    AUTHORIZED_FINISH("AUTHORIZED_FINISH", "授权完成"),
    AUTHORIZED_CANCEL("AUTHORIZED_CANCEL", "已取消");


    private final String code;
    private final String desc;

    public static ChecklistAuthStatusEnum getEnum(String code) {
        for (ChecklistAuthStatusEnum enums : ChecklistAuthStatusEnum.values()) {
            if (Objects.equals(enums.getCode(), code)) {
                return enums;
            }
        }
        return NULL;
    }
}
