package com.danding.cds.cull.api.vo;

import com.danding.logistics.api.common.page.Page;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/19
 */
@Data
public class CullOrderReqVO extends Page {

    /**
     * 关键词查询类型 declareOrderNo,inventoryNo,logisticsNo
     */
    private String queryType;

    /**
     * 关键词查询关键词
     */
    private String queryInfo;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 核注状态
     * @link: InventoryReviwStatus
     */
    private Integer reviewStatus;

    /**
     * 撤单完成0否1是
     */
    private Integer finishStatus;

    /**
     * 账册id
     */
    private Integer accountBookId;


    /**
     * 区内企业
     */
    private Integer areaCompanyId;



}
