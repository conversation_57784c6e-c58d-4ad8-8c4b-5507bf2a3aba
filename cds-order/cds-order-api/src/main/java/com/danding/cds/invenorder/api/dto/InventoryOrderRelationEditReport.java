package com.danding.cds.invenorder.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 出区订单编辑报告
 */
@Data
@ApiModel
public class InventoryOrderRelationEditReport implements Serializable {

    @ApiModelProperty("提交总数")
    private int totalCount;

    @ApiModelProperty("新增成功")
    private List<InventoryOrderRelationDTO> addSuccessList;

    @ApiModelProperty("新增失败")
    private List<InventoryOrderRelationDTO> addFailList;

    @ApiModelProperty("删除成功")
    private List<InventoryOrderRelationDTO> deleteSuccessList;

    @ApiModelProperty("删除失败")
    private List<InventoryOrderRelationDTO> deleteFailList;
}
