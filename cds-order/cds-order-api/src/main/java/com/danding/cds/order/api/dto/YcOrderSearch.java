package com.danding.cds.order.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class YcOrderSearch  implements Serializable {
    /**
     * 订单状态
     */
    @ApiModelProperty("订单状态" )
    private Long orderStatus;
    /**
     * 路径
     */
    @ApiModelProperty("路径")
    private String applyPath;

    /**
     * 异常类型
     */
    @ApiModelProperty("异常类型")
    private Integer exceptionType;

    /**
     * 订单号查询，运单号查询，清单号查询
     */
    @ApiModelProperty("订单号查询，运单号查询，清单号查询")
    private String queryNo;

}