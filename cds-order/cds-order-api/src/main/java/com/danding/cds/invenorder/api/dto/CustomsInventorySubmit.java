package com.danding.cds.invenorder.api.dto;

import com.danding.cds.order.api.dto.OrderSubmitItem;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class CustomsInventorySubmit implements Serializable {

    private String declareOrderNo;

    private String routeCode;

    private String expressCode;

    private String logisticsNo;

    private BigDecimal feeAmount = BigDecimal.ZERO;

    private BigDecimal insureAmount = BigDecimal.ZERO;

    private List<OrderSubmitItem> itemList;

    private String buyerTelNumber;

    private String buyerIdNumber;

    private String buyerName;

    private String consigneeProvince;

    private String consigneeCity;

    private String consigneeDistrict;

    private String consigneeAddress;

    private String consigneeTel;

    private String tenantOuterId;

    private String tenantName;

    // ---- 因重庆清单混着订单，加了下面的字段，后续考虑移除 ---
    private String declarePayNo;
    private BigDecimal taxAmount = BigDecimal.ZERO;
    private BigDecimal discount = BigDecimal.ZERO;
    private String payChannel;
}
