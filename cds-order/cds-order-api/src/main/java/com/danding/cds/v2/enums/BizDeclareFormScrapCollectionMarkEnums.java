package com.danding.cds.v2.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 边角料标志枚举
 */
@Getter
@AllArgsConstructor
public enum BizDeclareFormScrapCollectionMarkEnums {

    //I-料件E-成品
    NULL("", ""),
    YES("1", "是");

    private final String code;
    private final String desc;

    public static BizDeclareFormScrapCollectionMarkEnums getEnums(String code) {
        for (BizDeclareFormScrapCollectionMarkEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return NULL;
    }

    public static String getDesc(String code) {
        for (BizDeclareFormScrapCollectionMarkEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums.getDesc();
            }
        }
        return "";
    }

}
