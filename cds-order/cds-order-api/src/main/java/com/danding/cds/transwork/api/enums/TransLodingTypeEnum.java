package com.danding.cds.transwork.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: yousx
 * @Date: 2024/03/12
 * @Description:
 */
@AllArgsConstructor
@Getter
public enum TransLodingTypeEnum {

    NULL("", ""),
    FCL("FCL", "整箱"),
    LCL("LCL", "拼箱"),
    ;
    private final String code;
    private final String desc;

    public static TransLodingTypeEnum getEnum(String code) {
        for (TransLodingTypeEnum transLodingTypeEnum : TransLodingTypeEnum.values()) {
            if (transLodingTypeEnum.getCode().equals(code)) {
                return transLodingTypeEnum;
            }
        }
        return NULL;
    }
}
