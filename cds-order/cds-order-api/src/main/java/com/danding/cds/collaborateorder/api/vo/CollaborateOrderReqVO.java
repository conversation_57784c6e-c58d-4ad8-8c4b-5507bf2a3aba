package com.danding.cds.collaborateorder.api.vo;

import com.danding.logistics.api.common.page.Page;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 协同单对象
 * @date 2022/3/30
 */
@Data
public class CollaborateOrderReqVO extends Page {

    /**
     * 协同单号
     */
    private List<String> collaborateSns;
    /**
     * 协同类型
     */
    private String collaborateType;

    /**
     * 协同单状态
     */
    private String collaborateStatus;

    /**
     * 清关单号
     */
    private String inveCustomsSn;
    private List<String> inveCustomsSnList;

    /**
     * 业务单号
     */
    private List<String> inOutOrderNos;

    /**
     * 核注清单编号
     */
    private String endorsementSn;


    /**
     * 申报企业
     */
    private String agentCompanyId;

    /**
     * 实体仓id
     */
    private String warehouseName;

    /**
     * 创建开始时间
     */
    private String createStaTime;

    /**
     * 创建结束时间
     */
    private String createEndTime;

    /**
     * 完成开始时间
     */
    private String finishStaTime;

    /**
     * 完成结束时间
     */
    private String finishEndTime;

    /**
     * 清关完成时间
     */
    private String invStaFinishTime;

    /**
     * 清关结束时间
     */
    private String invEndFinishTime;

    /**
     * 理货完成时间
     */
    private String tallyStaTime;

    /**
     * 理货结束时间
     */
    private String tallyEndTime;

    /**
     * 搜索范围
     * 1.全部
     * 2.一线入境
     * 3.非一线入境
     */
    private Integer searchType;
}
