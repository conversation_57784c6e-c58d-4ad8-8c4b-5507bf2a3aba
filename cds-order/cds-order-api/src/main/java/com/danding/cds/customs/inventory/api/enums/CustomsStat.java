package com.danding.cds.customs.inventory.api.enums;

import org.springframework.util.StringUtils;

public enum CustomsStat {
    NULL("",""),
    SEND_WAIT("1","电子口岸已暂存"),
    SEND_ING("2","电子口岸申报中"),
    SEND_SUCCESS("3","发送海关成功"),
    SEND_FAIL("4","发送海关失败"),
    CUSTOMS_REFUSE("100","海关退单"),
    CUSTOMS_RECEIVE("120","海关入库"),
    CUSTOMS_PERSON("300","人工审核"),
    CUSTOMS_FINISH("399","海关审结"),
    CUSTOMS_PASS("800", "放行"),
    CUSTOMS_HANG_UP("600", "挂起"),
    CLEAR_FINISH("899", "结关"),
    CLEAR_CHECK("500", "查验"),
    CLEAR_LOCK_TG("501", "扣留移送通关"),
    CLEAR_LOCK_JS("502", "扣留移送缉私"),
    CLEAR_LOCK_FG("503", "扣留移送法规"),
    CLEAR_LOCK_OT("599", "其它扣留"),
    CLEAR_REFUSE("700", "退运"),
    DEAL_EXCEPTION("-1", "处理异常"),
    CUSTOMS_MANUAL_REVIEW("5", "手动审核通过"),

    EXCEPTION_301008("-301008", "处理异常"),
    EXCEPTION_301013("-301013", "处理异常"),
    EXCEPTION_301020("-301020", "处理异常"),
    EXCEPTION_301022("-301022", "处理异常"),
    EXCEPTION_505015("-505015", "处理异常"),
    EXCEPTION_505016("-505016", "处理异常"),
    EXCEPTION_505024("-505024", "处理异常"),
    EXCEPTION_621001("-621001", "处理异常"),
    EXCEPTION_621041("-621041", "处理异常"),
    EXCEPTION_621043("-621043", "处理异常"),
    EXCEPTION_621049("-621049", "处理异常"),
    EXCEPTION_621054("-621054", "处理异常"),
    EXCEPTION_621055("-621055", "处理异常"),
    EXCEPTION_621057("-621057", "处理异常"),
    EXCEPTION_9999("-9999", "处理异常"),
    EXCEPTION_301014("-301014", "处理异常"),
    EXCEPTION_505022("-505022", "处理异常：验签失败"),

    // 浙电的自定义异常，没有用字母的原因是因为 以前都是数字类型的，虽然是字符串，但是某些地方还是存在把value转成int的操作，存在不可预知的风险，会导致报错。所以这里只能取个随机的数字希望后面不要重复
    ZJ_PORT_EXCEPTION("-90749074", "浙江电子口岸回执异常"),
    ;

    private String value;

    private String desc;

    CustomsStat(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static CustomsStat getEnum(String value) {
        if (StringUtils.isEmpty(value)) {
            return NULL;
        }
        for (CustomsStat orderStatus : CustomsStat.values()) {
            if (orderStatus.getValue().equals(value)) {
                return orderStatus;
            }
        }
        if (Integer.parseInt(value) < 0) {
            return DEAL_EXCEPTION;
        }
        return NULL;
    }
}
