package com.danding.cds.exportorder.api.dto;

import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 核放单导入记录
 */
@Data
@ApiModel
public class ExportItemRecord implements Serializable {

    private static final long serialVersionUID = 2875783849132548548L;
    // 导入时必须提交
    @ApiModelProperty("快递公司")
    private String expressName;


    private String tempExpressName;

    @ApiModelProperty("运单号")
    private String mailNo;

    /**
     * 交接单号
     */
    private String handoverSn;

    // 中间存储字段
    @ApiModelProperty("放行清单编号")
    private String customsInventorySn;

    @ApiModelProperty("快递公司ID")
    private Long expressId;

    @ApiModelProperty("净重 合计")
    private BigDecimal netWeight;

    @ApiModelProperty("申报单号|包裹号")
    private String bizId;

    @ApiModelProperty("商品信息")
    private List<ExportSkuInfo> skuInfoList;

    // 返回展示字段

    @ApiModelProperty("行号")
    private int idx;

    @ApiModelProperty("毛重 合计")
    private BigDecimal grossWeight;

    @ApiModelProperty("错误信息")
    private String errorMsg;


    private CustomsInventoryDTO customsInventoryDTO;
}
