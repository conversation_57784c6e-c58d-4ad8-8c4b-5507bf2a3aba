package com.danding.cds.v2.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ReconciliationStatusEnums {
    EXCEPTION("EXCEPTION", "对账异常"),
    FINISH("FINISH", "对账完成"),
    ANALYSIS_ING("ANALYSIS_ING", "解析中"),
    ANALYSIS_FAIL("ANALYSIS_FAIL", "解析失败"),
    DISCARD("DISCARD", "已作废"),
    ;

    private final String code;
    private final String desc;

    public static ReconciliationStatusEnums getEnums(String code) {
        for (ReconciliationStatusEnums item : ReconciliationStatusEnums.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }
}
