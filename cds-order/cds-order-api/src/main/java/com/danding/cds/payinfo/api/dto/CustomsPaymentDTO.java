package com.danding.cds.payinfo.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: Raymond
 * @Date: 2020/8/25 10:33
 * @Description:
 */

@Data
public class CustomsPaymentDTO implements Serializable {
    private Long id;

    /**
     * 所属用户id
     */
    private Long userId;

    /**
     * 支付申报系统编号
     */
    private String sn;

    /**
     * 申报单系统编号
     */
    private String orderSn;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 外部订单号
     */
    private String outOrderNo;

    /**
     * 订单编号 申报单号
     */
    private String declareOrderNo;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 支付渠道编码
     */
    private String channel;

    /**
     * 支付渠道ID
     */
    private Long payChannelId;

    /**
     * 支付企业ID
     */
    private Long payCompanyId;

    /**
     * 电商平台ID
     */
    private Long ebpId;

    /**
     * 商户编码|收款账号
     */
    private String merchantCode;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 外部交易流水号
     */
    private String outTradeNo;

    /**
     * 验核机构名称
     */
    private String verDept;

    /**
     * 订购人姓名
     */
    private String buyerName;

    /**
     * 订购人证件类型
     */
    private String buyerIdType;

    /**
     * 订购人身份证号
     */
    private String buyerIdNo;

    /**
     * 关区口岸
     */
    private String customs;

    /**
     * 第三方支付流水号
     */
    private String tradePayNo;

    /**
     * 支付申报流水号
     */
    private String declarePayNo;

    /**
     * 验核机构交易流水号
     */
    private String payTransactionId;

    /**
     * 支付总价格
     */
    private BigDecimal amount;

    /**
     * 物流费
     */
    private BigDecimal taxFee;

    /**
     * 物流费
     */
    private BigDecimal discountFee;

    /**
     * 物流费
     */
    private BigDecimal transportFee;

    /**
     * 商品费用
     */
    private BigDecimal commodityFee;

    /**
     * 币制编码
     */
    private String currencyCode;

    /**
     * 最后一次回执时间
     */
    private Date lastCustomsTime;

    /**
     * 最后一次申报时间
     */
    private Date lastDeclareTime;

    /**
     * 完成时间
     */
    private Date finishTime;

    /**
     * 订单标签
     */
    private String tagsJson;

    /**
     * 租户ID
     */
    private String tenantId;

    private Date createTime;

    /**
     * 联动子订单
     */
    private String umfJson;

    /**
     * 支付流水号关联申报单号
     * 通联拆单申报
     */
    private String relDeclareOrderNoJson;

    /**
     * 支付商户号(外部)
     */
    private String payMerchantOutNo;
}
