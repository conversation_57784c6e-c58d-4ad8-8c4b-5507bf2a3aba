package com.danding.cds.payinfo.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: Raymond
 * @Date: 2020/8/25 18:39
 * @Description:
 */
@Data
public class CustomsPaymentDeclareDTO implements Serializable {
    private Long id;
    /**
     * 系统编号
     */
    private String sn;

    /**
     * 支付申报主表id
     */
    private Long paymentId;

    /**
     * 支付申报主表系统编号
     */
    private String paymentSn;

    /**
     * 报关流水号
     */
    private String outRequestNo;

    /**
     * 海关
     */
    private String customs;

    /**
     * 申报返回信息
     */
    private String returnMsg;

    /**
     * 商户海关备案号
     */
//    @Column(name = "merchant_customs_code")
//    private String merchantCustomsCode;

    /**
     * 商户海关备案名称
     */
//    @Column(name = "merchant_customs_name")
//    private String merchantCustomsName;

    /**
     * 状态
     */
    private Integer status;

    private String extra;

    private String postMsg;

    private Date returnTime;

    private Date createTime;
}
