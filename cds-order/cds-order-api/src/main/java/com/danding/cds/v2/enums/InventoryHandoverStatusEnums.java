package com.danding.cds.v2.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description:
 * @date 2022/9/23 14:00
 */
@Getter
@AllArgsConstructor
public enum InventoryHandoverStatusEnums {
    NULL(-1, ""),
    NO(0, "否"),
    YES(1, "是");

    private Integer code;
    private String desc;

    public static InventoryHandoverStatusEnums getEnums(Integer code) {
        for (InventoryHandoverStatusEnums i : InventoryHandoverStatusEnums.values()) {
            if (Objects.equals(i.getCode(), code)) {
                return i;
            }
        }
        return NULL;
    }
}
