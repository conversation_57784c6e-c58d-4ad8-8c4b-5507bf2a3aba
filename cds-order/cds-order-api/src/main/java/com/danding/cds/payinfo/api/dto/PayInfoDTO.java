package com.danding.cds.payinfo.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class PayInfoDTO implements Serializable {

    private Long id;

    /**
     * 申报单号
     */
    private String declareOrderSn;

    /**
     * 支付单流水号
     */
    private String tradePayNo;

    /**
     * 交易时间
     */
    private Date tradeTime;

    /**
     * 电商平台
     */
    private Long ebpId;

    /**
     * 支付企业
     */
    private Long payCompanyId;

    /**
     * 验核机构交易流水号
     */
    private String payTransactionId;

    /**
     * 验核机构名称
     */
    private String verDept;

    /**
     * 海关订单支付渠道
     */
    private String payChannel;

    /**
     * 海关订单支付方式-对应海关代码
     */
    private String payWay;

    /**
     * 支付单总金额
     */
    private BigDecimal payTransactionAmount;

    /**
     * 商品信息
     */
    private String itemJson;

    /**
     * 收款企业社会信用代码
     */
    private String recpCode;

    /**
     * 收款企业工商备案名称
     */
    private String recpName;

    /**
     * 收款渠道下的账号
     */
    private String recpAccount;

    /**
     * 支付请求原始数据
     */
    private String payRequestMessage;

    /**
     * 支付返回原始数据
     */
    private String payResponseMessage;
}
