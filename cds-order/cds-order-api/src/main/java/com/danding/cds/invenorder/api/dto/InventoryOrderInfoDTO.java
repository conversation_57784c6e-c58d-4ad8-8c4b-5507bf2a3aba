package com.danding.cds.invenorder.api.dto;

import com.danding.cds.v2.bean.dto.TTClearanceEntryorderCreateDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class InventoryOrderInfoDTO implements Serializable {
    private static final long serialVersionUID = -2433523850499947709L;
    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("清关单SN")
    private String inveCustomsSn;
    /**
     * 清关企业
     */
    @ApiModelProperty("清关企业(第一步骤)")
    private Long inveCompanyId;

//    /**
//     * 单据类型
//     */
//    @ApiModelProperty("单据类型")
//    private String inveOrderType;

    @ApiModelProperty("业务类型")
    private String inveBusinessType;

    @ApiModelProperty("选择区间转出，区内转出的业务类型时 ->关联转入账册字段")
    private String inAccountBook;

    @ApiModelProperty("选择区间转入，区内转入的业务类型时 ->关联转出账册")
    private String outAccountBook;

    @ApiModelProperty("核放单编号")
    private String refCheckOrderNo;
    /**
     * 核注清单编号
     */
    @ApiModelProperty("核注清单编号")
    private String refHzInveNo;

    /**
     * 指出区所需关联的外部入区核注单号
     */
    @ApiModelProperty("关联核注清单编号")
    private String associatedEndorsementNo;

    /**
     * 进出境关别
     */
    @ApiModelProperty("进出境关别")
    private String entryExitCustoms;
    /**
     * 运输方式
     */
    @ApiModelProperty("运输方式")
    private String transportMode;
    /**
     * 启运国
     */
    @ApiModelProperty("启运国")
    private String shipmentCountry;
    /**
     * 租户
     */
    @ApiModelProperty("租户")
    private String rentPerson;

    /**
     * 账册ID
     */
    @ApiModelProperty("账册ID(第一步骤)")
    private Long bookId;
    /**
     * 申请人
     */
    @ApiModelProperty("申请人(第一步骤)")
    private String applyPerson;
    /**
     * 备注
     */
    @ApiModelProperty("备注(第一步骤)")
    private String remark;

    @ApiModelProperty("来源渠道")
    private Integer channel;
    /**
     * 清关单状态
     */
    @ApiModelProperty("清关单状态")
    private String status;

    /**
     * 产品(沈费强)说干掉
     */
    @ApiModelProperty("审核状态")
    private String auditStatus;

    /**
     * 清关单状态对应时间
     */
    @ApiModelProperty("清关单状态对应时间")
    private Date statusTime;


    @ApiModelProperty("预计出区时间")
    private Date expectedOutAreaTime;

    @ApiModelProperty("预计到港时间")
    private Date expectedToPortTime;

    /**
     * 状态:0.停用;1.启用(默认)
     */
    @ApiModelProperty("状态:0.停用;1.启用(默认)")
    private Integer enable;
    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("更新时间")
    private Date updateTime;
    @ApiModelProperty("创建人ID")
    private Integer createBy;
    @ApiModelProperty("更新人ID")
    private Integer updateBy;
    @ApiModelProperty("逻辑删除")
    private Boolean deleted = false;
    @ApiModelProperty("清关单对应的分录信息")
    private List<InventoryOrderItemDTO> listItems;
    @ApiModelProperty("驳回原因")
    private String reason;

    @ApiModelProperty("上游单据类型: ReadyOrder(备货) Distribution(配货)")
    private String channelBusinessType;

    @ApiModelProperty("上游单据编号")
    private String channelBusinessSn;

    /**
     * 没有业务场景了 考虑废弃
     */
    @Deprecated
    @ApiModelProperty("清单类型（指区港联动等）")
    private String customsInvtType;

    @ApiModelProperty("对应报关单号(区港联动时才有)")
    private String customsEntryNo;

    @ApiModelProperty("对应报关企业")
    private Long customsEntryCompany;

    @ApiModelProperty("对应报关单类型")
    private String customsEntryType;

    @ApiModelProperty("主单号")
    private String masterOrderSn;

    @ApiModelProperty("子单号")
    private String subOrderSn;

    @ApiModelProperty("实体仓编码")
    private String entityWarehouseCode;

    @ApiModelProperty("实体仓名称")
    private String entityWarehouseName;

    @ApiModelProperty("WMS仓编码")
    private String wmsWarehouseCode;

    @ApiModelProperty("货主编码/名称")
    private String ownerCode;

    @ApiModelProperty("货主编码/名称")
    private String ownerName;

    /**
     * 核注企业单号
     */
    private String endorsementSn;

    /**
     * 报关标志(一线就默认都是报关，非一线就是非报关)(1:是 2:否)
     */
    private Integer customsFlag;

    /**
     * 是否两步申报(1:是 0:否)（指区港联动等）
     */
    private Integer twoStepFlag;

    /**
     * 是否生成报关单(1:是 0:否)
     */
    private Integer declarationFlag;

    /**
     * 提单号
     */
    @ApiModelProperty("提单号")
    private String pickUpNo;
    // TODO: 2022/3/31 (用作关联核注清单编号 过去式了 上线后修复)

    /**
     * 货代公司
     */
    private String forwardingCompany;

    /**
     * 集装箱号
     */
    private String conNo;

    /**
     * 到货港口/机场
     */
    private String arrivalPort;

    /**
     * 品名
     */
    private String productName;

    /**
     * 类目
     */
    private String category;

    /**
     * 实际到港日期
     */
    private Date actualArrivalDate;

    /**
     * 是否是关仓协同
     */
    private Boolean collaborateFlag;

    /**
     * 质押货主标志
     */
    private Boolean pledgeOwnerFlag;

    /**
     * 邮箱地址
     */
    private String mailAddrList;

    /**
     * 邮件状态
     */
    private Integer mailStatus;

    /**
     * 邮件驳回原因
     */
    private String mailRejectReason;

    /**
     * 调用erp驳回订单接口错误信息
     */
    private String callbackErpRejectOrderErrMsg;

    /**
     * 清关方式
     * {@link com.danding.cds.common.enums.InventoryDeclareWayEnum}
     */
    private Integer declareWay;

    /**
     * 外部单号
     */
    private String upstreamNo;

    /**
     * 出入库单号
     */
    private String inOutOrderNo;

    /**
     * 状态流
     * ['CREATED','CONFIRMING','ENDORSEMENT']
     */
    private String stateFlow;

    @Deprecated
    /**
     * 托数 打架数量?战斗数量?
     */
    private Integer fightQty;

    /**
     * 运输费
     */
    private BigDecimal shippingFee;

    /**
     * 转入实体仓
     */
    private String entityInWarehouseName;

    /**
     * 转出实体仓
     */
    private String entityOutWarehouseName;

    /**
     * 货主是否自备车辆
     */
    private Boolean selfOwnedVehicle;

    /**
     * 车牌号
     */
    private String licensePlate;

    /**
     * 车辆费用备注
     */
    private String vehicleCostRemark;

    /**
     * 托数
     */
    private Integer palletsNums;

    /**
     * 上游是否取消
     */
    private Boolean upstreamCancel;

    /**
     * 清关单标记 用位运算去搞
     */
    private Integer orderTag;

    /**
     * 清关单待办标记(位运算)
     */
    private Integer orderTodoTag;

    /**
     * 核注清单编号
     */
    private String endorsementRealOrderNo;

    /**
     * 关联草单附件名称
     */
    private String draftListAttachmentName;

    /**
     * 关联草单附件链接
     */
    private String draftListAttachmentUrl;

    /**
     * 转出方
     */
    private String transferor;

    /**
     * 转入方
     */
    private String transferee;

    /**
     * 是否开启中转 0:否 1:是
     */
    private Integer transitFlag;
    /**
     * 清关企业(终点)
     */
    private Long finalInveCompanyId;
    /**
     * 账册ID(终点)
     */
    private Long finalBookId;
    /**
     * 实体仓编码(终点)
     */
    private String finalEntityWarehouseCode;
    /**
     * 实体仓名称(终点)
     */
    private String finalEntityWarehouseName;
    /**
     * 货主编码(终点)
     */
    private String finalOwnerCode;
    /**
     * 货主名称(终点)
     */
    private String finalOwnerName;
    /**
     * 关联中转清关单号
     */
    private String associatedTransitOrderSn;
    /**
     * 关联调入清关单号
     */
    private String associatedInOrderSn;
    /**
     * 关联调出清关单号
     */
    private String associatedOutOrderSn;

    /**
     * 用户id
     * ERP下发(上游商家用户中心id)
     */
    private Long userId;

    /**
     * 用户名称
     * ERP下发(上游商家用户中心name)
     */
    private String userName;


    /**
     * 报关日期
     */
    private Date customsEntryDate;

    /**
     * 报关单附件链接
     */
    private String customsEntryAttachUrl;

    /**
     * 是否理货完成
     * 0-否 / 1-是
     */
    private Integer tallyComplete;

    /**
     * 清关回传状态
     */
    private Integer callbackStatus;

    /**
     * 清关回执错误信息
     */
    private String callbackErrorMsg;

    /**
     * 分区结转单
     */
    private String carryOverNo;

    /**
     * 报关类型
     * {@link com.danding.cds.common.enums.InventoryCustomsTypeEnums}
     */
    private Integer customsType;

    /**
     * 对应报关单申报单位
     */
    private Long corrCusDeclareCompanyId;

    /**
     * 关联报关单境内收发货人
     */
    private Long rltCusInnerSFHRCompanyId;

    /**
     * 关联报关单消费使用单位
     */
    private Long rltCusXFDYCompanyId;

    /**
     * 关联报关单申报单位
     */
    private Long rltCusDeclareCompanyId;

    /**
     * 进出区标志
     * {@link com.danding.cds.invenorder.api.enums.InventoryInOutEnum}
     */
    private String inOutFlag;

    /**
     * 非保标记 (1:是非保清关单, 0:不是非保清关单)
     */
    private Integer fbFlag;

    /**
     * 非保核放单流水号
     */
    private String fbChecklistSn;

    /**
     * 进境口岸
     */
    private String entryPort;

    /**
     * 起运港（始发机场）
     */
    private String fromLocation;

    /**
     * 上游原始报文
     * {@link TTClearanceEntryorderCreateDTO}
     */
    private String upstreamOrigMsg;


    /**
     * 约车单号
     */
    private String ycNo;

    /**
     * 是否约车
     */
    private String isYcDesc;

    /**
     * 约车状态desc
     */
    private String ycStatusDesc;

    /**
     * （调入/调出）关联清关单号
     */
    private String associatedInveCustomsSn;

    /**
     * 核注单备注
     */
    private String endorsementRemark;

    /**
     * 操作人
     */
    private String operatorName;

    /**
     * 清关完成时间
     */
    private Date inventoryCompleteTime;

    /**
     * 服务完成时间
     */
    private Date inventoryFinishTime;

    /**
     * 草单比对类型
     */
    private Integer draftCompareType;
    /**
     * 加急排序
     */
    private Integer urgentSort;

    /**
     * 申报表编号
     */
    private String declareFormNo;
    /**
     * 是否锁定库存
     * 0：未锁定 1：锁定
     */
    private Integer lockStockFlag;
}
