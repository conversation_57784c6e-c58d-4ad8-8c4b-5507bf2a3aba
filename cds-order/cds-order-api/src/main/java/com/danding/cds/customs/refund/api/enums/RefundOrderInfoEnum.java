package com.danding.cds.customs.refund.api.enums;

public enum RefundOrderInfoEnum {
    PAYMENT_BOOK("400", "原清单已经生成缴款书"),
    NOT_ENTERED("410", "货未到区内"),
    NOT_ARRIVED("420", "未运抵"),
    UNKNOWN_ADDRESS("430", "收件地址不详"),
    DECLARATION_ERROR("440", "企业申报错误"),
    DAMAGED("450", "影响二次销售");

    private String value;

    private String desc;

    RefundOrderInfoEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static RefundOrderInfoEnum getEnum(String value){
        for (RefundOrderInfoEnum orderStatus : RefundOrderInfoEnum.values()) {
            if (orderStatus.getValue().equals(value)){
                return orderStatus;
            }
        }
        return PAYMENT_BOOK;
    }
}
