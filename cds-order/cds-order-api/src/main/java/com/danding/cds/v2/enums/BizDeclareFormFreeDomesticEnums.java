package com.danding.cds.v2.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 保税区内销标志枚举
 */
@Getter
@AllArgsConstructor
public enum BizDeclareFormFreeDomesticEnums {

    //I-料件E-成品
    NULL("", ""),
    YES("Y", "是");

    private final String code;
    private final String desc;

    public static BizDeclareFormFreeDomesticEnums getEnums(String code) {
        for (BizDeclareFormFreeDomesticEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return NULL;
    }

    public static String getDesc(String code) {
        for (BizDeclareFormFreeDomesticEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums.getDesc();
            }
        }
        return "";
    }

}
