package com.danding.cds.order.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class TaxCallbackSubmit implements Serializable {

    private String systemGlobalSn;

    private String declareOrderNo;

    private String invtNo;

    private Long processTime;

    private BigDecimal totalTax;

    private String taxNo;

    private BigDecimal customsTax;

    private BigDecimal valueAddedTax;

    private BigDecimal consumptionTax;

    private String status;

    private String sendType;

    private List<TaxItem> itemList;

    @Data
    public static class TaxItem implements Serializable {
        /**
         * 商品项号,从1开始连续序号
         */
        protected int gnum;

        /**
         * 海关商品编码（10位） HS?
         */
        protected String gcode;

        /**
         * 完税总价格
         */
        protected BigDecimal taxPrice;

        /**
         * 应征关税
         */
        protected BigDecimal customsTax;

        /**
         * 应征增值税
         */
        protected BigDecimal valueAddedTax;

        /**
         * 应征消费税
         */
        protected BigDecimal consumptionTax;
    }
}
