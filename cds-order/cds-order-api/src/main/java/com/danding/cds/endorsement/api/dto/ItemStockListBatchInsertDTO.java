package com.danding.cds.endorsement.api.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 库存流水批量插入消息DTO
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
public class ItemStockListBatchInsertDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 核注单ID
     */
    private Long endorsementId;

    /**
     * 真实核注单号
     */
    private String realOrderNo;

    /**
     * 跟踪ID
     */
    private String traceId;
}
