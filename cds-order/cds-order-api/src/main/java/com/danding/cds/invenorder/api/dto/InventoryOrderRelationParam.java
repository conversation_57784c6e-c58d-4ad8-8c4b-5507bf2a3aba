package com.danding.cds.invenorder.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class InventoryOrderRelationParam implements Serializable {
    private static final long serialVersionUID = -2433113850499947709L;

    @ApiModelProperty("清关单ID")
    private Long invenOrderId;



    @ApiModelProperty("类型")
    private String  relType;

    @ApiModelProperty("编号")
    private List<String> relNos;



}
