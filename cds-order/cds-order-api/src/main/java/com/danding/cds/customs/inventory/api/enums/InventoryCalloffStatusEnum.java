package com.danding.cds.customs.inventory.api.enums;

/**
 * @Author: Raymond
 * @Date: 2020/10/14 9:37
 * @Description:取消状态
 */
public enum InventoryCalloffStatusEnum {
    CALLOFF_EMPTY("","空"),
    CALLOFF_WAITING("0","待取消"),
    CALLOFF_ING("1","取消中"),
    CALLOFF_SUCCESS("2","取消成功"),
    CALLOFF_FAIL("3","取消失败"),
    CALLOFF_REJECT("4","取消驳回");
    private String code;
    private String desc;
    private InventoryCalloffStatusEnum(String code, String desc)
    {
        this.code = code;
        this.desc = desc;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }
    public String getDesc() {
        return desc;
    }

    public static InventoryCalloffStatusEnum getEnum(String value){
        for (InventoryCalloffStatusEnum step : InventoryCalloffStatusEnum.values()) {
            if (step.getCode().equals(value)){
                return step;
            }
        }
        return CALLOFF_EMPTY;
    }
}
