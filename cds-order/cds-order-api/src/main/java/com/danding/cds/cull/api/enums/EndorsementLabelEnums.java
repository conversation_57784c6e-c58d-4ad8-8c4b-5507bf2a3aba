package com.danding.cds.cull.api.enums;


public enum EndorsementLabelEnums {
//    NULL("",null),
    ENDORSEMENT_CULL("endorsement_cull","核注剔除");


    private String code;

    private String desc;

    EndorsementLabelEnums(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static EndorsementLabelEnums getEnum(String code){
        for (EndorsementLabelEnums value : EndorsementLabelEnums.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        throw new IllegalArgumentException("unknown StatusEnums.code: " + code);
    }
}
