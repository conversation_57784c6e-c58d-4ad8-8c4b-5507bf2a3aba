package com.danding.cds.handoverOrder.api.dto;

import com.danding.cds.exportorder.api.dto.ExportItemWritingReport;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 提交预览
 * @date 2021/12/17
 */
@Data
public class HandoverWritingReport implements Serializable {

    /**
     * 交接单号
     */
    private String handoverSn;

    /**
     * 快递公司编码
     */
    private String expressCode;

    /**
     * 账册ID
     */
    private Long accountBookId;

    /**
     * 清关企业ID
     */
    private Long declareCompanyId;

    /**
     * 实体仓编码（wms）
     */
    private String entityWarehouseCode;


    /**
     * 出库单预览数据
     */
    ExportItemWritingReport exportItemWritingReport;


}
