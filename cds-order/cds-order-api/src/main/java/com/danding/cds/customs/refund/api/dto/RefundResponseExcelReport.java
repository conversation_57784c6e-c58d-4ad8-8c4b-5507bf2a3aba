package com.danding.cds.customs.refund.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel
@Data
public class RefundResponseExcelReport implements java.io.Serializable{
private static final long serialVersionUID = 11426533238422277L;
    @ApiModelProperty("代码标记")
    private int code;
    @ApiModelProperty("错误原因")
    private String errosMessage;

    @ApiModelProperty("导入总数")
    private int totalCount;

    @ApiModelProperty("增加数量")
    private int successNewCount;
    @ApiModelProperty("更新数量")
    private int successUpdateCount;
    @ApiModelProperty("失败数量")
    private int failCount;

    @ApiModelProperty("成功列表")
    private List<MailNoInfoRecord> successNewRecordList;

    @ApiModelProperty("更新列表")
    private List<MailNoInfoRecord> successUpdateRecordList;

    @ApiModelProperty("失败列表")
    private List<MailNoInfoRecord> failRecordList;
}
