package com.danding.cds.transwork.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: yousx
 * @Date: 2024/03/12
 * @Description:
 */
@AllArgsConstructor
@Getter
public enum TransAttachmentTypeEnum {

    NULL("", ""),
    MSDS("MSDS", "MSDS文件"),
    BOL("BOL", "提单文件");
    final String code;
    final String desc;

    public static TransAttachmentTypeEnum getEnum(String code) {
        for (TransAttachmentTypeEnum attachmentTypeEnum : TransAttachmentTypeEnum.values()) {
            if (attachmentTypeEnum.getCode().equals(code)) {
                return attachmentTypeEnum;
            }
        }
        return NULL;
    }
}
