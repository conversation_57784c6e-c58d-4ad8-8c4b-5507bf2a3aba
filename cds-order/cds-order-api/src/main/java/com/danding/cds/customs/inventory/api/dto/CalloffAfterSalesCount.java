package com.danding.cds.customs.inventory.api.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 业务中台工作总览 - 保税售后 - 撤单
 */
@Data
public class CalloffAfterSalesCount implements Serializable {

    /**
     * 用户名称
     */
    private String userName;
    private String userId;

    /**
     * 待处理
     * <p>
     * ALL+时间段
     */
    private Integer pending;

    /**
     * 已处理
     * <p>
     * 取消中+取消成功+取消失败+时间段
     */
    private Integer processed;

    /**
     * 取消驳回
     * <p>
     * 取消驳回+时间段
     */
    private Integer reject;

    /**
     * 未处理
     * <p>
     * 待取消+时间段
     */
    private Integer unprocessed;

}
