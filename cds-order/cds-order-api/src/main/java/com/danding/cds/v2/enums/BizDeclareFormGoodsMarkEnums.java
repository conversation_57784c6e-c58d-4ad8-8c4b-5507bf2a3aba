package com.danding.cds.v2.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BizDeclareFormGoodsMarkEnums {

    //I-料件E-成品
    NOT_IMPORTANT_GOODS("0", "非重点商品"),
    IMPORTANT_GOODS("1", "重点商品");

    private final String code;
    private final String desc;

    public static BizDeclareFormGoodsMarkEnums getEnums(String code) {
        for (BizDeclareFormGoodsMarkEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    public static String getDesc(String code) {
        for (BizDeclareFormGoodsMarkEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums.getDesc();
            }
        }
        return "";
    }

}
