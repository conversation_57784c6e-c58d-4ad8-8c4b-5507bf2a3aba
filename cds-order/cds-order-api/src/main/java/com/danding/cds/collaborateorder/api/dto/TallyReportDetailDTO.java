package com.danding.cds.collaborateorder.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description:
 * @date 2022/3/29 15:40
 */
@Data
public class TallyReportDetailDTO implements Serializable {

    /**
     * 理货编号
     */
    private String tallyOrderNo;

    /**
     * 货品ID
     */
    private String goodsCode;

    /**
     * 货品名称
     */
    private String goodsName;

    /**
     * 外部sku
     */
    private String sku;

    /**
     * 条形码
     */
    private String barcode;

    /**
     * 申报料号
     */
    private String productId;

    /**
     * 实际理货数量
     */
    private Integer tallyNum;

    /**
     * 净重
     */
    private BigDecimal netWeight;

    /**
     * 毛重
     */
    private BigDecimal grossWeight;

}
