package com.danding.cds.customs.inventory.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CustomsInventoryCancelDTO implements Serializable {
    private static final long serialVersionUID = -3037375954449192999L;
    @ApiModelProperty("ID")
    private Long id;
    @ApiModelProperty("申报单ID")
    private String orderId;
    @ApiModelProperty("撤单编号")
    private String orderCancelSn;
    //清单相关ID
    @ApiModelProperty("相关清单ID")
    private Long refInvoId;
    //清单相关编号
    @ApiModelProperty("相关清单编号")
    private String refInvoSn;
    //相关申报单ID
    @ApiModelProperty("相关申报单ID")
    private Long refOrderId;
    //渠道号
    @ApiModelProperty("渠道号")
    private String channelNo;
    //相关申报单编号
    @ApiModelProperty("相关申报单编号")
    private String refOrderSn;
    /**
     * 完成时间
     */
    @ApiModelProperty("完成时间")
    private  Date completeTime;
    /**
     * 最终状态
     */
    private String completeStatus;
    //海关清单号
    @ApiModelProperty("清单号")
    private String invoNo;
    //运单号
    @ApiModelProperty("运单号")
    private String mailNo;
    //快递公司ID
    @ApiModelProperty("快递公司ID")
    private Long expressId;
//    @ApiModelProperty("快递公司名称")
//    private String expressName;
//账册
@ApiModelProperty("账册id")
private Long customsBookId;
    @ApiModelProperty("账册编码")
    private String customsBookNo;
    //租户
    @ApiModelProperty("租户")
    private String lesseeNo;
    /**
     * 用户id
     */
    private String tenantId;

    //区内企业
    @ApiModelProperty("区内企业ID")
    private Long areaCompanyId;
    @ApiModelProperty("区内企业名称")
    private String areaCompanyName;
    //撤单状态
    @ApiModelProperty("撤单状态")
    private String status;

    //撤单状态发生时间
    @ApiModelProperty("撤单状态发生时间")
    private Date statusTime;
    //海关回执状态
    @ApiModelProperty("海关回执状态")
    private String customsStatus;
    //海关回执明细
    @ApiModelProperty("海关回执明细")
    private  String  customsCheckDetail;
    //海关回执时间
    @ApiModelProperty("海关回执时间")
    private Date customsLastTime;
    /**
     * 撤单原因
     */
    @ApiModelProperty("撤单原因")
    private String cancelReason;

    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("更新时间")
    private Date updateTime;
    private Integer createBy;
    private Integer updateBy;
    private Boolean deleted = false;

    //手工回执
    private String manualReceipt;

    private Long tenantryId;
}
