package com.danding.cds.order.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CustomsStatusMappingDTO implements Serializable {
    private Long id;
    /**
     * 申报项，用于区分模块
     */
    private String action;

    /**
     * 标识，各类场景根据特定规则拼凑成的唯一标识码
     */
    private String code;

    /**
     * 描述，会作为异常下拉列表的名称
     */
    private String detail;

    /**
     * 申报状态
     * PS::对应着申报单据的status
     *
     DEC_ING(20,"等待回执"),
     DEC_SUCCESS(100,"已放行"),
     DEC_FAIL(-1,"申报失败");
     */
    private Integer status;

    /**
     * 备注，可填写异常处理方式
     */
    private String note;
    /**
     * 是否属异常
     */
    private Boolean exceptionFlag;

    /**
     * 映射状态
     */
    private Integer mapStatus;

    /**
     * 海关状态码
     */
    private String customsStatusCode;

    /**
     * 详情状态码
     */
    private String detailCode;

    /**
     * 回执说明
     */
    private String receiptExplain;

    /**
     * 备注
     */
    private String remark;

    private Long exceptionId;

    /**
     * 是否终态回执（判断是否走正则表达式）
     */
    private Boolean finalReceiptFlag;

    /**
     * 正则表达式校验
     */
    private List<RegexCheck> regexCheckList;

    @Data
    public static class RegexCheck implements Serializable {

        /**
         * 映射Code
         */
        private String mappingCode;

        /**
         * 正则表达式
         */
        private String regex;
    }

}
