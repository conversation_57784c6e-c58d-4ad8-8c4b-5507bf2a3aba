package com.danding.cds.transwork.api.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class TransWorkOrderPackagingItemsVO implements Serializable {

    private Long id;

    /**
     * 运输作业id
     */
    @ApiModelProperty(value = "作业单id")
    private Long transId;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String itemCode;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String itemName;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private Long itemQuantity;

    /**
     * 实际发货数量
     */
    @ApiModelProperty(value = "实际发货数量")
    private Long actDeliveryQty;


    /**
     * 实际签收数量
     */
    @ApiModelProperty(value = "实际签收数量")
    private Long actSignQty;


    /**
     * 托盘号
     */
    @ApiModelProperty(value = "托盘号")
    private String palletNo;

    /**
     * 箱号
     */
    @ApiModelProperty(value = "箱号")
    private String boxNo;

    /**
     * 装箱明细行号
     */
    @ApiModelProperty(value = "装箱明细行号")
    private String packagingItemLineNo;

}