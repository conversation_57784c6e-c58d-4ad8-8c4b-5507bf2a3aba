package com.danding.cds.collaborateorder.api.enums;


public enum CollaborateStatus {
    WAIT_CUSTOMS_VERIFY(1, 1, "WAITCUSTOMSVERIFY", "待关务确认"),
    CUSTOMS_VERIFY(2, 2, "CUSTOMSVERIFY", "关务已确认"),
    GENERATE_ENDORSEMENT(3, 5, "GENERATEENDORSEMENT", "生成核注单"),
    CUSTOMS_FINISH(4, 6, "CUSTOMSFINISH", "清关完成"),
    SERVE_FINISH(5, 7, "SERVEFINISH", "服务完成"),
    @Deprecated
    TALLY_WAIT(6, 3, "TALLYWAIT", "理货确认中"),
    TALL_RETURNED(7, 4, "TALLRETURNED", "理货已回传"),
    END_WAIT_VERIFY(8, 8, "ENDWAITVERIFY", "已完结（待处理)"),
    <PERSON><PERSON>(9, 9, "<PERSON><PERSON>", "已完结"),
    OUT_OF_DATE(10, 10, "OUTOFDATE", "已作废");

    //一步申报
    private Integer ordinaryCode;
    //两步申报
    private Integer districtPortCode;
    private String code;

    private String desc;

    CollaborateStatus(Integer ordinaryCode,Integer districtPortCode,String code, String desc) {
        this.ordinaryCode = ordinaryCode;
        this.districtPortCode = districtPortCode;
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getDistrictPortCode() {
        return districtPortCode;
    }

    public Integer getOrdinaryCode() {
        return ordinaryCode;
    }

    public static CollaborateStatus getEnum(String code){
        for (CollaborateStatus value : CollaborateStatus.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        throw new IllegalArgumentException("unknown BondedType.code: " + code);
    }
}
