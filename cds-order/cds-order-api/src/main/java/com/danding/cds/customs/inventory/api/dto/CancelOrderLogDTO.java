package com.danding.cds.customs.inventory.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

/**
 * 取消单业务操作日志
 */
@Data
public class CancelOrderLogDTO implements Serializable {

    private static final long serialVersionUID = -3037375954449192999L;
    @ApiModelProperty("ID")
    private Long id;
    /**
     * 申报单号
     */
    @Column(name = "declare_order_no")
    private String declareOrderNo;


    /**
     * 清单编号
     */
    @Column(name = "inventory_no")
    private String inventoryNo;

    /**
     * 审核状态 0-初始化，1-申报中，2待总署审核，3-申报失败，4-总署驳回，5-审核通过，6-取消测单，7-撤单失败
     */
    @Column(name = "audit_status")
    private String auditStatus;

    /**
     * 描述
     */
    @Column(name = "log_describe")
    private String logDescribe;

    /**
     * 日志说明
     */
    @Column(name = "log_detail")
    private String logDetail;

    /**
     * 订单类型：1-取消单，2-退货单，3-撤单
     */
    @Column(name = "order_type")
    private Integer orderType;

    /**
     * 关联单ID
     */
    @Column(name = "related_id")
    private Long relatedId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @Column(name = "create_by")
    private Long createBy;

    /**
     * 创建人
     */
    private String createName;
}
