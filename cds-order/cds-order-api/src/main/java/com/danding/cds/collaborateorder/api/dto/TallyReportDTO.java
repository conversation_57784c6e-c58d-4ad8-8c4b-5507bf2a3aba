package com.danding.cds.collaborateorder.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 接收理货报告
 * @date 2022/3/29 14:54
 */
@Data
public class TallyReportDTO implements Serializable {

    /**
     * 入库单号/出库单号
     */
    private String inOutOrderNo;

    /**
     * 分区结转单号
     */
    private String carryOverNo;

    /**
     * 外部单号
     */
    private String upstreamNo;

    /**
     * 单据类型( 1 : 入库单, 2:出库单)
     */
    private String type;

    /**
     * 理货编号
     */
    private String tallyOrderNo;

    /**
     * 实际理货总数量
     */
    private Integer tallyTotalQty;

    /**
     * 理货状态；1：待客户确认；2：客户已确认, 3:已完成
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 理货完成时间
     */
    private Long tallyFinishTime;

    /**
     * 到库时间
     */
    private Long warehouseTime;

    /**
     * 理货详情
     */
    private List<TallyReportDetailDTO> tallyDetailList;

    /**
     * 总托数
     */
    private Integer palletsNums;

    /**
     * 草单附件名称
     */
    private String attachmentName;

    /**
     * 草单附件url
     */
    private String attachmentUrl;

    /**
     * 实际核出企业
     */
    private String actualOutCompanyName;
}
