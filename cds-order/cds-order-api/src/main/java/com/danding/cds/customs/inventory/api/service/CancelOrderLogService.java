package com.danding.cds.customs.inventory.api.service;

import com.danding.cds.customs.inventory.api.dto.CancelOrderLogDTO;

import java.util.List;

public interface CancelOrderLogService {

    /**
     * 创建日志
     * @param orderLogDO
     */
    void create(CancelOrderLogDTO orderLogDO);

    /**
     * 根据关联ID查询日志
     * @param relatedId
     * @return
     */
    List<CancelOrderLogDTO> selectByRelatedIdList(Long relatedId,Integer orderType);
}
