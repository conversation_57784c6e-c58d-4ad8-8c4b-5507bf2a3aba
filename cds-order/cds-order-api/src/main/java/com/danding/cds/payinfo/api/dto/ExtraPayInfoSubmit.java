package com.danding.cds.payinfo.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class ExtraPayInfoSubmit implements Serializable {
    private String declareOrderNo;

    private String tradePayNo;

    private Long tradeTime;

    private String payChannel;

    private String routeCode;

    private String firstIdentify;

    private String secondIdentify;

    private String thirdIdentify;

    private String payTransactionId;

    private String verDept;

    private String payWay;

    private BigDecimal payTransactionAmount;

    private String recpCode;

    private String recpName;

    private String recpAccount;

    private String payRequestMessage;

    private String payResponseMessage;

    private List<PayInfoGoodsInfo> origGoodsInfoList;
}
