package com.danding.cds.customs.logistics.api.enums;

public enum CancelOrderReceiptEnum {

    MANUAL_CHARGEBACK("6","人工退单"),
    INVENTORY_ORDER_NO("7","清单号不存在系统中"),
    CANNOT_WITHDRAW_ORDER("8","缴款书已生成的清单不能撤单"),
    REPORTED_NUCLEAR_BET("9","该撤销申请单对应清单已报核注清单，不允许撤单");

    private String value;

    private String desc;

    CancelOrderReceiptEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static CancelOrderReceiptEnum getEnum(String value){

        for (CancelOrderReceiptEnum orderStatus : CancelOrderReceiptEnum.values()) {
            if (orderStatus.getValue().equals(value)){
                return orderStatus;
            }
        }
        return MANUAL_CHARGEBACK;
    }
}
