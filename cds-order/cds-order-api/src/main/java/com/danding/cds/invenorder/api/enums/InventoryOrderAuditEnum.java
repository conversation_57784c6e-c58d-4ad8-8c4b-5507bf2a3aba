package com.danding.cds.invenorder.api.enums;

/**
 * @Author: Raymond
 * @Date: 2020/12/1 14:24
 * @Description:
 */
public enum InventoryOrderAuditEnum {
    EMPTY("","空"),
    STATUS_AUDITING("AUDITING","待审核"),
    STATUS_AUDITED("AUDITED","审核通过"),
    STATUS_REJECT("REJECT","审核驳回");

    private String code;
    private String desc;
    private InventoryOrderAuditEnum(String code,String desc)
    {
        this.code = code;
        this.desc = desc;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }
    public String getDesc() {
        return desc;
    }
    public static InventoryOrderAuditEnum getEnum(String value){
        for (InventoryOrderAuditEnum orderStatus : InventoryOrderAuditEnum.values()) {
            if (orderStatus.getCode().equals(value)){
                return orderStatus;
            }
        }
        return EMPTY;
    }
}
