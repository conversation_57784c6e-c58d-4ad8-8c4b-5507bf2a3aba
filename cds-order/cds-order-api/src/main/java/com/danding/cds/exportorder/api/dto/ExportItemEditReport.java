package com.danding.cds.exportorder.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 出区订单编辑报告
 */
@Data
@ApiModel
public class ExportItemEditReport implements Serializable {

    @ApiModelProperty("提交总数")
    private int totalCount;

    @ApiModelProperty("新增成功")
    private List<ExportItemRecord> addSuccessList;

    @ApiModelProperty("新增失败")
    private List<ExportItemRecord> addFailList;

    @ApiModelProperty("删除成功")
    private List<ExportItemRecord> deleteSuccessList;

    @ApiModelProperty("删除失败")
    private List<ExportItemRecord> deleteFailList;
}
