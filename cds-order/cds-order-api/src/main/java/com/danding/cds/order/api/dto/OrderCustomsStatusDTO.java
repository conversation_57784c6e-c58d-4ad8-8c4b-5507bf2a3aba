package com.danding.cds.order.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: yousx
 * @Date: 2024/01/04
 * @Description:
 */
@Data
@ApiModel
public class OrderCustomsStatusDTO implements Serializable {

    @ApiModelProperty("申报单sn, ','隔开 ")
    private String sns;

    @ApiModelProperty("海关状态")
    private Integer status;
}
