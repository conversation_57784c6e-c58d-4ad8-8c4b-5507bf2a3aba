package com.danding.cds.endorsement.api.dto;

import com.danding.logistics.api.common.page.Page;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 核注详情-电商清单分页查询参数
 */
@Data
public class EndorsementEbInvSearch extends Page {

    /**
     * id
     */
    @NotNull(message = "核注id不能为空")
    private Long id;

    /**
     * 关键词查询类型 declareOrderNo,inventoryNo,logisticsNo
     */
    private String queryType;

    /**
     * 关键词查询信息
     */
    private String queryInfo;
}
