package com.danding.cds.invenorder.api.enums;

public enum InventoryOrderRelationEnum {
    REL_TYPE_EMPTY("EMPTY","空"),
    REL_TYPE_YUNDAN("YUNDAN","运单"),
    REL_TYPE_DECLARE("DECLARE","报关单");
    private String code;
    private String desc;
    private InventoryOrderRelationEnum(String code, String desc)
    {
        this.code = code;
        this.desc = desc;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }
    public String getDesc() {
        return desc;
    }
    public static InventoryOrderRelationEnum getEnum(String value){
        for (InventoryOrderRelationEnum orderStatus : InventoryOrderRelationEnum.values()) {
            if (orderStatus.getCode().equals(value)){
                return orderStatus;
            }
        }
        return REL_TYPE_EMPTY;
    }
}
