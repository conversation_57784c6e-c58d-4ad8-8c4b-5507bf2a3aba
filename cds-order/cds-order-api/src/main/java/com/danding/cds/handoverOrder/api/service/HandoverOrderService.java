package com.danding.cds.handoverOrder.api.service;

import com.danding.cds.exportorder.api.dto.ExportOrderDTO;
import com.danding.cds.handoverOrder.api.dto.*;
import com.danding.cds.handoverOrder.api.enums.HandoverOrderStatus;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;

import java.util.List;

public interface HandoverOrderService {

    /**
     * 接收WMS推送交接单
     *
     * @param handoverOrderDTOList
     */
    void saveHandoverOrder(List<HandoverOrderDTO> handoverOrderDTOList);

    List<WarehouseDTO> getWarehouseInfo();

    List<HandoverOrderDTO> handoverOrderSyn(HandoverOrderDTO orderDTO) throws ArgsErrorException;

    ListVO<HandoverOrderVO> paging(HandoverOrderParam param);

    void saveExportOrder(HandoverOrderParam param);

    List<HandoverOrderDTO> pagingListAll(HandoverOrderParam param);

    HandoverOrderDTO getById(Long id);

    void updHandoverOrder(ExportOrderDTO exportOrderDTO, String handoverSn, String mailNo);

    HandoverOrderStatus getHandoverOrderStatus(String sn);

    HandoverOrderStatus getHandoverOrderStatus(List<HandoverOrderDetailDTO> orderDetailDTOList);

    void updateHandoverOrderStatus(String sn);

    HandoverExportItemRecord outboundOrderPreview(HandoverWritingReport report) throws ArgsErrorException;

    void updateHandoverStatus(String handoverSn, HandoverOrderStatus handoverOrderStatus);

    Boolean skipCheckHandoverByBookId(Long customsBookId);

    HandoverStatusCountResult getHandoverStatusCount(HandoverOrderParam param);

    void refresh(Long id);
}
