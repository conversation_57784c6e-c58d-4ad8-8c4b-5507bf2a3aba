package com.danding.cds.invenorder.api.dto;

import com.danding.logistics.api.common.page.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Deprecated
@Data
public class InventoryOrderSearch extends Page implements Serializable {
    private static final long serialVersionUID = -2433522850499947709L;
    @ApiModelProperty("清关单号")
    private String inveOrderSn;
    @ApiModelProperty("核注单号")
    private String endorsementSn;
    @ApiModelProperty("预录入核注清单编号")
    private String preOrderNo;
    @ApiModelProperty("清关状态")
    private String status;
    @ApiModelProperty("业务类型")
    private String bussinessType;
    @ApiModelProperty("是否主单")
    private Boolean isMaster;
    @ApiModelProperty("备货/配货单号")
    private String channelBusinessSn;
    @ApiModelProperty("实体仓名称")
    private String entityWarehouseCode;
    @ApiModelProperty("货主名称")
    private String ownerCode;
    @ApiModelProperty("出/入库单号")
    private String inOutOrderNo;
    @ApiModelProperty("外部单号")
    private String upstreamNo;
    @ApiModelProperty("账册id")
    private Long customsBookId;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("创建开始时间")
    private Long beginCreateTime;
    @ApiModelProperty("创建结束时间")
    private Long endCreateTime;
    @ApiModelProperty("完成开始时间")
    private Long beginCompleteTime;
    @ApiModelProperty("完成结束时间")
    private Long endCompleteTime;
}
