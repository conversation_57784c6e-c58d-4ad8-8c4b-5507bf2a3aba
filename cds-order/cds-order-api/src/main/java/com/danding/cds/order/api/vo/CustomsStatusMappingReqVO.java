package com.danding.cds.order.api.vo;

import com.danding.logistics.api.common.page.Page;
import lombok.Data;

@Data
public class CustomsStatusMappingReqVO extends Page {

    private Long id;
    /**
     * 申报项，用于区分模块
     */
    private String action;

    /**
     * 海关状态码
     */
    private String customsStatusCode;

    /**
     * 详情状态码
     */
    private String detailCode;

    /**
     * 映射状态码
     */
    private Integer mapStatus;

    /**
     * 海关回执
     */
    private String note;

    /**
     * 申报状态
     * PS::对应着申报单据的status
     *
     DEC_ING(20,"等待回执"),
     DEC_SUCCESS(100,"已放行"),
     DEC_FAIL(-1,"申报失败");
     */
    private Integer status;

    /**
     * 是否属异常
     */
    private Boolean exceptionFlag;


    /**
     * 开始时间
     */
    private Long staTime;

    /**
     * 结束时间
     */
    private Long endTime;

    /**
     * code
     */
    private String code;

    /**
     * 是否终态回执（判断是否走正则表达式）
     */
    private Boolean finalReceiptFlag;
}
