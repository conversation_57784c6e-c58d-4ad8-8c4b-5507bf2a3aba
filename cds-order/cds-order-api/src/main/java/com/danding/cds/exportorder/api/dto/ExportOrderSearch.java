package com.danding.cds.exportorder.api.dto;

import com.danding.cds.common.annotations.UcRoleAccountBookIdList;
import com.danding.logistics.api.common.page.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

@Data
public class ExportOrderSearch extends Page implements Serializable {
    private static final long serialVersionUID = -8940915844102658795L;

    @ApiModelProperty("状态，见下拉接口")
    private Integer status;

    @ApiModelProperty("快递公司ID")
    private Long expressId;

    @ApiModelProperty("关键词查询类型 mailNo运单号|sn申报出库单号|endorsementSn关联单证号")
    private String queryType;

    @ApiModelProperty("关键词查询关键词")
    private String queryInfo;

    @ApiModelProperty("创建起始时间")
    private Long createFrom;

    @ApiModelProperty("创建终止时间")
    private Long createTo;

    @ApiModelProperty("完成起始时间")
    private Long finishFrom;

    @ApiModelProperty("完成终止时间")
    private Long finishTo;

    /**
     * 实体仓编码
     */
    @ApiModelProperty("实体仓编码")
    private String entityWarehouseCode;

    @ApiModelProperty("id列表，前端可不填")
    private Set<Long> idSet;

    @ApiModelProperty("角色账册ID列表")
    @UcRoleAccountBookIdList
    private List<Long> roleAccountBookIdList;
}
