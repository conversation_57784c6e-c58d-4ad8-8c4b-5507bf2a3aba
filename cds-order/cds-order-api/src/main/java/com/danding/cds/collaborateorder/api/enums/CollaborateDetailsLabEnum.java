package com.danding.cds.collaborateorder.api.enums;

public enum CollaborateDetailsLabEnum {

    ADD("add","新增"),
    DEL("delete","已删");

    private String code;

    private String desc;

    CollaborateDetailsLabEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static CollaborateDetailsLabEnum getEnum(String code){
        for (CollaborateDetailsLabEnum value : CollaborateDetailsLabEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        throw new IllegalArgumentException("unknown BondedType.code: " + code);
    }
}
