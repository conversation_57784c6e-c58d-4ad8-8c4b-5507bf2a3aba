package com.danding.cds.invenorder.api.enums;

/**
 * @Author: Raymond
 * @Date: 2020/10/13 17:23
 * @Description: 清单节点
 */
public enum InventoryOrderStepEnum {
    STEP_EMPTY("","空"),
    STEP_CANCLE("0","可撤清单"),
    STEP_RETURN("1","可退清单");
    private String code;
    private String desc;
    private InventoryOrderStepEnum(String code, String desc)
    {
        this.code = code;
        this.desc = desc;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }
    public String getDesc() {
        return desc;
    }
    public static InventoryOrderStepEnum getEnum(String value){
        for (InventoryOrderStepEnum step : InventoryOrderStepEnum.values()) {
            if (step.getCode().equals(value)){
                return step;
            }
        }
        return STEP_EMPTY;
    }
}
