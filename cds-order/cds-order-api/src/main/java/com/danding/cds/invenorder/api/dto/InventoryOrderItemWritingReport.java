package com.danding.cds.invenorder.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel
public class InventoryOrderItemWritingReport implements Serializable {
    @ApiModelProperty("导入总数")
    private int totalCount;

    @ApiModelProperty("成功数量")
    private int successCount;

    @ApiModelProperty("失败数量")
    private int failCount;

    @ApiModelProperty("成功列表")
    private List<InventoryOrderItemRecord> successRecordList;

    @ApiModelProperty("失败列表")
    private List<InventoryOrderItemRecord> failRecordList;
}
