package com.danding.cds.customs.inventory.api.enums;

/**
 * 申报项状态
 */
public enum CustomsActionStatus {
    NULL(0,""),
    DEC_WAIT(10,"待申报"),
    DEC_ING(20,"申报中"),
    DEC_SUCCESS(100,"申报完成"),
    DEC_CANCEL(-10,"取消申报"),
    DEC_FAIL(-1,"申报终止");

    private Integer value;

    private String desc;

    CustomsActionStatus(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static CustomsActionStatus getEnum(Integer value){
        for (CustomsActionStatus orderStatus : CustomsActionStatus.values()) {
            if (orderStatus.getValue() == value){
                return orderStatus;
            }
        }
        return NULL;
    }
}
