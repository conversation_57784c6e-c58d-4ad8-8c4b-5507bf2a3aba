package com.danding.cds.endorsement.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class OrderEndorsementCheckRequest implements Serializable {
    private String jobId;
    private String dataType;
    private List<JobDetail> jobDetails;
    @Data
    public static class JobDetail implements Serializable {
        private List<String> bondInvtNoList;
        private String dclEtpsno;
        private String impexpType;
    }

    @Data
    public static class EndorsementRedisData implements Serializable {
        public String startDate;
        public String endDate;
    }
}
