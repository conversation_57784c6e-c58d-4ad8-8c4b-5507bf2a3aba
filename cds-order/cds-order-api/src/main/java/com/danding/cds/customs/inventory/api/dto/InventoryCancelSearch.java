package com.danding.cds.customs.inventory.api.dto;

import com.danding.cds.common.annotations.UcRoleAccountBookIdList;
import com.danding.logistics.api.common.page.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class InventoryCancelSearch  extends Page implements Serializable {
    private static final long serialVersionUID = -2433522850499347709L;
//    @ApiModelProperty("关键词查询类型 outOrderNo,declareOrderNo,inventoryNo,logisticsNo,declareNo")
//    private String queryType;
//    @ApiModelProperty("关键词查询关键词")
//    private String queryInfo;

    @ApiModelProperty("关键词查询类型 outOrderNo,declareOrderNo,inventoryNo,logisticsNo")
    private String queryType;

    @ApiModelProperty("关键词查询关键词")
    private String queryInfo;

    @ApiModelProperty("审核状态")
    private List<String> statusList;
    @ApiModelProperty("区内企业")
    private Integer areaCompanyId;
    @ApiModelProperty("创建开始时间")
    private Long beginCreateTime;
    @ApiModelProperty("创建结束时间")
    private Long endCreateTime;
    @ApiModelProperty("完成开始时间")
    private Long beginCompleteTime;
    @ApiModelProperty("完成结束时间")
    private Long endCompleteTime;
    /**
     * 账册编号id
     */
    private Long accountBookId;

    /**
     * 用户id
     */
    private String tenantId;
    @ApiModelProperty("勾选IDS")
    private String ids;
    @ApiModelProperty("角色账册ID列表")
    @UcRoleAccountBookIdList
    private List<Long> roleAccountBookIdList;
}
