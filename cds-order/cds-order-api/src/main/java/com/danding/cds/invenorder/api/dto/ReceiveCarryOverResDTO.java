package com.danding.cds.invenorder.api.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: 接收结转明细返回对象
 * @date 2023/4/22 12:33
 */
@Data
public class ReceiveCarryOverResDTO implements Serializable {
    /**
     * 结转单号
     */
    private String carryOverNo;

    /**
     * 清关单号
     */
    private String inventoryOrderSn;

    /**
     * 核注单号
     */
    private String endorsementSn;

    /**
     * 清关单类型
     * SECTIONINNER_OUT 区内流转(出)
     * SECTIONINNER_IN  区内流转(入)
     */
    private String businessType;

    /**
     * 10:待核注
     */
    private Integer endorsementStatus;
}
