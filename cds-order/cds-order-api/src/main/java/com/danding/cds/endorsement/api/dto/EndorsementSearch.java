package com.danding.cds.endorsement.api.dto;

import com.danding.cds.common.annotations.UcRoleAccountBookIdList;
import com.danding.logistics.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Set;

@Data
@ApiModel
public class EndorsementSearch extends Page {
    private static final long serialVersionUID = 4497578688263709109L;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("创建起始时间")
    private Long createFrom;

    @ApiModelProperty("创建终止时间")
    private Long createTo;

    @ApiModelProperty("完成起始时间")
    private Long finishFrom;

    @ApiModelProperty("完成终止时间")
    private Long finishTo;

    @ApiModelProperty("运单号")
    private String mailNo;

    @ApiModelProperty("Id集合，前端可不填")
    private Set<Long> idSet;

    @ApiModelProperty("业务类型")
    private String bussinessType;

    @ApiModelProperty("清关单Id集合，前端可不填")
    private List<Long> inventoryOrderIds;

    @ApiModelProperty ( "账册id" )
    private Long customsBookId;

    @ApiModelProperty ( "清关企业" )
    private Long agentCompanyId;

    @ApiModelProperty ( "sn：企业内部编码 realOrderNo：核注清单编号" )
    private String queryType;

    @ApiModelProperty ( "查询信息" )
    private String queryInfo;

    @ApiModelProperty("预录入核注单号")
    private String preOrderNos;

    @ApiModelProperty("企业内部编码")
    private String snNos;

    @ApiModelProperty("核注清单编号")
    private String realOrderNos;

    @ApiModelProperty("角色账册ID列表")
    @UcRoleAccountBookIdList
    private List<Long> roleAccountBookIdList;


    /**
     * 清关单号
     */
    private String invOrderSn;

    /**
     * 申报出库单号
     */
    private String exportOrderSn;

    /**
     * 出库单号id集合
     */
    private List<Long> exportOrderIds;

    /**
     * 是否核扣账册
     */
    private Boolean stockChangeEnable;

    /**
     * 关联账册备案号
     */
    private String rltCustomsBookNo;

    /**
     * 关联核注清单编号 （','分割）
     */
    private String rltEndorsementNo;

    /**
     * 操作人
     */
    private String operator;
    private Long operatorId;

    /**
     * 报关单生成状态
     */
    private String generateDeclareStatus;

    /**
     * 单据类型
     */
    private String orderType;
}
