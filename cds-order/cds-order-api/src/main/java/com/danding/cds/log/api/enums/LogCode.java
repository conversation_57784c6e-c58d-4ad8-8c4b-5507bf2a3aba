package com.danding.cds.log.api.enums;

import lombok.Getter;

@Getter
public enum LogCode {
    EMPTY("EMPTY","空"),//默认
    LOG_INVENTORY("LOG_INVENTORY","清单"),//清单
    LOG_ORDER("LOG_ORDER","订单"),  //订单
    LOG_LOGISTICS("LOG_LOGISTICS","运单"),  //运单
    lOG_INVENTORY_CANCEL("lOG_INVENTORY_CANCEL","撤单"),//撤单
    LOG_REFUND("LOG_REFUND","退单"),//退单
    LOG_PAYMENT("LOG_PAYMENT","支付单");//支付单
    private String code;
    private String desc;
    private LogCode(String code,String desc)
    {
        this.code = code;
        this.desc = desc;
    }

}
