package com.danding.cds.order.api.dto;

import com.danding.cds.payinfo.api.dto.PayInfoGoodsInfo;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: Raymond
 * @Date: 2020/9/9 15:22
 * @Description:
 */
@Data
public class DockerOrderDTO implements Serializable {
    private Long id;

    private String systemGlobalSn;

    private String outOrderNo;

    private String outTradeNo;


    private String declareOrderNo;

    /**
     * 申报路径 基础 A1 必填二选一
     */

    private String routeCode;

//
//    private String firstIdentify;
//
//
//    private String secondIdentify;
//
//
//    private String thirdIdentify;


    private BigDecimal feeAmount = BigDecimal.ZERO;

//    private String logisticsNo;
    /**
     * 保费
     */

    private BigDecimal insureAmount;


    private BigDecimal taxAmount;


    private BigDecimal discount;


    private BigDecimal goodsSumAmount;//用于校验

    // --- 清单订购人 ---

    private String buyerTelNumber;

    private String buyerIdNumber;

    private String buyerName;

    // --- 订单基础 ---


    private String payChannel;


    private String declarePayNo;


    private Long tradeTime;


    private String senderName;

    // --- 海关179公告对接 ---  ///////////////////////上报订单

    private Boolean payInfoDataCheckFlag = false;


    private String tradePayNo; // 订单里存一份，以防万一需要查

     private String payTransactionId;

     private String verDept;

     private String payWay;

     private BigDecimal payTransactionAmount;

     private String recpCode;

     private String recpName;

     private String recpAccount;


    private String payRequestMessage;


    private String payResponseMessage;

    private List<PayInfoGoodsInfo> origGoodsInfoList;

    /**
     * 新增
     */
     //通过仓库编码 获取 CustomsDistrictEnum
    private String customsCode;


    private String merchantCode;
}
