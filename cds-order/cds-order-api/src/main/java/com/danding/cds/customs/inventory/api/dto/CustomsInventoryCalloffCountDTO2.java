package com.danding.cds.customs.inventory.api.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 取消单管理页面 - 统计撤单 和 退货单数量
 */
@Data
public class CustomsInventoryCalloffCountDTO2 implements Serializable {

    /**
     * 撤单待取消
     */
    public int cancelOrderToCancel = 0;

    /**
     * 撤单取消中
     */
    public int cancelOrderCanceling = 0;

    /**
     * 退货待取消
     */
    public int refundToCancel = 0;

    /**
     * 退货取消中
     */
    public int refundCanceling = 0;
}
