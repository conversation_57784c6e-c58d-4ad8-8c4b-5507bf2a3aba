package com.danding.cds.exception.api.vo;

import com.danding.logistics.api.common.page.Page;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 查询请求对象
 * @date 2022/5/9
 */
@Data
public class ExceptionReqVO extends Page {

    /**
     * 异常id
     */
    private Long id;


    /**
     * 异常分类 1.业务异常2.系统异常3.海关异常
     */
    private Integer exceptionClassify;


    /**
     * 异常描述
     */
    private String exceptionDescribe;
}
