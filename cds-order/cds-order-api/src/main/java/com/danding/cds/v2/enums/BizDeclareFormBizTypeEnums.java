package com.danding.cds.v2.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BizDeclareFormBizTypeEnums {

    //A-分送集报、B-外发加工、C-保税展示交易、D-设备检测、E-设备维修、F-模具外发、G-简单加工、H-其他业务、Y-一纳企业进出区I-保区折料内销X-选择性征税分送集报
    A_DELIVERY_GROUP_REPORT("A", "分送集报"),
    B_OUTSOURCED_PROCESSING("B", "外发加工"),
    C_BONDED_EXHIBITION_TRADE("C", "保税展示交易"),
    D_EQUIPMENT_INSPECTION("D", "设备检测"),
    E_EQUIPMENT_MAINTENANCE("E", "设备维修"),
    F_MOLD_OUTSOURCING("F", "模具外发"),
    G_SIMPLE_PROCESSING("G", "简单加工"),
    H_OTHER_BUSINESS("H", "其他业务"),
    Y_ENTERPRISE_ZONE_MOVEMENT("Y", "一纳企业进出区"),
    I_BONDED_ZONE_MATERIAL_SALES("I", "保区折料内销"),
    X_SELECTIVE_TAX_DELIVERY_GROUP_REPORT("X", "选择性征税分送集报");

    private final String code;
    private final String desc;

    public static BizDeclareFormBizTypeEnums getEnums(String code) {
        for (BizDeclareFormBizTypeEnums bizDeclareFormBizTypeEnums : BizDeclareFormBizTypeEnums.values()) {
            if (bizDeclareFormBizTypeEnums.getCode().equals(code)) {
                return bizDeclareFormBizTypeEnums;
            }
        }
        return null;
    }

    public static String getDesc(String code) {
        for (BizDeclareFormBizTypeEnums bizDeclareFormBizTypeEnums : BizDeclareFormBizTypeEnums.values()) {
            if (bizDeclareFormBizTypeEnums.getCode().equals(code)) {
                return bizDeclareFormBizTypeEnums.getDesc();
            }
        }
        return "";
    }
}
