//package com.danding.cds.order.test;
//
//import org.apache.dubbo.config.annotation.Reference;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//
//@RunWith(SpringJUnit4ClassRunner.class)
//@SpringBootTest()
//public class TestApi  {
//    @Reference
//    private CdsFallBack cdsFallBack;
//    @Test
//    public void testApi()
//    {
//        System.out.println("******************************************");
//        cdsFallBack.dofallBack("",0,0);
//        System.out.println("******************************************");
//    }
//}