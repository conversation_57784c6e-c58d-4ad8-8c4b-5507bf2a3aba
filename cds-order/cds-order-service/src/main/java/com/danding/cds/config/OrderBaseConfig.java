package com.danding.cds.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/9/23 16:42
 */
@Component
@RefreshScope
@Data
public class OrderBaseConfig implements Serializable {

    /**
     * 配置分区自动结转是否自动推送
     */
    @Value("${auto_carry_over_order_push:false}")
    Boolean autoCarryOverOrderPush;
    @Value("${insufficient_leger_book_id_List:[]}")
    private Long[] insufficientLedgerBookIdList;
    @Value("${skip_hz_push_no_list:[]}")
    private String[] skipHzPushOrderList;
    @Value("${mock.jd.url:}")
    private String mockJdUrl;
    @Value("${endorsement.push.interval:1}")
    private Long endorsementPushInterval;
    @Value("${auto.carryOver.notify.config:}")
    private String autoCarryOverNotifyConfig;

    @Value("${ce.companyCode:}")
    private String ceCompanyCode;

    @Value("${byteDance.fb.note.config:}")
    private String fbNoteConfig;

    @Value("${export.item.writing.group.size.logistic:3000}")
    private Integer exportItemWritingGroupSizeLogistic;
    @Value("${export.item.writing.group.size.sku:999}")
    private Integer exportItemWritingGroupSizeSku;

    @Value("${inventory.special.company.idList:}")
    private Long[] inventorySpecialCompanyIdList;

    /**
     * 特殊查询条件中的ownerCode列表
     */
    @Value("${inventory.special.owner.code.list:}")
    private String[] inventorySpecialOwnerCodeList;
}
