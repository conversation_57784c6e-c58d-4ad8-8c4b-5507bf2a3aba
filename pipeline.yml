version: '1.1'

stages:
  - stage:
      - git-checkout:
         alias: repo

#  - stage:
#      - java:
#          alias: cds-company-service
#          params:
#            build_type: maven
#            workdir: ${repo}/cds-company
#            options: -am -pl cds-company-service
#            target: ./cds-company-service/target/ccs-company-service.jar
#            container_type: spring-boot
#      - java:
#          alias:  cds-item-service
#          params:
#            build_type: maven
#            workdir: ${repo}/cds-item
#            options: -am -pl cds-item-service
#            target: ./cds-item-service/target/ccs-item-service.jar
#            container_type: spring-boot
#      - java:
#          alias:  cds-order-service
#          params:
#            build_type: maven
#            workdir: ${repo}/cds-order
#            options: -am -pl cds-order-service
#            target: ./cds-order-service/target/ccs-order-service.jar
#            container_type: spring-boot
#      - java:
#          alias:  cds-web
#          params:
#            build_type: maven
#            workdir: ${repo}
#            options: -am -pl cds-web
#            target: ./cds-web/target/ccs-web.jar
#            container_type: spring-boot
#      - java:
#          alias:  cds-customs-declare
#          params:
#            build_type: maven
#            workdir: ${repo}/cds-customs-declare
#            options: -am -pl cds-customs-district-hangzhou
#            target: ./cds-customs-district-hangzhou/target/ccs-customs-district-hangzhou.jar
#            container_type: spring-boot

  - stage:
      - release:
          params:
            dice_yml: ${repo}/dice.yml
            image:
#              cds-company-service: ${cds-company-service:OUTPUT:image}
#              cds-item-service: ${cds-item-service:OUTPUT:image}
#              cds-order-service: ${cds-order-service:OUTPUT:image}
#              cds-web: ${cds-web:OUTPUT:image}
#              cds-fen-service: ${cds-customs-declare:OUTPUT:image}


  - stage:
      - dice:
          params:
            release_id_path: ${release}
