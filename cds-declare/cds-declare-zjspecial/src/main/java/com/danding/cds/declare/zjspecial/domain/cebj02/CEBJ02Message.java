package com.danding.cds.declare.zjspecial.domain.cebj02;

import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;
import java.util.List;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
})
@XmlRootElement(name = "CEBJ02Message")
@Data
public class CEBJ02Message implements Serializable {
    @XmlElement(name = "responseResult", required = true)
    private ResponseResult responseResult;

    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
    })
    @Data
    public static class ResponseResult implements Serializable {
        @XmlElement(name = "guid")
        protected String guid;
        @XmlElement(name = "invtSeqNo")
        protected String invtSeqNo;
        @XmlElement(name = "code")
        protected String code;
        @XmlElement(name = "results")
        protected Result results;
    }

    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
    })
    @Data
    public static class Result implements Serializable {
        @XmlElement(name = "information")
        protected List<String> information;
    }
}
