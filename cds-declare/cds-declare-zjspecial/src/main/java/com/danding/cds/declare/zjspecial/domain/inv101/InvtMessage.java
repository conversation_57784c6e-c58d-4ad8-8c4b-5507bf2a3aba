//
// ���ļ����� JavaTM Architecture for XML Binding (JAXB) ����ʵ�� v2.2.8-b130911.1802 ���ɵ�
// ����� <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// �����±���Դģʽʱ, �Դ��ļ��������޸Ķ�����ʧ��
// ����ʱ��: 2020.06.03 ʱ�� 05:12:37 PM CST 
//


package com.danding.cds.declare.zjspecial.domain.inv101;

import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;
import java.util.List;


/**
 * <p>anonymous complex type�� Java �ࡣ
 * <p>
 * <p>����ģʽƬ��ָ�������ڴ����е�Ԥ�����ݡ�
 * <p>
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="InvtHeadType" type="{}NemsInvtHeadType"/>
 *         &lt;element name="InvtListType" type="{}NemsInvtListType" maxOccurs="unbounded"/>
 *         &lt;element name="InvtDecHeadType" type="{}NemsInvtDecHeadType" minOccurs="0"/>
 *         &lt;element name="InvtDecListType" type="{}NemsInvtDecListType" maxOccurs="50" minOccurs="0"/>
 *         &lt;element name="InvtWarehouseType" type="{}NemsInvtWarehouseType" maxOccurs="unbounded"/>
 *         &lt;element name="InvtGoodsType" type="{}NemsInvtGoodsType" maxOccurs="unbounded"/>
 *         &lt;element name="InvtCbecBill" type="{}NemsInvtCbecBillType" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="SysId">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="3"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="OperCusRegCode">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="10"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
        "invtHeadType",
        "invtListType",
        "invtDecHeadType",
        "invtDecListType",
        "invtWarehouseType",
        "invtGoodsType",
        "invtCbecBill",
        "sysId",
        "operCusRegCode"
})
@XmlRootElement(name = "InvtMessage")
@Data
public class InvtMessage implements Serializable {

    @XmlElement(name = "InvtHeadType", required = true)
    protected NemsInvtHeadType invtHeadType;
    @XmlElement(name = "InvtListType", required = true)
    protected List<NemsInvtListType> invtListType;
    @XmlElement(name = "InvtDecHeadType")
    protected NemsInvtDecHeadType invtDecHeadType;
    @XmlElement(name = "InvtDecListType")
    protected List<NemsInvtDecListType> invtDecListType;
    @XmlElement(name = "InvtWarehouseType", required = true)
    protected List<NemsInvtWarehouseType> invtWarehouseType;
    @XmlElement(name = "InvtGoodsType", required = true)
    protected List<NemsInvtGoodsType> invtGoodsType;
    @XmlElement(name = "InvtCbecBill")
    protected List<NemsInvtCbecBillType> invtCbecBill;
    @XmlElement(name = "SysId", required = true)
    protected String sysId;
    @XmlElement(name = "OperCusRegCode", required = true)
    protected String operCusRegCode;

}
