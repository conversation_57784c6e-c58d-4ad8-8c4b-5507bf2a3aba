package com.danding.cds.declare.zjspecial.domain.inv211;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {

})
@Data
public class BwsDt implements Serializable {
    /**
     * 仓库账册号
     */
    @XmlElement(name = "bwsNo")
    private String bwsNo;
    /**
     * 变更次数
     */
    @XmlElement(name = "chgTmsCnt")
    private String chgTmsCnt;
    /**
     * 商品序号
     */
    @XmlElement(name = "gdsSeqno")
    private String gdsSeqno;
    /**
     * 最近入仓(核增）日期
     */
    @XmlElement(name = "inDate")
    private String inDate;
    /**
     * 商品料号
     */
    @XmlElement(name = "gdsMtno")
    private String gdsMtno;
    /**
     * 商品编码
     */
    @XmlElement(name = "gdecd")
    private String gdecd;
    /**
     * 商品名称
     */
    @XmlElement(name = "gdsNm")
    private String gdsNm;
    /**
     * 商品规格型号
     */
    @XmlElement(name = "gdsSpcfModelDesc")
    private String gdsSpcfModelDesc;
    /**
     * 国别代码
     */
    @XmlElement(name = "natcd")
    private String natcd;
    /**
     * 申报计量单位代码
     */
    @XmlElement(name = "dclUnitcd")
    private String dclUnitcd;
    /**
     * 法定计量单位代码
     */
    @XmlElement(name = "lawfUnitcd")
    private String lawfUnitcd;
    /**
     * 第二法定计量单位代码
     */
    @XmlElement(name = "secdLawfUnitcd")
    private String secdLawfUnitcd;
    /**
     * 申报单价金额
     */
    @XmlElement(name = "dclUprcAmt")
    private String dclUprcAmt;
    /**
     * 申报币制代码
     */
    @XmlElement(name = "dclCurrcd")
    private String dclCurrcd;
    /**
     * 平均美元单价
     */
    @XmlElement(name = "avgPrice")
    private String avgPrice;
    /**
     * 库存美元总价
     */
    @XmlElement(name = "totalAmt")
    private String totalAmt;
    /**
     * 入仓数量
     */
    @XmlElement(name = "inQty")
    private String inQty;
    /**
     * 入仓法定数量
     */
    @XmlElement(name = "inLawfQty")
    private String inLawfQty;
    /**
     * 第二入仓法定数量
     */
    @XmlElement(name = "inSecdLawfQty")
    private String inSecdLawfQty;
    /**
     * 实增数量
     */
    @XmlElement(name = "actlIncQty")
    private String actlIncQty;
    /**
     * 实减数量
     */
    @XmlElement(name = "actlRedcQty")
    private String actlRedcQty;
    /**
     * 预增数量
     */
    @XmlElement(name = "prevdIncQty")
    private String prevdIncQty;
    /**
     * 预减数量
     */
    @XmlElement(name = "prevdRedcQty")
    private String prevdRedcQty;
    /**
     * 最近出仓(区）日期
     */
    @XmlElement(name = "outDate")
    private String outDate;
    /**
     * 存储(监管）期限
     */
    @XmlElement(name = "limitDate")
    private String limitDate;
    /**
     * 设备入区方式代码 记账式系统自动返填,1:一线入区、2：二线入区、3:结转入区
     */
    @XmlElement(name = "inType")
    private String inType;
    /**
     * 记账清单编号
     */
    @XmlElement(name = "invtNo")
    private String invtNo;
    /**
     * 记账清单商品序号
     */
    @XmlElement(name = "invtGNo")
    private String invtGNo;
    /**
     * 海关执行标记代码
     */
    @XmlElement(name = "cusmExeMarkcd")
    private String cusmExeMarkcd;
    /**
     * 备注
     */
    @XmlElement(name = "rmk")
    private String rmk;
    /**
     * 修改标记 0-未修改 1-修改 2-删除 3-增加
     */
    @XmlElement(name = "modfMarkcd")
    private String modfMarkcd;
}
