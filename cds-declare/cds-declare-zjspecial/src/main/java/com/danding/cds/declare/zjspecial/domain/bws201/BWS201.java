package com.danding.cds.declare.zjspecial.domain.bws201;

import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;
import java.util.List;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
})
@XmlRootElement(name = "BWS201")
@Data
public class BWS201 implements Serializable {
    @XmlElement(name = "HdeApprResult")
    private HdeApprResult hdeApprResult;

    @XmlElement(name = "BwsBsc")
    private BwsBsc bwsBsc;

    @XmlElement(name = "BwsDt")
    private List<BwsDt> bwsDt;

}
