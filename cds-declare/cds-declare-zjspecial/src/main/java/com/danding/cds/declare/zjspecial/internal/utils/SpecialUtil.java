package com.danding.cds.declare.zjspecial.internal.utils;

import lombok.extern.slf4j.Slf4j;

import javax.xml.datatype.DatatypeFactory;
import javax.xml.datatype.XMLGregorianCalendar;
import java.util.Calendar;
import java.util.Date;
@Slf4j
public class SpecialUtil {

    public static XMLGregorianCalendar dateToXmlDate(Date date) {
        try {
            Calendar cal = Calendar.getInstance();
            cal.setTime(date);
            DatatypeFactory dtf = DatatypeFactory.newInstance();
            XMLGregorianCalendar dateType = dtf.newXMLGregorianCalendar();
            dateType.setYear(cal.get(Calendar.YEAR));
            //由于Calendar.MONTH取值范围为0~11,需要加1
            dateType.setMonth(cal.get(Calendar.MONTH) + 1);
            dateType.setDay(cal.get(Calendar.DAY_OF_MONTH));
            dateType.setHour(cal.get(Calendar.HOUR_OF_DAY));
            dateType.setMinute(cal.get(Calendar.MINUTE));
            dateType.setSecond(cal.get(Calendar.SECOND));
            return dateType;
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
        }
        return null;
    }
}
