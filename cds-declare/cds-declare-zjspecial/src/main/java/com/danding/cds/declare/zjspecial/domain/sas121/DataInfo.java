//
// ���ļ����� JavaTM Architecture for XML Binding (JAXB) ����ʵ�� v2.2.8-b130911.1802 ���ɵ�
// ����� <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// �����±���Դģʽʱ, �Դ��ļ��������޸Ķ�����ʧ��
// ����ʱ��: 2020.06.11 ʱ�� 05:07:42 PM CST 
//


package com.danding.cds.declare.zjspecial.domain.sas121;

import javax.xml.bind.annotation.*;
import java.io.Serializable;


/**
 * <p>anonymous complex type�� Java �ࡣ
 * 
 * <p>����ģʽƬ��ָ�������ڴ����е�Ԥ�����ݡ�
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element ref="{}PocketInfo"/>
 *         &lt;element name="BussinessData">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence>
 *                   &lt;element ref="{}PassPortMessage"/>
 *                   &lt;element name="DelcareFlag" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "pocketInfo",
    "bussinessData"
})
@XmlRootElement(name = "DataInfo")
public class DataInfo implements Serializable {

    @XmlElement(name = "PocketInfo", required = true)
    protected PocketInfo pocketInfo;
    @XmlElement(name = "BussinessData", required = true)
    protected BussinessData bussinessData;

    /**
     * ��ȡpocketInfo���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link PocketInfo }
     *     
     */
    public PocketInfo getPocketInfo() {
        return pocketInfo;
    }

    /**
     * ����pocketInfo���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link PocketInfo }
     *     
     */
    public void setPocketInfo(PocketInfo value) {
        this.pocketInfo = value;
    }

    /**
     * ��ȡbussinessData���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link BussinessData }
     *     
     */
    public BussinessData getBussinessData() {
        return bussinessData;
    }

    /**
     * ����bussinessData���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link BussinessData }
     *     
     */
    public void setBussinessData(BussinessData value) {
        this.bussinessData = value;
    }


    /**
     * <p>anonymous complex type�� Java �ࡣ
     * 
     * <p>����ģʽƬ��ָ�������ڴ����е�Ԥ�����ݡ�
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence>
     *         &lt;element ref="{}PassPortMessage"/>
     *         &lt;element name="DelcareFlag" type="{http://www.w3.org/2001/XMLSchema}int"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "passPortMessage",
        "delcareFlag"
    })
    public static class BussinessData implements Serializable {

        @XmlElement(name = "PassPortMessage", required = true)
        protected PassPortMessage passPortMessage;
        @XmlElement(name = "DelcareFlag")
        protected int delcareFlag;

        /**
         * ��ȡpassPortMessage���Ե�ֵ��
         * 
         * @return
         *     possible object is
         *     {@link PassPortMessage }
         *     
         */
        public PassPortMessage getPassPortMessage() {
            return passPortMessage;
        }

        /**
         * ����passPortMessage���Ե�ֵ��
         * 
         * @param value
         *     allowed object is
         *     {@link PassPortMessage }
         *     
         */
        public void setPassPortMessage(PassPortMessage value) {
            this.passPortMessage = value;
        }

        /**
         * ��ȡdelcareFlag���Ե�ֵ��
         * 
         */
        public int getDelcareFlag() {
            return delcareFlag;
        }

        /**
         * ����delcareFlag���Ե�ֵ��
         * 
         */
        public void setDelcareFlag(int value) {
            this.delcareFlag = value;
        }

    }

}
