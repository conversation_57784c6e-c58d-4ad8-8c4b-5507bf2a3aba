//
// ���ļ����� JavaTM Architecture for XML Binding (JAXB) ����ʵ�� v2.2.8-b130911.1802 ���ɵ�
// ����� <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// �����±���Դģʽʱ, �Դ��ļ��������޸Ķ�����ʧ��
// ����ʱ��: 2020.06.03 ʱ�� 05:12:37 PM CST 
//


package com.danding.cds.declare.zjspecial.domain.common;

import javax.xml.bind.annotation.*;
import java.io.Serializable;


@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "seqNo",
    "etpsPreentNo",
    "checkInfo",
    "dealFlag"
})
@XmlRootElement(name = "CommonResponeMessage")
public class CommonResponseMessage implements Serializable {

    @XmlElement(name = "SeqNo", required = true)
    private String seqNo;
    @XmlElement(name = "EtpsPreentNo", required = true)
    private String etpsPreentNo;
    @XmlElement(name = "CheckInfo", required = true)
    private String checkInfo;
    @XmlElement(name = "DealFlag", required = true)
    private String dealFlag;

    /**
     * ��ȡseqNo���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSeqNo() {
        return seqNo;
    }

    /**
     * ����seqNo���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSeqNo(String value) {
        this.seqNo = value;
    }

    /**
     * ��ȡetpsPreentNo���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEtpsPreentNo() {
        return etpsPreentNo;
    }

    /**
     * ����etpsPreentNo���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEtpsPreentNo(String value) {
        this.etpsPreentNo = value;
    }

    /**
     * ��ȡcheckInfo���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCheckInfo() {
        return checkInfo;
    }

    /**
     * ����checkInfo���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCheckInfo(String value) {
        this.checkInfo = value;
    }

    /**
     * ��ȡdealFlag���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDealFlag() {
        return dealFlag;
    }

    /**
     * ����dealFlag���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDealFlag(String value) {
        this.dealFlag = value;
    }

}
