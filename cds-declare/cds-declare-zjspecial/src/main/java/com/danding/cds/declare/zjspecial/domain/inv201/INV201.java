package com.danding.cds.declare.zjspecial.domain.inv201;

import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;
import java.util.List;

/**
 * @author: create by wu<PERSON><PERSON>
 * @version: v1.0
 * @description: com.sfebiz.logistics.service.customs.declare.customsaccount.inv201
 * @date:2020/6/10
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
})
@XmlRootElement(name = "INV201")
@Data
public class INV201 implements Serializable {

    @XmlElement(name = "HdeApprResult")
    protected HdeApprResult hdeApprResult;
    @XmlElement(name = "CheckInfo")
    protected List<CheckInfo> checkInfo;
    @XmlElement(name = "BondInvtBsc")
    protected BondInvtBsc bondInvtBsc;
    @XmlElement(name = "BondInvtDt")
    protected List<BondInvtDt> bondInvtDt;

    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {

    })
    @Data
    public static class HdeApprResult implements Serializable {

        @XmlElement(name = "etpsPreentNo")
        protected String etpsPreentNo;
        @XmlElement(name = "businessId")
        protected String businessId;
        @XmlElement(name = "tmsCnt")
        protected String tmsCnt;
        @XmlElement(name = "typecd")
        protected String typecd;
        @XmlElement(name = "manageResult")
        protected String manageResult;
        @XmlElement(name = "manageDate")
        protected String manageDate;
        @XmlElement(name = "rmk")
        protected String rmk;
    }

    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {

    })
    @Data
    public static class CheckInfo implements Serializable {

        @XmlElement(name = "note")
        protected String note;
    }


    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {

    })
    @Data
    public static class BondInvtBsc implements Serializable {

        @XmlElement(name = "bondInvtNo")
        protected String bondInvtNo;
        @XmlElement(name = "chgTmsCnt")
        protected String chgTmsCnt;
        @XmlElement(name = "invtPreentNo")
        protected String invtPreentNo;
        @XmlElement(name = "putrecNo")
        protected String putrecNo;
        @XmlElement(name = "etpsInnerInvtNo")
        protected String etpsInnerInvtNo;
        @XmlElement(name = "bizopEtpsSccd")
        protected String bizopEtpsSccd;
        @XmlElement(name = "bizopEtpsno")
        protected String bizopEtpsno;
        @XmlElement(name = "bizopEtpsNm")
        protected String bizopEtpsNm;
        @XmlElement(name = "rvsngdEtpsSccd")
        protected String rvsngdEtpsSccd;
        @XmlElement(name = "rcvgdEtpsno")
        protected String rcvgdEtpsno;
        @XmlElement(name = "rcvgdEtpsNm")
        protected String rcvgdEtpsNm;
        @XmlElement(name = "dclEtpsSccd")
        protected String dclEtpsSccd;
        @XmlElement(name = "dclEtpsno")
        protected String dclEtpsno;
        @XmlElement(name = "dclEtpsNm")
        protected String dclEtpsNm;
        @XmlElement(name = "invtDclTime")
        protected String invtDclTime;
        @XmlElement(name = "entryDclTime")
        protected String entryDclTime;
        @XmlElement(name = "entryNo")
        protected String entryNo;
        @XmlElement(name = "rltInvtNo")
        protected String rltInvtNo;
        @XmlElement(name = "rltPutrecNo")
        protected String rltPutrecNo;
        @XmlElement(name = "rltEntryNo")
        protected String rltEntryNo;
        @XmlElement(name = "rltEntryBizopEtpsSccd")
        protected String rltEntryBizopEtpsSccd;
        @XmlElement(name = "rltEntryBizopEtpsno")
        protected String rltEntryBizopEtpsno;
        @XmlElement(name = "rltEntryBizopEtpsNm")
        protected String rltEntryBizopEtpsNm;
        @XmlElement(name = "impexpPortcd")
        protected String impexpPortcd;
        @XmlElement(name = "dclPlcCuscd")
        protected String dclPlcCuscd;
        @XmlElement(name = "impexpMarkcd")
        protected String impexpMarkcd;
        @XmlElement(name = "mtpckEndprdMarkcd")
        protected String mtpckEndprdMarkcd;
        @XmlElement(name = "supvModecd")
        protected String supvModecd;
        @XmlElement(name = "trspModecd")
        protected String trspModecd;
        @XmlElement(name = "stshipTrsarvNatcd")
        protected String stshipTrsarvNatcd;
        @XmlElement(name = "applyNo")
        protected String applyNo;
        @XmlElement(name = "dclcusFlag")
        protected String dclcusFlag;
        @XmlElement(name = "dclcusTypecd")
        protected String dclcusTypecd;
        @XmlElement(name = "prevdTime")
        protected String prevdTime;
        @XmlElement(name = "formalVrfdedTime")
        protected String formalVrfdedTime;
        @XmlElement(name = "invtIochkptStucd")
        protected String invtIochkptStucd;
        @XmlElement(name = "vrfdedMarkcd")
        protected String vrfdedMarkcd;
        @XmlElement(name = "invtStucd")
        protected String invtStucd;
        @XmlElement(name = "vrfdedModecd")
        protected String vrfdedModecd;
        @XmlElement(name = "duCode")
        protected String duCode;
        @XmlElement(name = "rmk")
        protected String rmk;
        @XmlElement(name = "bondInvtTypecd")
        protected String bondInvtTypecd;
        @XmlElement(name = "entryStucd")
        protected String entryStucd;
        @XmlElement(name = "passportUsedTypecd")
        protected String passportUsedTypecd;
        @XmlElement(name = "param1")
        protected String param1;
        @XmlElement(name = "param2")
        protected String param2;
        @XmlElement(name = "param3")
        protected String param3;
        @XmlElement(name = "param4")
        protected String param4;
        @XmlElement(name = "needEntryModified")
        protected String needEntryModified;
        @XmlElement(name = "levyBlAmt")
        protected String levyBlAmt;
        @XmlElement(name = "dclTypecd")
        protected String dclTypecd;
    }

    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {

    })
    @Data
    public static class BondInvtDt implements Serializable {

        @XmlElement(name = "bondInvtNo")
        protected String bondInvtNo;
        @XmlElement(name = "chgTmsCnt")
        protected String chgTmsCnt;
        @XmlElement(name = "gdsSeqno")
        protected String gdsSeqno;
        @XmlElement(name = "putrecSeqno")
        protected String putrecSeqno;
        @XmlElement(name = "gdsMtno")
        protected String gdsMtno;
        @XmlElement(name = "gdecd")
        protected String gdecd;
        @XmlElement(name = "gdsNm")
        protected String gdsNm;
        @XmlElement(name = "gdsSpcfModelDesc")
        protected String gdsSpcfModelDesc;
        @XmlElement(name = "dclUnitcd")
        protected String dclUnitcd;
        @XmlElement(name = "lawfUnitcd")
        protected String lawfUnitcd;
        @XmlElement(name = "secdLawfUnitcd")
        protected String secdLawfUnitcd;
        @XmlElement(name = "natcd")
        protected String natcd;
        @XmlElement(name = "dclUprcAmt")
        protected String dclUprcAmt;
        @XmlElement(name = "dclTotalAmt")
        protected String dclTotalAmt;
        @XmlElement(name = "usdStatTotalAmt")
        protected String usdStatTotalAmt;
        @XmlElement(name = "dclCurrcd")
        protected String dclCurrcd;
        @XmlElement(name = "lawfQty")
        protected String lawfQty;
        @XmlElement(name = "secdLawfQty")
        protected String secdLawfQty;
        @XmlElement(name = "wtSfVal")
        protected String wtSfVal;
        @XmlElement(name = "fstSfVal")
        protected String fstSfVal;
        @XmlElement(name = "secdSfVal")
        protected String secdSfVal;
        @XmlElement(name = "dclQty")
        protected String dclQty;
        @XmlElement(name = "grossWt")
        protected String grossWt;
        @XmlElement(name = "netWt")
        protected String netWt;
        @XmlElement(name = "lvyrlfModecd")
        protected String lvyrlfModecd;
        @XmlElement(name = "ucnsVerno")
        protected String ucnsVerno;
        @XmlElement(name = "entryGdsSeqno")
        protected String entryGdsSeqno;
        @XmlElement(name = "applyTbSeqno")
        protected String applyTbSeqno;
        @XmlElement(name = "clyMarkcd")
        protected String clyMarkcd;
        @XmlElement(name = "rmk")
        protected String rmk;
        @XmlElement(name = "actlPassQty")
        protected String actlPassQty;
        @XmlElement(name = "passportUsedQty")
        protected String passportUsedQty;
        @XmlElement(name = "param1")
        protected String param1;
        @XmlElement(name = "param2")
        protected String param2;
        @XmlElement(name = "param3")
        protected String param3;
        @XmlElement(name = "param4")
        protected String param4;
        @XmlElement(name = "destinationNatcd")
        protected String destinationNatcd;
        @XmlElement(name = "modfMarkcd")
        protected String modfMarkcd;
    }
}
