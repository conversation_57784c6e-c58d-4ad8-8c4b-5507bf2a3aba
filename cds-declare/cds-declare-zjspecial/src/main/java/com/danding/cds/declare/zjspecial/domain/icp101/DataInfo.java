package com.danding.cds.declare.zjspecial.domain.icp101;


import com.danding.cds.declare.zjspecial.domain.sas121.PocketInfo;
import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Create 2021/4/26  15:16
 * @Describe
 **/
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
        "pocketInfo",
        "bussinessData"
})
@XmlRootElement(name = "DataInfo")
@Data
public class DataInfo implements Serializable {

    @XmlElement(name = "PocketInfo", required = true)
    protected PocketInfo pocketInfo;
    @XmlElement(name = "BussinessData", required = true)
    protected BussinessData bussinessData;

    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
            "sas2sPassPortMessage",
            "delcareFlag"
    })
    @Data
    public static class BussinessData implements Serializable {

        @XmlElement(name = "Sas2sPassPortMessage", required = true)
        protected Sas2sPassPortMessage sas2sPassPortMessage;
        @XmlElement(name = "DelcareFlag")
        protected int delcareFlag;
    }


}
