package com.danding.cds.declare.zjspecial.domain.sas101;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.List;

@Data
@XmlRootElement(name = "AppMessage")
@XmlAccessorType(XmlAccessType.FIELD)
public class AppMessage implements Serializable {
    @XmlElement(name = "AppHead", required = true)
    private AppHead appHead;

    @XmlElement(name = "AppGoods", required = true)
    private List<AppGoods> appGoodsList;

    @XmlElement(name = "AppUcns", required = true)
    private List<AppUcns> appUcnsList;

    @XmlElement(name = "OperCusRegCode", required = true)
    private String operCusRegCode;

    @XmlElement(name = "SasSign", required = true)
    private SasSign sasSign;
}