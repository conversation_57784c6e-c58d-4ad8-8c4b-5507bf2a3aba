package com.danding.cds.declare.zjspecial.domain.bws201;

import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
})
@XmlRootElement(name = "BwsBsc")
@Data
public class BwsBsc implements Serializable {
    @XmlElement(name = "bwsNo")
    private String bwsNo;
    @XmlElement(name = "chgTmsCnt")
    private String chgTmsCnt;
    @XmlElement(name = "etpsPreentNo")
    private String etpsPreentNo;
    @XmlElement(name = "dclTypecd")
    private String dclTypecd;
    @XmlElement(name = "bwlTypecd")
    private String bwlTypecd;
    @XmlElement(name = "masterCuscd")
    private String masterCuscd;
    @XmlElement(name = "bizopEtpsno")
    private String bizopEtpsno;
    @XmlElement(name = "bizopEtpsNm")
    private String bizopEtpsNm;
    @XmlElement(name = "bizopEtpsSccd")
    private String bizopEtpsSccd;
    @XmlElement(name = "houseNo")
    private String houseNo;
    @XmlElement(name = "houseNm")
    private String houseNm;
    @XmlElement(name = "dclEtpsno")
    private String dclEtpsno;
    @XmlElement(name = "dclEtpsNm")
    private String dclEtpsNm;
    @XmlElement(name = "dclEtpsSccd")
    private String dclEtpsSccd;
    @XmlElement(name = "dclEtpsTypecd")
    private String dclEtpsTypecd;
    @XmlElement(name = "contactEr")
    private String contactEr;
    @XmlElement(name = "contactTele")
    private String contactTele;
    @XmlElement(name = "houseTypecd")
    private String houseTypecd;
    @XmlElement(name = "houseArea")
    private String houseArea;
    @XmlElement(name = "houseVolume")
    private String houseVolume;
    @XmlElement(name = "houseAddress")
    private String houseAddress;
    @XmlElement(name = "dclTime")
    private String dclTime;
    @XmlElement(name = "inputDate")
    private String inputDate;
    @XmlElement(name = "taxTypecd")
    private String taxTypecd;
    @XmlElement(name = "putrecApprTime")
    private String putrecApprTime;
    @XmlElement(name = "chgApprTime")
    private String chgApprTime;
    @XmlElement(name = "finishValidDate")
    private String finishValidDate;
    @XmlElement(name = "pauseChgMarkcd")
    private String pauseChgMarkcd;
    @XmlElement(name = "emapvStucd")
    private String emapvStucd;
    @XmlElement(name = "dclMarkcd")
    private String dclMarkcd;
    @XmlElement(name = "appendTypecd")
    private String appendTypecd;
    @XmlElement(name = "rmk")
    private String rmk;
    @XmlElement(name = "ownerSystem")
    private String ownerSystem;
    @XmlElement(name = "usageTypecd")
    private String usageTypecd;
}
