//
// ���ļ����� JavaTM Architecture for XML Binding (JAXB) ����ʵ�� v2.2.8-b130911.1802 ���ɵ�
// ����� <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// �����±���Դģʽʱ, �Դ��ļ��������޸Ķ�����ʧ��
// ����ʱ��: 2020.06.03 ʱ�� 05:12:37 PM CST 
//


package com.danding.cds.declare.zjspecial.domain.inv101;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;


/**
 * ��ע�嵥��ͷ
 * 
 * <p>NemsInvtHeadType complex type�� Java �ࡣ
 * 
 * <p>����ģʽƬ��ָ�������ڴ����е�Ԥ�����ݡ�
 * 
 * <pre>
 * &lt;complexType name="NemsInvtHeadType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="SeqNo">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="18"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="BondInvtNo">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="64"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="ChgTmsCnt">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="19"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="PutrecNo">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="64"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="EtpsInnerInvtNo">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="64"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="BizopEtpsSccd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="18"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="BizopEtpsno">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="1"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="BizopEtpsNm">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="512"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="RvsngdEtpsSccd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="18"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="RcvgdEtpsno">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="10"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="RcvgdEtpsNm">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="512"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="DclEtpsSccd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="18"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="DclEtpsno">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="10"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="DclEtpsNm">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="512"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="InputCode">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="10"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="InputCreditCode">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="18"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="InputName">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="255"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="InputTime">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="19"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="InvtDclTime">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="19"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="EntryDclTime">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="19"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="EntryNo">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="64"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="CorrEntryDclEtpsSccd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="18"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="CorrEntryDclEtpsNo">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="10"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="CorrEntryDclEtpsNm">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="512"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="RltInvtNo">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="64"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="RltPutrecNo">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="64"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="RltEntryNo">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="64"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="RltEntryBizopEtpsSccd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="18"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="RltEntryBizopEtpsno">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="10"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="RltEntryBizopEtpsNm">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="512"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="RltEntryRvsngdEtpsSccd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="18"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="RltEntryRcvgdEtpsno">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="10"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="RltEntryRcvgdEtpsNm">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="512"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="RltEntryDclEtpsSccd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="18"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="RltEntryDclEtpsNm">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="512"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="RltEntryDclEtpsno">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="10"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="ImpexpPortcd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="4"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="DclPlcCuscd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="4"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="ImpexpMarkcd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="4"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="MtpckEndprdMarkcd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="4"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="SupvModecd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="6"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="TrspModecd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="6"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="ApplyNo">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="64"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="ListType">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="1"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="DclcusFlag">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="1"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="DclcusTypecd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="25"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="PrevdTime">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="19"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="FormalVrfdedTime">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="19"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="InvtIochkptStucd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="4"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="VrfdedMarkcd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="4"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="IcCardNo">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="20"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="ListStat">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="1"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="DecType">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="1"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="Rmk" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="4000"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="StshipTrsarvNatcd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="3"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="InvtType">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="1"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="EntryStucd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="1"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="PassportUsedTypeCd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="1"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="NeedEntryModified" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="1"/>
 *               &lt;minLength value="0"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="LevyBlAmt" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="25"/>
 *               &lt;minLength value="0"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="DclTypecd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="25"/>
 *               &lt;minLength value="0"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="GenDecFlag">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="1"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "NemsInvtHeadType", propOrder = {
    "seqNo",
    "bondInvtNo",
    "chgTmsCnt",
    "putrecNo",
    "etpsInnerInvtNo",
    "bizopEtpsSccd",
    "bizopEtpsno",
    "bizopEtpsNm",
    "rvsngdEtpsSccd",
    "rcvgdEtpsno",
    "rcvgdEtpsNm",
    "dclEtpsSccd",
    "dclEtpsno",
    "dclEtpsNm",
    "inputCode",
    "inputCreditCode",
    "inputName",
    "inputTime",
    "invtDclTime",
    "entryDclTime",
    "entryNo",
    "corrEntryDclEtpsSccd",
    "corrEntryDclEtpsNo",
    "corrEntryDclEtpsNm",
    "rltInvtNo",
    "rltPutrecNo",
    "rltEntryNo",
    "rltEntryBizopEtpsSccd",
    "rltEntryBizopEtpsno",
    "rltEntryBizopEtpsNm",
    "rltEntryRvsngdEtpsSccd",
    "rltEntryRcvgdEtpsno",
    "rltEntryRcvgdEtpsNm",
    "rltEntryDclEtpsSccd",
    "rltEntryDclEtpsNm",
    "rltEntryDclEtpsno",
    "impexpPortcd",
    "dclPlcCuscd",
    "impexpMarkcd",
    "mtpckEndprdMarkcd",
    "supvModecd",
    "trspModecd",
    "applyNo",
    "listType",
    "dclcusFlag",
    "dclcusTypecd",
    "prevdTime",
    "formalVrfdedTime",
    "invtIochkptStucd",
    "vrfdedMarkcd",
    "icCardNo",
    "listStat",
    "decType",
    "rmk",
    "stshipTrsarvNatcd",
    "invtType",
    "entryStucd",
    "passportUsedTypeCd",
    "needEntryModified",
    "levyBlAmt",
    "dclTypecd",
    "genDecFlag"
})
public class NemsInvtHeadType implements Serializable {

    @XmlElement(name = "SeqNo", required = true)
    protected String seqNo;
    @XmlElement(name = "BondInvtNo", required = true)
    protected String bondInvtNo;
    @XmlElement(name = "ChgTmsCnt", required = true)
    protected String chgTmsCnt;
    @XmlElement(name = "PutrecNo", required = true)
    protected String putrecNo;
    @XmlElement(name = "EtpsInnerInvtNo", required = true)
    protected String etpsInnerInvtNo;
    @XmlElement(name = "BizopEtpsSccd", required = true)
    protected String bizopEtpsSccd;
    @XmlElement(name = "BizopEtpsno", required = true)
    protected String bizopEtpsno;
    @XmlElement(name = "BizopEtpsNm", required = true)
    protected String bizopEtpsNm;
    @XmlElement(name = "RvsngdEtpsSccd", required = true)
    protected String rvsngdEtpsSccd;
    @XmlElement(name = "RcvgdEtpsno", required = true)
    protected String rcvgdEtpsno;
    @XmlElement(name = "RcvgdEtpsNm", required = true)
    protected String rcvgdEtpsNm;
    @XmlElement(name = "DclEtpsSccd", required = true)
    protected String dclEtpsSccd;
    @XmlElement(name = "DclEtpsno", required = true)
    protected String dclEtpsno;
    @XmlElement(name = "DclEtpsNm", required = true)
    protected String dclEtpsNm;
    @XmlElement(name = "InputCode", required = true)
    protected String inputCode;
    @XmlElement(name = "InputCreditCode", required = true)
    protected String inputCreditCode;
    @XmlElement(name = "InputName", required = true)
    protected String inputName;
    @XmlElement(name = "InputTime", required = true)
    protected String inputTime;
    @XmlElement(name = "InvtDclTime", required = true)
    protected String invtDclTime;
    @XmlElement(name = "EntryDclTime", required = true)
    protected String entryDclTime;
    @XmlElement(name = "EntryNo", required = true)
    protected String entryNo;
    @XmlElement(name = "CorrEntryDclEtpsSccd", required = true)
    protected String corrEntryDclEtpsSccd;
    @XmlElement(name = "CorrEntryDclEtpsNo", required = true)
    protected String corrEntryDclEtpsNo;
    @XmlElement(name = "CorrEntryDclEtpsNm", required = true)
    protected String corrEntryDclEtpsNm;
    @XmlElement(name = "RltInvtNo", required = true)
    protected String rltInvtNo;
    @XmlElement(name = "RltPutrecNo", required = true)
    protected String rltPutrecNo;
    @XmlElement(name = "RltEntryNo", required = true)
    protected String rltEntryNo;
    @XmlElement(name = "RltEntryBizopEtpsSccd", required = true)
    protected String rltEntryBizopEtpsSccd;
    @XmlElement(name = "RltEntryBizopEtpsno", required = true)
    protected String rltEntryBizopEtpsno;
    @XmlElement(name = "RltEntryBizopEtpsNm", required = true)
    protected String rltEntryBizopEtpsNm;
    @XmlElement(name = "RltEntryRvsngdEtpsSccd", required = true)
    protected String rltEntryRvsngdEtpsSccd;
    @XmlElement(name = "RltEntryRcvgdEtpsno", required = true)
    protected String rltEntryRcvgdEtpsno;
    @XmlElement(name = "RltEntryRcvgdEtpsNm", required = true)
    protected String rltEntryRcvgdEtpsNm;
    @XmlElement(name = "RltEntryDclEtpsSccd", required = true)
    protected String rltEntryDclEtpsSccd;
    @XmlElement(name = "RltEntryDclEtpsNm", required = true)
    protected String rltEntryDclEtpsNm;
    @XmlElement(name = "RltEntryDclEtpsno", required = true)
    protected String rltEntryDclEtpsno;
    @XmlElement(name = "ImpexpPortcd", required = true)
    protected String impexpPortcd;
    @XmlElement(name = "DclPlcCuscd", required = true)
    protected String dclPlcCuscd;
    @XmlElement(name = "ImpexpMarkcd", required = true)
    protected String impexpMarkcd;
    @XmlElement(name = "MtpckEndprdMarkcd", required = true)
    protected String mtpckEndprdMarkcd;
    @XmlElement(name = "SupvModecd", required = true)
    protected String supvModecd;
    @XmlElement(name = "TrspModecd", required = true)
    protected String trspModecd;
    @XmlElement(name = "ApplyNo", required = true)
    protected String applyNo;
    @XmlElement(name = "ListType", required = true)
    protected String listType;
    @XmlElement(name = "DclcusFlag", required = true)
    protected String dclcusFlag;
    @XmlElement(name = "DclcusTypecd", required = true)
    protected String dclcusTypecd;
    @XmlElement(name = "PrevdTime", required = true)
    protected String prevdTime;
    @XmlElement(name = "FormalVrfdedTime", required = true)
    protected String formalVrfdedTime;
    @XmlElement(name = "InvtIochkptStucd", required = true)
    protected String invtIochkptStucd;
    @XmlElement(name = "VrfdedMarkcd", required = true)
    protected String vrfdedMarkcd;
    @XmlElement(name = "IcCardNo", required = true)
    protected String icCardNo;
    @XmlElement(name = "ListStat", required = true)
    protected String listStat;
    @XmlElement(name = "DecType", required = true)
    protected String decType;
    @XmlElement(name = "Rmk")
    protected String rmk;
    @XmlElement(name = "StshipTrsarvNatcd", required = true)
    protected String stshipTrsarvNatcd;
    @XmlElement(name = "InvtType", required = true)
    protected String invtType;
    @XmlElement(name = "EntryStucd", required = true)
    protected String entryStucd;
    @XmlElement(name = "PassportUsedTypeCd", required = true)
    protected String passportUsedTypeCd;
    @XmlElement(name = "NeedEntryModified")
    protected String needEntryModified;
    @XmlElement(name = "LevyBlAmt")
    protected String levyBlAmt;
    @XmlElement(name = "DclTypecd", required = true)
    protected String dclTypecd;
    @XmlElement(name = "GenDecFlag", required = true)
    protected String genDecFlag;

    /**
     * ��ȡseqNo���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSeqNo() {
        return seqNo;
    }

    /**
     * ����seqNo���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSeqNo(String value) {
        this.seqNo = value;
    }

    /**
     * ��ȡbondInvtNo���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBondInvtNo() {
        return bondInvtNo;
    }

    /**
     * ����bondInvtNo���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBondInvtNo(String value) {
        this.bondInvtNo = value;
    }

    /**
     * ��ȡchgTmsCnt���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getChgTmsCnt() {
        return chgTmsCnt;
    }

    /**
     * ����chgTmsCnt���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setChgTmsCnt(String value) {
        this.chgTmsCnt = value;
    }

    /**
     * ��ȡputrecNo���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPutrecNo() {
        return putrecNo;
    }

    /**
     * ����putrecNo���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPutrecNo(String value) {
        this.putrecNo = value;
    }

    /**
     * ��ȡetpsInnerInvtNo���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEtpsInnerInvtNo() {
        return etpsInnerInvtNo;
    }

    /**
     * ����etpsInnerInvtNo���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEtpsInnerInvtNo(String value) {
        this.etpsInnerInvtNo = value;
    }

    /**
     * ��ȡbizopEtpsSccd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBizopEtpsSccd() {
        return bizopEtpsSccd;
    }

    /**
     * ����bizopEtpsSccd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBizopEtpsSccd(String value) {
        this.bizopEtpsSccd = value;
    }

    /**
     * ��ȡbizopEtpsno���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBizopEtpsno() {
        return bizopEtpsno;
    }

    /**
     * ����bizopEtpsno���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBizopEtpsno(String value) {
        this.bizopEtpsno = value;
    }

    /**
     * ��ȡbizopEtpsNm���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBizopEtpsNm() {
        return bizopEtpsNm;
    }

    /**
     * ����bizopEtpsNm���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBizopEtpsNm(String value) {
        this.bizopEtpsNm = value;
    }

    /**
     * ��ȡrvsngdEtpsSccd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRvsngdEtpsSccd() {
        return rvsngdEtpsSccd;
    }

    /**
     * ����rvsngdEtpsSccd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRvsngdEtpsSccd(String value) {
        this.rvsngdEtpsSccd = value;
    }

    /**
     * ��ȡrcvgdEtpsno���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRcvgdEtpsno() {
        return rcvgdEtpsno;
    }

    /**
     * ����rcvgdEtpsno���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRcvgdEtpsno(String value) {
        this.rcvgdEtpsno = value;
    }

    /**
     * ��ȡrcvgdEtpsNm���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRcvgdEtpsNm() {
        return rcvgdEtpsNm;
    }

    /**
     * ����rcvgdEtpsNm���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRcvgdEtpsNm(String value) {
        this.rcvgdEtpsNm = value;
    }

    /**
     * ��ȡdclEtpsSccd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDclEtpsSccd() {
        return dclEtpsSccd;
    }

    /**
     * ����dclEtpsSccd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDclEtpsSccd(String value) {
        this.dclEtpsSccd = value;
    }

    /**
     * ��ȡdclEtpsno���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDclEtpsno() {
        return dclEtpsno;
    }

    /**
     * ����dclEtpsno���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDclEtpsno(String value) {
        this.dclEtpsno = value;
    }

    /**
     * ��ȡdclEtpsNm���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDclEtpsNm() {
        return dclEtpsNm;
    }

    /**
     * ����dclEtpsNm���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDclEtpsNm(String value) {
        this.dclEtpsNm = value;
    }

    /**
     * ��ȡinputCode���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getInputCode() {
        return inputCode;
    }

    /**
     * ����inputCode���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setInputCode(String value) {
        this.inputCode = value;
    }

    /**
     * ��ȡinputCreditCode���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getInputCreditCode() {
        return inputCreditCode;
    }

    /**
     * ����inputCreditCode���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setInputCreditCode(String value) {
        this.inputCreditCode = value;
    }

    /**
     * ��ȡinputName���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getInputName() {
        return inputName;
    }

    /**
     * ����inputName���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setInputName(String value) {
        this.inputName = value;
    }

    /**
     * ��ȡinputTime���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getInputTime() {
        return inputTime;
    }

    /**
     * ����inputTime���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setInputTime(String value) {
        this.inputTime = value;
    }

    /**
     * ��ȡinvtDclTime���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getInvtDclTime() {
        return invtDclTime;
    }

    /**
     * ����invtDclTime���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setInvtDclTime(String value) {
        this.invtDclTime = value;
    }

    /**
     * ��ȡentryDclTime���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEntryDclTime() {
        return entryDclTime;
    }

    /**
     * ����entryDclTime���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEntryDclTime(String value) {
        this.entryDclTime = value;
    }

    /**
     * ��ȡentryNo���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEntryNo() {
        return entryNo;
    }

    /**
     * ����entryNo���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEntryNo(String value) {
        this.entryNo = value;
    }

    /**
     * ��ȡcorrEntryDclEtpsSccd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCorrEntryDclEtpsSccd() {
        return corrEntryDclEtpsSccd;
    }

    /**
     * ����corrEntryDclEtpsSccd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCorrEntryDclEtpsSccd(String value) {
        this.corrEntryDclEtpsSccd = value;
    }

    /**
     * ��ȡcorrEntryDclEtpsNo���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCorrEntryDclEtpsNo() {
        return corrEntryDclEtpsNo;
    }

    /**
     * ����corrEntryDclEtpsNo���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCorrEntryDclEtpsNo(String value) {
        this.corrEntryDclEtpsNo = value;
    }

    /**
     * ��ȡcorrEntryDclEtpsNm���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCorrEntryDclEtpsNm() {
        return corrEntryDclEtpsNm;
    }

    /**
     * ����corrEntryDclEtpsNm���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCorrEntryDclEtpsNm(String value) {
        this.corrEntryDclEtpsNm = value;
    }

    /**
     * ��ȡrltInvtNo���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRltInvtNo() {
        return rltInvtNo;
    }

    /**
     * ����rltInvtNo���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRltInvtNo(String value) {
        this.rltInvtNo = value;
    }

    /**
     * ��ȡrltPutrecNo���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRltPutrecNo() {
        return rltPutrecNo;
    }

    /**
     * ����rltPutrecNo���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRltPutrecNo(String value) {
        this.rltPutrecNo = value;
    }

    /**
     * ��ȡrltEntryNo���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRltEntryNo() {
        return rltEntryNo;
    }

    /**
     * ����rltEntryNo���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRltEntryNo(String value) {
        this.rltEntryNo = value;
    }

    /**
     * ��ȡrltEntryBizopEtpsSccd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRltEntryBizopEtpsSccd() {
        return rltEntryBizopEtpsSccd;
    }

    /**
     * ����rltEntryBizopEtpsSccd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRltEntryBizopEtpsSccd(String value) {
        this.rltEntryBizopEtpsSccd = value;
    }

    /**
     * ��ȡrltEntryBizopEtpsno���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRltEntryBizopEtpsno() {
        return rltEntryBizopEtpsno;
    }

    /**
     * ����rltEntryBizopEtpsno���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRltEntryBizopEtpsno(String value) {
        this.rltEntryBizopEtpsno = value;
    }

    /**
     * ��ȡrltEntryBizopEtpsNm���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRltEntryBizopEtpsNm() {
        return rltEntryBizopEtpsNm;
    }

    /**
     * ����rltEntryBizopEtpsNm���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRltEntryBizopEtpsNm(String value) {
        this.rltEntryBizopEtpsNm = value;
    }

    /**
     * ��ȡrltEntryRvsngdEtpsSccd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRltEntryRvsngdEtpsSccd() {
        return rltEntryRvsngdEtpsSccd;
    }

    /**
     * ����rltEntryRvsngdEtpsSccd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRltEntryRvsngdEtpsSccd(String value) {
        this.rltEntryRvsngdEtpsSccd = value;
    }

    /**
     * ��ȡrltEntryRcvgdEtpsno���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRltEntryRcvgdEtpsno() {
        return rltEntryRcvgdEtpsno;
    }

    /**
     * ����rltEntryRcvgdEtpsno���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRltEntryRcvgdEtpsno(String value) {
        this.rltEntryRcvgdEtpsno = value;
    }

    /**
     * ��ȡrltEntryRcvgdEtpsNm���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRltEntryRcvgdEtpsNm() {
        return rltEntryRcvgdEtpsNm;
    }

    /**
     * ����rltEntryRcvgdEtpsNm���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRltEntryRcvgdEtpsNm(String value) {
        this.rltEntryRcvgdEtpsNm = value;
    }

    /**
     * ��ȡrltEntryDclEtpsSccd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRltEntryDclEtpsSccd() {
        return rltEntryDclEtpsSccd;
    }

    /**
     * ����rltEntryDclEtpsSccd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRltEntryDclEtpsSccd(String value) {
        this.rltEntryDclEtpsSccd = value;
    }

    /**
     * ��ȡrltEntryDclEtpsNm���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRltEntryDclEtpsNm() {
        return rltEntryDclEtpsNm;
    }

    /**
     * ����rltEntryDclEtpsNm���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRltEntryDclEtpsNm(String value) {
        this.rltEntryDclEtpsNm = value;
    }

    /**
     * ��ȡrltEntryDclEtpsno���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRltEntryDclEtpsno() {
        return rltEntryDclEtpsno;
    }

    /**
     * ����rltEntryDclEtpsno���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRltEntryDclEtpsno(String value) {
        this.rltEntryDclEtpsno = value;
    }

    /**
     * ��ȡimpexpPortcd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getImpexpPortcd() {
        return impexpPortcd;
    }

    /**
     * ����impexpPortcd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setImpexpPortcd(String value) {
        this.impexpPortcd = value;
    }

    /**
     * ��ȡdclPlcCuscd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDclPlcCuscd() {
        return dclPlcCuscd;
    }

    /**
     * ����dclPlcCuscd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDclPlcCuscd(String value) {
        this.dclPlcCuscd = value;
    }

    /**
     * ��ȡimpexpMarkcd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getImpexpMarkcd() {
        return impexpMarkcd;
    }

    /**
     * ����impexpMarkcd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setImpexpMarkcd(String value) {
        this.impexpMarkcd = value;
    }

    /**
     * ��ȡmtpckEndprdMarkcd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMtpckEndprdMarkcd() {
        return mtpckEndprdMarkcd;
    }

    /**
     * ����mtpckEndprdMarkcd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMtpckEndprdMarkcd(String value) {
        this.mtpckEndprdMarkcd = value;
    }

    /**
     * ��ȡsupvModecd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSupvModecd() {
        return supvModecd;
    }

    /**
     * ����supvModecd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSupvModecd(String value) {
        this.supvModecd = value;
    }

    /**
     * ��ȡtrspModecd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTrspModecd() {
        return trspModecd;
    }

    /**
     * ����trspModecd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTrspModecd(String value) {
        this.trspModecd = value;
    }

    /**
     * ��ȡapplyNo���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getApplyNo() {
        return applyNo;
    }

    /**
     * ����applyNo���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setApplyNo(String value) {
        this.applyNo = value;
    }

    /**
     * ��ȡlistType���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getListType() {
        return listType;
    }

    /**
     * ����listType���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setListType(String value) {
        this.listType = value;
    }

    /**
     * ��ȡdclcusFlag���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDclcusFlag() {
        return dclcusFlag;
    }

    /**
     * ����dclcusFlag���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDclcusFlag(String value) {
        this.dclcusFlag = value;
    }

    /**
     * ��ȡdclcusTypecd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDclcusTypecd() {
        return dclcusTypecd;
    }

    /**
     * ����dclcusTypecd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDclcusTypecd(String value) {
        this.dclcusTypecd = value;
    }

    /**
     * ��ȡprevdTime���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPrevdTime() {
        return prevdTime;
    }

    /**
     * ����prevdTime���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPrevdTime(String value) {
        this.prevdTime = value;
    }

    /**
     * ��ȡformalVrfdedTime���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFormalVrfdedTime() {
        return formalVrfdedTime;
    }

    /**
     * ����formalVrfdedTime���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFormalVrfdedTime(String value) {
        this.formalVrfdedTime = value;
    }

    /**
     * ��ȡinvtIochkptStucd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getInvtIochkptStucd() {
        return invtIochkptStucd;
    }

    /**
     * ����invtIochkptStucd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setInvtIochkptStucd(String value) {
        this.invtIochkptStucd = value;
    }

    /**
     * ��ȡvrfdedMarkcd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getVrfdedMarkcd() {
        return vrfdedMarkcd;
    }

    /**
     * ����vrfdedMarkcd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setVrfdedMarkcd(String value) {
        this.vrfdedMarkcd = value;
    }

    /**
     * ��ȡicCardNo���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIcCardNo() {
        return icCardNo;
    }

    /**
     * ����icCardNo���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIcCardNo(String value) {
        this.icCardNo = value;
    }

    /**
     * ��ȡlistStat���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getListStat() {
        return listStat;
    }

    /**
     * ����listStat���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setListStat(String value) {
        this.listStat = value;
    }

    /**
     * ��ȡdecType���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDecType() {
        return decType;
    }

    /**
     * ����decType���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDecType(String value) {
        this.decType = value;
    }

    /**
     * ��ȡrmk���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRmk() {
        return rmk;
    }

    /**
     * ����rmk���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRmk(String value) {
        this.rmk = value;
    }

    /**
     * ��ȡstshipTrsarvNatcd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getStshipTrsarvNatcd() {
        return stshipTrsarvNatcd;
    }

    /**
     * ����stshipTrsarvNatcd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setStshipTrsarvNatcd(String value) {
        this.stshipTrsarvNatcd = value;
    }

    /**
     * ��ȡinvtType���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getInvtType() {
        return invtType;
    }

    /**
     * ����invtType���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setInvtType(String value) {
        this.invtType = value;
    }

    /**
     * ��ȡentryStucd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEntryStucd() {
        return entryStucd;
    }

    /**
     * ����entryStucd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEntryStucd(String value) {
        this.entryStucd = value;
    }

    /**
     * ��ȡpassportUsedTypeCd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPassportUsedTypeCd() {
        return passportUsedTypeCd;
    }

    /**
     * ����passportUsedTypeCd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPassportUsedTypeCd(String value) {
        this.passportUsedTypeCd = value;
    }

    /**
     * ��ȡneedEntryModified���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNeedEntryModified() {
        return needEntryModified;
    }

    /**
     * ����needEntryModified���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNeedEntryModified(String value) {
        this.needEntryModified = value;
    }

    /**
     * ��ȡlevyBlAmt���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLevyBlAmt() {
        return levyBlAmt;
    }

    /**
     * ����levyBlAmt���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLevyBlAmt(String value) {
        this.levyBlAmt = value;
    }

    /**
     * ��ȡdclTypecd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDclTypecd() {
        return dclTypecd;
    }

    /**
     * ����dclTypecd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDclTypecd(String value) {
        this.dclTypecd = value;
    }

    /**
     * ��ȡgenDecFlag���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGenDecFlag() {
        return genDecFlag;
    }

    /**
     * ����genDecFlag���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGenDecFlag(String value) {
        this.genDecFlag = value;
    }

}
