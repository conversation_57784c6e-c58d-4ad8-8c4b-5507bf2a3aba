package com.danding.cds.declare.zjspecial.domain.sas101;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import java.io.Serializable;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class AppHead implements Serializable {
    @XmlElement(name = "SeqNo")
    private String seqNo;

    @XmlElement(name = "SasDclNo")
    private String sasDclNo;

    @XmlElement(name = "MaserCuscd", required = true)
    private String masterCusCd;

    @XmlElement(name = "DclTypecd", required = true)
    private String dclTypeCd;

    @XmlElement(name = "BusinessTypecd", required = true)
    private String businessTypeCd;

    @XmlElement(name = "DirectionTypecd", required = true)
    private String directionTypeCd;

    @XmlElement(name = "AreainOriactNo")
    private String areaInOriActNo;

    @XmlElement(name = "AreaoutOriactNo")
    private String areaOutOriActNo;

    @XmlElement(name = "AreainEtpsno", required = true)
    private String areaInEtpsNo;

    @XmlElement(name = "AreainEtpsNm", required = true)
    private String areaInEtpsNm;

    @XmlElement(name = "AreainEtpsSccd")
    private String areaInEtpsSccd;

    @XmlElement(name = "AreaoutEtpsno")
    private String areaOutEtpsNo;

    @XmlElement(name = "AreaoutEtpsNm")
    private String areaOutEtpsNm;

    @XmlElement(name = "AreaoutEtpsSccd")
    private String areaOutEtpsSccd;

    @XmlElement(name = "DpstLevyBlNo", required = true)
    private String dpstLevyBlNo;

    @XmlElement(name = "ValidTime", required = true)
    private String validTime;

    @XmlElement(name = "DclEr", required = true)
    private String dclEr;

    @XmlElement(name = "ExhibitionPlace")
    private String exhibitionPlace;

    @XmlElement(name = "DclEtpsno", required = true)
    private String dclEtpsNo;

    @XmlElement(name = "DclEtpsNm", required = true)
    private String dclEtpsNm;

    @XmlElement(name = "DclEtpsSccd", required = true)
    private String dclEtpsSccd;

    @XmlElement(name = "InputCode", required = true)
    private String inputCode;

    @XmlElement(name = "InputSccd", required = true)
    private String inputSccd;

    @XmlElement(name = "InputName", required = true)
    private String inputName;

    @XmlElement(name = "EtpsPreentNo")
    private String etpsPreentNo;

    @XmlElement(name = "MtpckEndprdTypecd", required = true)
    private String mtpckEndprdTypeCd;

    @XmlElement(name = "Rmk")
    private String rmk;

    @XmlElement(name = "Col1")
    private String col1;

    @XmlElement(name = "Col2")
    private String col2;

    @XmlElement(name = "Col3")
    private String col3;

    @XmlElement(name = "Col4")
    private String col4;

    @XmlElement(name = "Col4Cus")
    private String col4Cus;

    @XmlElement(name = "FreeDomestic")
    private String freeDomestic;
}