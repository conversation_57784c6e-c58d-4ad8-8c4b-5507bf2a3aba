package com.danding.cds.declare.zjspecial.domain.sas221;

import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;
import java.util.List;

/**
 * @author: create by wu<PERSON><PERSON>
 * @version: v1.0
 * @description: com.sfebiz.logistics.service.customs.declare.customsaccount.message.sas221
 * @date:2020/6/12
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
})
@XmlRootElement(name = "SAS221")
@Data
public class SAS221 implements Serializable {
    @XmlElement(name = "HdeApprResult")
    private HdeApprResult hdeApprResult;
    @XmlElement(name = "CheckInfo")
    protected List<CheckInfo> checkInfo;
    @XmlElement(name = "SasPassportBsc")
    private SasPassportBsc sasPassportBsc;
    @XmlElement(name = "SasPassportRlt")
    private List<SasPassportRlt> sasPassportRlt;


    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
    })
    @Data
    public static class CheckInfo implements Serializable {

        @XmlElement(name = "note")
        protected String note;
    }
}
