package com.danding.cds.declare.zjspecial.domain.icp101;

import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Create 2021/4/26  9:53
 * @Describe
 **/
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
})
@XmlRootElement(name = "ICP101")
@Data
public class ICP101 implements Serializable {
    @XmlElement(name = "Sas2sPassPortMessage")
    private Sas2sPassPortMessage sas2sPassPortMessage;
    /**
     * 申报标志
     * 0--暂存；
     * 1--申报；
     */
    @XmlElement(name = "DelcareFlag")
    private Integer delcareFlag;
}
