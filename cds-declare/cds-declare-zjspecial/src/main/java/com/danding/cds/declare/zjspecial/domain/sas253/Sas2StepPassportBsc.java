package com.danding.cds.declare.zjspecial.domain.sas253;

import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Create 2021/4/26  10:17
 * @Describe
 **/
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
})
@XmlRootElement(name = "Sas2StepPassportBsc")
@Data
public class Sas2StepPassportBsc implements Serializable {
    @XmlElement(name = "passportNo")
    private String passportNo;
    @XmlElement(name = "sasPassportPreentNo")
    private String sasPassportPreentNo;
    @XmlElement(name = "chgTmsCnt")
    private String chgTmsCnt;
    @XmlElement(name = "dclTypecd")
    protected String dclTypecd;
    @XmlElement(name = "masterCuscd")
    private String masterCuscd;
    @XmlElement(name = "areainEtpsno")
    private String areainEtpsno;
    @XmlElement(name = "areainEtpsNm")
    private String areainEtpsNm;
    @XmlElement(name = "areainEtpsSccd")
    private String areainEtpsSccd;
    @XmlElement(name = "vehicleNo")
    private String vehicleNo;
    @XmlElement(name = "VehicleIcNo")
    private String vehicleIcNo;
    @XmlElement(name = "ContainerNo")
    protected String containerNo;
    @XmlElement(name = "dclErConc")
    private String dclErConc;
    @XmlElement(name = "dclTime")
    private String dclTime;
    @XmlElement(name = "logisticsStucd")
    private String logisticsStucd;
    @XmlElement(name = "passId")
    private String passId;
    @XmlElement(name = "secdPassId")
    private String secdPassId;
    @XmlElement(name = "passTime")
    private String passTime;
    @XmlElement(name = "secdPassTime")
    private String secdPassTime;
    @XmlElement(name = "stucd")
    private String stucd;
    @XmlElement(name = "emapvMarkcd")
    private String emapvMarkcd;
    @XmlElement(name = "rmk")
    private String rmk;
    @XmlElement(name = "ownerSystem")
    private String ownerSystem;
    @XmlElement(name = "col1")
    private String col1;
    @XmlElement(name = "col2")
    private String col2;
    @XmlElement(name = "col3")
    private String col3;
    @XmlElement(name = "col4")
    private String col4;


}
