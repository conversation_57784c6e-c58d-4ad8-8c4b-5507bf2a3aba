//
// ���ļ����� JavaTM Architecture for XML Binding (JAXB) ����ʵ�� v2.2.8-b130911.1802 ���ɵ�
// ����� <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// �����±���Դģʽʱ, �Դ��ļ��������޸Ķ�����ʧ��
// ����ʱ��: 2020.06.03 ʱ�� 05:12:37 PM CST 
//


package com.danding.cds.declare.zjspecial.domain.inv201;

import com.danding.cds.declare.zjspecial.domain.base.EnvelopInfo;

import javax.xml.bind.annotation.*;
import java.io.Serializable;


/**
 * <p>anonymous complex type�� Java �ࡣ
 * <p>
 * <p>����ģʽƬ��ָ�������ڴ����е�Ԥ�����ݡ�
 * <p>
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element ref="{}EnvelopInfo"/>
 *         &lt;element ref="{}DataInfo"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
        "envelopInfo",
        "dataInfo"
})
@XmlRootElement(name = "Package")
public class Package implements Serializable {

    @XmlElement(name = "EnvelopInfo", required = true)
    protected EnvelopInfo envelopInfo;
    @XmlElement(name = "DataInfo", required = true)
    protected DataInfo dataInfo;

    /**
     * ��ȡenvelopInfo���Ե�ֵ��
     *
     * @return possible object is
     * {@link EnvelopInfo }
     */
    public EnvelopInfo getEnvelopInfo() {
        return envelopInfo;
    }

    /**
     * ����envelopInfo���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link EnvelopInfo }
     */
    public void setEnvelopInfo(EnvelopInfo value) {
        this.envelopInfo = value;
    }

    /**
     * ��ȡdataInfo���Ե�ֵ��
     *
     * @return possible object is
     * {@link DataInfo }
     */
    public DataInfo getDataInfo() {
        return dataInfo;
    }

    /**
     * ����dataInfo���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link DataInfo }
     */
    public void setDataInfo(DataInfo value) {
        this.dataInfo = value;
    }

}
