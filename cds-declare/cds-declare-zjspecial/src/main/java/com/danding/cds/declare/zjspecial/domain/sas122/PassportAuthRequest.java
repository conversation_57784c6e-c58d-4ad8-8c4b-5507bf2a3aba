package com.danding.cds.declare.zjspecial.domain.sas122;

import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;
import java.util.List;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
        "passportAuthInfo",
        "etpsPreentNo",
        "sasSign"
})
@XmlRootElement(name = "PassportAuthRequest")
public class PassportAuthRequest implements Serializable {

    @XmlElement(name = "PassportAuthInfo", required = true)
    protected List<PassportAuthInfo> passportAuthInfo;
    @XmlElement(name = "EtpsPreentNo", required = true)
    protected String etpsPreentNo;
    @XmlElement(name = "SasSign")
    protected SasSign sasSign;

}

