package com.danding.cds.declare.zjspecial.domain.sas122;


import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;


@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
        "bussinessData"
})
@XmlRootElement(name = "DataInfo")
@Data
public class DataInfo implements Serializable {

    @XmlElement(name = "BussinessData", required = true)
    protected BussinessData bussinessData;

    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
            "passportAuthRequest",
            "delcareFlag"
    })
    @Data
    public static class BussinessData implements Serializable {
        @XmlElement(name = "PassportAuthRequest", required = true)
        protected PassportAuthRequest passportAuthRequest;
        @XmlElement(name = "DelcareFlag")
        protected int delcareFlag;
    }


}
