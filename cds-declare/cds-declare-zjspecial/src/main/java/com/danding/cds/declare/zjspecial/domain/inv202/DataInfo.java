package com.danding.cds.declare.zjspecial.domain.inv202;


import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;


@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
        "bussinessData"
})
@XmlRootElement(name = "DataInfo")
@Data
public class DataInfo implements Serializable {

    @XmlElement(name = "BussinessData", required = true)
    protected BussinessData bussinessData;

    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
            "inv202",
    })
    @Data
    public static class BussinessData implements Serializable {
        @XmlElement(name = "INV202", required = true)
        protected INV202 inv202;
    }


}
