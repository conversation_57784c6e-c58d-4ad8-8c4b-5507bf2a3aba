package com.danding.cds.declare.zjspecial.domain.sas221;

import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;

/**
 * @author: create by wu<PERSON><PERSON>
 * @version: v1.0
 * @description: com.sfebiz.logistics.service.customs.declare.customsaccount.message.sas221
 * @date:2020/6/12
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
})
@XmlRootElement(name = "SasPassportRlt")
@Data
public class SasPassportRlt implements Serializable {
    @XmlElement(name = "passportNo")
    private String passportNo;
    @XmlElement(name = "rltTbTypecd")
    private String rltTbTypecd;
    @XmlElement(name = "rltNo")
    private String rltNo;

}
