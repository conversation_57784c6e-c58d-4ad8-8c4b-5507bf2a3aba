package com.danding.cds.declare.zjspecial.internal.mq.base;

import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 特殊监管区域对接客户端
 */
@Slf4j
public abstract class ZJSpecialMQ {

    /**
     * MQ连接通道
     */
    protected Channel channel;

    /**
     * MQ连接
     */
    protected Connection connection;

    /**
     * 连接工厂
     */
    protected ConnectionFactory factory;

    /**
     * 队列名称
     */
    protected String endPointName;

    protected AtomicLong atomicLong = new AtomicLong(0);

    public ZJSpecialMQ(String host, String port, String userName, String password, String endPointName) {
        try {
            this.endPointName = endPointName;
            // Step::创建一个连接工厂 connection factory
            factory = new ConnectionFactory();
            // Step::设置rabbitmq-server服务IP地址
            factory.setHost(host);
            factory.setUsername(userName);
            factory.setPassword(password);
            factory.setPort(Integer.parseInt(port));
            factory.setVirtualHost("/");
            factory.setConnectionTimeout(10000);
            // Step::得到 连接
            connection = factory.newConnection();
            // Step::创建 channel实例
            channel = connection.createChannel();
            channel.queueDeclare(endPointName, false, false, false, null);
        } catch (Exception e) {
            log.error("Rabbitmq初始化接收回执客户端异常：message={}", e.getMessage(), e);
        }
    }

    public void createConnectionAndChannel() throws Exception {
        // Step::得到 连接
        connection = factory.newConnection();
        // Step::创建 channel实例
        createChannel();
    }

    public void createChannel() throws IOException {
        // Step::创建 channel实例
        channel = connection.createChannel();
        channel.queueDeclare(endPointName, false, false, false, null);
    }


    public void close() throws Exception {
        this.channel.close();
        this.connection.close();
    }

    /**
     * 发送数据
     *
     * @param message
     * @throws Exception
     */
    public void send(String message) throws Exception {
        channel.basicPublish("", endPointName, null, message.getBytes("utf-8"));
        close();
    }
}
