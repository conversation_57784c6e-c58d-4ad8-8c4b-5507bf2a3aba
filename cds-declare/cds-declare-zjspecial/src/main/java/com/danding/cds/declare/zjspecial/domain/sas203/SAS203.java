package com.danding.cds.declare.zjspecial.domain.sas203;

import com.danding.cds.declare.zjspecial.domain.sas201.SAS201;
import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;
import java.util.List;


@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
})
@XmlRootElement(name = "SAS203")
@Data
public class SAS203 implements Serializable {

    @XmlElement(name = "SasDclBsc")
    protected SAS201.SasDclBsc sasDclBsc;
    @XmlElement(name = "SasDclDt")
    protected List<SAS201.SasDclDt> sasDclDts;
    @XmlElement(name = "SasDclUcnsDt")
    protected List<SAS201.SasDclUcnsDt> sasDclUcnsDts;

}
