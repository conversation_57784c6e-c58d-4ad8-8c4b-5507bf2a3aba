package com.danding.cds.declare.zjspecial.domain.icp101;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Create 2021/4/26  9:47
 * @Describe
 **/
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
})
@Data
public class Sas2sPassportHead implements Serializable {
    @XmlElement(name = "SasPassportPreentNo")
    private String sasPassportPreentNo;
    @XmlElement(name = "PassportNo")
    private String passportNo;
    @XmlElement(name = "MasterCuscd")
    private String masterCuscd;
    @XmlElement(name = "DclTypecd")
    private String dclTypecd;
    @XmlElement(name = "AreainEtpsNo")
    private String areainEtpsNo;
    @XmlElement(name = "AreainEtpsNm")
    private String areainEtpsNm;
    @XmlElement(name = "AreainEtpsSccd")
    private String areainEtpsSccd;
    @XmlElement(name = "VehicleNo")
    private String vehicleNo;
    @XmlElement(name = "VehicleIcNo")
    private String vehicleIcNo;
    @XmlElement(name = "ContainerNo")
    private String containerNo;
    @XmlElement(name = "DclErConc")
    private String dclErConc;
    @XmlElement(name = "DclEtpsNo")
    private String dclEtpsNo;
    @XmlElement(name = "DclEtpsNm")
    private String dclEtpsNm;
    @XmlElement(name = "DclEtpsSccd")
    private String dclEtpsSccd;
    @XmlElement(name = "InputCode")
    private String inputCode;
    @XmlElement(name = "InputCreditCode")
    private String inputCreditCode;
    @XmlElement(name = "InputName")
    private String inputName;
    @XmlElement(name = "EtpsPreentNo")
    private String etpsPreentNo;
    @XmlElement(name = "Rmk")
    private String rmk;
    @XmlElement(name = "VehicleWt")
    private String vehicleWt;
    @XmlElement(name = "VehicleFrameNo")
    private String vehicleFrameNo;
    @XmlElement(name = "VehicleFrameWt")
    private String vehicleFrameWt;
    @XmlElement(name = "ContainerType")
    private String containerType;
    @XmlElement(name = "ContainerWt")
    private String containerWt;
    @XmlElement(name = "TotalWt")
    private String totalWt;
    @XmlElement(name = "TotalGrossWt")
    private String totalGrossWt;
}
