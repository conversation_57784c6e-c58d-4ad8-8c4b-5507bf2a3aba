package com.danding.cds.declare.zjspecial.domain.bws201;

import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
})
@XmlRootElement(name = "HdeApprResult")
@Data
public class HdeApprResult implements Serializable {
    @XmlElement(name = "etpsPreentNo")
    protected String etpsPreentNo;
    @XmlElement(name = "businessId")
    protected String businessId;
    @XmlElement(name = "tmsCnt")
    protected String tmsCnt;
    @XmlElement(name = "typecd")
    protected String typecd;
    @XmlElement(name = "manageResult")
    protected String manageResult;
    @XmlElement(name = "manageDate")
    protected String manageDate;
    @XmlElement(name = "rmk")
    protected String rmk;
}
