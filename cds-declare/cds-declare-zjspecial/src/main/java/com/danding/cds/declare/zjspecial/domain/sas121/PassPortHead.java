//
// ���ļ����� JavaTM Architecture for XML Binding (JAXB) ����ʵ�� v2.2.8-b130911.1802 ���ɵ�
// ����� <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// �����±���Դģʽʱ, �Դ��ļ��������޸Ķ�����ʧ��
// ����ʱ��: 2020.06.11 ʱ�� 05:07:42 PM CST 
//


package com.danding.cds.declare.zjspecial.domain.sas121;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;


/**
 * �˷ŵ���ͷ
 * 
 * <p>PassPortHead complex type�� Java �ࡣ
 * 
 * <p>����ģʽƬ��ָ�������ڴ����е�Ԥ�����ݡ�
 * 
 * <pre>
 * &lt;complexType name="PassPortHead">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="SeqNo" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="18"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="PassportNo" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="64"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="PassportTypecd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="1"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="MasterCuscd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="4"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="DclTypecd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="1"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="IoTypecd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="1"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="BindTypecd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="1"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="RltTbTypecd" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="1"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="RltNo" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="512"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="AreainOriactNo" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="64"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="AreainEtpsno">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="10"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="AreainEtpsNm">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="512"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="AreainEtpsSccd" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="18"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="VehicleNo" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="128"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="VehicleIcNo" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="1"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="ContainerNo" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="128"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="VehicleWt">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="19"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="VehicleFrameNo">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="256"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="VehicleFrameWt">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="19"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="ContainerType">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="256"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="ContainerWt" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="19"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="TotalWt">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="19"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="TotalGrossWt">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="19"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="TotalNetWt">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="19"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="DclErConc">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="512"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="DclTime" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="DclEtpsno">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="10"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="DclEtpsNm">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="512"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="DclEtpsSccd" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="18"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="InputCode">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="10"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="InputSccd" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="18"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="InputName">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="255"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="EtpsPreentNo" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="64"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="Rmk" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="512"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="Col1" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="255"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="Col2" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="255"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="Col3" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="255"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="Col4" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="255"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "PassPortHead", propOrder = {
    "seqNo",
    "passportNo",
    "passportTypecd",
    "masterCuscd",
    "dclTypecd",
    "ioTypecd",
    "bindTypecd",
    "rltTbTypecd",
    "rltNo",
    "areainOriactNo",
    "areainEtpsno",
    "areainEtpsNm",
    "areainEtpsSccd",
    "vehicleNo",
    "vehicleIcNo",
    "containerNo",
    "vehicleWt",
    "vehicleFrameNo",
    "vehicleFrameWt",
    "containerType",
    "containerWt",
    "totalWt",
    "totalGrossWt",
    "totalNetWt",
    "dclErConc",
    "dclTime",
    "dclEtpsno",
    "dclEtpsNm",
    "dclEtpsSccd",
    "inputCode",
    "inputSccd",
    "inputName",
    "etpsPreentNo",
    "rmk",
    "col1",
    "col2",
    "col3",
    "col4"
})
public class PassPortHead implements Serializable {

    @XmlElement(name = "SeqNo")
    protected String seqNo;
    @XmlElement(name = "PassportNo")
    protected String passportNo;
    @XmlElement(name = "PassportTypecd", required = true)
    protected String passportTypecd;
    @XmlElement(name = "MasterCuscd", required = true)
    protected String masterCuscd;
    @XmlElement(name = "DclTypecd", required = true)
    protected String dclTypecd;
    @XmlElement(name = "IoTypecd", required = true)
    protected String ioTypecd;
    @XmlElement(name = "BindTypecd", required = true)
    protected String bindTypecd;
    @XmlElement(name = "RltTbTypecd")
    protected String rltTbTypecd;
    @XmlElement(name = "RltNo")
    protected String rltNo;
    @XmlElement(name = "AreainOriactNo")
    protected String areainOriactNo;
    @XmlElement(name = "AreainEtpsno", required = true)
    protected String areainEtpsno;
    @XmlElement(name = "AreainEtpsNm", required = true)
    protected String areainEtpsNm;
    @XmlElement(name = "AreainEtpsSccd")
    protected String areainEtpsSccd;
    @XmlElement(name = "VehicleNo")
    protected String vehicleNo;
    @XmlElement(name = "VehicleIcNo")
    protected String vehicleIcNo;
    @XmlElement(name = "ContainerNo")
    protected String containerNo;
    @XmlElement(name = "VehicleWt", required = true)
    protected String vehicleWt;
    @XmlElement(name = "VehicleFrameNo", required = true)
    protected String vehicleFrameNo;
    @XmlElement(name = "VehicleFrameWt", required = true)
    protected String vehicleFrameWt;
    @XmlElement(name = "ContainerType", required = true)
    protected String containerType;
    @XmlElement(name = "ContainerWt")
    protected String containerWt;
    @XmlElement(name = "TotalWt", required = true)
    protected String totalWt;
    @XmlElement(name = "TotalGrossWt", required = true)
    protected String totalGrossWt;
    @XmlElement(name = "TotalNetWt", required = true)
    protected String totalNetWt;
    @XmlElement(name = "DclErConc", required = true)
    protected String dclErConc;
    @XmlElement(name = "DclTime")
    protected String dclTime;
    @XmlElement(name = "DclEtpsno", required = true)
    protected String dclEtpsno;
    @XmlElement(name = "DclEtpsNm", required = true)
    protected String dclEtpsNm;
    @XmlElement(name = "DclEtpsSccd")
    protected String dclEtpsSccd;
    @XmlElement(name = "InputCode", required = true)
    protected String inputCode;
    @XmlElement(name = "InputSccd")
    protected String inputSccd;
    @XmlElement(name = "InputName", required = true)
    protected String inputName;
    @XmlElement(name = "EtpsPreentNo")
    protected String etpsPreentNo;
    @XmlElement(name = "Rmk")
    protected String rmk;
    @XmlElement(name = "Col1")
    protected String col1;
    @XmlElement(name = "Col2")
    protected String col2;
    @XmlElement(name = "Col3")
    protected String col3;
    @XmlElement(name = "Col4")
    protected String col4;

    /**
     * ��ȡseqNo���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSeqNo() {
        return seqNo;
    }

    /**
     * ����seqNo���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSeqNo(String value) {
        this.seqNo = value;
    }

    /**
     * ��ȡpassportNo���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPassportNo() {
        return passportNo;
    }

    /**
     * ����passportNo���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPassportNo(String value) {
        this.passportNo = value;
    }

    /**
     * ��ȡpassportTypecd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPassportTypecd() {
        return passportTypecd;
    }

    /**
     * ����passportTypecd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPassportTypecd(String value) {
        this.passportTypecd = value;
    }

    /**
     * ��ȡmasterCuscd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMasterCuscd() {
        return masterCuscd;
    }

    /**
     * ����masterCuscd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMasterCuscd(String value) {
        this.masterCuscd = value;
    }

    /**
     * ��ȡdclTypecd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDclTypecd() {
        return dclTypecd;
    }

    /**
     * ����dclTypecd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDclTypecd(String value) {
        this.dclTypecd = value;
    }

    /**
     * ��ȡioTypecd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIoTypecd() {
        return ioTypecd;
    }

    /**
     * ����ioTypecd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIoTypecd(String value) {
        this.ioTypecd = value;
    }

    /**
     * ��ȡbindTypecd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBindTypecd() {
        return bindTypecd;
    }

    /**
     * ����bindTypecd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBindTypecd(String value) {
        this.bindTypecd = value;
    }

    /**
     * ��ȡrltTbTypecd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRltTbTypecd() {
        return rltTbTypecd;
    }

    /**
     * ����rltTbTypecd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRltTbTypecd(String value) {
        this.rltTbTypecd = value;
    }

    /**
     * ��ȡrltNo���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRltNo() {
        return rltNo;
    }

    /**
     * ����rltNo���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRltNo(String value) {
        this.rltNo = value;
    }

    /**
     * ��ȡareainOriactNo���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAreainOriactNo() {
        return areainOriactNo;
    }

    /**
     * ����areainOriactNo���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAreainOriactNo(String value) {
        this.areainOriactNo = value;
    }

    /**
     * ��ȡareainEtpsno���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAreainEtpsno() {
        return areainEtpsno;
    }

    /**
     * ����areainEtpsno���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAreainEtpsno(String value) {
        this.areainEtpsno = value;
    }

    /**
     * ��ȡareainEtpsNm���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAreainEtpsNm() {
        return areainEtpsNm;
    }

    /**
     * ����areainEtpsNm���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAreainEtpsNm(String value) {
        this.areainEtpsNm = value;
    }

    /**
     * ��ȡareainEtpsSccd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAreainEtpsSccd() {
        return areainEtpsSccd;
    }

    /**
     * ����areainEtpsSccd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAreainEtpsSccd(String value) {
        this.areainEtpsSccd = value;
    }

    /**
     * ��ȡvehicleNo���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getVehicleNo() {
        return vehicleNo;
    }

    /**
     * ����vehicleNo���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setVehicleNo(String value) {
        this.vehicleNo = value;
    }

    /**
     * ��ȡvehicleIcNo���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getVehicleIcNo() {
        return vehicleIcNo;
    }

    /**
     * ����vehicleIcNo���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setVehicleIcNo(String value) {
        this.vehicleIcNo = value;
    }

    /**
     * ��ȡcontainerNo���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getContainerNo() {
        return containerNo;
    }

    /**
     * ����containerNo���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setContainerNo(String value) {
        this.containerNo = value;
    }

    /**
     * ��ȡvehicleWt���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getVehicleWt() {
        return vehicleWt;
    }

    /**
     * ����vehicleWt���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setVehicleWt(String value) {
        this.vehicleWt = value;
    }

    /**
     * ��ȡvehicleFrameNo���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getVehicleFrameNo() {
        return vehicleFrameNo;
    }

    /**
     * ����vehicleFrameNo���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setVehicleFrameNo(String value) {
        this.vehicleFrameNo = value;
    }

    /**
     * ��ȡvehicleFrameWt���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getVehicleFrameWt() {
        return vehicleFrameWt;
    }

    /**
     * ����vehicleFrameWt���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setVehicleFrameWt(String value) {
        this.vehicleFrameWt = value;
    }

    /**
     * ��ȡcontainerType���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getContainerType() {
        return containerType;
    }

    /**
     * ����containerType���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setContainerType(String value) {
        this.containerType = value;
    }

    /**
     * ��ȡcontainerWt���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getContainerWt() {
        return containerWt;
    }

    /**
     * ����containerWt���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setContainerWt(String value) {
        this.containerWt = value;
    }

    /**
     * ��ȡtotalWt���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTotalWt() {
        return totalWt;
    }

    /**
     * ����totalWt���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTotalWt(String value) {
        this.totalWt = value;
    }

    /**
     * ��ȡtotalGrossWt���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTotalGrossWt() {
        return totalGrossWt;
    }

    /**
     * ����totalGrossWt���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTotalGrossWt(String value) {
        this.totalGrossWt = value;
    }

    /**
     * ��ȡtotalNetWt���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTotalNetWt() {
        return totalNetWt;
    }

    /**
     * ����totalNetWt���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTotalNetWt(String value) {
        this.totalNetWt = value;
    }

    /**
     * ��ȡdclErConc���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDclErConc() {
        return dclErConc;
    }

    /**
     * ����dclErConc���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDclErConc(String value) {
        this.dclErConc = value;
    }

    /**
     * ��ȡdclTime���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDclTime() {
        return dclTime;
    }

    /**
     * ����dclTime���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDclTime(String value) {
        this.dclTime = value;
    }

    /**
     * ��ȡdclEtpsno���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDclEtpsno() {
        return dclEtpsno;
    }

    /**
     * ����dclEtpsno���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDclEtpsno(String value) {
        this.dclEtpsno = value;
    }

    /**
     * ��ȡdclEtpsNm���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDclEtpsNm() {
        return dclEtpsNm;
    }

    /**
     * ����dclEtpsNm���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDclEtpsNm(String value) {
        this.dclEtpsNm = value;
    }

    /**
     * ��ȡdclEtpsSccd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDclEtpsSccd() {
        return dclEtpsSccd;
    }

    /**
     * ����dclEtpsSccd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDclEtpsSccd(String value) {
        this.dclEtpsSccd = value;
    }

    /**
     * ��ȡinputCode���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getInputCode() {
        return inputCode;
    }

    /**
     * ����inputCode���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setInputCode(String value) {
        this.inputCode = value;
    }

    /**
     * ��ȡinputSccd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getInputSccd() {
        return inputSccd;
    }

    /**
     * ����inputSccd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setInputSccd(String value) {
        this.inputSccd = value;
    }

    /**
     * ��ȡinputName���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getInputName() {
        return inputName;
    }

    /**
     * ����inputName���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setInputName(String value) {
        this.inputName = value;
    }

    /**
     * ��ȡetpsPreentNo���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEtpsPreentNo() {
        return etpsPreentNo;
    }

    /**
     * ����etpsPreentNo���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEtpsPreentNo(String value) {
        this.etpsPreentNo = value;
    }

    /**
     * ��ȡrmk���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRmk() {
        return rmk;
    }

    /**
     * ����rmk���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRmk(String value) {
        this.rmk = value;
    }

    /**
     * ��ȡcol1���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCol1() {
        return col1;
    }

    /**
     * ����col1���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCol1(String value) {
        this.col1 = value;
    }

    /**
     * ��ȡcol2���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCol2() {
        return col2;
    }

    /**
     * ����col2���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCol2(String value) {
        this.col2 = value;
    }

    /**
     * ��ȡcol3���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCol3() {
        return col3;
    }

    /**
     * ����col3���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCol3(String value) {
        this.col3 = value;
    }

    /**
     * ��ȡcol4���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCol4() {
        return col4;
    }

    /**
     * ����col4���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCol4(String value) {
        this.col4 = value;
    }

}
