package com.danding.cds.declare.zjspecial.domain.sas101;

import com.danding.cds.declare.zjspecial.domain.base.EnvelopInfo;
import lombok.Data;

import javax.xml.bind.annotation.*;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "Sas101Package", propOrder = {
        "envelopInfo",
        "dataInfo"
})
@XmlRootElement(name = "Package")
public class Sas101Package extends com.danding.cds.declare.zjspecial.domain.Package {

    @XmlElement(name = "EnvelopInfo", required = true)
    protected EnvelopInfo envelopInfo;
    @XmlElement(name = "DataInfo", required = true)
    protected DataInfo dataInfo;

}
