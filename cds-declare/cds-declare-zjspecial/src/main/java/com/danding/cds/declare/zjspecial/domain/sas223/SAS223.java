package com.danding.cds.declare.zjspecial.domain.sas223;


import com.danding.cds.declare.zjspecial.domain.sas221.HdeApprResult;
import com.danding.cds.declare.zjspecial.domain.sas221.SasPassportBsc;
import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;

/**
 * @author: create by wu<PERSON><PERSON>
 * @version: v1.0
 * @description: com.sfebiz.logistics.service.customs.declare.customsaccount.message.sas223
 * @date:2020/6/12
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
})
@XmlRootElement(name = "SAS223")
@Data
public class SAS223 implements Serializable {

    @XmlElement(name = "HdeApprResult")
    private HdeApprResult hdeApprResult;
    @XmlElement(name = "SasPassportBsc")
    private SasPassportBsc sasPassportBsc;

}
