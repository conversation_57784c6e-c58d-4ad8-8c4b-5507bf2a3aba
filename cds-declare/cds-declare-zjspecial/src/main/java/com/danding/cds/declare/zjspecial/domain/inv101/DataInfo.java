//
// ���ļ����� JavaTM Architecture for XML Binding (JAXB) ����ʵ�� v2.2.8-b130911.1802 ���ɵ�
// ����� <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// �����±���Դģʽʱ, �Դ��ļ��������޸Ķ�����ʧ��
// ����ʱ��: 2020.06.03 ʱ�� 05:12:37 PM CST 
//


package com.danding.cds.declare.zjspecial.domain.inv101;

import javax.xml.bind.annotation.*;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
 * <p>anonymous complex type�� Java �ࡣ
 * 
 * <p>����ģʽƬ��ָ�������ڴ����е�Ԥ�����ݡ�
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="BussinessData">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence>
 *                   &lt;element ref="{}InvtMessage"/>
 *                   &lt;element name="DelcareFlag" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *                   &lt;element name="NemsAcmpRLMessage">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           &lt;sequence>
 *                             &lt;element name="BlsNo">
 *                               &lt;simpleType>
 *                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *                                   &lt;maxLength value="64"/>
 *                                 &lt;/restriction>
 *                               &lt;/simpleType>
 *                             &lt;/element>
 *                             &lt;element name="BlsType">
 *                               &lt;simpleType>
 *                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *                                   &lt;maxLength value="4"/>
 *                                 &lt;/restriction>
 *                               &lt;/simpleType>
 *                             &lt;/element>
 *                             &lt;element name="ChgTmsCnt">
 *                               &lt;simpleType>
 *                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *                                   &lt;maxLength value="19"/>
 *                                 &lt;/restriction>
 *                               &lt;/simpleType>
 *                             &lt;/element>
 *                             &lt;element name="NemsAcmpRLType" type="{}NemsAcmpRLType" maxOccurs="unbounded"/>
 *                           &lt;/sequence>
 *                         &lt;/restriction>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "bussinessData"
})
@XmlRootElement(name = "DataInfo")
public class DataInfo implements Serializable {

    @XmlElement(name = "BussinessData", required = true)
    protected BussinessData bussinessData;

    /**
     * ��ȡbussinessData���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link BussinessData }
     *     
     */
    public BussinessData getBussinessData() {
        return bussinessData;
    }

    /**
     * ����bussinessData���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link BussinessData }
     *     
     */
    public void setBussinessData(BussinessData value) {
        this.bussinessData = value;
    }


    /**
     * <p>anonymous complex type�� Java �ࡣ
     * 
     * <p>����ģʽƬ��ָ�������ڴ����е�Ԥ�����ݡ�
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence>
     *         &lt;element ref="{}InvtMessage"/>
     *         &lt;element name="DelcareFlag" type="{http://www.w3.org/2001/XMLSchema}int"/>
     *         &lt;element name="NemsAcmpRLMessage">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 &lt;sequence>
     *                   &lt;element name="BlsNo">
     *                     &lt;simpleType>
     *                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
     *                         &lt;maxLength value="64"/>
     *                       &lt;/restriction>
     *                     &lt;/simpleType>
     *                   &lt;/element>
     *                   &lt;element name="BlsType">
     *                     &lt;simpleType>
     *                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
     *                         &lt;maxLength value="4"/>
     *                       &lt;/restriction>
     *                     &lt;/simpleType>
     *                   &lt;/element>
     *                   &lt;element name="ChgTmsCnt">
     *                     &lt;simpleType>
     *                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
     *                         &lt;maxLength value="19"/>
     *                       &lt;/restriction>
     *                     &lt;/simpleType>
     *                   &lt;/element>
     *                   &lt;element name="NemsAcmpRLType" type="{}NemsAcmpRLType" maxOccurs="unbounded"/>
     *                 &lt;/sequence>
     *               &lt;/restriction>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "invtMessage",
        "delcareFlag",
        "nemsAcmpRLMessage"
    })
    public static class BussinessData implements Serializable {

        @XmlElement(name = "InvtMessage", required = true)
        protected InvtMessage invtMessage;
        @XmlElement(name = "DelcareFlag")
        protected int delcareFlag;
        @XmlElement(name = "NemsAcmpRLMessage", required = true)
        protected NemsAcmpRLMessage nemsAcmpRLMessage;

        /**
         * ��ȡinvtMessage���Ե�ֵ��
         * 
         * @return
         *     possible object is
         *     {@link InvtMessage }
         *     
         */
        public InvtMessage getInvtMessage() {
            return invtMessage;
        }

        /**
         * ����invtMessage���Ե�ֵ��
         * 
         * @param value
         *     allowed object is
         *     {@link InvtMessage }
         *     
         */
        public void setInvtMessage(InvtMessage value) {
            this.invtMessage = value;
        }

        /**
         * ��ȡdelcareFlag���Ե�ֵ��
         * 
         */
        public int getDelcareFlag() {
            return delcareFlag;
        }

        /**
         * ����delcareFlag���Ե�ֵ��
         * 
         */
        public void setDelcareFlag(int value) {
            this.delcareFlag = value;
        }

        /**
         * ��ȡnemsAcmpRLMessage���Ե�ֵ��
         * 
         * @return
         *     possible object is
         *     {@link NemsAcmpRLMessage }
         *     
         */
        public NemsAcmpRLMessage getNemsAcmpRLMessage() {
            return nemsAcmpRLMessage;
        }

        /**
         * ����nemsAcmpRLMessage���Ե�ֵ��
         * 
         * @param value
         *     allowed object is
         *     {@link NemsAcmpRLMessage }
         *     
         */
        public void setNemsAcmpRLMessage(NemsAcmpRLMessage value) {
            this.nemsAcmpRLMessage = value;
        }


        /**
         * <p>anonymous complex type�� Java �ࡣ
         * 
         * <p>����ģʽƬ��ָ�������ڴ����е�Ԥ�����ݡ�
         * 
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       &lt;sequence>
         *         &lt;element name="BlsNo">
         *           &lt;simpleType>
         *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
         *               &lt;maxLength value="64"/>
         *             &lt;/restriction>
         *           &lt;/simpleType>
         *         &lt;/element>
         *         &lt;element name="BlsType">
         *           &lt;simpleType>
         *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
         *               &lt;maxLength value="4"/>
         *             &lt;/restriction>
         *           &lt;/simpleType>
         *         &lt;/element>
         *         &lt;element name="ChgTmsCnt">
         *           &lt;simpleType>
         *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
         *               &lt;maxLength value="19"/>
         *             &lt;/restriction>
         *           &lt;/simpleType>
         *         &lt;/element>
         *         &lt;element name="NemsAcmpRLType" type="{}NemsAcmpRLType" maxOccurs="unbounded"/>
         *       &lt;/sequence>
         *     &lt;/restriction>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "blsNo",
            "blsType",
            "chgTmsCnt",
            "nemsAcmpRLType"
        })
        public static class NemsAcmpRLMessage implements Serializable {

            @XmlElement(name = "BlsNo", required = true)
            protected String blsNo;
            @XmlElement(name = "BlsType", required = true)
            protected String blsType;
            @XmlElement(name = "ChgTmsCnt", required = true)
            protected String chgTmsCnt;
            @XmlElement(name = "NemsAcmpRLType", required = true)
            protected List<NemsAcmpRLType> nemsAcmpRLType;

            /**
             * ��ȡblsNo���Ե�ֵ��
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getBlsNo() {
                return blsNo;
            }

            /**
             * ����blsNo���Ե�ֵ��
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setBlsNo(String value) {
                this.blsNo = value;
            }

            /**
             * ��ȡblsType���Ե�ֵ��
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getBlsType() {
                return blsType;
            }

            /**
             * ����blsType���Ե�ֵ��
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setBlsType(String value) {
                this.blsType = value;
            }

            /**
             * ��ȡchgTmsCnt���Ե�ֵ��
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getChgTmsCnt() {
                return chgTmsCnt;
            }

            /**
             * ����chgTmsCnt���Ե�ֵ��
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setChgTmsCnt(String value) {
                this.chgTmsCnt = value;
            }

            /**
             * Gets the value of the nemsAcmpRLType property.
             * 
             * <p>
             * This accessor method returns a reference to the live list,
             * not a snapshot. Therefore any modification you make to the
             * returned list will be present inside the JAXB object.
             * This is why there is not a <CODE>set</CODE> method for the nemsAcmpRLType property.
             * 
             * <p>
             * For example, to add a new item, do as follows:
             * <pre>
             *    getNemsAcmpRLType().add(newItem);
             * </pre>
             * 
             * 
             * <p>
             * Objects of the following type(s) are allowed in the list
             * {@link NemsAcmpRLType }
             * 
             * 
             */
            public List<NemsAcmpRLType> getNemsAcmpRLType() {
                if (nemsAcmpRLType == null) {
                    nemsAcmpRLType = new ArrayList<NemsAcmpRLType>();
                }
                return this.nemsAcmpRLType;
            }

        }

    }

}
