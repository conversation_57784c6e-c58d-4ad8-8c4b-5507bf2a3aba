

package com.danding.cds.declare.zjspecial.domain.icp101;

import com.danding.cds.declare.zjspecial.domain.base.EnvelopInfo;
import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;


@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
        "envelopInfo",
        "dataInfo"
})
@XmlRootElement(name = "Package")
@Data
public class Package implements Serializable {

    @XmlElement(name = "EnvelopInfo", required = true)
    protected EnvelopInfo envelopInfo;
    @XmlElement(name = "DataInfo", required = true)
    protected DataInfo dataInfo;



}
