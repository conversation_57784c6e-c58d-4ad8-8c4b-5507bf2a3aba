//
// ���ļ����� JavaTM Architecture for XML Binding (JAXB) ����ʵ�� v2.2.8-b130911.1802 ���ɵ�
// ����� <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// �����±���Դģʽʱ, �Դ��ļ��������޸Ķ�����ʧ��
// ����ʱ��: 2020.06.03 ʱ�� 05:12:37 PM CST 
//


package com.danding.cds.declare.zjspecial.domain;

import javax.xml.bind.annotation.*;
import java.io.Serializable;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
        "canonicalizationMethod",
        "signatureMethod",
        "reference"
})
@XmlRootElement(name = "SignedInfo")
public class SignedInfo implements Serializable {

    @XmlElement(name = "CanonicalizationMethod", required = true)
    private CanonicalizationMethod canonicalizationMethod;
    @XmlElement(name = "SignatureMethod", required = true)
    private SignatureMethod signatureMethod;
    @XmlElement(name = "Reference", required = true)
    private Reference reference;

    /**
     * ��ȡcanonicalizationMethod���Ե�ֵ��
     *
     * @return possible object is
     * {@link CanonicalizationMethod }
     */
    public CanonicalizationMethod getCanonicalizationMethod() {
        return canonicalizationMethod;
    }

    /**
     * ����canonicalizationMethod���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link CanonicalizationMethod }
     */
    public void setCanonicalizationMethod(CanonicalizationMethod value) {
        this.canonicalizationMethod = value;
    }

    /**
     * ��ȡsignatureMethod���Ե�ֵ��
     *
     * @return possible object is
     * {@link SignatureMethod }
     */
    public SignatureMethod getSignatureMethod() {
        return signatureMethod;
    }

    /**
     * ����signatureMethod���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link SignatureMethod }
     */
    public void setSignatureMethod(SignatureMethod value) {
        this.signatureMethod = value;
    }

    /**
     * ��ȡreference���Ե�ֵ��
     *
     * @return possible object is
     * {@link Reference }
     */
    public Reference getReference() {
        return reference;
    }

    /**
     * ����reference���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link Reference }
     */
    public void setReference(Reference value) {
        this.reference = value;
    }


    /**
     * <p>anonymous complex type�� Java �ࡣ
     * <p>
     * <p>����ģʽƬ��ָ�������ڴ����е�Ԥ�����ݡ�
     * <p>
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;attribute name="Algorithm" use="required">
     *         &lt;simpleType>
     *           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyURI">
     *             &lt;enumeration value="http://www.w3.org/TR/2001/REC-xml-c14n-20010315"/>
     *           &lt;/restriction>
     *         &lt;/simpleType>
     *       &lt;/attribute>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "")
    public static class CanonicalizationMethod implements Serializable {

        @XmlAttribute(name = "Algorithm", required = true)
        protected String algorithm;

        /**
         * ��ȡalgorithm���Ե�ֵ��
         *
         * @return possible object is
         * {@link String }
         */
        public String getAlgorithm() {
            return algorithm;
        }

        /**
         * ����algorithm���Ե�ֵ��
         *
         * @param value allowed object is
         *              {@link String }
         */
        public void setAlgorithm(String value) {
            this.algorithm = value;
        }

    }


    /**
     * <p>anonymous complex type�� Java �ࡣ
     * <p>
     * <p>����ģʽƬ��ָ�������ڴ����е�Ԥ�����ݡ�
     * <p>
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence>
     *         &lt;element name="DigestMethod">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 &lt;attribute name="Algorithm" use="required">
     *                   &lt;simpleType>
     *                     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyURI">
     *                       &lt;enumeration value="http://www.w3.org/2000/09/xmldsig#sha1"/>
     *                     &lt;/restriction>
     *                   &lt;/simpleType>
     *                 &lt;/attribute>
     *               &lt;/restriction>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *         &lt;element name="DigestValue">
     *           &lt;simpleType>
     *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
     *               &lt;minLength value="0"/>
     *               &lt;maxLength value="256"/>
     *             &lt;/restriction>
     *           &lt;/simpleType>
     *         &lt;/element>
     *       &lt;/sequence>
     *       &lt;attribute name="URI" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
            "digestMethod",
            "digestValue"
    })
    public static class Reference {

        @XmlElement(name = "DigestMethod", required = true)
        protected DigestMethod digestMethod;
        @XmlElement(name = "DigestValue", required = true)
        protected String digestValue;
        @XmlAttribute(name = "URI", required = true)
        protected String uri;

        /**
         * ��ȡdigestMethod���Ե�ֵ��
         *
         * @return possible object is
         * {@link DigestMethod }
         */
        public DigestMethod getDigestMethod() {
            return digestMethod;
        }

        /**
         * ����digestMethod���Ե�ֵ��
         *
         * @param value allowed object is
         *              {@link DigestMethod }
         */
        public void setDigestMethod(DigestMethod value) {
            this.digestMethod = value;
        }

        /**
         * ��ȡdigestValue���Ե�ֵ��
         *
         * @return possible object is
         * {@link String }
         */
        public String getDigestValue() {
            return digestValue;
        }

        /**
         * ����digestValue���Ե�ֵ��
         *
         * @param value allowed object is
         *              {@link String }
         */
        public void setDigestValue(String value) {
            this.digestValue = value;
        }

        /**
         * ��ȡuri���Ե�ֵ��
         *
         * @return possible object is
         * {@link String }
         */
        public String getURI() {
            return uri;
        }

        /**
         * ����uri���Ե�ֵ��
         *
         * @param value allowed object is
         *              {@link String }
         */
        public void setURI(String value) {
            this.uri = value;
        }


        /**
         * <p>anonymous complex type�� Java �ࡣ
         * <p>
         * <p>����ģʽƬ��ָ�������ڴ����е�Ԥ�����ݡ�
         * <p>
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       &lt;attribute name="Algorithm" use="required">
         *         &lt;simpleType>
         *           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyURI">
         *             &lt;enumeration value="http://www.w3.org/2000/09/xmldsig#sha1"/>
         *           &lt;/restriction>
         *         &lt;/simpleType>
         *       &lt;/attribute>
         *     &lt;/restriction>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "")
        public static class DigestMethod implements Serializable {

            @XmlAttribute(name = "Algorithm", required = true)
            protected String algorithm;

            /**
             * ��ȡalgorithm���Ե�ֵ��
             *
             * @return possible object is
             * {@link String }
             */
            public String getAlgorithm() {
                return algorithm;
            }

            /**
             * ����algorithm���Ե�ֵ��
             *
             * @param value allowed object is
             *              {@link String }
             */
            public void setAlgorithm(String value) {
                this.algorithm = value;
            }

        }

    }


    /**
     * <p>anonymous complex type�� Java �ࡣ
     * <p>
     * <p>����ģʽƬ��ָ�������ڴ����е�Ԥ�����ݡ�
     * <p>
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;attribute name="Algorithm" use="required">
     *         &lt;simpleType>
     *           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyURI">
     *             &lt;enumeration value="http://www.w3.org/2000/09/xmldsig#rsa-sha1"/>
     *             &lt;enumeration value="http://www.w3.org/2001/04/xmldsig-more#rsa-md5"/>
     *           &lt;/restriction>
     *         &lt;/simpleType>
     *       &lt;/attribute>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "")
    public static class SignatureMethod implements Serializable {

        @XmlAttribute(name = "Algorithm", required = true)
        protected String algorithm;

        /**
         * ��ȡalgorithm���Ե�ֵ��
         *
         * @return possible object is
         * {@link String }
         */
        public String getAlgorithm() {
            return algorithm;
        }

        /**
         * ����algorithm���Ե�ֵ��
         *
         * @param value allowed object is
         *              {@link String }
         */
        public void setAlgorithm(String value) {
            this.algorithm = value;
        }

    }

}
