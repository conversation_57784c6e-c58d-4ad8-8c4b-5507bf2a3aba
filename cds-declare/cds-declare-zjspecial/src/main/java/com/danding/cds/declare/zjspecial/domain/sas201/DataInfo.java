package com.danding.cds.declare.zjspecial.domain.sas201;

import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;

/**
 * @author: create by wu<PERSON><PERSON>
 * @version: v1.0
 * @description: com.sfebiz.logistics.service.customs.declare.customsaccount.inv201
 * @date:2020/6/10
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
        "bussinessData", "pocketInfo"
})
@XmlRootElement(name = "DataInfo")
@Data
public class DataInfo implements Serializable {
    @XmlElement(name = "BussinessData", required = true)
    protected BussinessData bussinessData;
    @XmlElement(name = "PocketInfo", required = true)
    protected PocketInfo pocketInfo;

    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {

    })
    @Data
    public static class PocketInfo implements Serializable {

        @XmlElement(name = "pocket_id")
        protected String pocketId;
        @XmlElement(name = "total_pocket_qty")
        protected String totalPocketQty;
        @XmlElement(name = "cur_pocket_no")
        protected String curPocketNo;
        @XmlElement(name = "is_unstructured")
        protected String isUnstructured;
    }


    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
            "sas201"
    })
    @Data
    public static class BussinessData implements Serializable {

        @XmlElement(name = "SAS201", required = true)
        protected SAS201 sas201;
    }
}
