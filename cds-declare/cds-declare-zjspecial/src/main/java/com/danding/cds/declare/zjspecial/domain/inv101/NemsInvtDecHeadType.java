//
// ���ļ����� JavaTM Architecture for XML Binding (JAXB) ����ʵ�� v2.2.8-b130911.1802 ���ɵ�
// ����� <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// �����±���Դģʽʱ, �Դ��ļ��������޸Ķ�����ʧ��
// ����ʱ��: 2020.06.03 ʱ�� 05:12:37 PM CST 
//


package com.danding.cds.declare.zjspecial.domain.inv101;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;


/**
 * ��ע�嵥���ص���ͷ
 * 
 * <p>NemsInvtDecHeadType complex type�� Java �ࡣ
 * 
 * <p>����ģʽƬ��ָ�������ڴ����е�Ԥ�����ݡ�
 * 
 * <pre>
 * &lt;complexType name="NemsInvtDecHeadType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="SeqNo" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="18"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="DecSeqNo" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="18"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="PutrecNo" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="64"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="BizopEtpsSccd" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="18"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="BizopEtpsno">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="10"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="BizopEtpsNm">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="512"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="RvsngdEtpsSccd" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="18"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="RcvgdEtpsno">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="10"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="RcvgdEtpsNm">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="512"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="DclEtpsSccd" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="18"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="DclEtpsno" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="10"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="DclEtpsNm" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="512"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="InputCode">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="10"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="InputCreditCode" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="18"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="InputName">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="255"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="ImpexpPortcd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="4"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="DclPlcCuscd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="4"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="ImpexpMarkcd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="4"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="SupvModecd" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="6"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="TrspModecd" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="6"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="TradeCountry" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="6"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="DecType">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="4"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="Rmk" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="4000"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="CreateFlag" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="1"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="BillNo" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="32"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="ContrNo" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="32"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="CutMode" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="3"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="DistinatePort" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="6"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="FeeCurr" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="3"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="FeeMark" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="1"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="FeeRate" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="19"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="GrossWet" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="19"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="InsurCurr" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="3"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="InsurMark" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="1"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="InsurRate" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="19"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="LicenseNo" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="20"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="NetWt" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="19"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="OtherCurr" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="3"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="OtherMark" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="1"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="OtherRate" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="19"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="PackNo" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="9"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="TrafName" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="50"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="TransMode" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="1"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="Type" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="2"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="WrapType" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="2"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="PromiseItems">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="32"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="TradeAreaCode">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="3"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="DespPortCode" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="8"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="EntryPortCode" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="8"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="GoodsPlace" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="100"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="OverseasConsignorCode" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="50"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="OverseasConsignorCname" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="150"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="OverseasConsignorEname" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="100"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="OverseasConsignorAddr" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="100"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="OverseasConsigneeCode" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="50"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="OverseasConsigneeEname" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="400"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "NemsInvtDecHeadType", propOrder = {
    "seqNo",
    "decSeqNo",
    "putrecNo",
    "bizopEtpsSccd",
    "bizopEtpsno",
    "bizopEtpsNm",
    "rvsngdEtpsSccd",
    "rcvgdEtpsno",
    "rcvgdEtpsNm",
    "dclEtpsSccd",
    "dclEtpsno",
    "dclEtpsNm",
    "inputCode",
    "inputCreditCode",
    "inputName",
    "impexpPortcd",
    "dclPlcCuscd",
    "impexpMarkcd",
    "supvModecd",
    "trspModecd",
    "tradeCountry",
    "decType",
    "rmk",
    "createFlag",
    "billNo",
    "contrNo",
    "cutMode",
    "distinatePort",
    "feeCurr",
    "feeMark",
    "feeRate",
    "grossWet",
    "insurCurr",
    "insurMark",
    "insurRate",
    "licenseNo",
    "netWt",
    "otherCurr",
    "otherMark",
    "otherRate",
    "packNo",
    "trafName",
    "transMode",
    "type",
    "wrapType",
    "promiseItems",
    "tradeAreaCode",
    "despPortCode",
    "entryPortCode",
    "goodsPlace",
    "overseasConsignorCode",
    "overseasConsignorCname",
    "overseasConsignorEname",
    "overseasConsignorAddr",
    "overseasConsigneeCode",
    "overseasConsigneeEname"
})
public class NemsInvtDecHeadType implements Serializable {

    @XmlElement(name = "SeqNo")
    protected String seqNo;
    @XmlElement(name = "DecSeqNo")
    protected String decSeqNo;
    @XmlElement(name = "PutrecNo")
    protected String putrecNo;
    @XmlElement(name = "BizopEtpsSccd")
    protected String bizopEtpsSccd;
    @XmlElement(name = "BizopEtpsno", required = true)
    protected String bizopEtpsno;
    @XmlElement(name = "BizopEtpsNm", required = true)
    protected String bizopEtpsNm;
    @XmlElement(name = "RvsngdEtpsSccd")
    protected String rvsngdEtpsSccd;
    @XmlElement(name = "RcvgdEtpsno", required = true)
    protected String rcvgdEtpsno;
    @XmlElement(name = "RcvgdEtpsNm", required = true)
    protected String rcvgdEtpsNm;
    @XmlElement(name = "DclEtpsSccd")
    protected String dclEtpsSccd;
    @XmlElement(name = "DclEtpsno")
    protected String dclEtpsno;
    @XmlElement(name = "DclEtpsNm")
    protected String dclEtpsNm;
    @XmlElement(name = "InputCode", required = true)
    protected String inputCode;
    @XmlElement(name = "InputCreditCode")
    protected String inputCreditCode;
    @XmlElement(name = "InputName", required = true)
    protected String inputName;
    @XmlElement(name = "ImpexpPortcd", required = true)
    protected String impexpPortcd;
    @XmlElement(name = "DclPlcCuscd", required = true)
    protected String dclPlcCuscd;
    @XmlElement(name = "ImpexpMarkcd", required = true)
    protected String impexpMarkcd;
    @XmlElement(name = "SupvModecd")
    protected String supvModecd;
    @XmlElement(name = "TrspModecd")
    protected String trspModecd;
    @XmlElement(name = "TradeCountry")
    protected String tradeCountry;
    @XmlElement(name = "DecType", required = true)
    protected String decType;
    @XmlElement(name = "Rmk")
    protected String rmk;
    @XmlElement(name = "CreateFlag")
    protected String createFlag;
    @XmlElement(name = "BillNo")
    protected String billNo;
    @XmlElement(name = "ContrNo")
    protected String contrNo;
    @XmlElement(name = "CutMode")
    protected String cutMode;
    @XmlElement(name = "DistinatePort")
    protected String distinatePort;
    @XmlElement(name = "FeeCurr")
    protected String feeCurr;
    @XmlElement(name = "FeeMark")
    protected String feeMark;
    @XmlElement(name = "FeeRate")
    protected String feeRate;
    @XmlElement(name = "GrossWet")
    protected String grossWet;
    @XmlElement(name = "InsurCurr")
    protected String insurCurr;
    @XmlElement(name = "InsurMark")
    protected String insurMark;
    @XmlElement(name = "InsurRate")
    protected String insurRate;
    @XmlElement(name = "LicenseNo")
    protected String licenseNo;
    @XmlElement(name = "NetWt")
    protected String netWt;
    @XmlElement(name = "OtherCurr")
    protected String otherCurr;
    @XmlElement(name = "OtherMark")
    protected String otherMark;
    @XmlElement(name = "OtherRate")
    protected String otherRate;
    @XmlElement(name = "PackNo")
    protected String packNo;
    @XmlElement(name = "TrafName")
    protected String trafName;
    @XmlElement(name = "TransMode")
    protected String transMode;
    @XmlElement(name = "Type")
    protected String type;
    @XmlElement(name = "WrapType")
    protected String wrapType;
    @XmlElement(name = "PromiseItems", required = true)
    protected String promiseItems;
    @XmlElement(name = "TradeAreaCode", required = true)
    protected String tradeAreaCode;
    @XmlElement(name = "DespPortCode")
    protected String despPortCode;
    @XmlElement(name = "EntryPortCode")
    protected String entryPortCode;
    @XmlElement(name = "GoodsPlace")
    protected String goodsPlace;
    @XmlElement(name = "OverseasConsignorCode")
    protected String overseasConsignorCode;
    @XmlElement(name = "OverseasConsignorCname")
    protected String overseasConsignorCname;
    @XmlElement(name = "OverseasConsignorEname")
    protected String overseasConsignorEname;
    @XmlElement(name = "OverseasConsignorAddr")
    protected String overseasConsignorAddr;
    @XmlElement(name = "OverseasConsigneeCode")
    protected String overseasConsigneeCode;
    @XmlElement(name = "OverseasConsigneeEname")
    protected String overseasConsigneeEname;

    /**
     * ��ȡseqNo���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSeqNo() {
        return seqNo;
    }

    /**
     * ����seqNo���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSeqNo(String value) {
        this.seqNo = value;
    }

    /**
     * ��ȡdecSeqNo���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDecSeqNo() {
        return decSeqNo;
    }

    /**
     * ����decSeqNo���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDecSeqNo(String value) {
        this.decSeqNo = value;
    }

    /**
     * ��ȡputrecNo���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPutrecNo() {
        return putrecNo;
    }

    /**
     * ����putrecNo���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPutrecNo(String value) {
        this.putrecNo = value;
    }

    /**
     * ��ȡbizopEtpsSccd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBizopEtpsSccd() {
        return bizopEtpsSccd;
    }

    /**
     * ����bizopEtpsSccd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBizopEtpsSccd(String value) {
        this.bizopEtpsSccd = value;
    }

    /**
     * ��ȡbizopEtpsno���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBizopEtpsno() {
        return bizopEtpsno;
    }

    /**
     * ����bizopEtpsno���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBizopEtpsno(String value) {
        this.bizopEtpsno = value;
    }

    /**
     * ��ȡbizopEtpsNm���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBizopEtpsNm() {
        return bizopEtpsNm;
    }

    /**
     * ����bizopEtpsNm���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBizopEtpsNm(String value) {
        this.bizopEtpsNm = value;
    }

    /**
     * ��ȡrvsngdEtpsSccd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRvsngdEtpsSccd() {
        return rvsngdEtpsSccd;
    }

    /**
     * ����rvsngdEtpsSccd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRvsngdEtpsSccd(String value) {
        this.rvsngdEtpsSccd = value;
    }

    /**
     * ��ȡrcvgdEtpsno���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRcvgdEtpsno() {
        return rcvgdEtpsno;
    }

    /**
     * ����rcvgdEtpsno���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRcvgdEtpsno(String value) {
        this.rcvgdEtpsno = value;
    }

    /**
     * ��ȡrcvgdEtpsNm���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRcvgdEtpsNm() {
        return rcvgdEtpsNm;
    }

    /**
     * ����rcvgdEtpsNm���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRcvgdEtpsNm(String value) {
        this.rcvgdEtpsNm = value;
    }

    /**
     * ��ȡdclEtpsSccd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDclEtpsSccd() {
        return dclEtpsSccd;
    }

    /**
     * ����dclEtpsSccd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDclEtpsSccd(String value) {
        this.dclEtpsSccd = value;
    }

    /**
     * ��ȡdclEtpsno���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDclEtpsno() {
        return dclEtpsno;
    }

    /**
     * ����dclEtpsno���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDclEtpsno(String value) {
        this.dclEtpsno = value;
    }

    /**
     * ��ȡdclEtpsNm���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDclEtpsNm() {
        return dclEtpsNm;
    }

    /**
     * ����dclEtpsNm���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDclEtpsNm(String value) {
        this.dclEtpsNm = value;
    }

    /**
     * ��ȡinputCode���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getInputCode() {
        return inputCode;
    }

    /**
     * ����inputCode���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setInputCode(String value) {
        this.inputCode = value;
    }

    /**
     * ��ȡinputCreditCode���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getInputCreditCode() {
        return inputCreditCode;
    }

    /**
     * ����inputCreditCode���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setInputCreditCode(String value) {
        this.inputCreditCode = value;
    }

    /**
     * ��ȡinputName���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getInputName() {
        return inputName;
    }

    /**
     * ����inputName���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setInputName(String value) {
        this.inputName = value;
    }

    /**
     * ��ȡimpexpPortcd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getImpexpPortcd() {
        return impexpPortcd;
    }

    /**
     * ����impexpPortcd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setImpexpPortcd(String value) {
        this.impexpPortcd = value;
    }

    /**
     * ��ȡdclPlcCuscd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDclPlcCuscd() {
        return dclPlcCuscd;
    }

    /**
     * ����dclPlcCuscd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDclPlcCuscd(String value) {
        this.dclPlcCuscd = value;
    }

    /**
     * ��ȡimpexpMarkcd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getImpexpMarkcd() {
        return impexpMarkcd;
    }

    /**
     * ����impexpMarkcd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setImpexpMarkcd(String value) {
        this.impexpMarkcd = value;
    }

    /**
     * ��ȡsupvModecd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSupvModecd() {
        return supvModecd;
    }

    /**
     * ����supvModecd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSupvModecd(String value) {
        this.supvModecd = value;
    }

    /**
     * ��ȡtrspModecd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTrspModecd() {
        return trspModecd;
    }

    /**
     * ����trspModecd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTrspModecd(String value) {
        this.trspModecd = value;
    }

    /**
     * ��ȡtradeCountry���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTradeCountry() {
        return tradeCountry;
    }

    /**
     * ����tradeCountry���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTradeCountry(String value) {
        this.tradeCountry = value;
    }

    /**
     * ��ȡdecType���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDecType() {
        return decType;
    }

    /**
     * ����decType���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDecType(String value) {
        this.decType = value;
    }

    /**
     * ��ȡrmk���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRmk() {
        return rmk;
    }

    /**
     * ����rmk���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRmk(String value) {
        this.rmk = value;
    }

    /**
     * ��ȡcreateFlag���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCreateFlag() {
        return createFlag;
    }

    /**
     * ����createFlag���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCreateFlag(String value) {
        this.createFlag = value;
    }

    /**
     * ��ȡbillNo���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBillNo() {
        return billNo;
    }

    /**
     * ����billNo���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBillNo(String value) {
        this.billNo = value;
    }

    /**
     * ��ȡcontrNo���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getContrNo() {
        return contrNo;
    }

    /**
     * ����contrNo���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setContrNo(String value) {
        this.contrNo = value;
    }

    /**
     * ��ȡcutMode���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCutMode() {
        return cutMode;
    }

    /**
     * ����cutMode���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCutMode(String value) {
        this.cutMode = value;
    }

    /**
     * ��ȡdistinatePort���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDistinatePort() {
        return distinatePort;
    }

    /**
     * ����distinatePort���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDistinatePort(String value) {
        this.distinatePort = value;
    }

    /**
     * ��ȡfeeCurr���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFeeCurr() {
        return feeCurr;
    }

    /**
     * ����feeCurr���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFeeCurr(String value) {
        this.feeCurr = value;
    }

    /**
     * ��ȡfeeMark���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFeeMark() {
        return feeMark;
    }

    /**
     * ����feeMark���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFeeMark(String value) {
        this.feeMark = value;
    }

    /**
     * ��ȡfeeRate���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFeeRate() {
        return feeRate;
    }

    /**
     * ����feeRate���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFeeRate(String value) {
        this.feeRate = value;
    }

    /**
     * ��ȡgrossWet���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGrossWet() {
        return grossWet;
    }

    /**
     * ����grossWet���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGrossWet(String value) {
        this.grossWet = value;
    }

    /**
     * ��ȡinsurCurr���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getInsurCurr() {
        return insurCurr;
    }

    /**
     * ����insurCurr���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setInsurCurr(String value) {
        this.insurCurr = value;
    }

    /**
     * ��ȡinsurMark���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getInsurMark() {
        return insurMark;
    }

    /**
     * ����insurMark���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setInsurMark(String value) {
        this.insurMark = value;
    }

    /**
     * ��ȡinsurRate���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getInsurRate() {
        return insurRate;
    }

    /**
     * ����insurRate���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setInsurRate(String value) {
        this.insurRate = value;
    }

    /**
     * ��ȡlicenseNo���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLicenseNo() {
        return licenseNo;
    }

    /**
     * ����licenseNo���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLicenseNo(String value) {
        this.licenseNo = value;
    }

    /**
     * ��ȡnetWt���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNetWt() {
        return netWt;
    }

    /**
     * ����netWt���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNetWt(String value) {
        this.netWt = value;
    }

    /**
     * ��ȡotherCurr���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOtherCurr() {
        return otherCurr;
    }

    /**
     * ����otherCurr���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOtherCurr(String value) {
        this.otherCurr = value;
    }

    /**
     * ��ȡotherMark���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOtherMark() {
        return otherMark;
    }

    /**
     * ����otherMark���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOtherMark(String value) {
        this.otherMark = value;
    }

    /**
     * ��ȡotherRate���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOtherRate() {
        return otherRate;
    }

    /**
     * ����otherRate���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOtherRate(String value) {
        this.otherRate = value;
    }

    /**
     * ��ȡpackNo���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPackNo() {
        return packNo;
    }

    /**
     * ����packNo���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPackNo(String value) {
        this.packNo = value;
    }

    /**
     * ��ȡtrafName���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTrafName() {
        return trafName;
    }

    /**
     * ����trafName���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTrafName(String value) {
        this.trafName = value;
    }

    /**
     * ��ȡtransMode���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTransMode() {
        return transMode;
    }

    /**
     * ����transMode���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTransMode(String value) {
        this.transMode = value;
    }

    /**
     * ��ȡtype���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getType() {
        return type;
    }

    /**
     * ����type���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setType(String value) {
        this.type = value;
    }

    /**
     * ��ȡwrapType���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getWrapType() {
        return wrapType;
    }

    /**
     * ����wrapType���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setWrapType(String value) {
        this.wrapType = value;
    }

    /**
     * ��ȡpromiseItems���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPromiseItems() {
        return promiseItems;
    }

    /**
     * ����promiseItems���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPromiseItems(String value) {
        this.promiseItems = value;
    }

    /**
     * ��ȡtradeAreaCode���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTradeAreaCode() {
        return tradeAreaCode;
    }

    /**
     * ����tradeAreaCode���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTradeAreaCode(String value) {
        this.tradeAreaCode = value;
    }

    /**
     * ��ȡdespPortCode���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDespPortCode() {
        return despPortCode;
    }

    /**
     * ����despPortCode���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDespPortCode(String value) {
        this.despPortCode = value;
    }

    /**
     * ��ȡentryPortCode���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEntryPortCode() {
        return entryPortCode;
    }

    /**
     * ����entryPortCode���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEntryPortCode(String value) {
        this.entryPortCode = value;
    }

    /**
     * ��ȡgoodsPlace���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGoodsPlace() {
        return goodsPlace;
    }

    /**
     * ����goodsPlace���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGoodsPlace(String value) {
        this.goodsPlace = value;
    }

    /**
     * ��ȡoverseasConsignorCode���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOverseasConsignorCode() {
        return overseasConsignorCode;
    }

    /**
     * ����overseasConsignorCode���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOverseasConsignorCode(String value) {
        this.overseasConsignorCode = value;
    }

    /**
     * ��ȡoverseasConsignorCname���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOverseasConsignorCname() {
        return overseasConsignorCname;
    }

    /**
     * ����overseasConsignorCname���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOverseasConsignorCname(String value) {
        this.overseasConsignorCname = value;
    }

    /**
     * ��ȡoverseasConsignorEname���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOverseasConsignorEname() {
        return overseasConsignorEname;
    }

    /**
     * ����overseasConsignorEname���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOverseasConsignorEname(String value) {
        this.overseasConsignorEname = value;
    }

    /**
     * ��ȡoverseasConsignorAddr���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOverseasConsignorAddr() {
        return overseasConsignorAddr;
    }

    /**
     * ����overseasConsignorAddr���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOverseasConsignorAddr(String value) {
        this.overseasConsignorAddr = value;
    }

    /**
     * ��ȡoverseasConsigneeCode���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOverseasConsigneeCode() {
        return overseasConsigneeCode;
    }

    /**
     * ����overseasConsigneeCode���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOverseasConsigneeCode(String value) {
        this.overseasConsigneeCode = value;
    }

    /**
     * ��ȡoverseasConsigneeEname���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOverseasConsigneeEname() {
        return overseasConsigneeEname;
    }

    /**
     * ����overseasConsigneeEname���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOverseasConsigneeEname(String value) {
        this.overseasConsigneeEname = value;
    }

}
