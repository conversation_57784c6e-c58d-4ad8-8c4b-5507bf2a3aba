package com.danding.cds.declare.zjspecial.domain.icp101;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Create 2021/4/26  9:48
 * @Describe
 **/
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
})
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Sas2sPassportRlt implements Serializable {
    @XmlElement(name = "EntryId")
    private String entryId;
}
