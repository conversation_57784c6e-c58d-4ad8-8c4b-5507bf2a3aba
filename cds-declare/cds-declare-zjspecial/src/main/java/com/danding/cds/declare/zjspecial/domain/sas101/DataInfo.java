package com.danding.cds.declare.zjspecial.domain.sas101;


import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;


@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
        "bussinessData"
})
@XmlRootElement(name = "DataInfo")
@Data
public class DataInfo implements Serializable {

    @XmlElement(name = "BussinessData", required = true)
    protected BussinessData bussinessData;


    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
            "appMessage",
            "delcareFlag",
            "sasAcmpRLMessage"
    })
    @Data
    public static class BussinessData implements Serializable {
        @XmlElement(name = "AppMessage", required = true)
        protected AppMessage appMessage;
        @XmlElement(name = "DelcareFlag")
        protected int delcareFlag;
        @XmlElement(name = "SasAcmpRLMessage")
        protected Object sasAcmpRLMessage;
    }


}
