package com.danding.cds.declare.zjspecial.domain.sas221;

import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;

/**
 * @author: create by wu<PERSON><PERSON>
 * @version: v1.0
 * @description: com.sfebiz.logistics.service.customs.declare.customsaccount.message.sas221
 * @date:2020/6/12
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
})
@XmlRootElement(name = "SasPassportBsc")
@Data
public class SasPassportBsc implements Serializable {
    @XmlElement(name = "passportNo")
    private String passportNo;
    @XmlElement(name = "chgTmsCnt")
    private String chgTmsCnt;
    @XmlElement(name = "passportTypecd")
    private String passportTypecd;
    @XmlElement(name = "sasPassportPreentNo")
    private String sasPassportPreentNo;
    @XmlElement(name = "dclTypecd")
    private String dclTypecd;
    @XmlElement(name = "ioTypecd")
    private String ioTypecd;
    @XmlElement(name = "bindTypecd")
    private String bindTypecd;
    @XmlElement(name = "masterCuscd")
    private String masterCuscd;
    @XmlElement(name = "rltTbTypecd")
    private String rltTbTypecd;
    @XmlElement(name = "rltNo")
    private String rltNo;
    @XmlElement(name = "areainEtpsno")
    private String areainEtpsno;
    @XmlElement(name = "areainEtpsNm")
    private String areainEtpsNm;
    @XmlElement(name = "areainEtpsSccd")
    private String areainEtpsSccd;
    @XmlElement(name = "vehicleNo")
    private String vehicleNo;
    @XmlElement(name = "vehicleWt")
    private String vehicleWt;
    @XmlElement(name = "vehicleFrameNo")
    private String vehicleFrameNo;
    @XmlElement(name = "vehicleFrameWt")
    private String vehicleFrameWt;
    @XmlElement(name = "totalWt")
    private String totalWt;
    @XmlElement(name = "totalGrossWt")
    private String totalGrossWt;
    @XmlElement(name = "totalNetWt")
    private String totalNetWt;
    @XmlElement(name = "dclErConc")
    private String dclErConc;
    @XmlElement(name = "dclTime")
    private String dclTime;
    @XmlElement(name = "stucd")
    private String stucd;
    @XmlElement(name = "emapvMarkcd")
    private String emapvMarkcd;
    @XmlElement(name = "ownerSystem")
    private String ownerSystem;

}
