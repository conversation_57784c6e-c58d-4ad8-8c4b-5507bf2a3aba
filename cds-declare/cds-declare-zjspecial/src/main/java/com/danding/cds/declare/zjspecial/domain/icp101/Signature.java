package com.danding.cds.declare.zjspecial.domain.icp101;

import com.danding.cds.declare.zjspecial.domain.KeyInfo;
import com.danding.cds.declare.zjspecial.domain.SignedInfo;
import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Create 2021/4/26  19:24
 * @Describe
 **/
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
        "signedInfo",
        "signatureValue",
        "keyInfo",
        "object"
})
@XmlRootElement(name = "Signature")
@Data
public class Signature implements Serializable {

    @XmlElement(name = "SignedInfo", required = true)
    private SignedInfo signedInfo;
    @XmlElement(name = "SignatureValue", required = true)
    private String signatureValue;
    @XmlElement(name = "KeyInfo", required = true)
    private KeyInfo keyInfo;
    @XmlElement(name = "Object", required = true)
    protected Object object;

    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
            "_package"
    })
    public static class Object implements Serializable {

        @XmlElement(name = "Package", required = true)
        protected Package _package;
        @XmlAttribute(name = "Id")
        protected String id;

        public Package getPackage() {
            return _package;
        }

        public void setPackage(Package value) {
            this._package = value;
        }

        public String getId() {
            return id;
        }


        public void setId(String value) {
            this.id = value;
        }
    }
}
