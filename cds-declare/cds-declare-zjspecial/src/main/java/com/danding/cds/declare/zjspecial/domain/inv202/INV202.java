package com.danding.cds.declare.zjspecial.domain.inv202;

import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
        "invApprResult"
})
@XmlRootElement(name = "INV202")
@Data
public class INV202 {

    @XmlElement(name = "InvApprResult", required = true)
    private InvApprResult invApprResult;

    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
            "invPreentNo",
            "businessId",
            "entrySeqNo",
            "manageResult",
            "createDate",
            "reason"
    })
    @Data
    public static class InvApprResult implements Serializable {
        /**
         * 核注清单数据中心统一编号
         */
        @XmlElement(name = "invPreentNo")
        private String invPreentNo;

        /**
         * 核注清单编号
         */
        @XmlElement(name = "businessId")
        private String businessId;

        /**
         * 报关单统一编号（可选）
         */
        @XmlElement(name = "entrySeqNo")
        private String entrySeqNo;

        /**
         * 处理结果: 1.生成成功  2.生成失败
         */
        @XmlElement(name = "manageResult")
        private String manageResult;

        /**
         * 生成日期, 格式为 yyyy-MM-dd HH:mm:ss
         */
        @XmlElement(name = "createDate")
        private String createDate;

        /**
         * 生成失败原因（可选）
         */
        @XmlElement(name = "reason")
        private String reason;
    }
}
