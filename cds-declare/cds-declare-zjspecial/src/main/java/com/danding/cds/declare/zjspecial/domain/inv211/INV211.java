package com.danding.cds.declare.zjspecial.domain.inv211;

import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;
import java.util.List;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
})
@XmlRootElement(name = "INV211")
@Data
public class INV211 implements Serializable {

    @XmlElement(name = "BondInvtBsc")
    private BondInvtBsc bondInvtBsc;

    @XmlElement(name = "EmsPutrecDt")
    private List<EmsPutrecDt> emsPutrecDt;

    @XmlElement(name = "BwsDt")
    private List<BwsDt> bwsDt;
}
