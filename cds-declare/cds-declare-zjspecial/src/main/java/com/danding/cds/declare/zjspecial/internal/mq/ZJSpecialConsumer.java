package com.danding.cds.declare.zjspecial.internal.mq;

import com.danding.cds.declare.ceb.domain.dxpmsg.DxpMsg;
import com.danding.cds.declare.ceb.internal.utils.XMLUtil;
import com.danding.cds.declare.zjspecial.internal.mq.base.ZJSpecialMQ;
import com.rabbitmq.client.QueueingConsumer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

@Slf4j
public abstract class ZJSpecialConsumer extends ZJSpecialMQ {

    public ZJSpecialConsumer(String host, String port, String userName, String password, String endPointName) {
        super(host, port, userName, password, endPointName);
    }

    public void listen() {
        new Thread(() -> {
            try {
                long times = atomicLong.incrementAndGet();
                //  这里防止一些未知异常导致一直重推
                if (times > 100) {
                    log.error("Rabbitmq自动重连任务执行异常，且达到最大重试次数50，不在重试，请核查是否是正常的业务情况");
                    return;
                }
                // 这里校验下连接是否关闭，关闭的话就开启下，
                while (connection == null || !connection.isOpen()) {
                    try {
                        super.createConnectionAndChannel();
                        log.info("Rabbitmq自动重连[成功],host={},port={},queue={}", factory.getHost(), factory.getPort(), endPointName);
                    } catch (Exception ex) {
                        log.error("Rabbitmq自动重连[失败],10秒后重试,host={},port={},queue={},异常信息：{}", factory.getHost(), factory.getPort(), endPointName, ex.getMessage());
                        Thread.sleep(10 * 1000);
                    }
                }

                // 这个渠道有可能关闭所以重新创建个
                if (channel == null || !channel.isOpen()) {
                    log.info("Rabbitmq连接渠道已关闭，开始重新创建");
                    super.createChannel();
                    log.info("Rabbitmq连接,渠道重新创建成功");
                }
                // 创建队列消费者
                QueueingConsumer consumer = new QueueingConsumer(channel);
                // 指定消费队列
                channel.basicConsume(this.endPointName, true, consumer);
                while (true) {
                    String message = null;
                    try {
                        log.info("接收海关回执信息开始：endPointName={}", this.endPointName);
                        // nextDelivery是一个阻塞方法（内部实现其实是阻塞队列的take方法）
                        QueueingConsumer.Delivery delivery = null;
                        try {
                            delivery = consumer.nextDelivery();
                            message = new String(delivery.getBody(), "utf-8");
                        } catch (Exception e) {
                            log.error("Rabbitmq获取消息异常，重新唤起另外一个任务，执行监听，异常消息{}", e.getMessage(), e);
                            // 关下所有渠道
                            connection.abort();
                            this.listen();
                            break;
                        }
                        this.acceptMessage(message);
                    } catch (Exception e) {
                        log.error("接收海关回执处理异常：message = {},e=", message, e);
                    }
                }
            } catch (Exception e) {
                log.error("消费队列连接失败，开始自动重连检查：message={}", e.getMessage(), e);
                this.listen();
            }
        }).start();
    }

    public void acceptMessage(String message) throws Exception {
        log.info("接收到海关回执信息：message={}", message);
        if (!StringUtils.isEmpty(message)) {
            if (message.contains("dxp:DxpMsg")) {
                //message解码
                message = message.replace("dxp:", "")
                        .replace("xmlns:dxp=\"http://www.chinaport.gov.cn/dxp\"", "")
                        .replace("xmlns:ds=\"http://www.w3.org/2000/09/xmldsig#\"", "")
                        .replace("xmlns=\"http://www.chinaport.gov.cn/dxp\"", "")
                        .replace("xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"", "");
                DxpMsg dxpMsg = XMLUtil.converyToJavaBean(message, DxpMsg.class);
                message = dxpMsg.getData();
                Base64.Decoder decoder = Base64.getDecoder();
                message = new String(decoder.decode(message), StandardCharsets.UTF_8);
            }
            this.handleResponse(message);
        }
    }

    protected abstract void handleResponse(String responseStr);
}
