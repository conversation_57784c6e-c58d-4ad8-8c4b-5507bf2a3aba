package com.danding.cds.declare.zjspecial.domain.inv202;

import com.danding.cds.declare.zjspecial.domain.base.EnvelopInfo;
import lombok.Data;

import javax.xml.bind.annotation.*;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "Inv202Package", propOrder = {
        "envelopInfo",
        "dataInfo"
})
@XmlRootElement(name = "Package")
public class Inv202Package {

    @XmlElement(name = "EnvelopInfo", required = true)
    protected EnvelopInfo envelopInfo;
    @XmlElement(name = "DataInfo", required = true)
    protected DataInfo dataInfo;

}
