package com.danding.cds.declare.zjspecial.domain.sas221;

import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;

/**
 * @author: create by wu<PERSON><PERSON>
 * @version: v1.0
 * @description: com.sfebiz.logistics.service.customs.declare.customsaccount.message.sas221
 * @date:2020/6/12
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
})
@XmlRootElement(name = "HdeApprResult")
@Data
public class HdeApprResult implements Serializable {
    @XmlElement(name = "etpsPreentNo")
    protected String etpsPreentNo;
    @XmlElement(name = "businessId")
    protected String businessId;
    @XmlElement(name = "tmsCnt")
    protected String tmsCnt;
    @XmlElement(name = "typecd")
    protected String typecd;
    @XmlElement(name = "manageResult")
    protected String manageResult;
    @XmlElement(name = "manageDate")
    protected String manageDate;
    @XmlElement(name = "rmk")
    protected String rmk;
}
