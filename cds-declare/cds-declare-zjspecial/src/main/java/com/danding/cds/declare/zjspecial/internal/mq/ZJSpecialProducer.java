package com.danding.cds.declare.zjspecial.internal.mq;


import com.alibaba.fastjson.JSON;
import com.danding.cds.declare.ceb.domain.dxpmsg.AddInfo;
import com.danding.cds.declare.ceb.domain.dxpmsg.DxpMsg;
import com.danding.cds.declare.ceb.domain.dxpmsg.ReceiverIds;
import com.danding.cds.declare.ceb.domain.dxpmsg.TransInfo;
import com.danding.cds.declare.ceb.internal.utils.XMLUtil;
import com.danding.cds.declare.zjspecial.internal.mq.base.ZJSpecialMQ;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Slf4j
public class ZJSpecialProducer extends ZJSpecialMQ {

    private String icCardId;

    private String customsRecId;

    public ZJSpecialProducer(String host, String port, String userName, String password, String endPointName, String icCardId, String customsRecId) {
        super(host, port, userName, password, endPointName);
        this.icCardId = icCardId;
        this.customsRecId = customsRecId;
    }

    public void sendMessage(String fileName, String dataOrign) throws Exception {
        String message = getMessage(fileName, dataOrign);
        channel.basicPublish("", endPointName, null, message.getBytes("utf-8"));
        close();
    }

    public void sendMessage(String fileName, String dataOrign, boolean skipPush) throws Exception {
        String message = getMessage(fileName, dataOrign);
        log.info("核注单号: {} ,忽略推送到海关：{} ，金二推送原始报文 ===>{}", fileName, skipPush, message);
        if (!skipPush) {
            channel.basicPublish("", endPointName, null, message.getBytes("utf-8"));
            log.info("核注单号: {} ,推送海关完成", fileName);
        }
        close();
    }

    public String getMessage(String fileName, String dataOrign) throws Exception {
        DxpMsg dxpMsg = new DxpMsg();
        dxpMsg.setVer("1.0");
        dxpMsg.setData(Base64.getEncoder().encodeToString(getData(fileName, dataOrign)));
        TransInfo transInfo = new TransInfo();
        transInfo.setCopMsgId(UUID.randomUUID().toString());
        ReceiverIds receiverIds = new ReceiverIds();
        receiverIds.setReceiverId(customsRecId);
        transInfo.setReceiverIds(Collections.singletonList(receiverIds));
        transInfo.setCreatTime(new Date());
        transInfo.setMsgType("SAS");
        transInfo.setSenderId(endPointName);
        AddInfo addInfo = new AddInfo();
        addInfo.setIcCard(icCardId);
        addInfo.setFileName(fileName);
        dxpMsg.setTransInfo(transInfo);
        dxpMsg.setAddInfo(addInfo);
        log.info("金二推送报文：dxpMsg={}", JSON.toJSONString(dxpMsg));
        return XMLUtil.convertToXml(dxpMsg);
    }

    private byte[] getData(String fileName, String dataOrign) throws IOException {
        Map<String, byte[]> paramMap = Maps.newHashMap();
        paramMap.put(fileName.concat(".xml"), dataOrign.getBytes());
        return compress(paramMap).toByteArray();
    }

    private ByteArrayOutputStream compress(Map<String, byte[]> map) throws IOException {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        ZipOutputStream zipOut = new ZipOutputStream(byteArrayOutputStream);
        Set<Map.Entry<String, byte[]>> entries = map.entrySet();
        for (Map.Entry<String, byte[]> entry : entries) {
            zipOut.putNextEntry(new ZipEntry(entry.getKey()));
            zipOut.write(entry.getValue());
        }
        zipOut.close();
        return byteArrayOutputStream;
    }
}
