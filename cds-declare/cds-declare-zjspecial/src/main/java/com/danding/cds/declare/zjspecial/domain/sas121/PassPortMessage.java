//
// ���ļ����� JavaTM Architecture for XML Binding (JAXB) ����ʵ�� v2.2.8-b130911.1802 ���ɵ�
// ����� <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// �����±���Դģʽʱ, �Դ��ļ��������޸Ķ�����ʧ��
// ����ʱ��: 2020.06.11 ʱ�� 05:07:42 PM CST 
//


package com.danding.cds.declare.zjspecial.domain.sas121;

import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;
import java.util.List;


/**
 * <p>anonymous complex type�� Java �ࡣ
 * <p>
 * <p>����ģʽƬ��ָ�������ڴ����е�Ԥ�����ݡ�
 * <p>
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="PassportHead" type="{}PassPortHead"/>
 *         &lt;element name="PassportList" type="{}PassPortList" maxOccurs="unbounded"/>
 *         &lt;element name="PassportAcmp" type="{}PassPortAcmp" maxOccurs="unbounded"/>
 *         &lt;element name="OperCusRegCode">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="10"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
        "passportHead",
        "passportList",
        "passportAcmp",
        "operCusRegCode"
})
@XmlRootElement(name = "PassPortMessage")
@Data
public class PassPortMessage implements Serializable {

    @XmlElement(name = "PassportHead", required = true)
    protected PassPortHead passportHead;
    @XmlElement(name = "PassportList", required = true)
    protected List<PassPortList> passportList;
    @XmlElement(name = "PassportAcmp", required = true)
    protected List<PassPortAcmp> passportAcmp;
    @XmlElement(name = "OperCusRegCode", required = true)
    protected String operCusRegCode;


}
