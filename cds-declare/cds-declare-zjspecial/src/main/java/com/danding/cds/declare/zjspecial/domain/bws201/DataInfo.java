package com.danding.cds.declare.zjspecial.domain.bws201;

import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
        "bussinessData", "pocketInfo"
})
@XmlRootElement(name = "DataInfo")
@Data
public class DataInfo implements Serializable {
    @XmlElement(name = "BussinessData", required = true)
    protected BussinessData bussinessData;
    @XmlElement(name = "PocketInfo", required = true)
    protected PocketInfo pocketInfo;

    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {

    })
    @Data
    public static class PocketInfo implements Serializable {

        @XmlElement(name = "pocket_id")
        protected String pocketId;
        @XmlElement(name = "total_pocket_qty")
        protected String totalPocketQty;
        @XmlElement(name = "cur_pocket_no")
        protected String curPocketNo;
        @XmlElement(name = "is_unstructured")
        protected String isUnstructured;
    }


    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
            "bws201"
    })
    @Data
    public static class BussinessData implements Serializable {
        @XmlElement(name = "BWS201", required = true)
        protected BWS201 bws201;
    }

}
