package com.danding.cds.declare.zjspecial.domain.sas122;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
        "sysId",
        "etpsNo",
        "etpsSccd",
        "etpsName",
        "billType",
        "billNo",
        "syEtpsNo",
        "syEtpsSccd",
        "syEtpsName",
        "authType"
})
public class PassportAuthInfo implements Serializable {

    @XmlElement(name = "SysId", required = true)
    protected String sysId;
    @XmlElement(name = "EtpsNo", required = true)
    protected String etpsNo;
    @XmlElement(name = "EtpsSccd")
    protected String etpsSccd;
    @XmlElement(name = "EtpsName", required = true)
    protected String etpsName;
    @XmlElement(name = "BillType", required = true)
    protected String billType;
    @XmlElement(name = "BillNo", required = true)
    protected String billNo;
    @XmlElement(name = "SyEtpsNo", required = true)
    protected String syEtpsNo;
    @XmlElement(name = "SyEtpsSccd")
    protected String syEtpsSccd;
    @XmlElement(name = "SyEtpsName", required = true)
    protected String syEtpsName;
    @XmlElement(name = "AuthType", required = true)
    protected String authType;

}