//
// ���ļ����� JavaTM Architecture for XML Binding (JAXB) ����ʵ�� v2.2.8-b130911.1802 ���ɵ�
// ����� <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// �����±���Դģʽʱ, �Դ��ļ��������޸Ķ�����ʧ��
// ����ʱ��: 2020.06.03 ʱ�� 05:12:37 PM CST 
//


package com.danding.cds.declare.zjspecial.domain.inv101;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;


/**
 * ��ע�嵥���ص���Ʒ����
 * 
 * <p>NemsInvtDecListType complex type�� Java �ࡣ
 * 
 * <p>����ģʽƬ��ָ�������ڴ����е�Ԥ�����ݡ�
 * 
 * <pre>
 * &lt;complexType name="NemsInvtDecListType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="SeqNo" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="64"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="DecSeqNo" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="18"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="EntryGdsSeqno">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="19"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="PutrecSeqno" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="19"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="Gdecd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="10"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="GdsNm">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="512"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="GdsSpcfModelDesc">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="512"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="DclUnitcd" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="3"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="LawfUnitcd" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="3"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="SecdLawfUnitcd" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="3"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="DclUprcAmt" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="19"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="DclTotalAmt" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="19"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="DclCurrCd" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="3"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="NatCd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="3"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="DestinationNatcd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="3"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="LawfQty" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="19"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="SecdLawfQty" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="19"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="DclQty" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="19"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="UseCd" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="4"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="Rmk" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="4000"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="LvyrlfModecd" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="10"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="CiqCode" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="20"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="DeclGoodsEname" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="100"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="OrigPlaceCode" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="50"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="Purpose" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="4"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="ProdValidDt" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="20"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="ProdQgp" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="20"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="GoodsAttr" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="20"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="Stuff" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="400"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="UnCode" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="20"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="DangName" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="80"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="DangPackType" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="4"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="DangPackSpec" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="24"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="EngManEntCnm" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="100"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="NoDangFlag" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="1"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="DestCode" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="8"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="GoodsSpec" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="2000"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="GoodsModel" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="2000"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="GoodsBrand" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="2000"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="ProduceDate" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="2000"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="ProdBatchNo" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="2000"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="DistrictCode" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="5"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="CiqName" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="50"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="MnufctrRegno" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="20"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="MnufctrRegName" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="150"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "NemsInvtDecListType", propOrder = {
    "seqNo",
    "decSeqNo",
    "entryGdsSeqno",
    "putrecSeqno",
    "gdecd",
    "gdsNm",
    "gdsSpcfModelDesc",
    "dclUnitcd",
    "lawfUnitcd",
    "secdLawfUnitcd",
    "dclUprcAmt",
    "dclTotalAmt",
    "dclCurrCd",
    "natCd",
    "destinationNatcd",
    "lawfQty",
    "secdLawfQty",
    "dclQty",
    "useCd",
    "rmk",
    "lvyrlfModecd",
    "ciqCode",
    "declGoodsEname",
    "origPlaceCode",
    "purpose",
    "prodValidDt",
    "prodQgp",
    "goodsAttr",
    "stuff",
    "unCode",
    "dangName",
    "dangPackType",
    "dangPackSpec",
    "engManEntCnm",
    "noDangFlag",
    "destCode",
    "goodsSpec",
    "goodsModel",
    "goodsBrand",
    "produceDate",
    "prodBatchNo",
    "districtCode",
    "ciqName",
    "mnufctrRegno",
    "mnufctrRegName"
})
public class NemsInvtDecListType implements Serializable {

    @XmlElement(name = "SeqNo")
    protected String seqNo;
    @XmlElement(name = "DecSeqNo")
    protected String decSeqNo;
    @XmlElement(name = "EntryGdsSeqno", required = true)
    protected String entryGdsSeqno;
    @XmlElement(name = "PutrecSeqno")
    protected String putrecSeqno;
    @XmlElement(name = "Gdecd", required = true)
    protected String gdecd;
    @XmlElement(name = "GdsNm", required = true)
    protected String gdsNm;
    @XmlElement(name = "GdsSpcfModelDesc", required = true)
    protected String gdsSpcfModelDesc;
    @XmlElement(name = "DclUnitcd")
    protected String dclUnitcd;
    @XmlElement(name = "LawfUnitcd")
    protected String lawfUnitcd;
    @XmlElement(name = "SecdLawfUnitcd")
    protected String secdLawfUnitcd;
    @XmlElement(name = "DclUprcAmt")
    protected String dclUprcAmt;
    @XmlElement(name = "DclTotalAmt")
    protected String dclTotalAmt;
    @XmlElement(name = "DclCurrCd")
    protected String dclCurrCd;
    @XmlElement(name = "NatCd", required = true)
    protected String natCd;
    @XmlElement(name = "DestinationNatcd", required = true)
    protected String destinationNatcd;
    @XmlElement(name = "LawfQty")
    protected String lawfQty;
    @XmlElement(name = "SecdLawfQty")
    protected String secdLawfQty;
    @XmlElement(name = "DclQty")
    protected String dclQty;
    @XmlElement(name = "UseCd")
    protected String useCd;
    @XmlElement(name = "Rmk")
    protected String rmk;
    @XmlElement(name = "LvyrlfModecd")
    protected String lvyrlfModecd;
    @XmlElement(name = "CiqCode")
    protected String ciqCode;
    @XmlElement(name = "DeclGoodsEname")
    protected String declGoodsEname;
    @XmlElement(name = "OrigPlaceCode")
    protected String origPlaceCode;
    @XmlElement(name = "Purpose")
    protected String purpose;
    @XmlElement(name = "ProdValidDt")
    protected String prodValidDt;
    @XmlElement(name = "ProdQgp")
    protected String prodQgp;
    @XmlElement(name = "GoodsAttr")
    protected String goodsAttr;
    @XmlElement(name = "Stuff")
    protected String stuff;
    @XmlElement(name = "UnCode")
    protected String unCode;
    @XmlElement(name = "DangName")
    protected String dangName;
    @XmlElement(name = "DangPackType")
    protected String dangPackType;
    @XmlElement(name = "DangPackSpec")
    protected String dangPackSpec;
    @XmlElement(name = "EngManEntCnm")
    protected String engManEntCnm;
    @XmlElement(name = "NoDangFlag")
    protected String noDangFlag;
    @XmlElement(name = "DestCode")
    protected String destCode;
    @XmlElement(name = "GoodsSpec")
    protected String goodsSpec;
    @XmlElement(name = "GoodsModel")
    protected String goodsModel;
    @XmlElement(name = "GoodsBrand")
    protected String goodsBrand;
    @XmlElement(name = "ProduceDate")
    protected String produceDate;
    @XmlElement(name = "ProdBatchNo")
    protected String prodBatchNo;
    @XmlElement(name = "DistrictCode")
    protected String districtCode;
    @XmlElement(name = "CiqName")
    protected String ciqName;
    @XmlElement(name = "MnufctrRegno")
    protected String mnufctrRegno;
    @XmlElement(name = "MnufctrRegName")
    protected String mnufctrRegName;

    /**
     * ��ȡseqNo���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSeqNo() {
        return seqNo;
    }

    /**
     * ����seqNo���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSeqNo(String value) {
        this.seqNo = value;
    }

    /**
     * ��ȡdecSeqNo���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDecSeqNo() {
        return decSeqNo;
    }

    /**
     * ����decSeqNo���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDecSeqNo(String value) {
        this.decSeqNo = value;
    }

    /**
     * ��ȡentryGdsSeqno���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEntryGdsSeqno() {
        return entryGdsSeqno;
    }

    /**
     * ����entryGdsSeqno���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEntryGdsSeqno(String value) {
        this.entryGdsSeqno = value;
    }

    /**
     * ��ȡputrecSeqno���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPutrecSeqno() {
        return putrecSeqno;
    }

    /**
     * ����putrecSeqno���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPutrecSeqno(String value) {
        this.putrecSeqno = value;
    }

    /**
     * ��ȡgdecd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGdecd() {
        return gdecd;
    }

    /**
     * ����gdecd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGdecd(String value) {
        this.gdecd = value;
    }

    /**
     * ��ȡgdsNm���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGdsNm() {
        return gdsNm;
    }

    /**
     * ����gdsNm���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGdsNm(String value) {
        this.gdsNm = value;
    }

    /**
     * ��ȡgdsSpcfModelDesc���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGdsSpcfModelDesc() {
        return gdsSpcfModelDesc;
    }

    /**
     * ����gdsSpcfModelDesc���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGdsSpcfModelDesc(String value) {
        this.gdsSpcfModelDesc = value;
    }

    /**
     * ��ȡdclUnitcd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDclUnitcd() {
        return dclUnitcd;
    }

    /**
     * ����dclUnitcd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDclUnitcd(String value) {
        this.dclUnitcd = value;
    }

    /**
     * ��ȡlawfUnitcd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLawfUnitcd() {
        return lawfUnitcd;
    }

    /**
     * ����lawfUnitcd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLawfUnitcd(String value) {
        this.lawfUnitcd = value;
    }

    /**
     * ��ȡsecdLawfUnitcd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSecdLawfUnitcd() {
        return secdLawfUnitcd;
    }

    /**
     * ����secdLawfUnitcd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSecdLawfUnitcd(String value) {
        this.secdLawfUnitcd = value;
    }

    /**
     * ��ȡdclUprcAmt���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDclUprcAmt() {
        return dclUprcAmt;
    }

    /**
     * ����dclUprcAmt���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDclUprcAmt(String value) {
        this.dclUprcAmt = value;
    }

    /**
     * ��ȡdclTotalAmt���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDclTotalAmt() {
        return dclTotalAmt;
    }

    /**
     * ����dclTotalAmt���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDclTotalAmt(String value) {
        this.dclTotalAmt = value;
    }

    /**
     * ��ȡdclCurrCd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDclCurrCd() {
        return dclCurrCd;
    }

    /**
     * ����dclCurrCd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDclCurrCd(String value) {
        this.dclCurrCd = value;
    }

    /**
     * ��ȡnatCd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNatCd() {
        return natCd;
    }

    /**
     * ����natCd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNatCd(String value) {
        this.natCd = value;
    }

    /**
     * ��ȡdestinationNatcd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDestinationNatcd() {
        return destinationNatcd;
    }

    /**
     * ����destinationNatcd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDestinationNatcd(String value) {
        this.destinationNatcd = value;
    }

    /**
     * ��ȡlawfQty���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLawfQty() {
        return lawfQty;
    }

    /**
     * ����lawfQty���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLawfQty(String value) {
        this.lawfQty = value;
    }

    /**
     * ��ȡsecdLawfQty���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSecdLawfQty() {
        return secdLawfQty;
    }

    /**
     * ����secdLawfQty���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSecdLawfQty(String value) {
        this.secdLawfQty = value;
    }

    /**
     * ��ȡdclQty���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDclQty() {
        return dclQty;
    }

    /**
     * ����dclQty���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDclQty(String value) {
        this.dclQty = value;
    }

    /**
     * ��ȡuseCd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUseCd() {
        return useCd;
    }

    /**
     * ����useCd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUseCd(String value) {
        this.useCd = value;
    }

    /**
     * ��ȡrmk���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRmk() {
        return rmk;
    }

    /**
     * ����rmk���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRmk(String value) {
        this.rmk = value;
    }

    /**
     * ��ȡlvyrlfModecd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLvyrlfModecd() {
        return lvyrlfModecd;
    }

    /**
     * ����lvyrlfModecd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLvyrlfModecd(String value) {
        this.lvyrlfModecd = value;
    }

    /**
     * ��ȡciqCode���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCiqCode() {
        return ciqCode;
    }

    /**
     * ����ciqCode���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCiqCode(String value) {
        this.ciqCode = value;
    }

    /**
     * ��ȡdeclGoodsEname���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDeclGoodsEname() {
        return declGoodsEname;
    }

    /**
     * ����declGoodsEname���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDeclGoodsEname(String value) {
        this.declGoodsEname = value;
    }

    /**
     * ��ȡorigPlaceCode���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOrigPlaceCode() {
        return origPlaceCode;
    }

    /**
     * ����origPlaceCode���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOrigPlaceCode(String value) {
        this.origPlaceCode = value;
    }

    /**
     * ��ȡpurpose���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPurpose() {
        return purpose;
    }

    /**
     * ����purpose���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPurpose(String value) {
        this.purpose = value;
    }

    /**
     * ��ȡprodValidDt���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getProdValidDt() {
        return prodValidDt;
    }

    /**
     * ����prodValidDt���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setProdValidDt(String value) {
        this.prodValidDt = value;
    }

    /**
     * ��ȡprodQgp���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getProdQgp() {
        return prodQgp;
    }

    /**
     * ����prodQgp���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setProdQgp(String value) {
        this.prodQgp = value;
    }

    /**
     * ��ȡgoodsAttr���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGoodsAttr() {
        return goodsAttr;
    }

    /**
     * ����goodsAttr���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGoodsAttr(String value) {
        this.goodsAttr = value;
    }

    /**
     * ��ȡstuff���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getStuff() {
        return stuff;
    }

    /**
     * ����stuff���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setStuff(String value) {
        this.stuff = value;
    }

    /**
     * ��ȡunCode���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUnCode() {
        return unCode;
    }

    /**
     * ����unCode���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUnCode(String value) {
        this.unCode = value;
    }

    /**
     * ��ȡdangName���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDangName() {
        return dangName;
    }

    /**
     * ����dangName���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDangName(String value) {
        this.dangName = value;
    }

    /**
     * ��ȡdangPackType���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDangPackType() {
        return dangPackType;
    }

    /**
     * ����dangPackType���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDangPackType(String value) {
        this.dangPackType = value;
    }

    /**
     * ��ȡdangPackSpec���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDangPackSpec() {
        return dangPackSpec;
    }

    /**
     * ����dangPackSpec���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDangPackSpec(String value) {
        this.dangPackSpec = value;
    }

    /**
     * ��ȡengManEntCnm���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEngManEntCnm() {
        return engManEntCnm;
    }

    /**
     * ����engManEntCnm���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEngManEntCnm(String value) {
        this.engManEntCnm = value;
    }

    /**
     * ��ȡnoDangFlag���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNoDangFlag() {
        return noDangFlag;
    }

    /**
     * ����noDangFlag���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNoDangFlag(String value) {
        this.noDangFlag = value;
    }

    /**
     * ��ȡdestCode���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDestCode() {
        return destCode;
    }

    /**
     * ����destCode���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDestCode(String value) {
        this.destCode = value;
    }

    /**
     * ��ȡgoodsSpec���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGoodsSpec() {
        return goodsSpec;
    }

    /**
     * ����goodsSpec���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGoodsSpec(String value) {
        this.goodsSpec = value;
    }

    /**
     * ��ȡgoodsModel���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGoodsModel() {
        return goodsModel;
    }

    /**
     * ����goodsModel���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGoodsModel(String value) {
        this.goodsModel = value;
    }

    /**
     * ��ȡgoodsBrand���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGoodsBrand() {
        return goodsBrand;
    }

    /**
     * ����goodsBrand���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGoodsBrand(String value) {
        this.goodsBrand = value;
    }

    /**
     * ��ȡproduceDate���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getProduceDate() {
        return produceDate;
    }

    /**
     * ����produceDate���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setProduceDate(String value) {
        this.produceDate = value;
    }

    /**
     * ��ȡprodBatchNo���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getProdBatchNo() {
        return prodBatchNo;
    }

    /**
     * ����prodBatchNo���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setProdBatchNo(String value) {
        this.prodBatchNo = value;
    }

    /**
     * ��ȡdistrictCode���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDistrictCode() {
        return districtCode;
    }

    /**
     * ����districtCode���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDistrictCode(String value) {
        this.districtCode = value;
    }

    /**
     * ��ȡciqName���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCiqName() {
        return ciqName;
    }

    /**
     * ����ciqName���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCiqName(String value) {
        this.ciqName = value;
    }

    /**
     * ��ȡmnufctrRegno���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMnufctrRegno() {
        return mnufctrRegno;
    }

    /**
     * ����mnufctrRegno���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMnufctrRegno(String value) {
        this.mnufctrRegno = value;
    }

    /**
     * ��ȡmnufctrRegName���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMnufctrRegName() {
        return mnufctrRegName;
    }

    /**
     * ����mnufctrRegName���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMnufctrRegName(String value) {
        this.mnufctrRegName = value;
    }

}
