//
// ���ļ����� JavaTM Architecture for XML Binding (JAXB) ����ʵ�� v2.2.8-b130911.1802 ���ɵ�
// ����� <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// �����±���Դģʽʱ, �Դ��ļ��������޸Ķ�����ʧ��
// ����ʱ��: 2020.06.03 ʱ�� 05:12:37 PM CST 
//


package com.danding.cds.declare.zjspecial.domain.inv101;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;


/**
 * ������ϵ��
 * 
 * <p>NemsAcmpRLType complex type�� Java �ࡣ
 * 
 * <p>����ģʽƬ��ָ�������ڴ����е�Ԥ�����ݡ�
 * 
 * <pre>
 * &lt;complexType name="NemsAcmpRLType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="FileName">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="64"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="ChgTmsCnt">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="19"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="AcmpFormFmt">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="4"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="BlsType">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="4"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="AcmpFormSeqNo">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="19"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="AcmpFormTypeCD">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="25"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="AcmpFormNo">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="64"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="AcmpFormFileNm">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="10"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="InvtGdsSeqNo">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="19"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="IcCardNo">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="16"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="TransferTradeCode">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="18"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="Rmk">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="4000"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="ModfMarkCD">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="PocketId">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="4000"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="CurPocketNo">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="TotalPocketQty">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "NemsAcmpRLType", propOrder = {
    "fileName",
    "chgTmsCnt",
    "acmpFormFmt",
    "blsType",
    "acmpFormSeqNo",
    "acmpFormTypeCD",
    "acmpFormNo",
    "acmpFormFileNm",
    "invtGdsSeqNo",
    "icCardNo",
    "transferTradeCode",
    "rmk",
    "modfMarkCD",
    "pocketId",
    "curPocketNo",
    "totalPocketQty"
})
public class NemsAcmpRLType implements Serializable {

    @XmlElement(name = "FileName", required = true)
    protected String fileName;
    @XmlElement(name = "ChgTmsCnt", required = true)
    protected String chgTmsCnt;
    @XmlElement(name = "AcmpFormFmt", required = true)
    protected String acmpFormFmt;
    @XmlElement(name = "BlsType", required = true)
    protected String blsType;
    @XmlElement(name = "AcmpFormSeqNo", required = true)
    protected String acmpFormSeqNo;
    @XmlElement(name = "AcmpFormTypeCD", required = true)
    protected String acmpFormTypeCD;
    @XmlElement(name = "AcmpFormNo", required = true)
    protected String acmpFormNo;
    @XmlElement(name = "AcmpFormFileNm", required = true)
    protected String acmpFormFileNm;
    @XmlElement(name = "InvtGdsSeqNo", required = true)
    protected String invtGdsSeqNo;
    @XmlElement(name = "IcCardNo", required = true)
    protected String icCardNo;
    @XmlElement(name = "TransferTradeCode", required = true)
    protected String transferTradeCode;
    @XmlElement(name = "Rmk", required = true)
    protected String rmk;
    @XmlElement(name = "ModfMarkCD", required = true)
    protected String modfMarkCD;
    @XmlElement(name = "PocketId", required = true)
    protected String pocketId;
    @XmlElement(name = "CurPocketNo", required = true)
    protected String curPocketNo;
    @XmlElement(name = "TotalPocketQty", required = true)
    protected String totalPocketQty;

    /**
     * ��ȡfileName���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFileName() {
        return fileName;
    }

    /**
     * ����fileName���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFileName(String value) {
        this.fileName = value;
    }

    /**
     * ��ȡchgTmsCnt���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getChgTmsCnt() {
        return chgTmsCnt;
    }

    /**
     * ����chgTmsCnt���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setChgTmsCnt(String value) {
        this.chgTmsCnt = value;
    }

    /**
     * ��ȡacmpFormFmt���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAcmpFormFmt() {
        return acmpFormFmt;
    }

    /**
     * ����acmpFormFmt���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAcmpFormFmt(String value) {
        this.acmpFormFmt = value;
    }

    /**
     * ��ȡblsType���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBlsType() {
        return blsType;
    }

    /**
     * ����blsType���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBlsType(String value) {
        this.blsType = value;
    }

    /**
     * ��ȡacmpFormSeqNo���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAcmpFormSeqNo() {
        return acmpFormSeqNo;
    }

    /**
     * ����acmpFormSeqNo���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAcmpFormSeqNo(String value) {
        this.acmpFormSeqNo = value;
    }

    /**
     * ��ȡacmpFormTypeCD���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAcmpFormTypeCD() {
        return acmpFormTypeCD;
    }

    /**
     * ����acmpFormTypeCD���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAcmpFormTypeCD(String value) {
        this.acmpFormTypeCD = value;
    }

    /**
     * ��ȡacmpFormNo���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAcmpFormNo() {
        return acmpFormNo;
    }

    /**
     * ����acmpFormNo���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAcmpFormNo(String value) {
        this.acmpFormNo = value;
    }

    /**
     * ��ȡacmpFormFileNm���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAcmpFormFileNm() {
        return acmpFormFileNm;
    }

    /**
     * ����acmpFormFileNm���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAcmpFormFileNm(String value) {
        this.acmpFormFileNm = value;
    }

    /**
     * ��ȡinvtGdsSeqNo���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getInvtGdsSeqNo() {
        return invtGdsSeqNo;
    }

    /**
     * ����invtGdsSeqNo���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setInvtGdsSeqNo(String value) {
        this.invtGdsSeqNo = value;
    }

    /**
     * ��ȡicCardNo���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIcCardNo() {
        return icCardNo;
    }

    /**
     * ����icCardNo���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIcCardNo(String value) {
        this.icCardNo = value;
    }

    /**
     * ��ȡtransferTradeCode���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTransferTradeCode() {
        return transferTradeCode;
    }

    /**
     * ����transferTradeCode���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTransferTradeCode(String value) {
        this.transferTradeCode = value;
    }

    /**
     * ��ȡrmk���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRmk() {
        return rmk;
    }

    /**
     * ����rmk���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRmk(String value) {
        this.rmk = value;
    }

    /**
     * ��ȡmodfMarkCD���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getModfMarkCD() {
        return modfMarkCD;
    }

    /**
     * ����modfMarkCD���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setModfMarkCD(String value) {
        this.modfMarkCD = value;
    }

    /**
     * ��ȡpocketId���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPocketId() {
        return pocketId;
    }

    /**
     * ����pocketId���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPocketId(String value) {
        this.pocketId = value;
    }

    /**
     * ��ȡcurPocketNo���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCurPocketNo() {
        return curPocketNo;
    }

    /**
     * ����curPocketNo���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCurPocketNo(String value) {
        this.curPocketNo = value;
    }

    /**
     * ��ȡtotalPocketQty���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTotalPocketQty() {
        return totalPocketQty;
    }

    /**
     * ����totalPocketQty���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTotalPocketQty(String value) {
        this.totalPocketQty = value;
    }

}
