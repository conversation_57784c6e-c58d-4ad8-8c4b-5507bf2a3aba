package com.danding.cds.declare.zjspecial.domain.bws201;

import com.danding.cds.declare.zjspecial.domain.base.EnvelopInfo;

import javax.xml.bind.annotation.*;
import java.io.Serializable;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
        "envelopInfo",
        "dataInfo"
})
@XmlRootElement(name = "Package")
public class Package implements Serializable {

    @XmlElement(name = "EnvelopInfo", required = true)
    protected EnvelopInfo envelopInfo;
    @XmlElement(name = "DataInfo", required = true)
    protected DataInfo dataInfo;

    /**
     * ��ȡenvelopInfo���Ե�ֵ��
     *
     * @return possible object is
     * {@link EnvelopInfo }
     */
    public EnvelopInfo getEnvelopInfo() {
        return envelopInfo;
    }

    /**
     * ����envelopInfo���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link EnvelopInfo }
     */
    public void setEnvelopInfo(EnvelopInfo value) {
        this.envelopInfo = value;
    }

    /**
     * ��ȡdataInfo���Ե�ֵ��
     *
     * @return possible object is
     * {@link DataInfo }
     */
    public DataInfo getDataInfo() {
        return dataInfo;
    }

    /**
     * ����dataInfo���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link DataInfo }
     */
    public void setDataInfo(DataInfo value) {
        this.dataInfo = value;
    }

}