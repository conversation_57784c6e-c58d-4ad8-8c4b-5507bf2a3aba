package com.danding.cds.declare.zjspecial.domain.sas101;

import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;

@Data
@XmlRootElement(name = "SasSign")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
        "seqNo",
        "bizopEtpsNo",
        "bizopEtpsSccd",
        "ownerEtpsNo",
        "ownerEtpsSccd",
        "icCode",
        "signInfo",
        "signDate",
        "certNo"
})
public class SasSign implements Serializable {

    @XmlElement(name = "SeqNo")
    private String seqNo;

    @XmlElement(name = "BizopEtpsNo", required = true)
    private String bizopEtpsNo;

    @XmlElement(name = "BizopEtpsSccd")
    private String bizopEtpsSccd;

    @XmlElement(name = "OwnerEtpsNo", required = true)
    private String ownerEtpsNo;

    @XmlElement(name = "OwnerEtpsSccd")
    private String ownerEtpsSccd;

    @XmlElement(name = "IcCode", required = true)
    private String icCode;

    @XmlElement(name = "SignInfo", required = true)
    private String signInfo;

    @XmlElement(name = "SignDate", required = true)
    private String signDate;

    @XmlElement(name = "CertNo", required = true)
    private String certNo;
}
