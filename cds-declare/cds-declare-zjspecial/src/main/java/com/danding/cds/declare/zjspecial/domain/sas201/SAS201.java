package com.danding.cds.declare.zjspecial.domain.sas201;

import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
})
@XmlRootElement(name = "SAS201")
@Data
public class SAS201 implements Serializable {

    @XmlElement(name = "HdeApprResult")
    protected HdeApprResult hdeApprResult;
    @XmlElement(name = "CheckInfo")
    protected List<CheckInfo> checkInfo;
    @XmlElement(name = "SasDclBsc")
    protected SasDclBsc sasDclBsc;
    @XmlElement(name = "SasDclDt")
    protected List<SasDclDt> bondInvtDt;
    @XmlElement(name = "SasDclUcnsDt")
    protected List<SasDclUcnsDt> sasDclUcnsDt;

    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {

    })
    @Data
    public static class HdeApprResult implements Serializable {

        @XmlElement(name = "etpsPreentNo")
        protected String etpsPreentNo;
        @XmlElement(name = "businessId")
        protected String businessId;
        @XmlElement(name = "tmsCnt")
        protected String tmsCnt;
        @XmlElement(name = "typecd")
        protected String typecd;
        @XmlElement(name = "manageResult")
        protected String manageResult;
        @XmlElement(name = "manageDate")
        protected String manageDate;
        @XmlElement(name = "rmk")
        protected String rmk;
    }

    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {

    })
    @Data
    public static class CheckInfo implements Serializable {

        @XmlElement(name = "note")
        protected String note;
    }


    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {

    })
    @Data
    public static class SasDclBsc implements Serializable {
        /**
         * 申报表编号，申报表备案数据交接时反填，
         * 规则：Y+关区代码4位+业务类型1位+年份2位+流水号4位
         */
        @XmlElement(name = "sasDclNo")
        private String sasDclNo;

        /**
         * 变更次数，数据交接时反填，首次申请备案时为0，变更时根据执法库中的变更次数 + 1
         */
        @XmlElement(name = "chgTmsCnt")
        private double chgTmsCnt;

        /**
         * 主管关区代码，必填字段
         */
        @XmlElement(name = "masterCuscd")
        private String masterCuscd;

        /**
         * 申报表预录入编号，企业端生成，唯一，
         * 规则：关区代码4位 + S + 年份2位 + 11位流水号
         */
        @XmlElement(name = "sasDclPreentNo")
        private String sasDclPreentNo;

        /**
         * 申报类型代码，1 - 备案、2 - 变更、3 - 结案
         */
        @XmlElement(name = "dclTypecd")
        private String dclTypecd;

        /**
         * 业务类型代码，A - 分送集报、B - 外发加工、C - 保税展示交易、D - 设备检测、E - 设备维修、F - 模具外发、G - 简单加工、H - 其他业务、Y - 一纳企业进出区
         */
        @XmlElement(name = "businessTypecd")
        private String businessTypecd;

        /**
         * 货物流向代码，I：入区、E：出区
         */
        @XmlElement(name = "directionTypecd")
        private String directionTypecd;

        /**
         * 区内账册编号，分送集报、保税展示交易、简单加工必填
         */
        @XmlElement(name = "areaInOriactNo")
        private String areaInOriactNo;

        /**
         * 区外账册编号，系统不检控，如果关里审批集报是区内企业 - 区外企业一对一审批，企业可填
         */
        @XmlElement(name = "areaOutOriactNo")
        private String areaOutOriactNo;

        /**
         * 区内企业编码，必填字段
         */
        @XmlElement(name = "areaInEtpsno")
        private String areaInEtpsno;

        /**
         * 区内企业名称，必填字段
         */
        @XmlElement(name = "areaInEtpsNm")
        private String areaInEtpsNm;

        /**
         * 区内企业社会信用代码
         */
        @XmlElement(name = "areaInEtpsSccd")
        private String areaInEtpsSccd;

        /**
         * 区外企业编码，分送集报必填
         */
        @XmlElement(name = "areaOutEtpsno")
        private String areaOutEtpsno;

        /**
         * 区外企业名称，分送集报必填
         */
        @XmlElement(name = "areaOutEtpsNm")
        private String areaOutEtpsNm;

        /**
         * 区外企业社会信用代码
         */
        @XmlElement(name = "areaOutEtpsSccd")
        private String areaOutEtpsSccd;

        /**
         * 保证金征收单编号
         */
        @XmlElement(name = "dpstLevyBlNo")
        private String dpstLevyBlNo;

        /**
         * 有效期，必填字段
         */
        @XmlElement(name = "validTime")
        private String validTime;

        /**
         * 展示地，保税展示交易必填
         */
        @XmlElement(name = "exhibitionPlace")
        private String exhibitionPlace;

        /**
         * 申请人，必填字段
         */
        @XmlElement(name = "dclEr")
        private String dclEr;

        /**
         * 申报日期，必填字段
         */
        @XmlElement(name = "dclTime")
        private String dclTime;

        /**
         * 备案审批时间，系统返填
         */
        @XmlElement(name = "putrecEmapvTime")
        private String putrecEmapvTime;

        /**
         * 变更审批时间，系统返填
         */
        @XmlElement(name = "chgEmapvTime")
        private String chgEmapvTime;

        /**
         * 结案审批时间，系统返填
         */
        @XmlElement(name = "clsCaseTime")
        private String clsCaseTime;

        /**
         * 审批标志代码，1：通过，2：转人工，3：退单
         */
        @XmlElement(name = "emapvMarkcd")
        private String emapvMarkcd;

        /**
         * 申报表状态代码，1 - 正常执行、2 - 暂停、3 - 结案
         */
        @XmlElement(name = "dclTbStucd")
        private String dclTbStucd;

        /**
         * 所属系统，此字段用于区域、物流系统筛选各自数据，界面不显示。字段值由交接入库时返填
         */
        @XmlElement(name = "ownerSystem")
        private String ownerSystem;

        /**
         * 备注
         */
        @XmlElement(name = "rmk")
        private String rmk;

        /**
         * 边角料标志，只对出区分送集报申报表有意义，其他类型默认为空，1：是，默认为空
         */
        @XmlElement(name = "col1")
        private String col1;

        /**
         * 备用2
         */
        @XmlElement(name = "col2")
        private String col2;

        /**
         * 备用3
         */
        @XmlElement(name = "col3")
        private Date col3;

        /**
         * 担保征收比例%，担保征收比例，默认100
         */
        @XmlElement(name = "col4")
        private double col4;
    }

    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {

    })
    @Data
    public static class SasDclDt implements Serializable {
        /**
         * 申报表编号
         */
        @XmlElement(name = "sasDclNo")
        private String sasDclNo;

        /**
         * 申报序号，必填字段
         */
        @XmlElement(name = "sasDclSeqno")
        private double sasDclSeqno;

        /**
         * 变更次数，数据交接时反填，首次申请备案时为0，变更时根据执法库中的变更次数 + 1
         */
        @XmlElement(name = "chgTmsCnt")
        private double chgTmsCnt;

        /**
         * 料件成品标志代码，I：料件/半成品、E：成品/残次品，
         * 外发加工、简单加工专用，其他类型默认为I，界面不显示。外发加工，简单加工只包含I/E，必填字段
         */
        @XmlElement(name = "mtpckEndprdTypecd")
        private String mtpckEndprdTypecd;

        /**
         * 底账商品序号，区内电子账册号不为空，
         * 1、账册类型为加工账册（单耗）的申报表
         * 2、货物流向为出区且账册类型为物流账册的申报表
         * 3、简单加工申报表，必填字段
         */
        @XmlElement(name = "oriactGdsSeqno")
        private double oriactGdsSeqno;

        /**
         * 商品编码，必填字段
         */
        @XmlElement(name = "gdecd")
        private String gdecd;

        /**
         * 商品名称，必填字段
         */
        @XmlElement(name = "gdsNm")
        private String gdsNm;

        /**
         * 商品规格型号描述，必填字段
         */
        @XmlElement(name = "gdsSpcfModelDesc")
        private String gdsSpcfModelDesc;

        /**
         * 数量
         */
        @XmlElement(name = "dclQty")
        private double dclQty;

        /**
         * 申报计量单位代码，必填字段
         */
        @XmlElement(name = "dclUnitcd")
        private String dclUnitcd;

        /**
         * 单价，必填字段
         */
        @XmlElement(name = "dclUprcAmt")
        private double dclUprcAmt;

        /**
         * 总价
         */
        @XmlElement(name = "dclTotalAmt")
        private double dclTotalAmt;

        /**
         * 币制代码，必填字段
         */
        @XmlElement(name = "dclCurrcd")
        private String dclCurrcd;

        /**
         * 许可证编号
         */
        @XmlElement(name = "licenceNo")
        private String licenceNo;

        /**
         * 许可证有效期
         */
        @XmlElement(name = "licenceValidTime")
        private Date licenceValidTime;

        /**
         * 实际出区数量
         */
        @XmlElement(name = "actlAreaoutQty")
        private double actlAreaoutQty;

        /**
         * 实际入区数量
         */
        @XmlElement(name = "actlAreainQty")
        private double actlAreainQty;

        /**
         * 商品标记代码，0 - 非重点商品 1 - 重点商品
         */
        @XmlElement(name = "gdsMarkcd")
        private String gdsMarkcd;

        /**
         * 商品备注
         */
        @XmlElement(name = "gdsRmk")
        private String gdsRmk;

        /**
         * 清单核注数量，清单已核注数量，核注清单正式核注后自动反填，集报、保税展示专用
         */
        @XmlElement(name = "invtVdedtQty")
        private double invtVdedtQty;

        /**
         * 最近核注时间，核注清单正式核注时自动反填
         */
        @XmlElement(name = "rcntVdedtTime")
        private Date rcntVdedtTime;

        /**
         * 修改标志代码，0 - 未修改、1 - 修改、2 - 删除、3 - 增加
         */
        @XmlElement(name = "modfMarkcd")
        private String modfMarkcd;

        /**
         * 备注
         */
        @XmlElement(name = "rmk")
        private String rmk;

        /**
         * 成品类型，1：成品，2：残次品，3：边角料，4：副产品。外发加工专用，默认为空
         */
        @XmlElement(name = "endprdGdsTypecd")
        private String endprdGdsTypecd;

        /**
         * 重点商品标识，0 - 非重点商品，1 - 目录重点商品，必填字段
         */
        @XmlElement(name = "col1")
        private String col1;

        /**
         * 国别(地区)，代码详情，请参见“单一窗口”门户网站参数查询，简单加工的必填，其他类型不允许填写
         */
        @XmlElement(name = "col2")
        private String col2;

        /**
         * 备用3，备用字段，暂时为空
         */
        @XmlElement(name = "col3")
        private Date col3;

        /**
         * 备用4，备用字段，暂时为空
         */
        @XmlElement(name = "col4")
        private double col4;
    }

    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {

    })
    @Data
    public static class SasDclUcnsDt implements Serializable {
        /**
         * 申报表编号
         */
        @XmlElement(name = "sasDclNo")
        private String sasDclNo;

        /**
         * 变更次数
         */
        @XmlElement(name = "chgTmsCnt")
        private double chgTmsCnt;

        /**
         * 成品申报序号，必填字段
         */
        @XmlElement(name = "endprdSeqno")
        private double endprdSeqno;

        /**
         * 料件申报序号，必填字段
         */
        @XmlElement(name = "mtpckSeqno")
        private double mtpckSeqno;

        /**
         * 净耗数量
         */
        @XmlElement(name = "netUseupQty")
        private double netUseupQty;

        /**
         * 损耗率，必填字段
         */
        @XmlElement(name = "lossRate")
        private double lossRate;

        /**
         * 修改标志代码，0 - 未修改、1 - 修改、2 - 删除、3 - 增加
         */
        @XmlElement(name = "modfMarkcd")
        private String modfMarkcd;
    }
}
