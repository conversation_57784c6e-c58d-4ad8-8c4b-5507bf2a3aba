package com.danding.cds.declare.zjspecial.domain.sas253;

import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Create 2021/4/26  10:14
 * @Describe
 **/
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
})
@XmlRootElement(name = "SAS253")
@Data
public class SAS253 implements Serializable {
    @XmlElement(name = "HdeApprResult")
    private HdeApprResult hdeApprResult;
    @XmlElement(name = "Sas2StepPassportBsc")
    private Sas2StepPassportBsc sas2StepPassportBsc;

    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
    })
    @Data
    public static class HdeApprResult implements Serializable {

        @XmlElement(name = "etpsPreentNo")
        protected String etpsPreentNo;
        @XmlElement(name = "businessId")
        protected String businessId;
        @XmlElement(name = "tmsCnt")
        protected String tmsCnt;
        @XmlElement(name = "typecd")
        protected String typecd;
        @XmlElement(name = "manageResult")
        protected String manageResult;
        @XmlElement(name = "manageDate")
        protected String manageDate;
        @XmlElement(name = "rmk")
        protected String rmk;
    }
}
