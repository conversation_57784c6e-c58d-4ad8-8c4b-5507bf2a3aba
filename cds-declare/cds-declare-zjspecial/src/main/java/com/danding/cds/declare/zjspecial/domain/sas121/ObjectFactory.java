//
// ���ļ����� JavaTM Architecture for XML Binding (JAXB) ����ʵ�� v2.2.8-b130911.1802 ���ɵ�
// ����� <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// �����±���Դģʽʱ, �Դ��ļ��������޸Ķ�����ʧ��
// ����ʱ��: 2020.06.11 ʱ�� 05:07:42 PM CST 
//


package com.danding.cds.declare.zjspecial.domain.sas121;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;
import java.io.Serializable;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory implements Serializable {

    private final static QName _SignatureValue_QNAME = new QName("", "SignatureValue");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link DataInfo }
     * 
     */
    public DataInfo createDataInfo() {
        return new DataInfo();
    }

    /**
     * Create an instance of {@link SignedInfo }
     * 
     */
    public SignedInfo createSignedInfo() {
        return new SignedInfo();
    }

    /**
     * Create an instance of {@link Signature }
     * 
     */
    public Signature createSignature() {
        return new Signature();
    }

    /**
     * Create an instance of {@link SignedInfo.Reference }
     * 
     */
    public SignedInfo.Reference createSignedInfoReference() {
        return new SignedInfo.Reference();
    }

    /**
     * Create an instance of {@link PocketInfo }
     * 
     */
    public PocketInfo createPocketInfo() {
        return new PocketInfo();
    }

    /**
     * Create an instance of {@link DataInfo.BussinessData }
     * 
     */
    public DataInfo.BussinessData createDataInfoBussinessData() {
        return new DataInfo.BussinessData();
    }

    /**
     * Create an instance of {@link KeyInfo }
     * 
     */
    public KeyInfo createKeyInfo() {
        return new KeyInfo();
    }

    /**
     * Create an instance of {@link SignedInfo.CanonicalizationMethod }
     * 
     */
    public SignedInfo.CanonicalizationMethod createSignedInfoCanonicalizationMethod() {
        return new SignedInfo.CanonicalizationMethod();
    }

    /**
     * Create an instance of {@link SignedInfo.SignatureMethod }
     * 
     */
    public SignedInfo.SignatureMethod createSignedInfoSignatureMethod() {
        return new SignedInfo.SignatureMethod();
    }

    /**
     * Create an instance of {@link Signature.Object }
     * 
     */
    public Signature.Object createSignatureObject() {
        return new Signature.Object();
    }

    /**
     * Create an instance of {@link CommonResponeMessage }
     * 
     */
    public CommonResponeMessage createCommonResponeMessage() {
        return new CommonResponeMessage();
    }

    /**
     * Create an instance of {@link Package }
     *
     */
    public Package createPackage() {
        return new Package();
    }

    /**
     * Create an instance of {@link EnvelopInfo }
     * 
     */
    public EnvelopInfo createEnvelopInfo() {
        return new EnvelopInfo();
    }

    /**
     * Create an instance of {@link PassPortMessage }
     * 
     */
    public PassPortMessage createPassPortMessage() {
        return new PassPortMessage();
    }

    /**
     * Create an instance of {@link PassPortHead }
     * 
     */
    public PassPortHead createPassPortHead() {
        return new PassPortHead();
    }

    /**
     * Create an instance of {@link PassPortList }
     * 
     */
    public PassPortList createPassPortList() {
        return new PassPortList();
    }

    /**
     * Create an instance of {@link PassPortAcmp }
     * 
     */
    public PassPortAcmp createPassPortAcmp() {
        return new PassPortAcmp();
    }

    /**
     * Create an instance of {@link AnyData }
     * 
     */
    public AnyData createAnyData() {
        return new AnyData();
    }

    /**
     * Create an instance of {@link SignedInfo.Reference.DigestMethod }
     * 
     */
    public SignedInfo.Reference.DigestMethod createSignedInfoReferenceDigestMethod() {
        return new SignedInfo.Reference.DigestMethod();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "", name = "SignatureValue")
    public JAXBElement<String> createSignatureValue(String value) {
        return new JAXBElement<String>(_SignatureValue_QNAME, String.class, null, value);
    }

}
