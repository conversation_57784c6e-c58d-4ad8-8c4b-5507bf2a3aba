package com.danding.cds.declare.zjspecial.domain.icp101;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Create 2021/4/26  9:48
 * @Describe
 **/
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
})
@Data
public class Sas2sPassPortMessage implements Serializable {
    @XmlElement(name = "Sas2sPassportHead")
    private Sas2sPassportHead sas2sPassportHead;

    @XmlElement(name = "Sas2sPassportRlt")
    private List<Sas2sPassportRlt> sas2sPassportRlt;

    @XmlElement(name = "SysId")
    private String sysId;

    @XmlElement(name = "OperCusRegCode")
    private String operCusRegCode;

}
