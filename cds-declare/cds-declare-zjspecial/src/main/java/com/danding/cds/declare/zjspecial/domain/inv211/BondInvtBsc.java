package com.danding.cds.declare.zjspecial.domain.inv211;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {

})
@Data
public class BondInvtBsc implements Serializable {
    @XmlElement(name = "bondInvtNo")
    protected String bondInvtNo;
    @XmlElement(name = "chgTmsCnt")
    protected String chgTmsCnt;
    @XmlElement(name = "invtPreentNo")
    protected String invtPreentNo;
    @XmlElement(name = "putrecNo")
    protected String putrecNo;
    @XmlElement(name = "etpsInnerInvtNo")
    protected String etpsInnerInvtNo;
    @XmlElement(name = "bizopEtpsSccd")
    protected String bizopEtpsSccd;
    @XmlElement(name = "bizopEtpsno")
    protected String bizopEtpsno;
    @XmlElement(name = "bizopEtpsNm")
    protected String bizopEtpsNm;
    @XmlElement(name = "rvsngdEtpsSccd")
    protected String rvsngdEtpsSccd;
    @XmlElement(name = "rcvgdEtpsno")
    protected String rcvgdEtpsno;
    @XmlElement(name = "rcvgdEtpsNm")
    protected String rcvgdEtpsNm;
    @XmlElement(name = "dclEtpsSccd")
    protected String dclEtpsSccd;
    @XmlElement(name = "dclEtpsno")
    protected String dclEtpsno;
    @XmlElement(name = "dclEtpsNm")
    protected String dclEtpsNm;
    @XmlElement(name = "invtDclTime")
    protected String invtDclTime;
    @XmlElement(name = "entryDclTime")
    protected String entryDclTime;
    @XmlElement(name = "entryNo")
    protected String entryNo;
    @XmlElement(name = "rltInvtNo")
    protected String rltInvtNo;
    @XmlElement(name = "rltPutrecNo")
    protected String rltPutrecNo;
    @XmlElement(name = "rltEntryNo")
    protected String rltEntryNo;
    @XmlElement(name = "rltEntryBizopEtpsSccd")
    protected String rltEntryBizopEtpsSccd;
    @XmlElement(name = "rltEntryBizopEtpsno")
    protected String rltEntryBizopEtpsno;
    @XmlElement(name = "rltEntryBizopEtpsNm")
    protected String rltEntryBizopEtpsNm;
    @XmlElement(name = "impexpPortcd")
    protected String impexpPortcd;
    @XmlElement(name = "dclPlcCuscd")
    protected String dclPlcCuscd;
    @XmlElement(name = "impexpMarkcd")
    protected String impexpMarkcd;
    @XmlElement(name = "mtpckEndprdMarkcd")
    protected String mtpckEndprdMarkcd;
    @XmlElement(name = "supvModecd")
    protected String supvModecd;
    @XmlElement(name = "trspModecd")
    protected String trspModecd;
    @XmlElement(name = "stshipTrsarvNatcd")
    protected String stshipTrsarvNatcd;
    @XmlElement(name = "applyNo")
    protected String applyNo;
    @XmlElement(name = "dclcusFlag")
    protected String dclcusFlag;
    @XmlElement(name = "dclcusTypecd")
    protected String dclcusTypecd;
    @XmlElement(name = "prevdTime")
    protected String prevdTime;
    @XmlElement(name = "formalVrfdedTime")
    protected String formalVrfdedTime;
    @XmlElement(name = "invtIochkptStucd")
    protected String invtIochkptStucd;
    @XmlElement(name = "vrfdedMarkcd")
    protected String vrfdedMarkcd;
    @XmlElement(name = "invtStucd")
    protected String invtStucd;
    @XmlElement(name = "vrfdedModecd")
    protected String vrfdedModecd;
    @XmlElement(name = "duCode")
    protected String duCode;
    @XmlElement(name = "rmk")
    protected String rmk;
    @XmlElement(name = "bondInvtTypecd")
    protected String bondInvtTypecd;
    @XmlElement(name = "entryStucd")
    protected String entryStucd;
    @XmlElement(name = "passportUsedTypecd")
    protected String passportUsedTypecd;
    @XmlElement(name = "param1")
    protected String param1;
    @XmlElement(name = "param2")
    protected String param2;
    @XmlElement(name = "param3")
    protected String param3;
    @XmlElement(name = "param4")
    protected String param4;
    @XmlElement(name = "needEntryModified")
    protected String needEntryModified;
    @XmlElement(name = "levyBlAmt")
    protected String levyBlAmt;
    @XmlElement(name = "dclTypecd")
    protected String dclTypecd;
}
