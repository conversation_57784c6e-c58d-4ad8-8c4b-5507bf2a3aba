package com.danding.cds.declare.zjspecial.domain.sas101;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class AppGoods implements Serializable {
    @XmlElement(name = "SeqNo")
    private String seqNo;

    @XmlElement(name = "SasDclNo")
    private String sasDclNo;

    @XmlElement(name = "SasDclSeqno", required = true)
    private BigDecimal sasDclSeqNo;

    @XmlElement(name = "OriactGdsSeqno", required = true)
    private BigDecimal oriActGdsSeqNo;

    @XmlElement(name = "MtpckEndprdTypecd", required = true)
    private String mtpckEndprdTypeCd;

    @XmlElement(name = "Gdecd", required = true)
    private String gdeCd;

    @XmlElement(name = "GdsNm", required = true)
    private String gdsNm;

    @XmlElement(name = "GdsSpcfModelDesc", required = true)
    private String gdsSpcfModelDesc;

    @XmlElement(name = "DclQty", required = true)
    private BigDecimal dclQty;

    @XmlElement(name = "DclUnitcd", required = true)
    private String dclUnitCd;

    @XmlElement(name = "DclUprcAmt", required = true)
    private BigDecimal dclUprcAmt;

    @XmlElement(name = "DclTotalAmt", required = true)
    private BigDecimal dclTotalAmt;

    @XmlElement(name = "DclCurrcd", required = true)
    private String dclCurrCd;

    @XmlElement(name = "LicenceNo")
    private String licenceNo;

    @XmlElement(name = "LicenceVaildTime")
    private String licenceVaildTime;

    @XmlElement(name = "GdsMarkcd")
    private String gdsMarkCd;

    @XmlElement(name = "GdsRmk")
    private String gdsRmk;

    @XmlElement(name = "ModfMarkcd", required = true)
    private String modfMarkCd;

    @XmlElement(name = "Rmk")
    private String rmk;

    @XmlElement(name = "GdsMtno", required = true)
    private String gdsMtNo;

    @XmlElement(name = "LawfUnitcd", required = true)
    private String lawfUnitCd;

    @XmlElement(name = "SecdLawfUnitcd")
    private String secdLawfUnitCd;

    @XmlElement(name = "EndprdGdsTypecd")
    private String endprdGdsTypeCd;

    @XmlElement(name = "Col1")
    private String col1;

    @XmlElement(name = "Col2")
    private String col2;

    @XmlElement(name = "Col3")
    private BigDecimal col3;

    @XmlElement(name = "Col4")
    private String col4;
}
