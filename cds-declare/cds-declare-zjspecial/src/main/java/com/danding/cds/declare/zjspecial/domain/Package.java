package com.danding.cds.declare.zjspecial.domain;

import com.danding.cds.declare.zjspecial.domain.inv101.Inv101Package;
import com.danding.cds.declare.zjspecial.domain.sas101.Sas101Package;
import com.danding.cds.declare.zjspecial.domain.sas122.Sas122Package;

import javax.xml.bind.annotation.XmlSeeAlso;
import java.io.Serializable;

@XmlSeeAlso({
        Inv101Package.class,
        Sas122Package.class,
        Sas101Package.class
})
public abstract class Package implements Serializable {

}
