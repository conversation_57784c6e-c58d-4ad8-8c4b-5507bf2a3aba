//
// ���ļ����� JavaTM Architecture for XML Binding (JAXB) ����ʵ�� v2.2.8-b130911.1802 ���ɵ�
// ����� <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// �����±���Դģʽʱ, �Դ��ļ��������޸Ķ�����ʧ��
// ����ʱ��: 2020.06.03 ʱ�� 05:12:37 PM CST 
//


package com.danding.cds.declare.zjspecial.domain.inv101;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;


/**
 * �򵥼ӹ���һ�ɳ�Ʒ������Ʒ��ϸ[��ע�嵥��Ʒ����(���漯���嵥�ϼ���Ϣ)]
 * 
 * <p>NemsInvtGoodsType complex type�� Java �ࡣ
 * 
 * <p>����ģʽƬ��ָ�������ڴ����е�Ԥ�����ݡ�
 * 
 * <pre>
 * &lt;complexType name="NemsInvtGoodsType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="SeqNo">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;length value="18"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="GdsSeqno">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="19"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="PutrecSeqno">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="19"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="GdsMtno">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="32"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="Gdecd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="10"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="GdsNm">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="512"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="GdsSpcfModelDesc">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="512"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="DclUnitcd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="3"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="LawfUnitcd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="3"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="SecdLawfUnitcd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="3"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="Natcd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="3"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="DclUprcAmt">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="25"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="DclTotalAmt">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="25"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="UsdStatTotalAmt">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="25"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="DclCurrcd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="3"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="LawfQty">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="19"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="SecdLawfQty">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="19"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="WtSfVal">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="19"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="FstSfVal">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="19"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="SecdSfVal">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="19"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="DclQty">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="19"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="GrossWt">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="19"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="NetWt">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="19"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="UseCd" minOccurs="0">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="4"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="LvyrlfModecd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="6"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="UcnsVerno">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="8"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="EntryGdsSeqno">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="19"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="ApplyTbSeqno">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="19"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="ClyMarkcd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="4"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="Rmk">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="4000"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="DestinationNatcd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="3"/>
 *               &lt;minLength value="0"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="ModfMarkcd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;maxLength value="1"/>
 *               &lt;minLength value="0"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "NemsInvtGoodsType", propOrder = {
    "seqNo",
    "gdsSeqno",
    "putrecSeqno",
    "gdsMtno",
    "gdecd",
    "gdsNm",
    "gdsSpcfModelDesc",
    "dclUnitcd",
    "lawfUnitcd",
    "secdLawfUnitcd",
    "natcd",
    "dclUprcAmt",
    "dclTotalAmt",
    "usdStatTotalAmt",
    "dclCurrcd",
    "lawfQty",
    "secdLawfQty",
    "wtSfVal",
    "fstSfVal",
    "secdSfVal",
    "dclQty",
    "grossWt",
    "netWt",
    "useCd",
    "lvyrlfModecd",
    "ucnsVerno",
    "entryGdsSeqno",
    "applyTbSeqno",
    "clyMarkcd",
    "rmk",
    "destinationNatcd",
        "modfMarkcd",
        "param1"
})
public class NemsInvtGoodsType implements Serializable {

    @XmlElement(name = "SeqNo", required = true)
    protected String seqNo;
    @XmlElement(name = "GdsSeqno", required = true)
    protected String gdsSeqno;
    @XmlElement(name = "PutrecSeqno", required = true)
    protected String putrecSeqno;
    @XmlElement(name = "GdsMtno", required = true)
    protected String gdsMtno;
    @XmlElement(name = "Gdecd", required = true)
    protected String gdecd;
    @XmlElement(name = "GdsNm", required = true)
    protected String gdsNm;
    @XmlElement(name = "GdsSpcfModelDesc", required = true)
    protected String gdsSpcfModelDesc;
    @XmlElement(name = "DclUnitcd", required = true)
    protected String dclUnitcd;
    @XmlElement(name = "LawfUnitcd", required = true)
    protected String lawfUnitcd;
    @XmlElement(name = "SecdLawfUnitcd", required = true)
    protected String secdLawfUnitcd;
    @XmlElement(name = "Natcd", required = true)
    protected String natcd;
    @XmlElement(name = "DclUprcAmt", required = true)
    protected String dclUprcAmt;
    @XmlElement(name = "DclTotalAmt", required = true)
    protected String dclTotalAmt;
    @XmlElement(name = "UsdStatTotalAmt", required = true)
    protected String usdStatTotalAmt;
    @XmlElement(name = "DclCurrcd", required = true)
    protected String dclCurrcd;
    @XmlElement(name = "LawfQty", required = true)
    protected String lawfQty;
    @XmlElement(name = "SecdLawfQty", required = true)
    protected String secdLawfQty;
    @XmlElement(name = "WtSfVal", required = true)
    protected String wtSfVal;
    @XmlElement(name = "FstSfVal", required = true)
    protected String fstSfVal;
    @XmlElement(name = "SecdSfVal", required = true)
    protected String secdSfVal;
    @XmlElement(name = "DclQty", required = true)
    protected String dclQty;
    @XmlElement(name = "GrossWt", required = true)
    protected String grossWt;
    @XmlElement(name = "NetWt", required = true)
    protected String netWt;
    @XmlElement(name = "UseCd")
    protected String useCd;
    @XmlElement(name = "LvyrlfModecd", required = true)
    protected String lvyrlfModecd;
    @XmlElement(name = "UcnsVerno", required = true)
    protected String ucnsVerno;
    @XmlElement(name = "EntryGdsSeqno", required = true)
    protected String entryGdsSeqno;
    @XmlElement(name = "ApplyTbSeqno", required = true)
    protected String applyTbSeqno;
    @XmlElement(name = "ClyMarkcd", required = true)
    protected String clyMarkcd;
    @XmlElement(name = "Rmk", required = true)
    protected String rmk;
    @XmlElement(name = "DestinationNatcd", required = true)
    protected String destinationNatcd;
    @XmlElement(name = "ModfMarkcd", required = true)
    protected String modfMarkcd;
    @XmlElement(name = "Param1", required = true)
    protected String param1;

    /**
     * ��ȡseqNo���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSeqNo() {
        return seqNo;
    }

    /**
     * ����seqNo���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSeqNo(String value) {
        this.seqNo = value;
    }

    /**
     * ��ȡgdsSeqno���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGdsSeqno() {
        return gdsSeqno;
    }

    /**
     * ����gdsSeqno���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGdsSeqno(String value) {
        this.gdsSeqno = value;
    }

    /**
     * ��ȡputrecSeqno���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPutrecSeqno() {
        return putrecSeqno;
    }

    /**
     * ����putrecSeqno���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPutrecSeqno(String value) {
        this.putrecSeqno = value;
    }

    /**
     * ��ȡgdsMtno���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGdsMtno() {
        return gdsMtno;
    }

    /**
     * ����gdsMtno���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGdsMtno(String value) {
        this.gdsMtno = value;
    }

    /**
     * ��ȡgdecd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGdecd() {
        return gdecd;
    }

    /**
     * ����gdecd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGdecd(String value) {
        this.gdecd = value;
    }

    /**
     * ��ȡgdsNm���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGdsNm() {
        return gdsNm;
    }

    /**
     * ����gdsNm���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGdsNm(String value) {
        this.gdsNm = value;
    }

    /**
     * ��ȡgdsSpcfModelDesc���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGdsSpcfModelDesc() {
        return gdsSpcfModelDesc;
    }

    /**
     * ����gdsSpcfModelDesc���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGdsSpcfModelDesc(String value) {
        this.gdsSpcfModelDesc = value;
    }

    /**
     * ��ȡdclUnitcd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDclUnitcd() {
        return dclUnitcd;
    }

    /**
     * ����dclUnitcd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDclUnitcd(String value) {
        this.dclUnitcd = value;
    }

    /**
     * ��ȡlawfUnitcd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLawfUnitcd() {
        return lawfUnitcd;
    }

    /**
     * ����lawfUnitcd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLawfUnitcd(String value) {
        this.lawfUnitcd = value;
    }

    /**
     * ��ȡsecdLawfUnitcd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSecdLawfUnitcd() {
        return secdLawfUnitcd;
    }

    /**
     * ����secdLawfUnitcd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSecdLawfUnitcd(String value) {
        this.secdLawfUnitcd = value;
    }

    /**
     * ��ȡnatcd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNatcd() {
        return natcd;
    }

    /**
     * ����natcd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNatcd(String value) {
        this.natcd = value;
    }

    /**
     * ��ȡdclUprcAmt���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDclUprcAmt() {
        return dclUprcAmt;
    }

    /**
     * ����dclUprcAmt���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDclUprcAmt(String value) {
        this.dclUprcAmt = value;
    }

    /**
     * ��ȡdclTotalAmt���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDclTotalAmt() {
        return dclTotalAmt;
    }

    /**
     * ����dclTotalAmt���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDclTotalAmt(String value) {
        this.dclTotalAmt = value;
    }

    /**
     * ��ȡusdStatTotalAmt���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUsdStatTotalAmt() {
        return usdStatTotalAmt;
    }

    /**
     * ����usdStatTotalAmt���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUsdStatTotalAmt(String value) {
        this.usdStatTotalAmt = value;
    }

    /**
     * ��ȡdclCurrcd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDclCurrcd() {
        return dclCurrcd;
    }

    /**
     * ����dclCurrcd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDclCurrcd(String value) {
        this.dclCurrcd = value;
    }

    /**
     * ��ȡlawfQty���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLawfQty() {
        return lawfQty;
    }

    /**
     * ����lawfQty���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLawfQty(String value) {
        this.lawfQty = value;
    }

    /**
     * ��ȡsecdLawfQty���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSecdLawfQty() {
        return secdLawfQty;
    }

    /**
     * ����secdLawfQty���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSecdLawfQty(String value) {
        this.secdLawfQty = value;
    }

    /**
     * ��ȡwtSfVal���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getWtSfVal() {
        return wtSfVal;
    }

    /**
     * ����wtSfVal���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setWtSfVal(String value) {
        this.wtSfVal = value;
    }

    /**
     * ��ȡfstSfVal���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFstSfVal() {
        return fstSfVal;
    }

    /**
     * ����fstSfVal���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFstSfVal(String value) {
        this.fstSfVal = value;
    }

    /**
     * ��ȡsecdSfVal���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSecdSfVal() {
        return secdSfVal;
    }

    /**
     * ����secdSfVal���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSecdSfVal(String value) {
        this.secdSfVal = value;
    }

    /**
     * ��ȡdclQty���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDclQty() {
        return dclQty;
    }

    /**
     * ����dclQty���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDclQty(String value) {
        this.dclQty = value;
    }

    /**
     * ��ȡgrossWt���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGrossWt() {
        return grossWt;
    }

    /**
     * ����grossWt���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGrossWt(String value) {
        this.grossWt = value;
    }

    /**
     * ��ȡnetWt���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNetWt() {
        return netWt;
    }

    /**
     * ����netWt���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNetWt(String value) {
        this.netWt = value;
    }

    /**
     * ��ȡuseCd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUseCd() {
        return useCd;
    }

    /**
     * ����useCd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUseCd(String value) {
        this.useCd = value;
    }

    /**
     * ��ȡlvyrlfModecd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLvyrlfModecd() {
        return lvyrlfModecd;
    }

    /**
     * ����lvyrlfModecd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLvyrlfModecd(String value) {
        this.lvyrlfModecd = value;
    }

    /**
     * ��ȡucnsVerno���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUcnsVerno() {
        return ucnsVerno;
    }

    /**
     * ����ucnsVerno���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUcnsVerno(String value) {
        this.ucnsVerno = value;
    }

    /**
     * ��ȡentryGdsSeqno���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEntryGdsSeqno() {
        return entryGdsSeqno;
    }

    /**
     * ����entryGdsSeqno���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEntryGdsSeqno(String value) {
        this.entryGdsSeqno = value;
    }

    /**
     * ��ȡapplyTbSeqno���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getApplyTbSeqno() {
        return applyTbSeqno;
    }

    /**
     * ����applyTbSeqno���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setApplyTbSeqno(String value) {
        this.applyTbSeqno = value;
    }

    /**
     * ��ȡclyMarkcd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getClyMarkcd() {
        return clyMarkcd;
    }

    /**
     * ����clyMarkcd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setClyMarkcd(String value) {
        this.clyMarkcd = value;
    }

    /**
     * ��ȡrmk���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRmk() {
        return rmk;
    }

    /**
     * ����rmk���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRmk(String value) {
        this.rmk = value;
    }

    /**
     * ��ȡdestinationNatcd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDestinationNatcd() {
        return destinationNatcd;
    }

    /**
     * ����destinationNatcd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDestinationNatcd(String value) {
        this.destinationNatcd = value;
    }

    /**
     * ��ȡmodfMarkcd���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getModfMarkcd() {
        return modfMarkcd;
    }

    /**
     * ����modfMarkcd���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setModfMarkcd(String value) {
        this.modfMarkcd = value;
    }

    public void setParam1(String param1) {
        this.param1 = param1;
    }

    public String getParam1() {
        return param1;
    }

}
