package com.danding.cds.declare.zjspecial.domain.sas202;

import com.danding.cds.declare.zjspecial.domain.sas201.SAS201;
import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;
import java.util.List;


@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
})
@XmlRootElement(name = "SAS202")
@Data
public class SAS202 implements Serializable {

    @XmlElement(name = "HdeApprResult")
    protected HdeApprResult hdeApprResult;
    @XmlElement(name = "SasDclBsc")
    protected SAS201.SasDclBsc sasDclBsc;
    @XmlElement(name = "SasDclDt")
    protected List<SAS201.SasDclDt> sasDclDts;

    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {

    })
    @Data
    public static class HdeApprResult implements Serializable {

        @XmlElement(name = "etpsPreentNo")
        protected String etpsPreentNo;
        @XmlElement(name = "businessId")
        protected String businessId;
        @XmlElement(name = "tmsCnt")
        protected String tmsCnt;
        @XmlElement(name = "typecd")
        protected String typecd;
        @XmlElement(name = "manageResult")
        protected String manageResult;
        @XmlElement(name = "manageDate")
        protected String manageDate;
        @XmlElement(name = "rmk")
        protected String rmk;
    }
}
