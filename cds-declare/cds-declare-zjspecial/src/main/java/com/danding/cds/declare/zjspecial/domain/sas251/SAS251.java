package com.danding.cds.declare.zjspecial.domain.sas251;

import com.danding.cds.declare.zjspecial.domain.sas253.Sas2StepPassportBsc;
import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Create 2021/4/26  10:30
 * @Describe
 **/
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
})
@XmlRootElement(name = "SAS251")
@Data
public class SAS251 implements Serializable {
    @XmlElement(name = "HdeApprResult")
    private HdeApprResult hdeApprResult;
    @XmlElement(name = "CheckInfo")
    private List<CheckInfo> checkInfo;
    @XmlElement(name = "Sas2StepPassportBsc")
    private Sas2StepPassportBsc sas2StepPassportBsc;

    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
    })
    @Data
    public static class HdeApprResult implements Serializable {

        @XmlElement(name = "etpsPreentNo")
        protected String etpsPreentNo;
        @XmlElement(name = "businessId")
        protected String businessId;
        @XmlElement(name = "tmsCnt")
        protected String tmsCnt;
        @XmlElement(name = "typecd")
        protected String typecd;
        @XmlElement(name = "manageResult")
        protected String manageResult;
        @XmlElement(name = "manageDate")
        protected String manageDate;
        @XmlElement(name = "rmk")
        protected String rmk;
    }


    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
    })
    @Data
    public static class CheckInfo implements Serializable {

        @XmlElement(name = "note")
        protected String note;
    }

}
