package com.danding.cds.declare.zjspecial.domain.sas101;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class AppUcns implements Serializable {
    @XmlElement(name = "SeqNo")
    private String seqNo;

    @XmlElement(name = "SasDclNo")
    private String sasDclNo;

    @XmlElement(name = "EndprdSeqno", required = true)
    private BigDecimal endprdSeqNo;

    @XmlElement(name = "MtpckSeqno", required = true)
    private BigDecimal mtpckSeqNo;

    @XmlElement(name = "NetUseupQty", required = true)
    private BigDecimal netUseupQty;

    @XmlElement(name = "LossRate", required = true)
    private BigDecimal lossRate;

    @XmlElement(name = "ModfMarkcd", required = true)
    private String modfMarkCd;

    @XmlElement(name = "Col1")
    private String col1;

    @XmlElement(name = "Col2")
    private String col2;

    @XmlElement(name = "Col3")
    private BigDecimal col3;

    @XmlElement(name = "Col4")
    private String col4;
}
