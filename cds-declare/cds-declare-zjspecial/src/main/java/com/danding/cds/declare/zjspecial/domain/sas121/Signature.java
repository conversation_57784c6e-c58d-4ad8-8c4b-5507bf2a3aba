//
// ���ļ����� JavaTM Architecture for XML Binding (JAXB) ����ʵ�� v2.2.8-b130911.1802 ���ɵ�
// ����� <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// �����±���Դģʽʱ, �Դ��ļ��������޸Ķ�����ʧ��
// ����ʱ��: 2020.06.11 ʱ�� 05:07:42 PM CST 
//


package com.danding.cds.declare.zjspecial.domain.sas121;

import javax.xml.bind.annotation.*;
import java.io.Serializable;


/**
 * <p>anonymous complex type�� Java �ࡣ
 * <p>
 * <p>����ģʽƬ��ָ�������ڴ����е�Ԥ�����ݡ�
 * <p>
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element ref="{}SignedInfo"/>
 *         &lt;element ref="{}SignatureValue"/>
 *         &lt;element ref="{}KeyInfo"/>
 *         &lt;element name="Object">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence>
 *                   &lt;element ref="{}Package"/>
 *                 &lt;/sequence>
 *                 &lt;attribute name="Id" type="{http://www.w3.org/2001/XMLSchema}string" />
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
        "signedInfo",
        "signatureValue",
        "keyInfo",
        "object"
})
@XmlRootElement(name = "Signature")
public class Signature implements Serializable {

    @XmlElement(name = "SignedInfo", required = true)
    protected SignedInfo signedInfo;
    @XmlElement(name = "SignatureValue", required = true)
    protected String signatureValue;
    @XmlElement(name = "KeyInfo", required = true)
    protected KeyInfo keyInfo;
    @XmlElement(name = "Object", required = true)
    protected Object object;

    /**
     * ��ȡsignedInfo���Ե�ֵ��
     *
     * @return possible object is
     * {@link SignedInfo }
     */
    public SignedInfo getSignedInfo() {
        return signedInfo;
    }

    /**
     * ����signedInfo���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link SignedInfo }
     */
    public void setSignedInfo(SignedInfo value) {
        this.signedInfo = value;
    }

    /**
     * ��ȡsignatureValue���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getSignatureValue() {
        return signatureValue;
    }

    /**
     * ����signatureValue���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setSignatureValue(String value) {
        this.signatureValue = value;
    }

    /**
     * ��ȡkeyInfo���Ե�ֵ��
     *
     * @return possible object is
     * {@link KeyInfo }
     */
    public KeyInfo getKeyInfo() {
        return keyInfo;
    }

    /**
     * ����keyInfo���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link KeyInfo }
     */
    public void setKeyInfo(KeyInfo value) {
        this.keyInfo = value;
    }

    /**
     * ��ȡobject���Ե�ֵ��
     *
     * @return possible object is
     * {@link Object }
     */
    public Object getObject() {
        return object;
    }

    /**
     * ����object���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link Object }
     */
    public void setObject(Object value) {
        this.object = value;
    }


    /**
     * <p>anonymous complex type�� Java �ࡣ
     * <p>
     * <p>����ģʽƬ��ָ�������ڴ����е�Ԥ�����ݡ�
     * <p>
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence>
     *         &lt;element ref="{}Package"/>
     *       &lt;/sequence>
     *       &lt;attribute name="Id" type="{http://www.w3.org/2001/XMLSchema}string" />
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
            "_package"
    })
    public static class Object implements Serializable {

        @XmlElement(name = "Package", required = true)
        protected Package _package;
        @XmlAttribute(name = "Id")
        protected String id;

        /**
         * ��ȡpackage���Ե�ֵ��
         *
         * @return possible object is
         * {@link Package }
         */
        public Package getPackage() {
            return _package;
        }

        /**
         * ����package���Ե�ֵ��
         *
         * @param value allowed object is
         *              {@link Package }
         */
        public void setPackage(Package value) {
            this._package = value;
        }

        /**
         * ��ȡid���Ե�ֵ��
         *
         * @return possible object is
         * {@link String }
         */
        public String getId() {
            return id;
        }

        /**
         * ����id���Ե�ֵ��
         *
         * @param value allowed object is
         *              {@link String }
         */
        public void setId(String value) {
            this.id = value;
        }

    }

}
