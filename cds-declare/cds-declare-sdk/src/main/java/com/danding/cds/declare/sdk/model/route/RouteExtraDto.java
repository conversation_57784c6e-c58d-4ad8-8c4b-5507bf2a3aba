package com.danding.cds.declare.sdk.model.route;

import lombok.Data;

import java.io.Serializable;

/**
 * @program: cds-center
 * @description: 路由extra
 * @author: 潘本乐（Belep）
 * @create: 2022-03-24 11:43
 **/
@Data
public class RouteExtraDto implements Serializable {
    /**
     * 电商平台ID
     */
    private Long ebpId;

    /**
     * 电商企业ID
     */
    private Long ebcId;

    /**
     * 账册ID
     */
    private Long customsBookId;

    /**
     * 清单申报企业ID
     */
    private Long listDeclareCompanyId;

    /**
     * 订单报文传输企业ID
     */
    private Long orderDeclareCompanyId;

    /**
     * 担保企业ID
     */
    private Long assureCompanyId;

    /**
     * 运单报文传输企业ID
     */
    private Long logisticsDeclareCompanyId;
}
