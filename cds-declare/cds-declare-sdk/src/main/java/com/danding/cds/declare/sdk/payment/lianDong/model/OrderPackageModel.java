package com.danding.cds.declare.sdk.payment.lianDong.model;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 * @date 2021/11/25
 */
@Data
public class OrderPackageModel implements Serializable {

    private long id;

    /**
     * 包裹编号
     */
    private String sn;


    /**
     * 应用id
     */
    private String appId;

    /**
     * 模块
     */
    private int subMod;



    /**
     * 分销标识
     */
    private boolean retailFlag;

    /**
     * 订单编码
     */
    private String orderSn;

    /**
     * 用户id
     */
    private long userId;

    private long outShopId;

    /**
     * 总价
     */
    private BigDecimal totalPrice;

    /**
     * 商品价格
     */
    private BigDecimal goodsPrice;

    /**
     * 运费
     */
    private BigDecimal expPrice;

    /**
     * 操作费
     */
    private BigDecimal operation;

    /**
     * 税费
     */
    private BigDecimal tax;

    /**
     * 折扣费用
     */
    private BigDecimal discountAmount;

    /**
     * 圈货商品总价
     */
    private BigDecimal loadPrice;

    /**
     * 平台费
     */
    private BigDecimal platformFee;

    /**
     * 额外收费（主要是订单取消时产生的费用,这些是固定不返还给商户的费用)
     */
    private BigDecimal extraFee;

    /**
     * 快递公司
     */
    private long expCompany;

    /**
     * 发货时间
     */
    private long expTime;

    /**
     * 快递单号
     */
    private String expNum;


    /**
     * 下次查询申报状态的时间
     */
    private long customsSelectTime;



    /**
     * 海关支付编码
     */
    private String customsPaySn;


    /**
     * 海关支付时间
     */
    private long customsPayTime;

    /**
     * 供应商编号
     */
    private String supplierSn;

    /**
     * 子供应商编号
     */
    private String subSupplier;

    /**
     * 仓库id
     */
    private long depotId;

    /**
     * 删除标志
     */
    private boolean delFlag;

    /**
     * 创建时间
     */
    private long createTime;

    /**
     * 供货价格
     */
    private BigDecimal supplyPriceTotal;

    /**
     * 系统计算的总重量
     */
    private BigDecimal weight;

    /**
     * 仓库回传的真实总重量
     */
    private BigDecimal actualWeight;


    /**
     * 奇门货主编码
     */
    private String qmOwnerCode = "";


    /**
     * 海关逻辑校验通过时间
     */
    private long logisPassTime;

    private int checkFlag;

    /**
     * 广州OMS订单信息
     */
    private String gzOMSOrderInfo;

    /**
     * 清单编号
     */
    private String invtNo;

    /**
     * 清单时间
     */
    private long invtTime;

    /**
     * 备注信息
     */
    private String remark = "";

    /**
     * 包裹费用备注
     */
    private String adminRemarkJson;

    /**
     * 标签数据
     */
    private String tagJson;

    /**
     * 结算数据
     */
    private String settleJSON;

    /**
     * 海关申报相关数据
     */
    private String customsJson;

    /**
     * 申报单号
     */
    private String declareSn;

    /**
     * 订单暂停标记
     */
    private int pauseFlag;



    /**
     * 广州子账户key
     */
    private String SubAccountKey;

    /**
     * 商户编号
     */
    private String merchantCode;

    /**
     * 额外信息JSON串
     */
    private String extraJson;

    /**
     * ********---wmsSn
     */
    private String wmsSn;



}
