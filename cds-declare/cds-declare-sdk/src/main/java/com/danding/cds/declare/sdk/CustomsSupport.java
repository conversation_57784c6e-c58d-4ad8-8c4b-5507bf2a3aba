package com.danding.cds.declare.sdk;

import com.alibaba.fastjson.JSON;
import com.danding.cds.declare.sdk.clear.base.CustomsClientRegistry;
import com.danding.cds.declare.sdk.clear.base.CustomsResult;
import com.danding.cds.declare.sdk.clear.chongqing.CQCustomsClient;
import com.danding.cds.declare.sdk.clear.hangzhou.HzDataCenterCustomsClient;
import com.danding.cds.declare.sdk.clear.simulate.SimulateCustomsClient;
import com.danding.cds.declare.sdk.clear.tianjin.TJCustomsClient;
import com.danding.cds.declare.sdk.clear.zhejiang.ZJCallbackResponse;
import com.danding.cds.declare.sdk.clear.zhejiang.ZJCustomsClient;
import com.danding.cds.declare.sdk.config.SpecialConfig;
import com.danding.cds.declare.sdk.enums.CustomsType;
import com.danding.cds.declare.sdk.executor.CustomsClearExecutor;
import com.danding.cds.declare.sdk.executor.CustomsIeExecutor;
import com.danding.cds.declare.sdk.handler.CustomsCallbackHandler;
import com.danding.cds.declare.sdk.listener.CustomsListener;
import com.danding.cds.declare.sdk.model.bizDeclareForm.Sas101MessageRequest;
import com.danding.cds.declare.sdk.model.cancel.WarpCancelOrderInfo;
import com.danding.cds.declare.sdk.model.check.WrapDataPayExInfo;
import com.danding.cds.declare.sdk.model.checkList.Icp101MessageRequest;
import com.danding.cds.declare.sdk.model.checkList.Inv101MessageRequest;
import com.danding.cds.declare.sdk.model.checkList.Sas121MessageRequest;
import com.danding.cds.declare.sdk.model.checklistAuth.Sas122MessageRequest;
import com.danding.cds.declare.sdk.model.inventory.WrapInventoryOrderInfo;
import com.danding.cds.declare.sdk.model.order.WrapOrderDeclareInfo;
import com.danding.cds.declare.sdk.model.payment.WrapPaymentInfo;
import com.danding.cds.declare.sdk.model.refund.WarpRefundOrderInfo;
import com.danding.cds.declare.sdk.model.shipment.WrapShipmentInfo;
import com.danding.cds.declare.sdk.payment.alipay.AlipayChannel;
import com.danding.cds.declare.sdk.payment.base.ChannelRegistry;
import com.danding.cds.declare.sdk.payment.lianDong.LianDongPayChannel;
import com.danding.cds.declare.sdk.payment.tonglian.TongLianPayChannel;
import com.danding.cds.declare.sdk.payment.wechat.WechatpayChannel;
import com.danding.logistics.mq.common.handler.MessageSender;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.io.Serializable;

/**
 * 海关对接总入口
 * ==>各类关区 浙江、重庆、天津……
 * ==>各类系统 浙江电子口岸、杭州数据分中心、重庆关贸、天津综服
 * ==>各类平台 支付宝、微信、各类运单等第三方代申报平台，拼多多云内申报、wms内申报
 */
@Data
@Slf4j
@RefreshScope
public class CustomsSupport implements Serializable {

    /**
     * 变更申报单号，总署
     */
    @Value("${zj.change.declare.no.list:}")
    String[] zjChangeDeclareNo;

    public static final String ENV_ONLINE = "online";
    public static final String ENV_TEST = "test";
    public static final String ENV_MOCK = "mock";
    public static String env = ENV_MOCK;
    /**
     * 对接动作接听，负责接收各类动作的上报，回执的回调。需使用者实现
     */
    private CustomsListener listener;

    private CustomsClientRegistry clientRegistry;

    private ChannelRegistry channelRegistry;

    private SpecialConfig specialConfig;

    public MessageSender messageSender;

    public CustomsSupport(CustomsListener listener, String env, SpecialConfig specialConfig, MessageSender messageSender) {
        CustomsSupport.env = env;
        this.listener = listener;
        this.specialConfig = specialConfig;
        this.messageSender = messageSender;
        clientRegistry = new CustomsClientRegistry();
        new ZJCustomsClient(this, clientRegistry, env); // 注册浙江口岸
        new CQCustomsClient(this, clientRegistry, env); // 注册重庆口岸
        new TJCustomsClient(this, clientRegistry, env); // 注册天津口岸
        new HzDataCenterCustomsClient(this, clientRegistry, env); // 注册杭州数据中心-直接消息发总署报文
        new SimulateCustomsClient(this, clientRegistry, env); // 注册杭州数据中心-直接消息发总署报文

        this.channelRegistry = new ChannelRegistry();
        AlipayChannel alipayChannel = new AlipayChannel(this);
        channelRegistry.register(alipayChannel.getPayChannel().getValue(), alipayChannel);
        WechatpayChannel wechatpayChannel = new WechatpayChannel(this);
        channelRegistry.register(wechatpayChannel.getPayChannel().getValue(), wechatpayChannel);
        LianDongPayChannel lianDongPayChannel = new LianDongPayChannel(this);
        channelRegistry.register(lianDongPayChannel.getPayChannel().getValue(), lianDongPayChannel);
        TongLianPayChannel tongLianPayChannel = new TongLianPayChannel(this);
        channelRegistry.register(tongLianPayChannel.getPayChannel().getValue(), tongLianPayChannel);
    }

    /**
     * 统一动作上报方法
     *
     * @param report
     * @return
     */
    public CustomsResult accept(CustomsReport report) {
        log.info("[op:CustomsSupport-accept] report={}", JSON.toJSONString(report));
        try {
            this.listener.accept(report);
            return CustomsResult.success();
        } catch (Exception e) {
            log.error("[op:CustomsSupport-accept] exception, cause={}", e.getMessage(), e);
            return CustomsResult.fail(e.getMessage());
        }
    }

    public CustomsResult clearPaymentDeclare(WrapPaymentInfo info) {
        try {
            CustomsClearExecutor executor = new CustomsClearExecutor();
            return executor.paymentDeclare(this, info, env);
        } catch (Exception e) {
            log.error("[op:CustomsSupport-clearPaymentDeclare] exception, cause={}", e.getMessage(), e);
            return CustomsResult.fail(e.getMessage());
        }
    }


    public CustomsResult clareOrderDeclare(WrapOrderDeclareInfo info) {
        try {
            CustomsClearExecutor executor = new CustomsClearExecutor();
            return executor.orderDeclare(this, info);
        } catch (Exception e) {
            log.error("[op:CustomsSupport-clareOrderDeclare] exception, cause={}", e.getMessage(), e);
            return CustomsResult.fail(e.getMessage());
        }
    }


    public CustomsResult clearInventoryDeclare(WrapInventoryOrderInfo info) {
        try {
            CustomsClearExecutor executor = new CustomsClearExecutor();
            return executor.inventoryDeclare(this, info);
        } catch (Exception e) {
            log.error("[op:CustomsSupport-clearInventoryDeclare] exception, cause={}", e.getMessage(), e);
            return CustomsResult.fail(e.getMessage());
        }
    }

    public CustomsResult clearShipmentDeclare(WrapShipmentInfo info) {
        try {
            CustomsClearExecutor executor = new CustomsClearExecutor();
            return executor.logisticsDeclare(this, info);
        } catch (Exception e) {
            log.error("[op:CustomsSupport-clearLogisticsDeclare] exception, cause={}", e.getMessage(), e);
            return CustomsResult.fail(e.getMessage());
        }
    }

    public CustomsResult clearInventoryCancel(WarpCancelOrderInfo warpCancelOrderInfo) {
        try {
            CustomsClearExecutor executor = new CustomsClearExecutor();
            return executor.inventoryCancel(this, warpCancelOrderInfo);
        } catch (Exception e) {
            log.error("[op:CustomsSupport-clearInventoryDeclare] exception, cause={}", e.getMessage(), e);
            return CustomsResult.fail(e.getMessage());
        }
    }

    public CustomsResult clearInventoryRefund(WarpRefundOrderInfo warpRefundOrderInfo) {
        try {
            CustomsClearExecutor executor = new CustomsClearExecutor();
            return executor.inventoryRefund(this, warpRefundOrderInfo);
        } catch (Exception e) {
            log.error("[op:CustomsSupport-clearInventoryRefund] exception, cause={}", e.getMessage(), e);
            return CustomsResult.fail(e.getMessage());
        }
    }

    public CustomsResult handleSimulateCallback(ZJCallbackResponse zjCallbackResponse) {
        CustomsCallbackHandler handler = new CustomsCallbackHandler();
        return handler.handle(this, zjCallbackResponse, CustomsType.DT_CCS);
    }

    public CustomsResult handleZJCallback(ZJCallbackResponse zjCallbackResponse) {
        CustomsCallbackHandler handler = new CustomsCallbackHandler();
        return handler.handle(this, zjCallbackResponse, CustomsType.ZHE_JIANG);
    }

    public CustomsResult handleCQCallback(String data) {
        CustomsCallbackHandler handler = new CustomsCallbackHandler();
        return handler.handle(this, data, CustomsType.CHONG_QING);
    }

    public CustomsResult handleTJCallback(String data) {
        CustomsCallbackHandler handler = new CustomsCallbackHandler();
        return handler.handle(this, data, CustomsType.TIAN_JIN);
    }

    public CustomsResult handleHzDataCenterCallback(String data) {
        CustomsCallbackHandler handler = new CustomsCallbackHandler();
        return handler.handle(this, data, CustomsType.HANG_ZHOU_DATA_CENTER);
    }

    public String handleCQSign(String data) {
        CQCustomsClient cqCustomsClient = (CQCustomsClient) clientRegistry.findClient(CustomsType.CHONG_QING.getValue());
        try {
            cqCustomsClient.sign(data);
            return "True";
        } catch (Exception e) {
            log.error("重庆申报签名异常：{}", e.getMessage(), e);
            return "False";
        }
    }

    /**
     * 杭州核注
     *
     * @return
     */
    public CustomsResult ieEndorsementHZ(Inv101MessageRequest request) {
        CustomsIeExecutor executor = new CustomsIeExecutor(this);
        return executor.endorsementHZ(request);
    }

    /**
     * 杭州核放
     */
    public CustomsResult ieChecklistHZ(Sas121MessageRequest request) {
        log.info("[ieChecklistHZ.push request = {}]", JSON.toJSONString(request));
        CustomsIeExecutor executor = new CustomsIeExecutor(this);
        return executor.checklistHZ(request);
    }

    /**
     * 杭数 核放单调用授权
     */
    public CustomsResult ieChecklistAuthHZ(Sas122MessageRequest request) {
        log.info("[ieChecklistAuthHZ.push request = {}]", JSON.toJSONString(request));
        CustomsIeExecutor executor = new CustomsIeExecutor(this);
        return executor.checklistAuthHZ(request);
    }

    /**
     * 杭数 核放单调用授权
     */
    public CustomsResult ieBizDeclareFormHZ(Sas101MessageRequest request) {
        log.info("[ieBizDeclareFormHZ.push request = {}]", JSON.toJSONString(request));
        CustomsIeExecutor executor = new CustomsIeExecutor(this);
        return executor.bizDeclareFormHZ(request);
    }

    public void dataCheckSubmit(WrapDataPayExInfo infoDTO) {
        ZJCustomsClient zjCustomsClient = (ZJCustomsClient) clientRegistry.findClient(CustomsType.ZHE_JIANG.getValue());
        zjCustomsClient.dataCheckSubmit(infoDTO);
    }

    public CustomsResult ieChecklistTwoStep(Icp101MessageRequest twoStepRequest) {
        log.info("[ieChecklistTwoStep.push request = {}]", JSON.toJSONString(twoStepRequest));
        CustomsIeExecutor executor = new CustomsIeExecutor(this);
        return executor.checklistTwoStep(twoStepRequest);
    }
}

/*
 * 模型选择，因为相比于传统平台类别的SDK，该模块的方法拓展较少，形式固定，所以不用拓展形式的Request-Response，
 * 选择使用直接定义固定的function来调用。
 * */

// accept回调函数的调用，不一定固定在外层。部分的申报方式。
// 可能当时未能收到回复，该方式无异常只能表示发起无异常，不表示调用无异常