package com.danding.cds.declare.sdk.payment.alipay;


import com.danding.cds.declare.sdk.payment.base.BaseToken;
import lombok.Data;

import java.io.Serializable;

/**
 * DATE: 16/8/22 下午4:24 <br>
 * MAIL: <EMAIL> <br>
 * AUTHOR: zhanghecheng
 */
@Data
public class AlipayToken extends BaseToken implements Serializable {

    private static final long serialVersionUID = 4598974360361523669L;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 支付宝接口的pid
     */
    private String pid;

    /**
     * 收款账户
     */
    private String account;

    /**
     * 签名方式
     */
    private String signType;

    /**
     * 支付宝接口的密钥(md5), 签名和验签的时候使用
     */
    private String key;

    /**
     * 验签方式为RSA时使用
     */
    private String alipayRsaPubKey;

    /**
     * 签名方式为RSA时使用
     */
    private String rsaPriKey;

    /**
     * 验签方式为DSA时使用
     */
    private String alipayDsaPubKey;

    /**
     * 签名方式为DSA时使用
     */
    private String dsaPriKey;

    /**
     * 老版合作者身份ID
     */
    private String partner;
}
