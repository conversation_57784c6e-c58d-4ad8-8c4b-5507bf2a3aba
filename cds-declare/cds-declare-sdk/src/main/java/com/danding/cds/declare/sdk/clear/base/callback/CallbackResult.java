package com.danding.cds.declare.sdk.clear.base.callback;

import com.danding.cds.declare.sdk.clear.base.CustomsResult;
import lombok.Data;

@Data
public class CallbackResult extends CustomsResult {

    /**
     * 回执类型
     */
    private CallbackType callbackType;

    /**
     * 原始报文
     */
    private String origMsg;

    /**
     * 回执发送方
     * {@link com.danding.cds.common.enums.TrackLogConstantMixAll}
     */
    private String sender;

    /**
     *  发送方的编码 为什么加编码，因为sender会变成EWTP节点，senderCode会固定EWTP
     */
    private  String senderCode;
}
