package com.danding.cds.declare.sdk.bean.dto;

import com.danding.cds.declare.ceb.internal.enums.CebMessageEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @program: cds-center
 * @description: 海关报文
 * @author: 潘本乐（Belep）
 * @create: 2021-09-26 21:13
 **/
@Data
@NoArgsConstructor
public class CustomsMsgDto implements Serializable {

    /**
     * ceb枚举
     */
    private CebMessageEnum cebMsgEnum;
    /**
     * 请求ceb报文
     */
    private String cebMsg;
    /**
     * 请求CEB签名后报文
     */
    private String cebSignMsg;
    /**
     * 请求Dxp报文
     */
    private String dxpMsg;

    /**
     * guid
     */
    private String guid;

    /**
     * 推送响应报文
     */
    private String response;

    public CustomsMsgDto(CebMessageEnum cebMsgEnum, String cebMsg, String cebSignMsg, String dxpMsg) {
        this.cebMsgEnum = cebMsgEnum;
        this.cebMsg = cebMsg;
        this.cebSignMsg = cebSignMsg;
        this.dxpMsg = dxpMsg;
    }

    public CustomsMsgDto(CebMessageEnum cebMsgEnum, String cebMsg, String dxpMsg) {
        this.cebMsgEnum = cebMsgEnum;
        this.cebMsg = cebMsg;
        this.dxpMsg = dxpMsg;
    }

    public CustomsMsgDto(CebMessageEnum cebMsgEnum, String cebMsg, String cebSignMsg, String dxpMsg, String guid) {
        this.cebMsgEnum = cebMsgEnum;
        this.cebMsg = cebMsg;
        this.cebSignMsg = cebSignMsg;
        this.dxpMsg = dxpMsg;
        this.guid = guid;
    }
}
