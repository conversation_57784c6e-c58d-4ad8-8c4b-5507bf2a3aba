package com.danding.cds.declare.sdk.model.checkList;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
public class Inv101MessageRequest extends MessageRequest implements Serializable {
    private static final long serialVersionUID = -2988467713109244696L;
    /**
     * 申报标志
     * 0--暂存；
     * 1--申报；
     */
    private Integer delcareFlag;
    /**
     * 业务类型
     */
    private String bussinessType;
    /**
     * 核注清单编号内部
     */
    private String endorsementOrderNo;
    /**
     * 预录入编号
     */
    private String preEndorsementOrderNo;
    /**
     * 核注清单编号
     */
    private String realEndorsementOrderNo;
    /**
     * 保税电商清单编号
     */
    private List<String> invtNos = new ArrayList<>();
    /**
     * 核注清单表体
     */
    private List<InvtItemBody> invtItemBodyList = new ArrayList<>();
    /**
     * 核注清单料件表体
     */
    private List<InvtItemGoodsBody> invtItemGoodsBodyList = new ArrayList<>();
    /**
     * 核注清单表头
     */
    private InvtHeadType invtHeadType;
}
