package com.danding.cds.declare.sdk.utils;

import com.danding.cds.common.utils.HttpRequestUtil;
import com.github.kevinsawicki.http.HttpRequest;

import java.util.HashMap;
import java.util.Map;

public class HttpSignUtil {

    public static String xmlSign(String url, String dxpId, String origStr) {
        Map<String, String> params = new HashMap<>();
        params.put("dxpId", dxpId);
        params.put("origStr", origStr);
        try {
            HttpRequest httpRequest = HttpRequestUtil.post(url + "/swxa/xmlSign", params);
            if (httpRequest.ok()) {
                return httpRequest.body();
            } else {
                return null;
            }
        } catch (HttpRequest.HttpRequestException e) {
            return null;
        }
    }
}

