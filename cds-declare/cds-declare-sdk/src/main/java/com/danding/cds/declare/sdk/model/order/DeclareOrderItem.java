package com.danding.cds.declare.sdk.model.order;

import lombok.Data;

import java.io.Serializable;

@Data
public class DeclareOrderItem implements Serializable {
    /**
     * 企业自定义的商品编码
     */
    private String goodsNo;

    /**
     * 物品名称
     */
    private String goodsName;

    /**
     * 商品规格、型号
     */
    private String goodsModel;

    /**
     * HS编码
     */
    private String hsCode;

    /**
     * 申报单价
     */
    private String unitPrice;
    /**
     * 申报计量单位
     */
    private String goodsUnit;

    /**
     * 申报数量
     */
    private int goodsCount;

    /**
     * 产销国 原产国
     */
    private String originCountry;
}
