package com.danding.cds.declare.sdk.clear.chongqing;

import com.alibaba.fastjson.JSON;
import com.danding.cds.declare.ceb.domain.ceb312.CEB312Message;
import com.danding.cds.declare.ceb.domain.ceb622.CEB622Message;
import com.danding.cds.declare.ceb.internal.utils.XMLUtil;
import com.danding.cds.declare.cqport.domain.CQDxpMsg.DxpMsg;
import com.danding.cds.declare.cqport.domain.CQDxpMsg.ReceiverIds;
import com.danding.cds.declare.cqport.domain.CQDxpMsg.TransInfo;
import com.danding.cds.declare.cqport.domain.CQZHGMOrderMessage;
import com.danding.cds.declare.cqport.internal.CQCustomsConfig;
import com.danding.cds.declare.sdk.CustomsReport;
import com.danding.cds.declare.sdk.CustomsSupport;
import com.danding.cds.declare.sdk.clear.base.CustomsClient;
import com.danding.cds.declare.sdk.clear.base.CustomsClientRegistry;
import com.danding.cds.declare.sdk.clear.base.callback.CallbackResult;
import com.danding.cds.declare.sdk.clear.base.callback.CallbackType;
import com.danding.cds.declare.sdk.clear.base.callback.module.DeliverResult;
import com.danding.cds.declare.sdk.clear.base.callback.module.InventoryCallback;
import com.danding.cds.declare.sdk.clear.base.callback.module.OrderCallback;
import com.danding.cds.declare.sdk.clear.base.result.InventoryDeclareResult;
import com.danding.cds.declare.sdk.clear.chongqing.builder.CQInventoryBuilder;
import com.danding.cds.declare.sdk.clear.chongqing.model.CQDeliverModel;
import com.danding.cds.declare.sdk.enums.CustomsType;
import com.danding.cds.declare.sdk.model.inventory.WrapInventoryOrderInfo;
import com.danding.cds.declare.sdk.utils.CebSignUtil;
import com.danding.component.http.SaasHttpConstant;
import com.danding.core.tenant.SimpleTenantHelper;
import com.github.kevinsawicki.http.HttpRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Deprecated
public class CQCustomsClient extends CustomsClient {


    /**
     * 初始化注入
     *
     * @param support
     * @param registry
     * @param env
     */
    public CQCustomsClient(CustomsSupport support, CustomsClientRegistry registry, String env) {
        super(support, registry, env);
    }

    @Override
    public CustomsType getSystem() {
        return CustomsType.CHONG_QING;
    }

    @Override
    public InventoryDeclareResult inventoryDeclare(WrapInventoryOrderInfo info) {
        CustomsReport report = new CustomsReport(CustomsReport.TYPE_SEND,CustomsReport.SYSTEM_CUSTOMS_CLIENT,CustomsReport.PROCESS_CLEAR_DECLARE_INVENTORY);
        InventoryDeclareResult result = new InventoryDeclareResult();
        result.setOrderNo(info.getCustomsInventoryDto().getOrderNo());
        result.setSn(info.getCustomsInventoryDto().getSn());
        result.setStatus(info.getCustomsInventoryDto().getStatus());
        report.buildProcessData(result);
        try{
            // Step::数据组装
            CQCustomsConfig cqCustomsConfig = new CQCustomsConfig();
            CQZHGMOrderMessage orderMessage =  new CQInventoryBuilder(cqCustomsConfig,info).build();
            String xmlStr = XMLUtil.convertToXml(orderMessage);
            report.buildRequest(xmlStr);
            // Step::加签
            Map<String, String> reqParams = new HashMap<String, String>();
            reqParams.put("data", Base64.encodeBase64String(xmlStr.getBytes("UTF-8")));
            StringBuffer requestBuf = new StringBuffer();
            for (String key : reqParams.keySet()) {
                requestBuf.append(key).append("=").append(URLEncoder.encode(reqParams.get(key), "UTF-8")).append("&");
            }
            String sendStr = requestBuf.toString();
            log.info("op:[CQDeclareInventoryOrderCommand] url={}, param={}", cqCustomsConfig.getInterfaceUrl(), sendStr);
//            try {
//                TrackLogDTO trackLogDTO = new TrackLogDTO();
//                trackLogDTO.setOrderType(InventoryOrderType.INVENTORY_ORDER.getCode());
//                trackLogDTO.setDeclareOrderNo(result.getOrderNo())
//                        .setOldStatus(OrderStatus.DEC_WAIT.getValue()).setNewStatus(OrderStatus.DEC_ING.getValue())
//                        .setOperateDes(TrackLogEnums.INVENTORY_DECLARE.getCode()).setLogDes("重庆口岸报文已推送").setContent(xmlStr).setHasXmlMessage(1);
//                support.messageSender.sendMsg(JSON.toJSONString(trackLogDTO),"ccs-trackLog-topic");
//            }catch (Exception e){
//                log.info("[op:CQCustomsClient-inventoryDeclare trackLog error={}]",e.getMessage());
//            }

            String declareResp;
            if (CustomsSupport.ENV_ONLINE.equals(env)) {
                HttpRequest httpRequest = HttpRequest.post(
                                cqCustomsConfig.getInterfaceUrl())
                        .header("Content-Type", "application/x-www-form-urlencoded;charset=utf-8")
                        .header(SaasHttpConstant.HEADER_TENANT_ID, SimpleTenantHelper.getTenantIdStr())
                        .send(sendStr);
                if (httpRequest.ok()) {
                    declareResp = httpRequest.body();
                    log.info("op:[CQDeclareInventoryOrderCommand] request succ, res={}", declareResp);
                }else {
                    log.info("op:[CQDeclareInventoryOrderCommand] http net error");
                    throw new RuntimeException("网络请求异常:" + httpRequest.code());
                }
            }else {
                declareResp = "True";
            }
            report.buildResponse(declareResp);
        }catch (Exception e){
            log.error("op:CQDeclareInventoryOrderCommand 异常={}",e.getMessage(),e.getCause());
            throw new RuntimeException("重庆口岸申报失败", e);
        }
        support.accept(report);
        return result;
    }

    public void sign(String data){
        CustomsReport reportAccept = new CustomsReport(CustomsReport.TYPE_ACCEPT,CustomsReport.SYSTEM_CUSTOMS_CLIENT);
        CustomsReport reportSend = new CustomsReport(CustomsReport.TYPE_SEND, CustomsReport.SYSTEM_CUSTOMS_CLIENT);
        CQCustomsConfig cqCustomsConfig = new CQCustomsConfig();
        String callBackContent ="";
        try {
            callBackContent = new String(Base64.decodeBase64(data.getBytes("UTF-8")));
            reportAccept.buildResponse(callBackContent);

            log.info("[op:CQPortCallBackHandler  重庆口岸加签回调] 原始报文callBackContent={}", callBackContent);
            byte[] bs = callBackContent.getBytes(StandardCharsets.UTF_8.name());

            String domain = "http://ccs.fen.daily.yang800.com/swxa";
            if (CustomsSupport.ENV_ONLINE.equals(CustomsSupport.env)){
                domain = "http://ccs.fen.yang800.com/swxa";
            }

            byte[] signed = CebSignUtil.httpXml(
                    domain ,
                    "DXPENT0000026916",
                    new String(bs, StandardCharsets.UTF_8.name())).getBytes();
            String signedString = new String(signed, StandardCharsets.UTF_8.name());
            signedString = signedString.replace("<?xml version=\"1.0\"?>", "")
                    .replace("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>", "");
            String content = buildDxpMsg(signedString.getBytes());
            Map<String, String> params = new HashMap<String, String>();
            params.put("data", Base64.encodeBase64String(content.getBytes("UTF-8")));
            StringBuffer requestParams = new StringBuffer();
            for (String key : params.keySet()) {
                requestParams.append(key).append("=").append(URLEncoder.encode(params.get(key), "UTF-8")).append("&");
            }
            reportSend.buildRequest(content);
            log.info("[op:CQPortCallBackHandler  重庆口岸加签回调] 加密后的报文={}", content);
            log.info("[op:CQPortCallBackHandler  重庆口岸加签回调] 加签请求报文数据 request={}", requestParams.toString());
            String response;
            if (CustomsSupport.ENV_ONLINE.equals(env)){
                HttpRequest httpRequest = HttpRequest.post(cqCustomsConfig.getInterfaceSignUrl())
                        .header("Content-Type", "application/x-www-form-urlencoded;charset=utf-8")
                        .header(SaasHttpConstant.HEADER_TENANT_ID, SimpleTenantHelper.getTenantIdStr())
                        .send(requestParams.toString());
                response = httpRequest.body();
            }else {
                response = "True";
            }
            reportSend.buildResponse(response);
        } catch (Exception e) {
            log.error("[op:CQPortCallBackHandler 重庆关贸加签回调]: 业务处理异常! callBackContent={}, e={}", callBackContent, e);
            throw new RuntimeException("重庆加签失败",e);
        }
        support.accept(reportAccept);
        support.accept(reportSend);
    }

    @Override
    public CallbackResult parserCallback(Object request) {
        CallbackResult result = new CallbackResult();
        String content = (String) request;
        if (!content.startsWith("<")){
            try {
                content = new String(Base64.decodeBase64(content.getBytes("UTF-8")));
            } catch (UnsupportedEncodingException e) {
                log.warn("处理异常：{}", e.getMessage(), e);
            }
        }
        log.info("[OP:CQCustomsClient] 重庆解密报文:{}", content);
        CallbackType callbackType = getEventKey(content);
        result.setCallbackType(callbackType);
        result.setOrigMsg(content);
        if (CallbackType.NULL.equals(result.getCallbackType())){
            CustomsReport report = new CustomsReport(
                    CustomsReport.TYPE_ACCEPT,
                    CustomsReport.SYSTEM_CUSTOMS_CLIENT,
                    CustomsReport.PROCESS_CLEAR_CALLBACK_UN_KNOW);
            report.setResponseMsg(JSON.toJSONString(content));
            report.setProcessData(JSON.toJSONString(result));
            support.accept(report);
        }
        return result;
    }

    public static CallbackType getEventKey(String content) {
        if (content.contains("CEB312Message")) {
            return CallbackType.ORDER;
        } else if (content.contains("CEB622Message")) {
            return CallbackType.INVENTORY;
        } else if (content.contains("ORDER_XT2WMS")) {
            return CallbackType.DELIVER;
        }
        return CallbackType.NULL;
    }

    private static String buildDxpMsg(byte[] bs) throws Exception {
        DxpMsg dxpMsg = new DxpMsg();
        dxpMsg.setVer("1.0");
        dxpMsg.setDs("http://www.w3.org/2000/09/xmldsig#");
        dxpMsg.setXsi("http://www.w3.org/2001/XMLSchema-instance");
        dxpMsg.setDxp("http://www.chinaport.gov.cn/dxp");
        dxpMsg.setData(java.util.Base64.getEncoder().encodeToString(bs));
        TransInfo transInfo = new TransInfo();
        Date now = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'.0Z'");
        transInfo.setCopMsgId("DXPENT0000026891"
                + simpleDateFormat.format(now) + String.valueOf(System.currentTimeMillis()).substring(9, 13));
        ReceiverIds receiverIds = new ReceiverIds();
        receiverIds.setReceiverId("DXPEDCCEB0000002");
        transInfo.setReceiverIds(Collections.singletonList(receiverIds));
        transInfo.setCreatTime(simpleDateFormat2.format(now));
        transInfo.setMsgType("CEB311");
        transInfo.setSenderId("DXPENT0000026891");
        dxpMsg.setTransInfo(transInfo);
        return XMLUtil.convertToXmlWithOutXmlHead(dxpMsg);
    }

    @Override
    public OrderCallback handelOrderMsg(CallbackResult callbackResult) {
        CustomsReport report = new CustomsReport(
                CustomsReport.TYPE_ACCEPT,
                CustomsReport.SYSTEM_CUSTOMS_CLIENT,
                CustomsReport.PROCESS_CLEAR_CALLBACK_ORDER);

        log.info("[op:CQCustomsClient-handelOrderMsg] CallbackResult={}", JSON.toJSONString(callbackResult));
        OrderCallback orderCallback = new OrderCallback();
        try {
            // Module::总署回执
            String requestXmlString = callbackResult.getOrigMsg();
            report.setResponseMsg(requestXmlString);
            requestXmlString = requestXmlString.replace("xmlns=\"http://www.chinaport.gov.cn/ceb\"", "");
            CEB312Message response = XMLUtil.converyToJavaBean(requestXmlString, CEB312Message.class);
            orderCallback.setEbpCode(response.getOrderReturn().getEbpCode());
            orderCallback.setOrderNo(response.getOrderReturn().getOrderNo());
            Date returnDate =  DateUtils.parseDate(response.getOrderReturn().getReturnTime(), "yyyyMMddHHmmssSSS");
            orderCallback.setReturnTime(returnDate);
            orderCallback.setReturnStatus(response.getOrderReturn().getReturnStatus());
            orderCallback.setReturnInfo(response.getOrderReturn().getReturnInfo());
            report.setProcessData(JSON.toJSONString(orderCallback));
            support.accept(report);
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
        }
        log.info("[op:CQCustomsClient-handelOrderMsg] result={}", JSON.toJSONString(orderCallback));
        return orderCallback;
    }

    @Override
    public InventoryCallback handelInventoryMsg(CallbackResult callbackResult) {
        CustomsReport report = new CustomsReport(
                CustomsReport.TYPE_ACCEPT,
                CustomsReport.SYSTEM_CUSTOMS_CLIENT,
                CustomsReport.PROCESS_CLEAR_CALLBACK_INVENTORY);
        report.setRequestMsg("");
        log.info("[op:ZJCustomsClient-handelInventoryMsg] CallbackResult={}", JSON.toJSONString(callbackResult));
        InventoryCallback inventoryCallback = new InventoryCallback();
        try {
            // Module::总署回执
            String requestXmlString = callbackResult.getOrigMsg();
            report.setResponseMsg(requestXmlString);
            requestXmlString = requestXmlString.replace("xmlns=\"http://www.chinaport.gov.cn/ceb\"", "");
            CEB622Message response = XMLUtil.converyToJavaBean(requestXmlString, CEB622Message.class);
            inventoryCallback.setEbpCode(response.getInventoryReturn().getEbpCode());
            //这里用法不对 应该塞到sn里 废弃方法不做改动
            inventoryCallback.setOrderNo(response.getInventoryReturn().getCopNo());
            if (StringUtils.isEmpty(response.getInventoryReturn().getReturnTime())){
                inventoryCallback.setReturnTime(new Date());
            }else {
                Date returnDate =  DateUtils.parseDate(response.getInventoryReturn().getReturnTime(), "yyyyMMddHHmmssSSS");
                inventoryCallback.setReturnTime(returnDate);
            }
            inventoryCallback.setInvtNo(response.getInventoryReturn().getInvtNo());
            inventoryCallback.setPreNo(response.getInventoryReturn().getPreNo());
            inventoryCallback.setReturnStatus(response.getInventoryReturn().getReturnStatus());
            inventoryCallback.setReturnInfo(response.getInventoryReturn().getReturnInfo());

            report.setProcessData(JSON.toJSONString(inventoryCallback));
            support.accept(report);
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
        }
        log.info("[op:ZJCustomsClient-handelInventoryMsg] result={}", JSON.toJSONString(inventoryCallback));
        return inventoryCallback;
    }

    @Override
    public DeliverResult handelDeliverMsg(CallbackResult callbackResult) {
        CustomsReport report = new CustomsReport(CustomsReport.TYPE_ACCEPT,
                CustomsReport.SYSTEM_CUSTOMS_CLIENT,
                CustomsReport.PROCESS_CALLBACK_DELIVER);
        report.setRequestMsg("");
        String requestXmlString = callbackResult.getOrigMsg();
        report.setResponseMsg(requestXmlString);
        DeliverResult deliverResult = new DeliverResult();
        try {
            CQDeliverModel response = XMLUtil.converyToJavaBean(requestXmlString, CQDeliverModel.class);
            deliverResult.setEbpCode(response.getORDER_HEAD().getEBP_CODE());
            deliverResult.setDeclareOrderNo(response.getORDER_HEAD().getORIGINAL_ORDER_NO());
            deliverResult.setDepotOrderNo(response.getORDER_HEAD().getORIGINAL_ORDER_NO());
            deliverResult.setLogisticsNo(response.getORDER_HEAD().getLOGISTICS_NO());
            deliverResult.setWeight(BigDecimal.ZERO);
            deliverResult.setShipmentTime(System.currentTimeMillis());
            report.buildProcessData(deliverResult);
            support.accept(report);
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
        }
        return deliverResult;
    }
}
