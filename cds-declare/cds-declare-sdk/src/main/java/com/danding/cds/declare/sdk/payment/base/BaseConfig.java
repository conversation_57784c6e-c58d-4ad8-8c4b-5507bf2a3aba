package com.danding.cds.declare.sdk.payment.base;

import lombok.Data;

import java.io.Serializable;

@Data
public abstract class BaseConfig implements Serializable {
    /**
     * 海关支付单推送网关
     */
    private String customDeclareOrderGateway;

    /**
     * 海关支付单查询网关
     */
    private String customDeclareQueryGateway;

    /**
     * 海关重新推送支付单网关
     */
    private String customDeclareRestGateway;
}
