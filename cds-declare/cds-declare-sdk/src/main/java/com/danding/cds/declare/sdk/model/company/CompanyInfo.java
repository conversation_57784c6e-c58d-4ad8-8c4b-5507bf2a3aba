package com.danding.cds.declare.sdk.model.company;

import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
public class CompanyInfo implements Serializable {
    private static final long serialVersionUID = 909295051394333555L;
    /**
     * 企业名称
     */
    private String name;
    /**
     * 海关十位备案编码
     */
    private String code;

    /**
     * 总署备案编码
     */
    private String cebCode;

    /**
     * 总署备案名称
     */
    private String cebName;

    /**
     * dxpId
     */
    private String dxpId;

    /**
     * 申报方式配置列表
     */
    private List<CompanyDeclareConfigDto> declareConfigList;
}
