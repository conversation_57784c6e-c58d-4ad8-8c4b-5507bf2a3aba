package com.danding.cds.declare.sdk.config;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CustomsSpecialToken implements Serializable {
    /**
     * 海关备案编码（十位）
     */
    private String customsCode;
    /**
     * 海关备案名称
     */
    private String customsName;
    /**
     * 企业社会信用代码
     */
    private String socialCreditCode;
    /**
     * ic卡号
     */
    private String icCard;
    /**
     * 特殊监管区域mq发送队列
     */
    private String senderId;
    /**
     * 特殊监管区域mq接收队列
     */
    private String consumerId;
    /**
     * 海关账册编码
     */
    private String customsBookCode;
    /**
     * 兼容多本账册的情况
     */
    private List<String> customsBookCodes;
    /**
     * 连接方式，用以区分不同的连接方式
     */
    private String connectionType;
    /**
     * 特殊监管区域调用地址，当 connectionType=SpecialCustomsApp 有效
     */
    private String specialCustomsAppUrl;
    /**
     * 租户id 用于回执接收时区分租户
     */
    private String tenantId;
}
