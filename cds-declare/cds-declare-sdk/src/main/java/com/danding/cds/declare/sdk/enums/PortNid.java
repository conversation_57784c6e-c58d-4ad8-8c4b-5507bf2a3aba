package com.danding.cds.declare.sdk.enums;

import org.apache.commons.lang3.StringUtils;
public enum PortNid {
    EMPTY(0, "EMPTY", "空口岸"),
    HANGZHOU(1, "HANGZHOU", "杭州口岸"),
    GUANGZHOU(2, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>O<PERSON>", "广州口岸"),
    NINGBO(3, "<PERSON><PERSON><PERSON>", "宁波口岸"),
    CUSTOMSOFFICE(4, "CUSTOMSOFFICE", "海关总署"),
    JINAN(5, "<PERSON><PERSON><PERSON>", "济南口岸"),
    XIAMEN(6, "XIAMEN", "厦门口岸"),
    PINGTAN(7, "PINGTAN", "平潭口岸"),
    SHATIAN(8, "SH<PERSON><PERSON><PERSON>", "沙田口岸"),
    CHONGQING(9, "CHONGQING", "重庆口岸"),
    QINGDAO(10, "QINGDAO", "青岛口岸"),
    GYPORT(11, "GYPORT", "贵阳口岸"),
    J<PERSON><PERSON><PERSON><PERSON>(12, "<PERSON><PERSON>NG<PERSON><PERSON>", "江阴口岸"),
    NANCHANG(13, "NANCHANG", "南昌口岸"),
    ZHENGZHOU(14, "ZHENGZHOU", "郑州口岸"),
    QIANHAI(15, "QIANHAI", "前海口岸"),
    CHENGDU(16, "CHENGDU", "成都口岸"),
    HEFEI(17, "HEFEI", "合肥口岸");
    public static final String typeExceptionDesc = "类型异常";

    private int value;
    private String nid;
    private String description;

    PortNid(int value, String nid, String description) {
        this.value = value;
        this.nid = nid;
        this.description = description;

    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getNid() {
        return nid;
    }

    public void setNid(String nid) {
        this.nid = nid;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public static String getDescByCode(Long code) {
        if (code != null) {
            for (PortNid portNid : PortNid.values()) {
                if (portNid.getValue() == code.intValue()) {
                    return portNid.getDescription();
                }
            }
        }
        return typeExceptionDesc;
    }

    public static String getNidByCode(Long code) {
        if (code != null) {
            for (PortNid portNid : PortNid.values()) {
                if (portNid.getValue() == code.intValue()) {
                    return portNid.getNid();
                }
            }
        }
        return EMPTY.getNid();
    }


    public static Integer getCodeByNid(String nid) {        
        if (StringUtils.isNotBlank(nid)) {
            for (PortNid portNid : PortNid.values()) {
                if (StringUtils.equals(portNid.getNid(), nid)) {
                    return portNid.getValue();
                }
            }
        }
        return EMPTY.getValue();
    }
    
    public static PortNid getByNid(String nid) {        
        if (StringUtils.isNotBlank(nid)) {
            for (PortNid portNid : PortNid.values()) {
                if (StringUtils.equals(portNid.getNid(), nid)) {
                    return portNid;
                }
            }
        }
        return EMPTY;
    }

    @Override
    public String toString() {
        return this.nid;
    }
}
