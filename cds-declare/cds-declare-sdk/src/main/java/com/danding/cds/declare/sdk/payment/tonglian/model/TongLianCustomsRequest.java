package com.danding.cds.declare.sdk.payment.tonglian.model;

import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;

/**
 * 通联支付申报请求参数
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(propOrder = {"head", "body"})
@XmlRootElement(name = "PAYMENT_INFO")
public class TongLianCustomsRequest implements Serializable {

    @XmlElement(name = "HEAD")
    private TongLianCustomsXmlHead head;

    @XmlElement(name = "BODY")
    private Body body;

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(propOrder = {"customsCode", "paymentChannel", "cusId", "paymentDatetime", "mchtOrderNo", "paymentOrderNo",
            "paymentAmount", "currency", "eshopEntCode", "eshopEntName", "payerName", "paperType", "paperNumber",
            "paperPhone", "memo", "mainPaymentOrderNo", "goodsFee", "taxFee", "freightFee", "bizTypeCode", "orgCode",
            "isCheck", "customsAreaCode"})
    @XmlRootElement(name = "BODY")
    public static class Body implements Serializable {

        /**
         * 海关类别
         */
        @XmlElement(name = "CUSTOMS_CODE")
        private String customsCode;
        /**
         * 支付渠道
         */
        @XmlElement(name = "PAYMENT_CHANNEL")
        private String paymentChannel;
        /**
         * 在支付渠道进行支付用的商户号
         */
        @XmlElement(name = "CUS_ID")
        private String cusId;
        /**
         * 支付时间 yyyyMMddHH24mmss
         */
        @XmlElement(name = "PAYMENT_DATETIME")
        private String paymentDatetime;
        /**
         * 商户平台订单号
         */
        @XmlElement(name = "MCHT_ORDER_NO")
        private String mchtOrderNo;
        /**
         * 支付流水号
         */
        @XmlElement(name = "PAYMENT_ORDER_NO")
        private String paymentOrderNo;
        /**
         * 支付总额 单位为分
         */
        @XmlElement(name = "PAYMENT_AMOUNT")
        private Long paymentAmount;
        /**
         * 支付币制  固定值人民币 156
         */
        @XmlElement(name = "CURRENCY")
        private String currency;
        /**
         * 电商平台代码
         * 海关分配的电商平台代码（注意：广州海关与其他关不同,请使用C开头的10位编码）
         */
        @XmlElement(name = "ESHOP_ENT_CODE")
        private String eshopEntCode;
        /**
         * 电商平台名称
         * 海关分配的电商平台名称（注意：广州海关与其他关不同）
         */
        @XmlElement(name = "ESHOP_ENT_NAME")
        private String eshopEntName;
        /**
         * 支付人姓名
         */
        @XmlElement(name = "PAYER_NAME")
        private String payerName;
        /**
         * 支付人证件类型 01 身份证
         */
        @XmlElement(name = "PAPER_TYPE")
        private String paperType;
        /**
         * 支付人证件号码
         */
        @XmlElement(name = "PAPER_NUMBER")
        private String paperNumber;
        /**
         * 支付人手机号
         */
        @XmlElement(name = "PAPER_PHONE")
        private String paperPhone;
        /**
         * 备注
         */
        @XmlElement(name = "MEMO")
        private String memo;
        /**
         * 主支付流水号
         */
        @XmlElement(name = "MAIN_PAYMENT_ORDER_NO")
        private String mainPaymentOrderNo;
        /**
         * 商品货款金额 单位为分 杭州必填
         */
        @XmlElement(name = "GOODS_FEE")
        private Long goodsFee;
        /**
         * 税款金额 单位为分 杭州必填
         */
        @XmlElement(name = "TAX_FEE")
        private Long taxFee;
        /**
         * 运费 单位为分 杭州必填
         */
        @XmlElement(name = "FREIGHT_FEE")
        private Long freightFee;
        /**
         * 业务类型 广州必填
         */
        @XmlElement(name = "BIZ_TYPE_CODE")
        private String bizTypeCode;
        /**
         * 检验检疫机构 广州必填
         */
        @XmlElement(name = "ORG_CODE")
        private String orgCode;
        /**
         * 是否报南沙国 广州必填 填F 不报南沙国检
         */
        @XmlElement(name = "IS_CHECK")
        private String isCheck;
        /**
         * 主管海关代码 广州必填
         * 根据商户实际情况填写（参见海关提供的关区代码）
         */
        @XmlElement(name = "CUSTOMS_AREA_CODE")
        private String customsAreaCode;
    }
}