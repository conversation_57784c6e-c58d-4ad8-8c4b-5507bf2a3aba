package com.danding.cds.declare.sdk.model.inventory;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class CustomsInventoryInfo implements Serializable {
    private static final long serialVersionUID = -7755162995496909499L;
    /**
     * 业务编号
     */
    private Long id;
    private String sn;

    private Integer status;

    /**
     * 账册编号
     */
    private String bookNo;

    /**
     * 账册ID
     */
    private Long bookId;

    /**
     * 申报单号
     */
    private String orderNo;

    /**
     * 运单号
     */
    private String logisticsNo;

    /**
     * 申报口岸 TODO:这里的申报口岸未作转换
     */
    private String customs;//JINYI -> zhejiang
    private String clientCustoms;

    /**
     * 订购人信息
     */
    private String buyerIdType;
    private String buyerTelNumber;
    private String buyerIdNumber;
    private String buyerName;

    /**
     * 收件人信息
     */
    private String consigneeAddress;

    /**
     * 用作进口时间
     */
    private Date createTime;

    /**
     * 监管场所代码
     */
    private String customsField;

    /**
     * 运费(元)
     */
    private BigDecimal freight;

    /**
     * 主要货物名称
     */
    private String mainGName;
    /**
     * 预录入编
     */
    private String preNo;
    /**
     * 清单编号 申请单编号
     */
    private String inventoryNo;
    /**
     * 电商平台
     */
    private String ebpCode;
    /**
     * 保费
     */
    private BigDecimal insureAmount;
    /**
     * 支付流水号
     */
    private String payTransactionId;
    /**
     * 净重
     */
    private BigDecimal netWeight;
    /**
     * 毛重
     */
    private BigDecimal grossWeight;

    /**
     * 税费
     */
    private BigDecimal taxFee;
    /**
     * 折扣
     */
    private BigDecimal discountFee;

    /**
     * 备注
     */
    private String note;

}
