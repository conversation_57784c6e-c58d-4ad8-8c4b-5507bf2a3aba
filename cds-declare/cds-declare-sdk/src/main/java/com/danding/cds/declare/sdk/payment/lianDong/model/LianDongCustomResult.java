package com.danding.cds.declare.sdk.payment.lianDong.model;


import com.alibaba.fastjson.annotation.JSONField;
import com.umf.api.payments.Link;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


@NoArgsConstructor
@Data
public class LianDongCustomResult implements Serializable {
    private LianDongMeta meta;
    private LianDongCustomsParam customes_declaration_only;
    private List<Link> links = new ArrayList<>();
    @JSONField(name="customs_declarations")
    private List<CustomsDeclarationsDTO> customsDeclarations;


    public String getVerifyDept() {

        if (CollectionUtils.isEmpty(customsDeclarations)) {
            return "异常";
        }
        return this.customsDeclarations.get(0).getVerifyDepartment();
    }


    @NoArgsConstructor
    @Data
    public static class CustomsDeclarationsDTO implements Serializable {
        @JSONField(name="tax_amount")
        private TaxAmountDTO taxAmount;
        @JSONField(name="freight_amount")
        private FreightAmountDTO freightAmount;
        @JSONField(name="customs_clearance_date")
        private String customsClearanceDate;
        @JSONField(name="verify_department")
        private String verifyDepartment;
        @JSONField(name="mer_customs_code")
        private String merCustomsCode;
        @JSONField(name="sub_order")
        private SubOrderDTO subOrder;
        @JSONField(name="ec_plat_id")
        private String ecPlatId;
        @JSONField(name="verify_department_pay_trace")
        private String verifyDepartmentPayTrace;
        @JSONField(name="id")
        private String id;
        @JSONField(name="sub_order_amount")
        private SubOrderAmountDTO subOrderAmount;
        @JSONField(name="notify_url")
        private String notifyUrl;
        @JSONField(name="customs_id")
        private String customsId;

        @NoArgsConstructor
        @Data
        public static class TaxAmountDTO implements Serializable {
            @JSONField(name="total")
            private String total;
            @JSONField(name="currency")
            private String currency;
            @JSONField(name="total_cny")
            private String totalCny;
        }

        @NoArgsConstructor
        @Data
        public static class FreightAmountDTO implements Serializable {
            @JSONField(name="total")
            private String total;
            @JSONField(name="currency")
            private String currency;
            @JSONField(name="total_cny")
            private String totalCny;
        }

        @NoArgsConstructor
        @Data
        public static class SubOrderDTO implements Serializable {
            @JSONField(name="mer_sub_reference_id")
            private String merSubReferenceId;
            @JSONField(name="sub_customs_trace")
            private String subCustomsTrace;
            @JSONField(name="items")
            private List<Item> items;
        }

        @NoArgsConstructor
        @Data
        public static class SubOrderAmountDTO implements Serializable {
            @JSONField(name="total")
            private String total;
            @JSONField(name="currency")
            private String currency;
            @JSONField(name="total_cny")
            private String totalCny;
        }
    }
}
