package com.danding.cds.declare.sdk.clear.base.callback.module;

import com.danding.cds.http.saas.annotation.TenantHttpField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class InventoryCallback implements Serializable {
    /**
     * 电商平台
     */
    private String ebpCode;

    /**
     * 担保企业
     */
    private String agentCode;

    /**
     * 企业内部编号 非必有
     */
    @TenantHttpField(alias = "customsInventorySn")
    private String sn;

    /**
     * 申报单号 非必有
     */
    private String orderNo;

    /**
     * 回执时间
     */
    private Date returnTime;

    /**
     * 回执状态
     */
    private String returnStatus;

    /**
     * 回执信息
     */
    private String returnInfo;

    /**
     * 预录入编号
     */
    private String preNo;

    /**
     * 清单编号
     */
//    @TenantHttpField(alias = "inventoryNo")
    private String invtNo;
}
