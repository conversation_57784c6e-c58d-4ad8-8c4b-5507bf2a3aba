package com.danding.cds.declare.sdk.payment.lianDong.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.umf.api.payments.Amount;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description 子订单
 * @date 2021/12/2
 */
@Data
public class LianDongSubOrder implements Serializable {

    /**
     * 子订单对象的id.(非必填，不传联动返回，作为报送海关的订单号)
     */
    @JSONField( name = "mer_sub_reference_id" )
    private String mer_sub_reference_id	;

    /**
     * 子订单金额.
     */
    @JSONField( name = "amount" )
    private Amount amount;

    /**
     * 商品的交易码
     */
    @JSONField( name = "trans_code" )
    private String trans_code;

    /**
     * 	是否报关.
     */
    @JSONField( name = "is_customs" )
    private String is_customs;

    /**
     * 	子订单收据.
     */
    @JSONField( name = "invoice_id" )
    private String invoice_id;

    /**
     * 	子订单收据.
     */
    @JSONField( name = "items" )
    private List<Item> items;

    /**
     * 	报关流水.商户无需上送，请求UMF报关后返回
     */
    @JSONField( name = "sub_customs_trace" )
    private String sub_customs_trace;

    /**
     * 	物流单号.更新物流单号时上传
     */
    @JSONField( name = "tracking_number" )
    private String tracking_number;
}
