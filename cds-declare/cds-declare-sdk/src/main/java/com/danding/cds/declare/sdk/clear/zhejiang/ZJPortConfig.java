package com.danding.cds.declare.sdk.clear.zhejiang;

import com.danding.cds.declare.sdk.CustomsSupport;
import com.danding.cds.declare.sdk.clear.zhejiang.encry.CebSignInfo;
import com.danding.cds.declare.sdk.enums.CustomsType;
import com.danding.cds.declare.sdk.swxa.SwxaSignUtil;
import com.danding.cds.declare.sdk.utils.TokenUtil;
import com.sansec.jce.provider.SwxaProvider;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.security.Security;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Data
@Configuration
@ConfigurationProperties(prefix = "zjport.config")
@ConditionalOnProperty(prefix = "zjport.config", name = "open", havingValue = "true")
public class ZJPortConfig {

    private Boolean open;

    /**
     * 企业访问令牌
     */
    private List<ZJAgentToken> tokens;

    @Autowired
    private CustomsSupport support;

    @PostConstruct
    public void init(){
        ZJCustomsClient client = (ZJCustomsClient)support.getClientRegistry().findClient(CustomsType.ZHE_JIANG.getValue());
        for (ZJAgentToken token : tokens) {
            if (token.getCebSignInfo() != null && token.getCebSignInfo().getSignWay() == 2){
                File swsdsTempFile = null;
                try {
                    swsdsTempFile = loadTempConfig("swsds", ".ini");
                    Security.insertProviderAt(new SwxaProvider(swsdsTempFile.getAbsolutePath()), 2);
                }catch (Exception fne){
                    log.error("启动连接加密机失败,失败原因={}",fne.getMessage(), fne);
                    throw  new RuntimeException("加密机加载失败",fne);
                }finally{
                    try {
                        if (swsdsTempFile != null) {
                            FileUtils.forceDelete(swsdsTempFile);
                        }
                    }catch (Exception ex){
                        ex.printStackTrace();
                    }
                }
                break;
            }
        }
        File signTempFile=null;
        try{
            for (ZJAgentToken token : tokens) {
                log.info("[op:ZJPortConfig] load name={}", token.getName());
                CebSignInfo cebSignInfo = token.getCebSignInfo();
                this.setX509Cert(cebSignInfo);
                List<CebSignInfo> cebSignInfoList = token.getCebSignInfoList();
                if (CollectionUtils.isEmpty(cebSignInfoList)) {
                    cebSignInfoList = new ArrayList<>();
                }
                List<String> dxpIdList = cebSignInfoList.stream().map(CebSignInfo::getDxpId).distinct().collect(Collectors.toList());
                if (Objects.nonNull(cebSignInfo) && !dxpIdList.contains(cebSignInfo.getDxpId())) {
                    cebSignInfoList.add(cebSignInfo);
                }
                for (CebSignInfo info : cebSignInfoList) {
                    if (info != null && !StringUtils.isEmpty(info.getSignFile())) {
                        this.setX509Cert(info);
                    }
                }
                token.setCebSignInfoList(cebSignInfoList);
                client.addToken(token);
            }
            // 这里把初始化好的token数据,放到CebMessageUtil里面，方便后续使用
            TokenUtil.initZjportConfig(tokens);
        }catch (Exception fne){
            log.warn("启动证书加载失败,失败原因={}", fne.getMessage(), fne);
            throw  new RuntimeException("加密机加载失败",fne);

        } finally {
            try {
                if (signTempFile != null) {
                    FileUtils.forceDelete(signTempFile);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    private static void setX509Cert(CebSignInfo info) throws IOException {
        if (Objects.isNull(info) || StringUtils.isEmpty(info.getSignFile())) {
            return;
        }
        if (Objects.nonNull(info.getX509cert())) {
            return;
        }
        File signTempFile = loadTempConfig(info.getSignFile(), ".cer");
        X509Certificate x509cert = SwxaSignUtil.d2i_X509Cerificate(signTempFile.getAbsolutePath());
        if (x509cert == null) {
            log.error("[op:loadJceSwxa] {}启动证书加载失败", info.getDxpId());
            System.exit(0);
        } else {
            info.setX509cert(x509cert);
            log.info("恭喜您，{}启动证书加载成功", info.getDxpId());
        }
    }

    private static File loadTempConfig(String name, String suffix) throws IOException {
        ClassPathResource classPathResource = new ClassPathResource(name + suffix);
        InputStream inputStream = classPathResource.getInputStream();
        File tempFile = File.createTempFile(name, suffix);
        try {
            FileUtils.copyInputStreamToFile(inputStream, tempFile);
        } finally {
            IOUtils.closeQuietly(inputStream);
        }
        return tempFile;
    }
}