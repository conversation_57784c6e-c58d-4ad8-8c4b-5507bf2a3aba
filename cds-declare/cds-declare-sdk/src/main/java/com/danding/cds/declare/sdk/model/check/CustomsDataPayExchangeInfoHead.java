package com.danding.cds.declare.sdk.model.check;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

@Data
public class CustomsDataPayExchangeInfoHead implements Serializable {
    @JSONField(ordinal=1)
    private String guid; //系统唯一序号
    @JSONField(ordinal=2)
    private String initalRequest; //原始请求
    @JSONField(ordinal=3)
    private String initalResponse; //原始响应
    @JSONField(ordinal=4)
    private String ebpCode; //电商平台代码
    @JSONField(ordinal=5)
    private String payCode; //支付企业代码
    @JSONField(ordinal=6)
    private String payTransactionId; //交易流水号
    @JSONField(ordinal=7)
    private Double totalAmount; //交易金额
    @JSONField(ordinal=8)
    private String currency; //币制
    @JSONField(ordinal=9)
    private String verDept; //验核机构
    @JSONField(ordinal=10)
    private String payType; //支付类型
    @JSONField(ordinal=11)
    private String tradingTime; //交易成功时间
    @JSONField(ordinal=12)
    private String note; //备注
}
