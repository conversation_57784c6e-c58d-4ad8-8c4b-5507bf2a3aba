package com.danding.cds.declare.sdk.enums;

import java.util.Objects;

public enum BusinessTypeEnums {

    JIE_ZHOU("JIEZHOU", "芥舟");

    private String code;
    private String desc;

    BusinessTypeEnums(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    public String getCode() {
        return this.code;
    }
    public String getDesc() {
        return this.desc;
    }
    public static BusinessTypeEnums getEnum(String code) {
        for (BusinessTypeEnums t : BusinessTypeEnums.values()) {
            if (Objects.equals(t.getCode(), code)) {
                return t;
            }
        }
        return null;
    }
}
