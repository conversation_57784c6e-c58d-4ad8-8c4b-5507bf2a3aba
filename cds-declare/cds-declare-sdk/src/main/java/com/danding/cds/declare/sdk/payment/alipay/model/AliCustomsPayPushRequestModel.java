package com.danding.cds.declare.sdk.payment.alipay.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

@Data
public class AliCustomsPayPushRequestModel implements Serializable {
    /**
     * 接口名称
     */
    @JSONField(name = "service")
    private String service;
    /**
     * 合作者身份ID;
     * 签约的支付宝账号对应的支付宝唯一用户号。以2088开头的16位纯数字组成
     */
    @JSONField(name = "partner")
    private String partner;
    /**
     *参数编码字符集
     * 商户网站使用的编码格式，如UTF-8、GBK、GB2312等
     */
    @JSONField(name = "_input_charset")
    private String InputCharset;
    /**
     * 签名方式
     * DSA、RSA、MD5三个值可选，必须大写
     */
//    @JSONField(name = "sign_type")
//    private String signType;
    /**
     *签名
     * 请参见本文档“附录：签名与验签”
     */
//    @JSONField(name = "sign")
//    private String sign;

    /**
     * 报关流水号
     * 商户生成的用于唯一标识一次报关操作的业务编号。
     * 建议生成规则：yyyymmmdd型8位日期拼接4位序列号。
     * 该参数长度为6~32位。
     */
    @JSONField(name = "out_request_no")
    private String outRequestNo;
    /**
     * 支付宝交易号
     * 该交易在支付宝系统中的交易流水号，最长64位。
     */
    @JSONField(name = "trade_no")
    private String tradeNo;
    /**
     * 商户海关备案编号
     *
     */
    @JSONField(name = "merchant_customs_code")
    private String merchantCustomsCode;
    /**
     *报关金额
     */
    @JSONField(name = "amount")
    private String amount;
    /**
     * 海关编号
     */
    @JSONField(name = "customs_place")
    private String customsPlace;
    /**
     * 商户海关备案名称
     */
    @JSONField(name = "merchant_customs_name")
    private String merchantCustomsName;
    /**
     * 是否拆单
     */
    @JSONField(name = "is_split")
    private String isSplit="T";
    /**
     * 子订单号
     */
    @JSONField(name = "sub_out_biz_no")
    private String subOutBizNo;

    /**
     * 订购人姓名
     */
    @JSONField(name = "buyer_name")
    private String buyerName;

    /**
     * 订购人身份证
     */
    @JSONField(name = "buyer_id_no")
    private String buyerIdNo;
}
