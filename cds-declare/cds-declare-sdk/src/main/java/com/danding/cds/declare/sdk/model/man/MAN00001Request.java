package com.danding.cds.declare.sdk.model.man;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/2/23 17:38
 */
@Data
public class MAN00001Request implements Serializable {
    private ManSign manSign;

    private List<ManFbStockInOutDetail> manFbStockInOutDetailList;

    @Data
    public static class ManFbStockInOutDetail implements Serializable {
        private String inOutSeq;

        private String manualId;

        private String sourceNo;

        private String itemType;

        private String inOutFlag;

        private BigDecimal inOutAmount;

        private String inOutTime;

        /**
         * 非必填
         */
        private String jobFormId;
    }
}
