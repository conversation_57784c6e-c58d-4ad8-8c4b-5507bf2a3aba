package com.danding.cds.declare.sdk.swxa;

import com.danding.cds.declare.sdk.clear.zhejiang.encry.CebSignInfo;
import com.sansec.jce.provider.SwxaProvider;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.security.Security;
import java.security.cert.X509Certificate;
import java.util.List;
import java.util.Objects;

@Data
@Slf4j
@Configuration
@ConfigurationProperties(prefix = "swxa.config")
public class JCESwxaConfig  {

    private List<CebSignInfo> list;

    @PostConstruct
    public void init(){
        // 启动加密机连接
        if (CollectionUtils.isEmpty(list)){
            return;
        }
        File swsdsTempFile = null;
        try {
            swsdsTempFile = loadTempConfig("swsds", ".ini");
            Security.insertProviderAt(new SwxaProvider(swsdsTempFile.getAbsolutePath()), 2);
        }catch (Exception fne){
            log.error("启动连接加密机失败,失败原因={}",fne.getMessage(), fne);
            throw  new RuntimeException("加密机加载失败",fne);
        }finally{
            try {
                if (swsdsTempFile != null) {
                    FileUtils.forceDelete(swsdsTempFile);
                }
            }catch (Exception ex){
                ex.printStackTrace();
            }
        }
        // 加载证书
        File signTempFile=null;
        try
        {
            // Step::各申报企业证书文件加载检测
            for (CebSignInfo cebSignInfo : list) {
                if (cebSignInfo!=null && !StringUtils.isEmpty(cebSignInfo.getSignFile())){
                    signTempFile = loadTempConfig(cebSignInfo.getSignFile(), ".cer");
                    X509Certificate x509cert = SwxaSignUtil.d2i_X509Cerificate(signTempFile.getAbsolutePath());
                    if (x509cert == null){
                        log.error("[op:loadJceSwxa] {}启动证书加载失败", cebSignInfo.getDxpId());
                        System.exit(0);
                    }else {
                        cebSignInfo.setX509cert(x509cert);
                        log.info("恭喜您，{}启动证书加载成功", cebSignInfo.getDxpId());
                    }
                }
            }
        }catch (Exception fne){
            log.error("启动证书加载失败,失败原因={}", fne.getMessage(), fne);
            throw new RuntimeException("加密机加载失败",fne);

        }finally{
            try {
                if (signTempFile != null) {
                    FileUtils.forceDelete(signTempFile);
                }
            }catch (Exception ex){
                log.error("处理异常：{}", ex.getMessage(), ex);
            }
        }
    }

    public CebSignInfo getInfo(String dxpId){
        for (CebSignInfo cebSignInfo : list) {
            if (cebSignInfo.getDxpId().equals(dxpId)){
                return cebSignInfo;
            }
        }
        return null;
    }

    /**
     * 通过海关十位编码获取签名信息
     *
     * @param cebCode
     * @return
     */
    public CebSignInfo getSignInfoByCebCode(String cebCode) {
        for (CebSignInfo cebSignInfo : list) {
            if (Objects.equals(cebSignInfo.getCebCode(), cebCode)) {
                return cebSignInfo;
            }
        }
        return null;
    }

    private static File loadTempConfig(String name,String suffix)throws IOException {
        ClassPathResource classPathResource = new ClassPathResource(name+suffix);
        InputStream inputStream = classPathResource.getInputStream();
        File tempFile = File.createTempFile(name, suffix);
        try {
            FileUtils.copyInputStreamToFile(inputStream, tempFile);
        } finally {
            IOUtils.closeQuietly(inputStream);
        }
        return tempFile;
    }
}
