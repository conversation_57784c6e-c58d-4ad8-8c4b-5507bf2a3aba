package com.danding.cds.declare.sdk.bean.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @program: cds-center
 * @description: s
 * @author: 潘本乐（Belep）
 * @create: 2021-09-29 14:30
 **/
@Data
public class ShipmentReceiveInfoReqVo implements Serializable {
    /**
     * province : 重庆市
     * city : 重庆城区
     * area : 江北区
     * town :
     * address_detail : 观音桥/红旗河沟洋河三村63-2-2
     * receiver_eamil :
     * receiver_compay :
     * receiver_card_no : 500105198709291223
     * receiver_card_type : 1
     * receiver_zip_code : 000000
     * receiver_city_code :
     * receive_phone : 18623540929
     * receive_name : 廖倩
     */

    @JsonProperty("province")
    private String province;
    @JsonProperty("city")
    private String city;
    @JsonProperty("area")
    private String area;
    @JsonProperty("town")
    private String town;
    @JsonProperty("address_detail")
    private String addressDetail;
    @JsonProperty("receiver_eamil")
    private String receiverEamil;
    @JsonProperty("receiver_compay")
    private String receiverCompay;
    @JsonProperty("receiver_card_no")
    private String receiverCardNo;
    @JsonProperty("receiver_card_type")
    private String receiverCardType;
    @JsonProperty("receiver_zip_code")
    private String receiverZipCode;
    @JsonProperty("receiver_city_code")
    private String receiverCityCode;
    @JsonProperty("receive_phone")
    private String receivePhone;
    @JsonProperty("receive_name")
    private String receiveName;
}
