package com.danding.cds.declare.sdk.utils;

import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson.JSON;
import com.danding.cds.common.utils.ExceptionJoinUtil;
import com.danding.cds.common.utils.HttpRequestUtil;
import com.danding.cds.declare.ceb.internal.swxa.SwxaSignUtil;
import com.danding.cds.declare.sdk.bean.vo.IcCardSignResVo;
import com.danding.cds.declare.sdk.clear.zhejiang.encry.CebSignInfo;
import com.danding.cds.declare.sdk.pdd.PddSignUtils;
import com.danding.cds.declare.sdk.pdd.PddXmlUtils;
import com.danding.cds.declare.sdk.swxa.SwxaSignSm2Util;
import com.github.kevinsawicki.http.HttpRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class CebSignUtil {

    public static String signXml(CebSignInfo cebSignInfo, String origXml) throws Exception {
        try {
            return signXml(cebSignInfo, origXml, false);
        } catch (Exception ex) {
            log.error("加密机加签异常 -cebCode={}, domain={}, signWay={}, message={},", cebSignInfo.getCebCode(), cebSignInfo.getDomain(), cebSignInfo.getSignWay(), ex.getMessage(), ex);
            throw ex;
        }
    }

    /**
     * @param cebSignInfo
     * @param origXml
     * @param isPddOfficial 用于区分是否为官方代报
     * @return
     * @throws Exception
     */
    public static String signXml(CebSignInfo cebSignInfo, String origXml, Boolean isPddOfficial) throws Exception {
        if (cebSignInfo.getSignWay() == 1) {
            byte[] bs = origXml.getBytes(StandardCharsets.UTF_8.name());
            return CebSignUtil.httpXml(
                    cebSignInfo.getDomain(),
                    cebSignInfo.getDxpId(),
                    new String(bs, StandardCharsets.UTF_8.name()));
        } else if (cebSignInfo.getSignWay() == 3) {
            // 获取摘要数据
            String dataDigest = null;
            if (!isPddOfficial) {
                dataDigest = PddSignUtils.getDigestValue(origXml.getBytes(StandardCharsets.UTF_8));
            }
            // 组装待签名数据
            boolean isSm2 = SwxaSignSm2Util.isSm2(cebSignInfo.getSignFile());
            String dataWaitToSign;
            if (isSm2) {
                dataWaitToSign = PddXmlUtils.createDataToSignSm2(dataDigest);
            } else {
                dataWaitToSign = PddXmlUtils.createDataToSign(dataDigest);
            }
            String dataSignature = null;
            byte[] bs = dataWaitToSign.getBytes(StandardCharsets.UTF_8.name());
            if (!isPddOfficial) {
                dataSignature = CebSignUtil.httpRsa(
                        cebSignInfo.getDomain(),
                        cebSignInfo.getDxpId(),
                        new String(bs, StandardCharsets.UTF_8.name()));
            }
            // Step 5. 海关分发加密机证书的编号
            String cryptoMachineCertificateNo = cebSignInfo.getCertNo();
            String x509Certificate = Base64.encodeBase64String(cebSignInfo.getX509cert().getEncoded());
            // Step 6. 组装出来有签名的数据
            String signatureStr = PddXmlUtils.creatSignatureNode(dataWaitToSign, dataSignature, cryptoMachineCertificateNo, x509Certificate);
            // Step 7. 组装待推送给海关的报文
            String finalXmlWaitPushToCustoms = PddXmlUtils.appendSignature(origXml, signatureStr);
            return finalXmlWaitPushToCustoms;
        } else if (cebSignInfo.getSignWay() == 4) {
            return icCardSign(cebSignInfo.getDomain(), cebSignInfo.getCebCode(), origXml);
        } else if (cebSignInfo.getSignWay() == 5) {
            String domain = cebSignInfo.getDomain();
            //拼装SignedInfo
            boolean isSm2 = SwxaSignSm2Util.isSm2(cebSignInfo.getSignFile());
            String dataDigest;
            //待加签数据
            String dataWaitToSign;
            if (isSm2) {
                //生成sm2摘要
                dataDigest = SwxaSignSm2Util.getDigestValue(origXml.getBytes(StandardCharsets.UTF_8));
                dataWaitToSign = PddXmlUtils.createDataToSignSm2(dataDigest);
            } else {
                //sha1摘要
                dataDigest = PddSignUtils.getDigestValue(origXml.getBytes(StandardCharsets.UTF_8));
                dataWaitToSign = PddXmlUtils.createDataToSign(dataDigest);
            }
            //换取签名
            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("dataDigest", dataWaitToSign);
            requestBody.put("cebCode", cebSignInfo.getCebCode());
            HttpRequest httpRequest = HttpRequestUtil.post(domain, requestBody);
            String signatureStr = "";
            if (httpRequest.ok()) {
                signatureStr = httpRequest.body();
            }
//            byte[] bs = origXml.getBytes(StandardCharsets.UTF_8);
//            String signXml = SwxaSignSm2Util.creatXml(new String(bs, StandardCharsets.UTF_8.name()), signatureStr);
            return PddXmlUtils.appendSignature(origXml, signatureStr);
        } else {
            byte[] bs = origXml.getBytes(StandardCharsets.UTF_8.name());
            byte[] signed = SwxaSignUtil.signXml(bs, cebSignInfo.getX509cert(), cebSignInfo.getIndex());
            String singedXmlStr = new String(signed, StandardCharsets.UTF_8.name());
            return singedXmlStr;
        }
    }

    public static String signRsa(CebSignInfo cebSignInfo, String origStr) throws UnsupportedEncodingException {
        byte[] bs = origStr.getBytes(StandardCharsets.UTF_8.name());
        if (cebSignInfo.getSignWay() == 1) {
            return CebSignUtil.httpRsa(
                    cebSignInfo.getDomain(),
                    cebSignInfo.getDxpId(),
                    new String(bs, StandardCharsets.UTF_8.name()));
        } else if (cebSignInfo.getSignWay() == 5) {
            return icCardDataSign(cebSignInfo.getDomain(), origStr);
        } else {
            String signed = SwxaSignUtil.rsaSign(cebSignInfo.getIndex(), origStr);
            return signed;
        }
    }

    public static String icCardDataSign(String url, String data) {

        try {
            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("data", data);
            HttpRequest httpRequest = HttpRequestUtil.postFormUrlencoded(url, requestBody);
            String body = httpRequest.body();
            if (httpRequest.ok()) {
                IcCardSignResVo icCardSignResVo = JSON.parseObject(body, IcCardSignResVo.class);
                if (icCardSignResVo.getSuccess()) {
                    return icCardSignResVo.getMsg();
                }
            }
            throw new RuntimeException("Ic卡加签返回数据异常 [加签方式5]:" + body);
        } catch (Exception ex) {
            log.warn("Ic卡加签异常 [ 加签方式5 ]：{}", ex.getMessage(), ex);
            throw new RuntimeException("Ic卡加签调用异常 [加签方式5]" + ex.getMessage());
        }
    }

    public static String icCardSign(String url, String customsCode, String data) {

        try {
            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("xml", data);
            HttpRequest httpRequest = HttpRequestUtil.postFormUrlencoded(url, requestBody);
            String body = httpRequest.body();
            if (httpRequest.ok()) {
                IcCardSignResVo icCardSignResVo = JSON.parseObject(body, IcCardSignResVo.class);
                if (icCardSignResVo.getSuccess()) {
                    return icCardSignResVo.getMsg();
                }
            }
            throw new RuntimeException("Ic卡加签返回数据异常 :" + body);
        } catch (Exception ex) {
            log.warn("Ic卡加签异常 ：{}", ex.getMessage(), ex);
            throw new RuntimeException("Ic卡加签调用异常" + ex.getMessage());
        }
    }


    public static String httpXml(String url, String dxpId, String origStr) {
        String traceId = UUID.fastUUID().toString(true);
        Map<String, String> params = new HashMap<>();
        params.put("dxpId", dxpId);
        params.put("traceId", traceId);
        params.put("origStr", origStr);
        try {
            HttpRequest httpRequest = HttpRequestUtil.post(url + "/xmlSign", params);
            String body = httpRequest.body();
            if (httpRequest.ok()) {
                return body;
            } else {
                log.error("加密机加签异常 - [追踪ID]: {} ;[异常信息]：{};[url]：{}/xmlSign ;[dxpId]：{},[加签数据]：{}",
                        traceId, ExceptionJoinUtil.removeCrlf(body), url, dxpId, ExceptionJoinUtil.removeCrlf(origStr));
                return null;
            }
        } catch (Exception e) {
            log.error("加密机加签异常 - [追踪ID]: {} ;[异常信息]:{};[url]：{}/xmlSign ;[dxpId]：{},[加签数据]：{}",
                    traceId, e.getMessage(), url, dxpId, ExceptionJoinUtil.removeCrlf(origStr), e);
            return null;
        }
    }


    public static String httpRsa(String url, String dxpId, String origStr) {
        String traceId = UUID.fastUUID().toString(true);
        Map<String, String> params = new HashMap<>();
        params.put("dxpId", dxpId);
        params.put("traceId", traceId);
        params.put("origStr", origStr);
        try {
            HttpRequest httpRequest = HttpRequestUtil.post(url + "/rsaSign", params);
            String body = httpRequest.body();
            if (httpRequest.ok()) {
                return body;
            } else {
                log.error("加密机加签异常 - [追踪ID]: {} ;[异常信息]：{};[url]：{}/rsaSign ;[dxpId]：{},[加签数据]：{}",
                        traceId, ExceptionJoinUtil.removeCrlf(body), url, dxpId, ExceptionJoinUtil.removeCrlf(origStr));
                return null;
            }
        } catch (HttpRequest.HttpRequestException e) {
            log.error("加密机加签异常 - [追踪ID]: {} ;[异常信息]:{};[url]：{}/rsaSign ;[dxpId]：{},[加签数据]：{}",
                    traceId, e.getMessage(), url, dxpId, ExceptionJoinUtil.removeCrlf(origStr), e);
            return null;
        }
    }

    public static String httpSm2Digest(String url, String dxpId, String origStr) {
        String traceId = UUID.fastUUID().toString(true);
        Map<String, String> params = new HashMap<>();
        params.put("dxpId", dxpId);
        params.put("traceId", traceId);
        params.put("origStr", origStr);
        try {
            HttpRequest httpRequest = HttpRequestUtil.post(url + "/rsaSign", params);
            String body = httpRequest.body();
            if (httpRequest.ok()) {
                return body;
            } else {
                log.error("加密机加签异常 - [追踪ID]: {} ;[异常信息]：{};[url]：{}/rsaSign ;[dxpId]：{},[加签数据]：{}",
                        traceId, ExceptionJoinUtil.removeCrlf(body), url, dxpId, ExceptionJoinUtil.removeCrlf(origStr));
                return null;
            }
        } catch (HttpRequest.HttpRequestException e) {
            log.error("加密机加签异常 - [追踪ID]: {} ;[异常信息]:{};[url]：{}/rsaSign ;[dxpId]：{},[加签数据]：{}",
                    traceId, e.getMessage(), url, dxpId, ExceptionJoinUtil.removeCrlf(origStr), e);
            return null;
        }
    }

}
