package com.danding.cds.declare.sdk.ie;

import com.danding.cds.declare.sdk.config.CustomsSpecialToken;
import com.danding.cds.declare.sdk.constant.CustomsDistrictCons;
import com.danding.cds.declare.sdk.model.checkList.CheckListPassPort;
import com.danding.cds.declare.sdk.model.checkList.CustomsChecklistOrderInfo;
import com.danding.cds.declare.sdk.model.checkList.CustomsEndorsementOrderInfo;
import com.danding.cds.declare.sdk.model.checkList.Sas121MessageRequest;
import com.danding.cds.declare.zjspecial.domain.sas121.Package;
import com.danding.cds.declare.zjspecial.domain.sas121.*;
import com.danding.cds.declare.zjspecial.internal.enums.CustomsMessageType;
import com.danding.cds.declare.zjspecial.internal.utils.SpecialUtil;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

public class ChecklistBuilder implements Serializable {

    private CustomsSpecialToken target;

    public Signature build(Sas121MessageRequest req, CustomsSpecialToken specialToken){
        this.target = specialToken;
        Signature signature = new Signature();
        signature.setSignedInfo(buildSignedInfo());
        signature.setSignatureValue("");
        signature.setKeyInfo(buildKeyInfo());
        signature.setObject(buildObject(req));
        return signature;
    }

    private KeyInfo buildKeyInfo() {
        KeyInfo keyInfo = new KeyInfo();
        keyInfo.setKeyName("aa");
        return keyInfo;
    }

    private SignedInfo buildSignedInfo() {
        SignedInfo signedInfo = new SignedInfo();
        return signedInfo;
    }

    private Signature.Object buildObject(Sas121MessageRequest req) {
        Signature.Object object = new Signature.Object();
        object.setPackage(buildPackage(req));
        return object;
    }

    private Package buildPackage(Sas121MessageRequest req) {
        Package _package = new Package();
        _package.setEnvelopInfo(buildEnvelopInfo(req));
        _package.setDataInfo(buildDataInfo(req));
        return _package;
    }

    private DataInfo buildDataInfo(Sas121MessageRequest req) {
        DataInfo dataInfo = new DataInfo();
        dataInfo.setBussinessData(buildBussinessData(req));
        return dataInfo;
    }

    private EnvelopInfo buildEnvelopInfo(Sas121MessageRequest req) {
        EnvelopInfo envelopInfo = new EnvelopInfo();
        envelopInfo.setBusinessId(req.getCustomsChecklistOrderInfo().getChecklistOrderNo());
        envelopInfo.setFileName(req.getCustomsChecklistOrderInfo().getChecklistOrderNo());
        envelopInfo.setIcCard(target.getIcCard());//ic卡号
        envelopInfo.setMessageId(UUID.randomUUID().toString());
        envelopInfo.setMessageType(CustomsMessageType.SAS121.name());
        envelopInfo.setReceiverId("DXPEDCSAS0000001");
        envelopInfo.setSenderId(target.getSenderId());
        envelopInfo.setSendTime(SpecialUtil.dateToXmlDate(new Date()));
        envelopInfo.setVersion("1.0");
        return envelopInfo;
    }

    private DataInfo.BussinessData buildBussinessData(Sas121MessageRequest req) {
        DataInfo.BussinessData bussinessData = new DataInfo.BussinessData();
        bussinessData.setDelcareFlag(1);
        bussinessData.setPassPortMessage(buildPassPortMessge(req));
        // bussinessData.setNemsAcmpRLMessage();
        return bussinessData;
    }

    private PassPortMessage buildPassPortMessge(Sas121MessageRequest req) {
        PassPortMessage passPortMessage = new PassPortMessage();
        passPortMessage.setPassportHead(buildPassportHead(req));
        if (StringUtils.isNotBlank(req.getCustomsChecklistOrderInfo().getBindTypeCd())) {
            //只有核放单类型为卡口登记货物或者绑定类型为一票多车时才需要录入表体数据
            if (req.getCustomsChecklistOrderInfo().getBindTypeCd().equals("3")) {
                passPortMessage.setPassportList(buildPassPortList(req));
            }
            //只有绑定类型为一车多票的才可以录入关联单证
            //不是空车核放都录入关联单证
            if (!"CLOSE".equalsIgnoreCase(req.getDeclareType()) && !"6".equals(req.getCustomsChecklistOrderInfo().getPassPortTypeCd())) {
                passPortMessage.setPassportAcmp(buildPassprotAcmp(req));
            }
        }
        passPortMessage.setOperCusRegCode(target.getCustomsCode());
        return passPortMessage;
    }


    private PassPortHead buildPassportHead(Sas121MessageRequest req) {
        CustomsChecklistOrderInfo checklistOrderInfo = req.getCustomsChecklistOrderInfo();
        PassPortHead passPortHead = new PassPortHead();
        if (StringUtils.isNotBlank(checklistOrderInfo.getPreChecklistOrderNo())) {
            passPortHead.setSeqNo(checklistOrderInfo.getPreChecklistOrderNo());
        }
        passPortHead.setMasterCuscd("2924");
        if ("L2923B21A004".equals(target.getCustomsBookCode())) {//义乌优诚
            passPortHead.setMasterCuscd("2923");
        } else if (CustomsDistrictCons.YW_2925_CUSTOMS_DISTRICT.contains(target.getCustomsBookCode())) {
            passPortHead.setMasterCuscd("2925");
        }
        // 这里获取下配置的海关关区，如果不为空，则直接使用
        String customsAreaCode = req.getCustomsAreaCode();
        if (StringUtils.isNotBlank(customsAreaCode)) {
            passPortHead.setMasterCuscd(customsAreaCode);
        }
        if ("CLOSE".equalsIgnoreCase(req.getDeclareType())) {
            passPortHead.setPassportNo(checklistOrderInfo.getRealChecklistOrderNo());
            passPortHead.setDclTypecd("3");
        } else {
            passPortHead.setDclTypecd("1");
        }
        if ("SECOND_IN".equalsIgnoreCase(checklistOrderInfo.getChecklistType())) {
            passPortHead.setIoTypecd("I");//进区：I，出区：E
        } else {
            passPortHead.setIoTypecd("E");//进区：I，出区：E
        }
        if (Objects.nonNull(checklistOrderInfo.getRemark())) {
            passPortHead.setRmk(checklistOrderInfo.getRemark());
        }
        passPortHead.setAreainEtpsNm(target.getCustomsName()); //TODO  配置暂时写死，待修改
        passPortHead.setAreainEtpsno(target.getCustomsCode());
        passPortHead.setAreainEtpsSccd(target.getSocialCreditCode());
        passPortHead.setAreainOriactNo(target.getCustomsBookCode());
        //IC卡
        if (!Objects.isNull(checklistOrderInfo.getVehicleIcNo())) {
            passPortHead.setVehicleIcNo(checklistOrderInfo.getVehicleIcNo());
        }
        passPortHead.setVehicleNo(checklistOrderInfo.getLicensePlate());
        passPortHead.setVehicleWt(checklistOrderInfo.getVehicleWeight());
        passPortHead.setVehicleFrameNo(checklistOrderInfo.getVehicleFrameNo());
        passPortHead.setVehicleFrameWt(checklistOrderInfo.getVehicleFrameWeight());
        if ("6".equalsIgnoreCase(checklistOrderInfo.getPassPortTypeCd())) {//空车进出区
            passPortHead.setPassportTypecd("6");
            passPortHead.setTotalGrossWt("0");
            passPortHead.setTotalNetWt("0");
            BigDecimal totalWt = new BigDecimal(checklistOrderInfo.getVehicleWeight()).add(new BigDecimal(checklistOrderInfo.getVehicleFrameWeight()));
            passPortHead.setTotalWt(totalWt.toString());
        } else {
            //绑定类型
            //1：一车多票（一车必须将多票货物全部拉完），2：一票一车（一车必须将一票货物全部拉完），3：一票多车（一票货物可以分多车拉）
            //卡口登记货物、空车进出区类型的核放单不可填写，其他类型为必填。
            passPortHead.setBindTypecd(checklistOrderInfo.getBindTypeCd());
            if (Objects.equals(checklistOrderInfo.getBindTypeCd(), "1")) {//一车多票
                passPortHead.setRltNo(req.getCustomsEndorsementOrderInfo().stream()
                        .map(CustomsEndorsementOrderInfo::getRealEndorsementOrderNo)
                        .collect(Collectors.joining("\\")));
            } else if (Objects.equals(checklistOrderInfo.getBindTypeCd(), "2") || Objects.equals(checklistOrderInfo.getBindTypeCd(), "3")) {
                passPortHead.setRltNo(req.getCustomsEndorsementOrderInfo().get(0).getRealEndorsementOrderNo());
            }
            passPortHead.setRltTbTypecd(checklistOrderInfo.getRltTbTypeCd());
            passPortHead.setPassportTypecd(checklistOrderInfo.getPassPortTypeCd());
            BigDecimal totalGrossWt = Objects.nonNull(checklistOrderInfo.getTotalGrossWt()) ?
                    new BigDecimal(checklistOrderInfo.getTotalGrossWt()) : BigDecimal.ZERO;
            totalGrossWt = totalGrossWt.divide(new BigDecimal("1"), 2, RoundingMode.HALF_UP);
            passPortHead.setTotalGrossWt(String.valueOf(totalGrossWt));
            BigDecimal totalNetWt = Objects.nonNull(checklistOrderInfo.getTotalNetWt()) ?
                    new BigDecimal(checklistOrderInfo.getTotalNetWt()) : BigDecimal.ZERO;
            totalNetWt = totalNetWt.divide(new BigDecimal("1"), 2, RoundingMode.HALF_UP);
            passPortHead.setTotalNetWt(String.valueOf(totalNetWt));
            BigDecimal vehicleWeight = Objects.nonNull(checklistOrderInfo.getVehicleWeight()) ?
                    new BigDecimal(checklistOrderInfo.getVehicleWeight()) : BigDecimal.ZERO;
            BigDecimal vehicleFrameWeight = Objects.nonNull(checklistOrderInfo.getVehicleFrameWeight()) ?
                    new BigDecimal(checklistOrderInfo.getVehicleFrameWeight()) : BigDecimal.ZERO;
            BigDecimal totalWeight = totalGrossWt.add(vehicleWeight).add(vehicleFrameWeight);
            totalWeight = totalWeight.divide(new BigDecimal("1"), 2, RoundingMode.HALF_UP);
            passPortHead.setTotalWt(totalWeight.toString());
        }
        passPortHead.setDclErConc(checklistOrderInfo.getApplicant());
        passPortHead.setDclTime(new SimpleDateFormat("yyyyMMdd").format(new Date()));
        passPortHead.setDclEtpsno(target.getCustomsCode());
        passPortHead.setDclEtpsNm(target.getCustomsName());
        passPortHead.setDclEtpsSccd(target.getSocialCreditCode());
        passPortHead.setInputCode(target.getCustomsCode());
        passPortHead.setInputName(target.getCustomsName());
        passPortHead.setInputSccd(target.getSocialCreditCode());
        passPortHead.setEtpsPreentNo(req.getCustomsChecklistOrderInfo().getChecklistOrderNo());//企业内部编号
        return passPortHead;
    }

    /**
     * 不是空车核放才有
     *
     * @param req
     * @return
     */
    private List<PassPortAcmp> buildPassprotAcmp(Sas121MessageRequest req) {
        return req.getCustomsEndorsementOrderInfo().stream().map(customsEndorsementOrderInfo -> {
            PassPortAcmp passPortAcmp = new PassPortAcmp();
            passPortAcmp.setRtlBillTypecd("1");
            passPortAcmp.setRtlBillNo(customsEndorsementOrderInfo.getRealEndorsementOrderNo());
            return passPortAcmp;
        }).collect(Collectors.toList());
    }

    private List<PassPortList> buildPassPortList(Sas121MessageRequest req) {
        int i = 1;
        List<PassPortList> passPortList = new ArrayList<>();
        for (CheckListPassPort checkListPassPort : req.getCheckListPassPortList()) {
            PassPortList passPort = new PassPortList();
            passPort.setPassportSeqNo(String.valueOf(i));
            //料号
            passPort.setGdsMtno(checkListPassPort.getGoodsNo());
            //商品编码
            passPort.setGdecd(checkListPassPort.getHsCode());
            //商品名称
            passPort.setGdsNm(checkListPassPort.getGoodsName());
            //关联商品序号
            passPort.setRltGdsSeqno(checkListPassPort.getRelateGoodsSeqNo());
            //申报单位代码
            passPort.setDclUnitcd(checkListPassPort.getDeclareUnit());
            //申报数量
            passPort.setDclQty(checkListPassPort.getDeclareQty());
            passPortList.add(passPort);
            i++;
        }
        return passPortList;
    }

}
