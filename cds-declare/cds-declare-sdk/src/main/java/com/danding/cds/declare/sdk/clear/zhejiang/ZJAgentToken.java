package com.danding.cds.declare.sdk.clear.zhejiang;

import com.danding.cds.declare.sdk.clear.zhejiang.encry.CebSignInfo;
import com.danding.cds.declare.sdk.clear.zhejiang.encry.KeyInfo;
import com.danding.cds.declare.sdk.clear.zhejiang.encry.SdEportKeyInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 浙江关区 申报企业令牌信息
 */
@Data
public class ZJAgentToken implements Serializable {

    private String mode = "ceb";

    /**
     * 企业总署备案编码
     */
    private String code;

    /**
     * 企业总署备案名称
     */
    private String name;

    /**
     * 域名，用于一个应用接收多个企业的回执信息时，区分企业时使用
     */
    private String domain;

    /**
     * 口岸密钥信息
     */
    private KeyInfo zjPortKeyInfo;

    /**
     * 山东口岸信息
     */
    private SdEportKeyInfo sdEportKeyInfo;

    /**
     * 总署证书信息
     */
    private CebSignInfo cebSignInfo;

    private String appId;

    private String appKey;

    /**
     * 拓展配置多DXP情况
     */
    private List<CebSignInfo> cebSignInfoList;
}
