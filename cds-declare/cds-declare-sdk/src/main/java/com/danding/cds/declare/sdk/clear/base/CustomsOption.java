package com.danding.cds.declare.sdk.clear.base;

import com.danding.cds.declare.sdk.clear.base.callback.CallbackResult;
import com.danding.cds.declare.sdk.clear.base.callback.module.*;
import com.danding.cds.declare.sdk.clear.base.result.*;
import com.danding.cds.declare.sdk.model.cancel.WarpCancelOrderInfo;
import com.danding.cds.declare.sdk.model.check.WrapDataPayExInfo;
import com.danding.cds.declare.sdk.model.inventory.WrapInventoryOrderInfo;
import com.danding.cds.declare.sdk.model.order.WrapOrderDeclareInfo;
import com.danding.cds.declare.sdk.model.payment.WrapPaymentInfo;
import com.danding.cds.declare.sdk.model.refund.WarpRefundOrderInfo;
import com.danding.cds.declare.sdk.model.shipment.WrapShipmentInfo;

import java.util.List;

public interface CustomsOption {

    void dataCheckSubmit(WrapDataPayExInfo infoDTO);
    /**
     * 订单申报
     * @param declareInfo
     */
    OrderDeclareResult orderDeclare(WrapOrderDeclareInfo declareInfo);

    /**
     * 清单申报
     * @param info
     */
    InventoryDeclareResult inventoryDeclare(WrapInventoryOrderInfo info);

    /**
     * 支付单申报
     * @param info
     */
    void paymentDeclare(WrapPaymentInfo info);


    /**
     * 运单申报
     * @param info
     * @return
     */
    ShipmentDeclareResult shipmentDeclare(WrapShipmentInfo info);

    /**
     * 清单取消
     * @param info
     */
    InventoryCancelResult inventoryCancel(WarpCancelOrderInfo info);

    /**
     * 清单退货
     * @param info
     */
    InventoryRefundResult inventoryRefund(WarpRefundOrderInfo info);

    /**
     * 解析回执的HTTP请求 获得回执类型和明文
     * @param request
     * @return
     */
    CallbackResult parserCallback(Object request);

    /**
     * 解析订单回执
     * @param callbackResult
     * @return
     */
    OrderCallback handelOrderMsg(CallbackResult callbackResult);

    /**
     * 税单回执
     * @param callbackResult
     * @return
     */
    List<TaxResult> handelTaxMsg(CallbackResult callbackResult);

    /**
     * 税单状态回执
     * @param callbackResult
     * @return
     */
    List<TaxStatus> handelTaxStatusMsg(CallbackResult callbackResult);

    /**
     * 清单回执
     * @param callbackResult
     * @return
     */
    InventoryCallback handelInventoryMsg(CallbackResult callbackResult);

    /**
     * 运单回执
     * @param callbackResult
     * @return
     */
    ShipmentCallback handleShipmentMsg(CallbackResult callbackResult);
    /**
     * 撤单回执
     * @param callbackResult
     * @return
     */
    List<InventoryCancel> handelInventoryCancel(CallbackResult callbackResult);

    /**
     * 退货回执
     * @param callbackResult
     * @return
     */
    List<InventoryRefund> handelInventoryRefund(CallbackResult callbackResult);

    DeliverResult handelDeliverMsg(CallbackResult callbackResult);
}
