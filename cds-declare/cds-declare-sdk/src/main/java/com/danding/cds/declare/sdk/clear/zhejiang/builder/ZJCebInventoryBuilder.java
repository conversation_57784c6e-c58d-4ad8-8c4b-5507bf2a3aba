package com.danding.cds.declare.sdk.clear.zhejiang.builder;

import com.danding.cds.declare.ceb.domain.base.BaseTransfer;
import com.danding.cds.declare.ceb.domain.ceb621.CEB621Message;
import com.danding.cds.declare.ceb.domain.ceb621.Inventory;
import com.danding.cds.declare.ceb.domain.ceb621.InventoryHead;
import com.danding.cds.declare.ceb.domain.ceb621.InventoryList;
import com.danding.cds.declare.ceb.internal.enums.CebDeclareType;
import com.danding.cds.declare.ceb.internal.utils.CebDateUtil;
import com.danding.cds.declare.sdk.constant.CustomsDistrictCons;
import com.danding.cds.declare.sdk.enums.PortCodeEnum;
import com.danding.cds.declare.sdk.enums.PortTrafModeEnum;
import com.danding.cds.declare.sdk.model.inventory.CustomsInventoryItemInfo;
import com.danding.cds.declare.sdk.model.inventory.WrapInventoryOrderInfo;
import com.danding.cds.declare.sdk.utils.DateUtil;
import com.danding.cds.declare.sdk.utils.NumberUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

public class ZJCebInventoryBuilder implements Serializable {
    private static final String portNid = "HANGZHOU";

    private WrapInventoryOrderInfo info;

    private String dxpId;

    public ZJCebInventoryBuilder(WrapInventoryOrderInfo info, String dxpId) {
        this.info = info;
        this.dxpId = dxpId;
    }

    public CEB621Message build() {
        return buildOrderMessage();
    }

    /**
     * 构造CEB621Message
     *
     * @param
     * @return
     * @throws Exception
     */
    private CEB621Message buildOrderMessage() {
        CEB621Message ceb621Message = new CEB621Message();
        Inventory inventory = buildInventory();
        List<Inventory> inventories = Lists.newArrayList(inventory);
        BaseTransfer baseTransfer = buildBaseTransfer();
        ceb621Message.setGuid(inventory.inventoryHead.guid);
        ceb621Message.inventory = inventories;
        ceb621Message.baseTransfer = baseTransfer;
        return ceb621Message;
    }

    /**
     * 构造inventory
     *
     * @param
     * @return
     * @throws Exception
     */
    private Inventory buildInventory() {
        Inventory inventory = new Inventory();
        InventoryHead inventoryHead = buildInventoryHead();
        List<InventoryList> inventoryLists = buildInventoryList(inventoryHead);
        inventory.setInventoryHead(inventoryHead);
        inventory.setInventoryList(inventoryLists);
        return inventory;
    }

    /**
     * 构造明细
     *
     * @param inventoryHead
     * @return
     */
    private List<InventoryList> buildInventoryList(InventoryHead inventoryHead) {
        BigDecimal grossWeight = new BigDecimal("0");
        BigDecimal netWeight = new BigDecimal("0");
        List<InventoryList> inventoryLists = Lists.newArrayList();

        int num = 1;
        for (CustomsInventoryItemInfo itemDO : info.getListCustomsInventoryItemInfo()) {

            InventoryList inventoryList = new InventoryList();

            inventoryList.setGnum(num + "");
            String itemRecordNo = itemDO.getItemRecordNo();
            if (itemRecordNo == null) itemRecordNo = "";
            //账册备案料号 保税进口必填
            inventoryList.setItemRecordNo(itemRecordNo.trim());
            inventoryList.setItemNo(itemDO.getItemNo());
            inventoryList.setItemName(itemDO.getItemName());
            String hsCode = itemDO.getHsCode();
            if (hsCode == null) hsCode = "";
            inventoryList.setGcode(hsCode.trim());
            inventoryList.setGname(itemDO.getItemName());
            String gmodle = itemDO.getGmodle();
            if (gmodle == null) gmodle = "";
            inventoryList.setGmodel(gmodle.trim());
            //原产国

            String country = itemDO.getCountry();
            if (country == null) country = "";
            inventoryList.setCountry(country.trim());
            inventoryList.setCurrency("142");
            inventoryList.setQty("" + itemDO.getCount());
            //法定数量
            inventoryList.setQty1(NumberUtil.parseForDeclareValue(itemDO.getFirstCount(), itemDO.getCount()));
            String unit = itemDO.getUnit();
            if (unit == null) unit = "";
            inventoryList.setUnit(unit.trim());
            String unit1 = itemDO.getUnit1();
            if (unit1 == null) unit1 = "";
            inventoryList.setUnit1(unit1.trim());

            if (StringUtils.isNotBlank(itemDO.getUnit2())) {
                //19 <secondUnit>   第二计量单位  NVARCHAR2(15)   否   填写商品HS编码对应的第二单位
                inventoryList.setUnit2(itemDO.getUnit2().trim());
                //20 <secondCount>  第二数量    NUMBER(14,4)    否   根据第二单位填写对应数量
                inventoryList.setQty2(NumberUtil.parseForDeclareValue(itemDO.getSecondCount().trim(), itemDO.getCount()));
            }
            if (itemDO.getUnitPrice() != null)
                inventoryList.setPrice(itemDO.getUnitPrice().toString());
            else
                inventoryList.setPrice("0.00");
            if (itemDO.getUnitPrice() != null && itemDO.getCount() != null) {
                inventoryList.setTotalPrice((new BigDecimal(itemDO.getCount()).multiply(itemDO.getUnitPrice())).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            } else
                inventoryList.setTotalPrice("0.00");
            if (StringUtils.isNotBlank(itemDO.getBarCode())) {
                inventoryList.setBarCode(itemDO.getBarCode());
            }
            num++;
            inventoryLists.add(inventoryList);
            if (null != itemDO.getWeight()) {
                grossWeight = grossWeight.add(BigDecimal.valueOf(itemDO.getWeight() * itemDO.getCount()));
                netWeight = netWeight.add(BigDecimal.valueOf(Float.valueOf(itemDO.getFirstCount()) * itemDO.getCount()));
            }
        }
        inventoryHead.setNetWeight(netWeight.setScale(4, BigDecimal.ROUND_HALF_UP).toString());
        inventoryHead.setGrossWeight(grossWeight.setScale(4, BigDecimal.ROUND_HALF_UP).toString());
        return inventoryLists;
    }

    private static final String TYPE_EDIT = "EDIT";

    /**
     * 构造头信息
     * F
     *
     * @param
     * @return
     * @throws Exception
     */
    private InventoryHead buildInventoryHead() {
        //获取动态配置信息
        InventoryHead inventoryHead = new InventoryHead();
        //系统唯一序号、报送类型、报送时间、业务状态
        inventoryHead.setGuid(UUID.randomUUID().toString().toUpperCase());
        inventoryHead.setAppType(CebDeclareType.CREATE.getType());
        // 变更申报
        if (TYPE_EDIT.equals(info.getType())) {
            inventoryHead.setAppType(CebDeclareType.EDIT.getType());
            inventoryHead.setInvtNo(info.getCustomsInventoryDto().getInventoryNo());
            inventoryHead.setPreNo(info.getCustomsInventoryDto().getPreNo());
        }
        inventoryHead.setAppTime(CebDateUtil.format(new Date(), CebDateUtil.defaultCebDateStringFormat));
        inventoryHead.setAppStatus("2");
        //订单编号

        inventoryHead.setOrderNo(info.getCustomsInventoryDto().getOrderNo());
        //电商平台代码、电商平台名称、电商企业代码、电商企业名称
        inventoryHead.setEbpCode(info.getEbpCompanyDTO().getCebCode());
        inventoryHead.setEbpName(info.getEbpCompanyDTO().getCebName());
        inventoryHead.setEbcCode(info.getEbcCompanyDTO().getCebCode());
        inventoryHead.setEbcName(info.getEbcCompanyDTO().getCebName());
//        //物流运单编号、物流企业代码、物流企业名称
        inventoryHead.setLogisticsNo(info.getCustomsInventoryDto().getLogisticsNo());
        if (info.isDeclareLogisticsInSystem()) {
            inventoryHead.setLogisticsCode(info.getLogisticDeclareCompany().getCebCode());
            inventoryHead.setLogisticsName(info.getLogisticDeclareCompany().getCebName());
        } else {
            inventoryHead.setLogisticsCode(info.getLogisticsCompanyDTO().getCebCode());
            inventoryHead.setLogisticsName(info.getLogisticsCompanyDTO().getCebName());
        }

        //企业内部标识单证的编号 、担保企业编号
        //inventoryHead.setCopNo(String.valueOf(info.getCustomsInventoryDto().getId()));
        //inventoryHead.setCopNo(CcbServiceUtil.getPreEntryNumber(String.valueOf(info.getCustomsInventoryDto().getId())));
        inventoryHead.setCopNo(info.getCustomsInventoryDto().getSn());
        inventoryHead.setAssureCode(info.getAssureCompanyDTO().getCebCode());
        inventoryHead.setEmsNo(info.getCustomsInventoryDto().getBookNo());

        //进出口标记、申报日期、申报海关代码、口岸海关代码
        inventoryHead.setIeFlag("I");
        inventoryHead.setDeclTime(DateUtil.getCDateString("yyyyMMdd"));
        /**
         * 申报海关代码
         */
        //inventoryHead.setCustomsCode(info.getCustomsInventoryDto().getCustoms());
        inventoryHead.setCustomsCode("2924");
        String portCode = PortCodeEnum.getPortCode(info.getCustomsInventoryDto().getCustoms());
        //inventoryHead.setPortCode(portCode);
        inventoryHead.setPortCode("2924");
        if ("L2923B21A004".equals(info.getCustomsInventoryDto().getBookNo())) {
            inventoryHead.setCustomsCode("2923");
            inventoryHead.setPortCode("2923");
        } else if (CustomsDistrictCons.YW_2925_CUSTOMS_DISTRICT.contains(info.getCustomsInventoryDto().getBookNo())) {
            inventoryHead.setCustomsCode("2925");
            inventoryHead.setPortCode("2925");
        }
        // 这里获取下账册维护的海关关区，如果不为空，则直接使用
        String customsAreaCode = info.getCustomsAreaCode();
        if (StringUtils.isNotBlank(customsAreaCode)) {
            inventoryHead.setCustomsCode(customsAreaCode);
            inventoryHead.setPortCode(customsAreaCode);
        }
        inventoryHead.setIeDate(DateUtil.getTCurrentDate("yyyyMMdd"));

        //订购人信息 固定身份证
        inventoryHead.setBuyerIdType(info.getCustomsInventoryDto().getBuyerIdType());
        inventoryHead.setBuyerIdNumber(info.getCustomsInventoryDto().getBuyerIdNumber());
        inventoryHead.setBuyerTelephone(info.getCustomsInventoryDto().getBuyerTelNumber());
        inventoryHead.setBuyerName(info.getCustomsInventoryDto().getBuyerName());
        inventoryHead.setConsigneeAddress(info.getCustomsInventoryDto().getConsigneeAddress());


        String declareCompanyCode = info.getDeclareCompanyDTO().getCebCode();
        String declareCompanyName = info.getDeclareCompanyDTO().getCebName();
        String internalAreaCompanyNo = info.getInternalAreaCompany().getCebCode();
        String internalAreaCompanyName = info.getInternalAreaCompany().getCebName();
        String customsField = info.getCustomsInventoryDto().getCustomsField();

        //申报企业代码、名称、区内企业代码、区内企业名称
        inventoryHead.setAgentCode(declareCompanyCode);
        inventoryHead.setAgentName(declareCompanyName);
        inventoryHead.setAreaCode(internalAreaCompanyNo);
        // todo 楠岳有多个海关十位编码，C单区内企业编码和担保、申报区分开，版本没有上，所以先兼容写死
        if (Objects.equals("3307680004", inventoryHead.getAreaCode())) {
            inventoryHead.setAreaCode("3307660066");
        }
        inventoryHead.setAreaName(internalAreaCompanyName);

        inventoryHead.setAssureCode(info.getAssureCompanyDTO().getCebCode());
        // 针对同一申报地海关下有多个跨境电子商务的监管场所,需要填写区分
        //inventoryHead.setLoctNo(customsField);
        inventoryHead.setLoctNo("292401");
        if ("L2923B21A004".equals(info.getCustomsInventoryDto().getBookNo())) {
            inventoryHead.setLoctNo("292301");
        } else if (CustomsDistrictCons.YW_2925_CUSTOMS_DISTRICT.contains(info.getCustomsInventoryDto().getBookNo())) {
            inventoryHead.setLoctNo("292501");
        }
        // 这里获取下账册维护的海关关区，如果不为空，则直接使用，后面+"01"是
        if (StringUtils.isNotBlank(customsAreaCode)) {
            inventoryHead.setLoctNo(customsAreaCode + "01");
        }
        //贸易方式 ,直购进口填写“9610”，保税进口填写“1210”。
        inventoryHead.setTradeMode("1210");
        String trafMode = PortTrafModeEnum.getTrafCode(this.portNid);
        //运输方式,
        // inventoryHead.setTrafMode(trafMode);
        inventoryHead.setTrafMode("7");
        //起运国、运费、保费、币种、件数
        //String country  = this.customsInventoryDto.getCountry();
        String country = "142"; // 保税业务起运国都是中国 142
        if (country == null) country = "";
        inventoryHead.setCountry(country);
        if (info.getCustomsInventoryDto().getFreight() != null)
            inventoryHead.setFreight(info.getCustomsInventoryDto().getFreight().toString());
        else
            inventoryHead.setFreight("0");
        inventoryHead.setInsuredFee("0");
        inventoryHead.setCurrency("142");
        inventoryHead.setPackNo("1");
        String note = info.getCustomsInventoryDto().getNote();
        if (Objects.nonNull(note)) {
            note = note.trim();
            if (note.length() > 1000) {
                note = note.substring(0, 1000);
            }
            inventoryHead.setNote(note);
        }
        return inventoryHead;
    }

    /**
     * @return
     */
    private BaseTransfer buildBaseTransfer() {
        BaseTransfer baseTransfer = new BaseTransfer();
        String declareCompanyCode = info.getDeclareCompanyDTO().getCebCode();
        String declareCompanyName = info.getDeclareCompanyDTO().getCebName();
        baseTransfer.setCopCode(declareCompanyCode);
        baseTransfer.setCopName(declareCompanyName);
        baseTransfer.setDxpMode("DXP");
        baseTransfer.setDxpId(dxpId);
        return baseTransfer;
    }
}

