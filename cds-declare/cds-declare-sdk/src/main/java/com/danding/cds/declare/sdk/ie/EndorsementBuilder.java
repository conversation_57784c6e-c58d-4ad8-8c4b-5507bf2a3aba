package com.danding.cds.declare.sdk.ie;

import com.danding.cds.declare.sdk.config.CustomsSpecialToken;
import com.danding.cds.declare.sdk.constant.CustomsDistrictCons;
import com.danding.cds.declare.sdk.model.checkList.Inv101MessageRequest;
import com.danding.cds.declare.sdk.model.checkList.InvtHeadType;
import com.danding.cds.declare.sdk.model.checkList.InvtItemBody;
import com.danding.cds.declare.zjspecial.domain.KeyInfo;
import com.danding.cds.declare.zjspecial.domain.Signature;
import com.danding.cds.declare.zjspecial.domain.SignedInfo;
import com.danding.cds.declare.zjspecial.domain.base.EnvelopInfo;
import com.danding.cds.declare.zjspecial.domain.inv101.*;
import com.danding.cds.declare.zjspecial.internal.enums.CustomsMessageType;
import com.danding.cds.declare.zjspecial.internal.utils.SpecialUtil;
import com.github.pagehelper.util.StringUtil;
import com.google.common.collect.Lists;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;

public class EndorsementBuilder implements Serializable {

    private CustomsSpecialToken target;

    public Signature build(Inv101MessageRequest req, CustomsSpecialToken specialToken) {
        this.target = specialToken;
        Signature signature = new Signature();
        signature.setSignedInfo(buildSignedInfo());
        signature.setSignatureValue("");
        signature.setKeyInfo(buildKeyInfo());
        signature.setObject(buildObject(req));
        return signature;
    }

    private KeyInfo buildKeyInfo() {
        KeyInfo keyInfo = new KeyInfo();
        keyInfo.setKeyName("aa");
        return keyInfo;
    }

    private SignedInfo buildSignedInfo() {
        SignedInfo signedInfo = new SignedInfo();
        return signedInfo;
    }

    private Signature.Object buildObject(Inv101MessageRequest req) {
        Signature.Object object = new Signature.Object();
        object.setPackage(buildPackage(req));
        return object;
    }

    private Inv101Package buildPackage(Inv101MessageRequest req) {
        Inv101Package _package = new Inv101Package();
        _package.setEnvelopInfo(buildEnvelopInfo(req));
        _package.setDataInfo(buildDataInfo(req));
        return _package;
    }

    private DataInfo buildDataInfo(Inv101MessageRequest req) {
        DataInfo dataInfo = new DataInfo();
        dataInfo.setBussinessData(buildBussinessData(req));
        return dataInfo;
    }

    private DataInfo.BussinessData buildBussinessData(Inv101MessageRequest req) {
        DataInfo.BussinessData bussinessData = new DataInfo.BussinessData();
        bussinessData.setDelcareFlag(1);
        bussinessData.setInvtMessage(buildInvtMessage(req));
        return bussinessData;
    }

    private InvtMessage buildInvtMessage(Inv101MessageRequest req) {
        InvtMessage invtMessage = new InvtMessage();
        invtMessage.setInvtHeadType(buildHeadType(req));
        invtMessage.setInvtListType(buildInvtListType(req));
        invtMessage.setInvtCbecBill(buildCbecBill(req));
        invtMessage.setOperCusRegCode(target.getCustomsCode());
        invtMessage.setSysId("Z7");
        if ("L2923B21A004".equals(target.getCustomsBookCode())
                || Objects.equals("L2992B22A001", target.getCustomsBookCode())
                || Objects.equals("L2973B24A002", target.getCustomsBookCode())) {//义乌优诚
            invtMessage.setSysId("Z8");
        }
        return invtMessage;
    }

    private List<NemsInvtCbecBillType> buildCbecBill(Inv101MessageRequest req) {
        List<NemsInvtCbecBillType> nemsInvtCbecBillTypeList = new ArrayList<>();
        for (String invtNo : req.getInvtNos()) {
            NemsInvtCbecBillType nemsInvtCbecBillType = new NemsInvtCbecBillType();
            nemsInvtCbecBillType.setCbecBillNo(invtNo);
            nemsInvtCbecBillType.setSeqNo(!StringUtils.isEmpty(req.getPreEndorsementOrderNo()) ? req.getPreEndorsementOrderNo() : "");
            nemsInvtCbecBillTypeList.add(nemsInvtCbecBillType);
        }
        return nemsInvtCbecBillTypeList;
    }

    private List<NemsInvtListType> buildInvtListType(Inv101MessageRequest req) {
        List<NemsInvtListType> invtListTypes = Lists.newArrayList();
        int i = 1;
        for (InvtItemBody invtItemBody : req.getInvtItemBodyList()) {
            NemsInvtListType nemsInvtListType = new NemsInvtListType();
            nemsInvtListType.setSeqNo(!StringUtils.isEmpty(req.getPreEndorsementOrderNo()) ? req.getPreEndorsementOrderNo() : "");
            nemsInvtListType.setGdsSeqno(String.valueOf(i));
            nemsInvtListType.setPutrecSeqno(invtItemBody.getItemRecordNo());
            nemsInvtListType.setGdsMtno(invtItemBody.getProductId());
            nemsInvtListType.setGdecd(invtItemBody.getGcode());
            nemsInvtListType.setGdsNm(invtItemBody.getGname());
            nemsInvtListType.setGdsSpcfModelDesc(invtItemBody.getGmodel());
            nemsInvtListType.setDclUnitcd(invtItemBody.getUnit());
            nemsInvtListType.setLawfUnitcd(invtItemBody.getUnit1());
            nemsInvtListType.setNatcd(invtItemBody.getCountry());
            nemsInvtListType.setDclUprcAmt(invtItemBody.getPrice());
            DecimalFormat format = new DecimalFormat("0.00");
            nemsInvtListType.setDclTotalAmt(format.format(new BigDecimal(invtItemBody.getTotalPrice())));
            nemsInvtListType.setDclCurrcd(invtItemBody.getCurrency());
            nemsInvtListType.setLawfQty(invtItemBody.getTotalQty1());
            nemsInvtListType.setDclQty(invtItemBody.getQty());
//            if (checkClyMarkcd(null, req.getInvtHeadType().getInvtType(), req.getInvtHeadType().getSupvModecd())
//                    && Objects.nonNull(invtItemBody.getClyMarkcd())) {
            if (Objects.nonNull(invtItemBody.getClyMarkcd())) {
                nemsInvtListType.setClyMarkcd(invtItemBody.getClyMarkcd());
            }
            if (!StringUtils.isEmpty(invtItemBody.getUnit2())) {
                nemsInvtListType.setSecdLawfUnitcd(invtItemBody.getUnit2());
                nemsInvtListType.setSecdLawfQty(invtItemBody.getTotalQty2());
            }
            nemsInvtListType.setLvyrlfModecd("3");
            nemsInvtListType.setDestinationNatcd(invtItemBody.getDestCountry());
            nemsInvtListType.setModfMarkcd("0");
            nemsInvtListType.setParam1(invtItemBody.getParam1());
            invtListTypes.add(nemsInvtListType);
            i++;
        }
        return invtListTypes;
    }

    /**
     * 危化品标志校验
     * 表头备案号以T、H开头或以L开头且第6位字符为‘B’,清单类型不是'简单加工'、监管方式不是‘AAAA’时，必填, 其他情况为空
     *
     * @param bookNo
     * @param invtType
     * @param supvModecd
     * @return
     */
//    private boolean checkClyMarkcd(String bookNo, String invtType, String supvModecd) {
//        String regex = "^(T|H|L....B).*";
//        Pattern pattern = Pattern.compile(regex);
//        Matcher matcher = pattern.matcher(bookNo);
//        boolean matches = matcher.matches();
//        return matches && !invtType.equals("4") && !supvModecd.equals("AAAA");
//    }
    private NemsInvtHeadType buildHeadType(Inv101MessageRequest req) {
        //TODO  配置暂时写死，待修改
        NemsInvtHeadType nemsInvtHeadType = new NemsInvtHeadType();
        if (!StringUtils.isEmpty(req.getPreEndorsementOrderNo())) {
            nemsInvtHeadType.setSeqNo(req.getPreEndorsementOrderNo());
        }
        nemsInvtHeadType.setPutrecNo(target.getCustomsBookCode());
        if (Objects.nonNull(req.getCustomsBookCode())) {
            //为了解决 企业:账册 一对多的情况，账册取核注单数据
            nemsInvtHeadType.setPutrecNo(req.getCustomsBookCode());
        }
//        req.getInvtHeadType().setPutrecNo(nemsInvtHeadType.getPutrecNo());
        nemsInvtHeadType.setEtpsInnerInvtNo(req.getEndorsementOrderNo());//企业内部清单编号
        nemsInvtHeadType.setBizopEtpsNm(target.getCustomsName());
        nemsInvtHeadType.setBizopEtpsno(target.getCustomsCode());
        nemsInvtHeadType.setBizopEtpsSccd(target.getSocialCreditCode());
        nemsInvtHeadType.setRcvgdEtpsNm(target.getCustomsName());
        nemsInvtHeadType.setRcvgdEtpsno(target.getCustomsCode());
        nemsInvtHeadType.setRvsngdEtpsSccd(target.getSocialCreditCode());
        nemsInvtHeadType.setDclEtpsno(target.getCustomsCode());
        nemsInvtHeadType.setDclEtpsNm(target.getCustomsName());
        nemsInvtHeadType.setDclEtpsSccd(target.getSocialCreditCode());
        nemsInvtHeadType.setDclPlcCuscd("2924");
        nemsInvtHeadType.setImpexpPortcd("2924");
        if ("L2923B21A004".equals(target.getCustomsBookCode())) {//义乌优诚
            nemsInvtHeadType.setDclPlcCuscd("2923");
            nemsInvtHeadType.setImpexpPortcd("2923");
        } else if (CustomsDistrictCons.YW_2925_CUSTOMS_DISTRICT.contains(target.getCustomsBookCode())) {
            nemsInvtHeadType.setDclPlcCuscd("2925");
            nemsInvtHeadType.setImpexpPortcd("2925");
        }
        // 这里获取下海关关区，如果不为空，则直接使用
        String customsAreaCode = req.getCustomsAreaCode();
        if (StringUtils.hasText(customsAreaCode)) {
            nemsInvtHeadType.setDclPlcCuscd(customsAreaCode);
            nemsInvtHeadType.setImpexpPortcd(customsAreaCode);
        }
        nemsInvtHeadType.setImpexpMarkcd("E");
        nemsInvtHeadType.setMtpckEndprdMarkcd("I");
        nemsInvtHeadType.setSupvModecd("5000");
        nemsInvtHeadType.setTrspModecd("9");
        nemsInvtHeadType.setDclcusFlag("2");
        nemsInvtHeadType.setInputCode(target.getCustomsCode());
        nemsInvtHeadType.setInputCreditCode(target.getSocialCreditCode());
        nemsInvtHeadType.setInputName(target.getCustomsName());
        nemsInvtHeadType.setIcCardNo(target.getIcCard());
        nemsInvtHeadType.setInputTime(new SimpleDateFormat("yyyyMMdd").format(new Date()));
        nemsInvtHeadType.setStshipTrsarvNatcd("142");
        nemsInvtHeadType.setInvtType("8");
        nemsInvtHeadType.setDclTypecd("1");
        nemsInvtHeadType.setGenDecFlag("2");
        InvtHeadType invtHeadType = req.getInvtHeadType();
        if (Objects.nonNull(invtHeadType)) {
            if (StringUtil.isNotEmpty(invtHeadType.getInvtType())) {
                nemsInvtHeadType.setInvtType(invtHeadType.getInvtType());
            }
            if (StringUtil.isNotEmpty(invtHeadType.getIcCardNo())) {
                nemsInvtHeadType.setIcCardNo(invtHeadType.getIcCardNo());
            }
            if (StringUtil.isNotEmpty(invtHeadType.getSupvModecd())) {
                nemsInvtHeadType.setSupvModecd(invtHeadType.getSupvModecd());
            }
            if (StringUtil.isNotEmpty(invtHeadType.getRemark())) {
                nemsInvtHeadType.setRmk(invtHeadType.getRemark());
            }
//            nemsInvtHeadType.setTrspModecd(Optional.ofNullable(invtHeadType.getTrspModecd()).orElse("9"));
        }
        return nemsInvtHeadType;
    }

    private EnvelopInfo buildEnvelopInfo(Inv101MessageRequest req) {
        EnvelopInfo envelopInfo = new EnvelopInfo();
        String uuid = UUID.randomUUID().toString();
        envelopInfo.setBusinessId("businessId-" + uuid);
        envelopInfo.setFileName("fileName-" + uuid);
        envelopInfo.setIcCard(target.getIcCard());//ic卡号
        envelopInfo.setMessageId("messageId-" + uuid);
        envelopInfo.setMessageType(CustomsMessageType.INV101.name());
        envelopInfo.setReceiverId("DXPEDCSAS0000001");
        envelopInfo.setSenderId(target.getSenderId());
        envelopInfo.setSendTime(SpecialUtil.dateToXmlDate(new Date()));
        envelopInfo.setVersion("1.0");
        return envelopInfo;
    }

}
