package com.danding.cds.declare.sdk.payment.lianDong;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.cds.declare.sdk.CustomsReport;
import com.danding.cds.declare.sdk.CustomsSupport;
import com.danding.cds.declare.sdk.enums.PayCustomsChannel;
import com.danding.cds.declare.sdk.model.payment.CustomsPayDeclareResult;
import com.danding.cds.declare.sdk.model.payment.WrapPaymentInfo;
import com.danding.cds.declare.sdk.payment.base.BaseChannel;
import com.danding.cds.declare.sdk.payment.base.PayException;
import com.danding.cds.declare.sdk.payment.lianDong.model.*;
import com.danding.cds.declare.sdk.utils.RSASignUtils;
import com.danding.component.http.SaasHttpConstant;
import com.danding.core.tenant.SimpleTenantHelper;
import com.github.kevinsawicki.http.HttpRequest;
import com.umf.api.payments.SubOrder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.map.HashedMap;

import java.io.InputStream;
import java.util.*;

/**
 * <AUTHOR>
 * @description 联动国际推送
 * @date 2021/11/25
 */
@Slf4j
public class LianDongPayChannel extends BaseChannel<LianDongToken> {


    protected LianDongConfig lianDongConfig;


    @Override
    public LianDongToken makeToken(String tokenJson) {
        return JSON.parseObject(tokenJson, LianDongToken.class);
    }

    public LianDongPayChannel(CustomsSupport support) {
        super(support);
        lianDongConfig = new LianDongConfig();
    }


    @Override
    public PayCustomsChannel getPayChannel() {
        return PayCustomsChannel.LIANDONG_PAY;
    }


    public CustomsPayDeclareResult customDeclare(WrapPaymentInfo info, String tokenJson) throws PayException {
        log.info("[op:liandongpush] 联动推送-{}-{}", JSONObject.toJSONString(info), tokenJson);
        CustomsPayDeclareResult payDeclareResult = new CustomsPayDeclareResult();
        CustomsReport customsReport = new CustomsReport(CustomsReport.TYPE_SEND, CustomsReport.SYSTEM_PAY_CHANNEL, CustomsReport.PROCESS_CLEAR_DECLARE_PAYMENT);
        LianDongToken token = makeToken(tokenJson);
        LianDongCustomsParam params = new LianDongCustomsParam();
        params.setCustomsId("HZHG");

        //支付总金额
        Amount orderAmount = new Amount();
        orderAmount.setCurrency("CNY");
        orderAmount.setTotal(info.getAmount().toString());
        orderAmount.setTotalCny(info.getAmount().toString());
        params.setSubOrderAmount(orderAmount);
        //物流费
        Amount freightAmount = new Amount();
        freightAmount.setCurrency("CNY");
        freightAmount.setTotal(info.getTransportFee().toString());
        freightAmount.setTotalCny(info.getTransportFee().toString());
        params.setFreightAmount(freightAmount);
        //税费
        Amount taxAmount = new Amount();
        taxAmount.setTotal(info.getTaxFee().toString());
        taxAmount.setCurrency("CNY");
        taxAmount.setTotalCny(info.getTaxFee().toString());
        params.setTaxAmount(taxAmount);

        SubOrder subOrder = info.getSubOrder();
        LianDongSubOrder lianDongSubOrder = new LianDongSubOrder();
        lianDongSubOrder.setMer_sub_reference_id(info.getDeclareNos());
        List<Item> itemList = new ArrayList<>();
        for (com.umf.api.payments.Item items : subOrder.getItems()) {
            Item item = new Item();
            item.setMer_item_id(items.getMerItemId());
            itemList.add(item);
        }
        lianDongSubOrder.setItems(itemList);
        params.setSub_order(lianDongSubOrder);


        params.setMerCustomsCode(info.getMerchantCustomsCode());
        params.setEcPlatId(info.getMerchantCustomsCode());
        params.setNotifyUrl("");

        //请求头
        Map<String, String> headMap = new HashMap<>();
        headMap.put("Content-Type", "application/json");
        headMap.put("Accept-Language", "zh");
        headMap.put("Authorization", "Bearer" + token.getAccess_token());
        log.warn("[LianDongPayChannel-{}]", JSONObject.toJSONString(headMap));

        String url = "";
        HttpRequest httpRequest = null;
        if (Objects.equals(info.getAction(), "add")) {
            headMap.put("Signature", getSign(JSON.toJSONString(params)));
            log.info("[op:liandongpush] params={}", JSON.toJSONString(params));
            //post请求
            url = lianDongConfig.lianDongCustomsDeclareUrl + "/payments/payment/" + info.getTradePayNo() + "/apply_to_customs";
            httpRequest = HttpRequest.post(url).headers(headMap)
                    .header(SaasHttpConstant.HEADER_TENANT_ID, SimpleTenantHelper.getTenantIdStr())
                    .acceptGzipEncoding().uncompress(true).send(JSON.toJSONString(params));
            log.info("联动支付 申报单号: {} ,  请求信息={} params={}, heads={}, payMentId={}", info.getDeclareNos(), url, JSON.toJSONString(params), JSON.toJSONString(headMap), info.getTradePayNo());
        } else if (Objects.equals(info.getAction(), "edit")) {
            //get请求重推
            url = lianDongConfig.lianDongCustomsDeclareUrl + "/declare_again";
            // declare_again
            Map<String, Object> declareAgainMap = new HashedMap() {{
                put("declare_type", 0);
                put("mer_reference_id", info.getDeclareNos());
            }};
            String requestJson = JSON.toJSONString(declareAgainMap);
            headMap.put("Signature", getSign(requestJson));
            // 配置下签名
            httpRequest = HttpRequest.post(url)
                    .header(SaasHttpConstant.HEADER_TENANT_ID, SimpleTenantHelper.getTenantIdStr())
                    .headers(headMap).acceptGzipEncoding().uncompress(true).send(requestJson);
            log.info("联动支付重推 申报单号： {} ,请求头 - {}, 请求地址 - {} , 请求信息 - {} ", info.getDeclareNos(), JSONObject.toJSONString(headMap), url, requestJson);
        }
        //请求结果
        String body = httpRequest.body();
        log.info("[op:liandongCustomsResult] 申报单号: {} , 联动支付 返回信息 ={}", info.getDeclareNos(), body);
        LianDongCustomResult lianDongCustomResult = JSON.parseObject(body, LianDongCustomResult.class);
        LianDongMeta meta = lianDongCustomResult.getMeta();
        if (meta.getRetCode().equals("0000")) {
            payDeclareResult.setSuccess(true);
            payDeclareResult.setPostMsg("申报成功");
        } else {
            payDeclareResult.setSuccess(false);
            payDeclareResult.setPostMsg("申报失败");
            log.error("申报单号: {} ,联动支付单申报失败,body:{}", info.getDeclareNos(), body);
        }
        payDeclareResult.setPostMsg(JSON.toJSONString(params));
        payDeclareResult.setExtra(body);
        payDeclareResult.setCustoms(info.getCustoms());
        payDeclareResult.setOutRequestNo(info.getOutRequestNo());
        payDeclareResult.setPayTransactionId(info.getTradePayNo());
        payDeclareResult.setSubBankNo(info.getTradePayNo());
        if (lianDongCustomResult != null) {
            payDeclareResult.setVerDept(lianDongCustomResult.getVerifyDept());
        }
        customsReport.buildProcessData(payDeclareResult);
        support.accept(customsReport);
        return payDeclareResult;
    }

    /**
     * 获取联动签名
     *
     * @param jsonData
     * @return
     */
    private String getSign(String jsonData) {
        //正式环境：53624_.key.p8  测试环境 6260_.key.p8
        InputStream p8FileInstream = LianDongPayChannel.class.getClassLoader().getResourceAsStream("53624_.key.p8");
        return RSASignUtils.createSign(jsonData, 1, "rest", p8FileInstream);
    }

}
