package com.danding.cds.declare.sdk.model.route;

import lombok.Data;

import java.io.Serializable;

/**
 * @program: cds-center
 * @description: 申报配置
 * @author: 潘本乐（Belep）
 * @create: 2021-11-17 14:31
 **/
@Data
public class RouteDeclareConfig implements Serializable {
    /**
     * 最终申报实现类型code
     */
    private String finalCode;
    /**
     * 最终申报实现
     */
    private String finalImpl;

    /**
     * 代理申报实现的类型code
     */
    private String proxyCode;
    /**
     * 代理的实现
     */
    private String proxyImpl;

    /**
     * 申报类型code
     */
    private String declareCode;
    /**
     * 申报的实现
     */
    private String declareImpl;

    /**
     * 申报类型：支付单-payment;订单-customsOrder;运单-shipment;清单-inventory;清单取消-inventoryCancel;清单退货-inventoryRefund;
     */
    private String type;

    private String dxpId;
}
