package com.danding.cds.declare.sdk.bean.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @program: cds-center
 * @description: s
 * @author: 潘本乐（Belep）
 * @create: 2021-09-29 14:33
 **/
@Data
public class ShipmentOrderItemsBeanReqVo implements Serializable {
    /**
     * item_name : NATURAL BALANCE/天衡宝特级健乐鸡肉三文鱼配方全猫粮15磅/袋
     * item_code : JHYC723633003155
     * item_price : 298.00
     * item_qty : 1
     * item_weight : 0.1
     */

    @JsonProperty("item_name")
    private String itemName;
    @JsonProperty("item_code")
    private String itemCode;
    @JsonProperty("item_price")
    private String itemPrice;
    @JsonProperty("item_qty")
    private String itemQty;
    @JsonProperty("item_weight")
    private String itemWeight;
}
