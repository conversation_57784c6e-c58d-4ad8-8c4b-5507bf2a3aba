package com.danding.cds.declare.sdk.enums;

import java.io.Serializable;

public class PortCodeEnum implements Serializable {
    enum portCode{
        HZPORT("HANGZHOU","2900","杭州关区");
        private String portNid;
        private String portCode;
        private String desc;

        private portCode(String portNid,String portCode,String desc)
        {
            this.portNid = portNid;
            this.portCode = portCode;
            this.desc  = desc;
        }
        public String getPortNid() {
            return portNid;
        }

        public void setPortNid(String portNid) {
            this.portNid = portNid;
        }

        public String getPortCode() {
            return portCode;
        }

        public void setPortCode(String portCode) {
            this.portCode = portCode;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }

    }

    enum DestinationPortCode{
        HZPORT("HANGZHOU","2900","杭州关区");
        private String portNid;
        private String portCode;
        private String desc;

        private DestinationPortCode(String portNid,String portCode,String desc)
        {
            this.portNid = portNid;
            this.portCode = portCode;
            this.desc  = desc;
        }
        public String getPortNid() {
            return portNid;
        }

        public void setPortNid(String portNid) {
            this.portNid = portNid;
        }

        public String getPortCode() {
            return portCode;
        }

        public void setPortCode(String portCode) {
            this.portCode = portCode;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }

    }

    public static String getPortCode(String portNid)
    {
        for (portCode item : portCode.values()) {
            if (item.portNid.equals(portNid)) {
                return item.portCode;
            }
        }
        return null;
    }


    public static String getDestinationPortCode(String portNid)
    {
        for (DestinationPortCode item : DestinationPortCode.values()) {
            if (item.portNid.equals(portNid)) {
                return item.portCode;
            }
        }
        return null;
    }
}
