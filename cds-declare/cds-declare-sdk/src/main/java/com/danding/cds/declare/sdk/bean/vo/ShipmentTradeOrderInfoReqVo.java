package com.danding.cds.declare.sdk.bean.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @program: cds-center
 * @description: s
 * @author: 潘本乐（Belep）
 * @create: 2021-09-29 14:32
 **/
@Data
public class ShipmentTradeOrderInfoReqVo implements Serializable {
    /**
     * waybill_no : 8022090806803
     * apply_type : 1
     * ec_reg_cid : 530166052A
     * ec_reg_name : 昆明云免实业有限公司
     * ec_gj_cid : 3301964J31
     * ec_gj_name : 330766K00Q
     * port_code : 2923
     * customs_code : HZCUSTOMSNEW
     * gross_weight : 0.1
     * net_weight : 0.1
     * order_no : JYCK673039911
     * total_fees : 298.00
     * insured_fees : 0
     * curreny_code : RMB
     * order_items : [{"item_name":"NATURAL BALANCE/天衡宝特级健乐鸡肉三文鱼配方全猫粮15磅/袋","item_code":"JHYC723633003155","item_price":"298.00","item_qty":"1","item_weight":"0.1"}]
     * freight : 0.00
     */
    @JsonProperty("waybill_no")
    private String waybillNo;
    @JsonProperty("apply_type")
    private String applyType;
    @JsonProperty("ec_reg_cid")
    private String ecRegCid;
    @JsonProperty("ec_reg_name")
    private String ecRegName;
    @JsonProperty("ec_gj_cid")
    private String ecGjCid;
    @JsonProperty("ec_gj_name")
    private String ecGjName;
    @JsonProperty("port_code")
    private String portCode;
    @JsonProperty("customs_code")
    private String customsCode;
    @JsonProperty("gross_weight")
    private String grossWeight;
    @JsonProperty("net_weight")
    private String netWeight;
    @JsonProperty("order_no")
    private String orderNo;
    @JsonProperty("total_fees")
    private String totalFees;
    @JsonProperty("insured_fees")
    private String insuredFees;
    @JsonProperty("curreny_code")
    private String currenyCode;
    @JsonProperty("freight")
    private String freight;
    @JsonProperty("order_items")
    private List<ShipmentOrderItemsBeanReqVo> orderItems;
}
