package com.danding.cds.declare.sdk.model.hzdc.fb;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 非保进出区核放单申报（businessType 为 1,2）
 */
@Data
@NoArgsConstructor
public class PassCheckDeclareRequest implements Serializable {
    private String messageId; // 消息id,uuid (必填)
    private NbJobFormHead head; // 非保核放单表头 (必填)
    private List<NbJobFormDetail> details; // 非保核放单明细 (必填)

    @Data
    @NoArgsConstructor
    public static class NbJobFormHead implements Serializable {
        private String checkId; // 核放单编号 (非必填)
        private String regulatorySites; // 监管场所 (必填)
        private String customsCode; // 关区代码 (必填)
        private String companyName; // 企业名称 (必填)
        private String companyCode; // 企业海关编码 (必填)
        private String declarePerson; // 申请人 (必填)
        private String manualId; // 账册编号 (必填)
        private String tradeCode; // 经营单位海关编号 (必填)
        private String tradeName; // 经营单位名称 (必填)
        private String ownerCode; // 收货单位海关编号
        private String ownerName; // 收货单位(货至单位) (必填)
        private String supplyCode; // 发货单位海关编号
        private String supplyName; // 发货单位(货源单位) (必填)
        private int businessType; // 业务类型（参见2.5所定义） (必填)
        private String assEntryId; // 关联报关单号 (非必填)
        private String assCheckId; // 关联核放单号(保税业务必填) (非必填)
        private int wrapType; // 包装种类（参见2.6定义） (必填)
        private BigDecimal grossWt; // 毛重(Kg) (必填)
        private BigDecimal netWt; // 净重(Kg) (必填)
        private int packNum; // 件数 (必填)
        private String containerCode; // 集装箱号 (非必填)
        private BigDecimal totalWeight; // 总重量 (非必填)
        private int containerTwentyNum; // 折20尺集装箱个数 (非必填)
        private int containerNum; // 集装箱自然箱个数 (非必填)
        private String carCode; // 承运车牌号 (必填)
        private String virCarCode; // 电子车牌号 (非必填)
        private String carWeight; // 车自重 (非必填)
        private BigDecimal totalPrice; // 总价 (必填)
        private String remark; // 备注 (非必填)
        private String dbcdNo; // 分送集报号 (非必填)
    }

    @Data
    @NoArgsConstructor
    public static class NbJobFormDetail implements Serializable {
        private String sourceNo; // 料号 (必填)
        private String goodsNo; // 商品编码 (必填)
        private String itemNo; // 账册项号 (必填)
        private String goodsName; // 商品名称 (必填)
        private String goodsSpec; // 商品规格 (非必填)
        private BigDecimal unitPrice; // 单价 (必填)
        private String currency; // 币制 (必填)
        private BigDecimal declareQuantity; // 数量 (必填)
        private String declareUnit; // 计量单位 (必填)
        private BigDecimal totalPrice; // 总价 (必填)
        private int itemType; // 料件性质（参见2.3所定义） (必填)
    }
}
