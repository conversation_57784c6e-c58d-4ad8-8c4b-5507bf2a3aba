package com.danding.cds.declare.sdk.model.hzdc.fb;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 非保出入库单删除申请
 */
@Data
@NoArgsConstructor
public class WarehousingDeleteDeclareRequest implements Serializable {
    private String messageId; // 消息id,uuid (必填)
    private BoStockDelete head; // 删单信息表头 (必填)
    private List<BoStockDeleteDetail> details; // 删单信息明细

    @Data
    @NoArgsConstructor
    public static class BoStockDelete implements Serializable {
        private String deleteId; // 申请删除的出入库单号 (必填)
        private String regulatorySites; // 监管场所 (必填)
        private String customsCode; // 关区代码 (必填)
        private String companyName; // 企业名称 (必填)
        private String companyCode; // 企业海关编码 (必填)
        private String declareDate; // 删单申请时间yyyy-MM-dd HH:mm:ss (必填)
        private String inOutTime; // 删单申请时间yyyy-MM-dd HH:mm:ss (必填)
        private String declareReason; // 删单原因 (必填)
        private String manualId; // 账册编号 (必填)
        private String jobFormId; // 核放单编号,出入库类型为正常进库(非保税入区)、正常出库(非保税出区)、残次品出库时必填 (非必填)
        private int inOutFlag; // 出入库类型（参见2.7所定义） (必填)
        private int manualType; // 账册类型（1-非保物流账册，2-非保一般纳税人账册，一期只有1） (必填)
        private int businessType; // 业务类型（参见2.5所定义） (必填)
        private String remark; // 备注 (非必填)
    }

    @Data
    @NoArgsConstructor
    public static class BoStockDeleteDetail implements Serializable {
        private String sourceNo; // 料号 (必填)
        private String goodsNo; // 商品编码 (必填)
        private String itemNo; // 账册项号 (必填)
        private BigDecimal declareQuantity; // 数量 (必填)
        private String declareUnit; // 数量单位 (必填)
        private int itemType; // 料件性质（参见2.3所定义） (必填)
    }
}
