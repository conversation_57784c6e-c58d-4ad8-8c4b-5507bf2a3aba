package com.danding.cds.declare.sdk.ie;

import cn.hutool.core.util.StrUtil;
import com.danding.cds.declare.sdk.config.CustomsSpecialToken;
import com.danding.cds.declare.sdk.constant.CustomsDistrictCons;
import com.danding.cds.declare.sdk.model.checkList.Inv101MessageRequest;
import com.danding.cds.declare.sdk.model.checkList.InvtHeadType;
import com.danding.cds.declare.sdk.model.checkList.InvtItemBody;
import com.danding.cds.declare.zjspecial.domain.KeyInfo;
import com.danding.cds.declare.zjspecial.domain.Signature;
import com.danding.cds.declare.zjspecial.domain.SignedInfo;
import com.danding.cds.declare.zjspecial.domain.base.EnvelopInfo;
import com.danding.cds.declare.zjspecial.domain.inv101.*;
import com.danding.cds.declare.zjspecial.internal.enums.CustomsMessageType;
import com.danding.cds.declare.zjspecial.internal.utils.SpecialUtil;
import com.danding.cds.invenorder.api.enums.Inv101TrspModecd;
import com.danding.cds.invenorder.api.enums.InventoryOrderBusinessEnum;
import com.github.pagehelper.util.StringUtil;
import com.google.common.collect.Lists;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


public class EndorsementBuilderByInvtOrder implements Serializable {
    private CustomsSpecialToken target;

    public Signature build(Inv101MessageRequest req, CustomsSpecialToken specialToken) {
        this.target = specialToken;
        Signature signature = new Signature();
        signature.setSignedInfo(buildSignedInfo());
        signature.setSignatureValue("");
        signature.setKeyInfo(buildKeyInfo());
        signature.setObject(buildObject(req));
        return signature;
    }

    private KeyInfo buildKeyInfo() {
        KeyInfo keyInfo = new KeyInfo();
        keyInfo.setKeyName("aa");
        return keyInfo;
    }

    private SignedInfo buildSignedInfo() {
        SignedInfo signedInfo = new SignedInfo();
        return signedInfo;
    }

    private Signature.Object buildObject(Inv101MessageRequest req) {
        Signature.Object object = new Signature.Object();
        object.setPackage(buildPackage(req));
        return object;
    }

    private Inv101Package buildPackage(Inv101MessageRequest req) {
        Inv101Package _package = new Inv101Package();
        _package.setEnvelopInfo(buildEnvelopInfo(req));
        _package.setDataInfo(buildDataInfo(req));
        return _package;
    }

    private DataInfo buildDataInfo(Inv101MessageRequest req) {
        DataInfo dataInfo = new DataInfo();
        dataInfo.setBussinessData(buildBussinessData(req));
        return dataInfo;
    }

    private DataInfo.BussinessData buildBussinessData(Inv101MessageRequest req) {
        DataInfo.BussinessData bussinessData = new DataInfo.BussinessData();
        bussinessData.setDelcareFlag(req.getDelcareFlag());
        bussinessData.setInvtMessage(buildInvtMessage(req));
        return bussinessData;
    }

    private InvtMessage buildInvtMessage(Inv101MessageRequest req) {
        InvtMessage invtMessage = new InvtMessage();
        invtMessage.setInvtHeadType(buildHeadType(req));
        invtMessage.setInvtListType(buildInvtListType(req));
        invtMessage.setInvtCbecBill(buildCbecBill(req));
        invtMessage.setInvtGoodsType(buildInvtGoodsType(req));
        invtMessage.setOperCusRegCode(target.getCustomsCode());
        invtMessage.setSysId("Z7");
        if ("L2923B21A004".equals(target.getCustomsBookCode())
                || Objects.equals("L2992B22A001", target.getCustomsBookCode())
                || Objects.equals("L2973B24A002", target.getCustomsBookCode())
        ) {
            invtMessage.setSysId("Z8");
        }
        return invtMessage;
    }

    private List<NemsInvtGoodsType> buildInvtGoodsType(Inv101MessageRequest req) {
        return req.getInvtItemGoodsBodyList().stream().map(i -> {
            NemsInvtGoodsType nemsInvtGoodsType = new NemsInvtGoodsType();
            nemsInvtGoodsType.setSeqNo(i.getSeqNo());
            nemsInvtGoodsType.setGdsSeqno(i.getGdsSeqNo());
            nemsInvtGoodsType.setPutrecSeqno(i.getPutrecSeqNo());
            nemsInvtGoodsType.setGdsMtno(i.getGdsMtno());
            nemsInvtGoodsType.setGdecd(i.getGdecd());
            nemsInvtGoodsType.setGdsNm(i.getGdsNm());
            nemsInvtGoodsType.setGdsSpcfModelDesc(i.getGdsSpcfModelDesc());
            nemsInvtGoodsType.setDclUnitcd(i.getDclUnitcd());
            nemsInvtGoodsType.setLawfUnitcd(i.getLawfUnitcd());
            nemsInvtGoodsType.setSecdLawfUnitcd(i.getSecdLawfUnitcd());
            nemsInvtGoodsType.setNatcd(i.getNatcd());
            nemsInvtGoodsType.setDclUprcAmt(i.getDclUprcAmt());
            nemsInvtGoodsType.setDclTotalAmt(i.getDclTotalAmt());
            nemsInvtGoodsType.setUsdStatTotalAmt(i.getUsdStatTotalAmt());
            nemsInvtGoodsType.setDclCurrcd(i.getDclCurrcd());
            nemsInvtGoodsType.setLawfQty(i.getLawfQty());
            nemsInvtGoodsType.setSecdLawfQty(i.getSecdLawfQty());
            nemsInvtGoodsType.setWtSfVal(i.getWtSfval());
            nemsInvtGoodsType.setFstSfVal(i.getFstSfval());
            nemsInvtGoodsType.setSecdSfVal(i.getSecdSfval());
            nemsInvtGoodsType.setDclQty(i.getDclQty());
            nemsInvtGoodsType.setGrossWt(i.getGrossWt());
            nemsInvtGoodsType.setNetWt(i.getNetWt());
            nemsInvtGoodsType.setUseCd(i.getUseCd());
            nemsInvtGoodsType.setLvyrlfModecd(i.getLvyrlfModecd());
            nemsInvtGoodsType.setUcnsVerno(i.getUcnsVerno());
            nemsInvtGoodsType.setEntryGdsSeqno(i.getEntryGdsSeqno());
            nemsInvtGoodsType.setApplyTbSeqno(i.getApplyTbSeqno());
            nemsInvtGoodsType.setClyMarkcd(i.getClyMarkcd());
            nemsInvtGoodsType.setRmk(i.getRmk());
            nemsInvtGoodsType.setDestinationNatcd(i.getDestinationNatcd());
            nemsInvtGoodsType.setModfMarkcd(i.getModfMarkcd());
            nemsInvtGoodsType.setParam1(i.getParam1());
            return nemsInvtGoodsType;
        }).collect(Collectors.toList());
    }

    private List<NemsInvtCbecBillType> buildCbecBill(Inv101MessageRequest req) {
        List<NemsInvtCbecBillType> nemsInvtCbecBillTypeList = new ArrayList<>();
        for (String invtNo : req.getInvtNos()) {
            NemsInvtCbecBillType nemsInvtCbecBillType = new NemsInvtCbecBillType();
            nemsInvtCbecBillType.setCbecBillNo(invtNo);
            nemsInvtCbecBillType.setSeqNo(!StringUtils.isEmpty(req.getPreEndorsementOrderNo()) ? req.getPreEndorsementOrderNo() : "");
            nemsInvtCbecBillTypeList.add(nemsInvtCbecBillType);
        }
        return nemsInvtCbecBillTypeList;
    }

    private List<NemsInvtListType> buildInvtListType(Inv101MessageRequest req) {
        List<NemsInvtListType> invtListTypes = Lists.newArrayList();
        int i = 1;
        for (InvtItemBody invtItemBody : req.getInvtItemBodyList()) {
            NemsInvtListType nemsInvtListType = new NemsInvtListType();
            nemsInvtListType.setSeqNo(!StringUtils.isEmpty(req.getPreEndorsementOrderNo()) ? req.getPreEndorsementOrderNo() : "");
            nemsInvtListType.setGdsSeqno(String.valueOf(i));
            if (StringUtil.isNotEmpty(invtItemBody.getEntryGdsSeqno())) {
                nemsInvtListType.setEntryGdsSeqno(invtItemBody.getEntryGdsSeqno());
            }
            if ("ONELINE_IN".equals(req.getBussinessType())) {
                nemsInvtListType.setEntryGdsSeqno(String.valueOf(i));
            }
            if ("SIMPLE_PROCESSING".equalsIgnoreCase(req.getBussinessType())) {
                nemsInvtListType.setApplyTbSeqno(invtItemBody.getApplyTbSeqno());
            }
            nemsInvtListType.setPutrecSeqno(invtItemBody.getItemRecordNo());
            nemsInvtListType.setGdsMtno(invtItemBody.getProductId());
            nemsInvtListType.setGdecd(invtItemBody.getGcode());
            nemsInvtListType.setGdsNm(invtItemBody.getGname());
            nemsInvtListType.setGdsSpcfModelDesc(invtItemBody.getGmodel());
            nemsInvtListType.setDclUnitcd(invtItemBody.getUnit());
            nemsInvtListType.setLawfUnitcd(invtItemBody.getUnit1());
            nemsInvtListType.setNatcd(invtItemBody.getCountry());
            nemsInvtListType.setDclUprcAmt(invtItemBody.getPrice());
            DecimalFormat format = new DecimalFormat("0.00");
            nemsInvtListType.setDclTotalAmt(format.format(new BigDecimal(invtItemBody.getTotalPrice())));
            nemsInvtListType.setDclCurrcd(invtItemBody.getCurrency());
            DecimalFormat formatLawfQty = new DecimalFormat("0.00000");
            nemsInvtListType.setLawfQty(formatLawfQty.format(new BigDecimal(invtItemBody.getTotalQty1())));
            nemsInvtListType.setDclQty(invtItemBody.getQty());
            if (!StringUtils.isEmpty(invtItemBody.getUnit2())) {
                nemsInvtListType.setSecdLawfUnitcd(invtItemBody.getUnit2());
                nemsInvtListType.setSecdLawfQty(formatLawfQty.format(new BigDecimal(invtItemBody.getTotalQty2())));
            }
            nemsInvtListType.setLvyrlfModecd("3");
            nemsInvtListType.setDestinationNatcd(invtItemBody.getDestCountry());
            nemsInvtListType.setModfMarkcd("0");
            if (Objects.nonNull(invtItemBody.getClyMarkcd())
                    && checkClyMarkcd(req.getInvtHeadType().getPutrecNo(), req.getInvtHeadType().getInvtType(), req.getInvtHeadType().getSupvModecd())) {
                nemsInvtListType.setClyMarkcd(invtItemBody.getClyMarkcd());
            }
            nemsInvtListType.setParam1(invtItemBody.getParam1());
            nemsInvtListType.setUcnsVerno(invtItemBody.getUcnsVerno());
            invtListTypes.add(nemsInvtListType);
            i++;
        }
        return invtListTypes;
    }

    private NemsInvtHeadType buildHeadType(Inv101MessageRequest req) {
        //TODO  配置暂时写死，待修改
        InvtHeadType invtHeadType = req.getInvtHeadType();
        NemsInvtHeadType nemsInvtHeadType = new NemsInvtHeadType();
        if (!StringUtils.isEmpty(req.getPreEndorsementOrderNo())) {
            nemsInvtHeadType.setSeqNo(req.getPreEndorsementOrderNo());
        }
        nemsInvtHeadType.setPutrecNo(target.getCustomsBookCode());
        if (Objects.nonNull(req.getCustomsBookCode())) {
            //为了解决 企业:账册 一对多的情况，账册取核注单数据
            nemsInvtHeadType.setPutrecNo(req.getCustomsBookCode());
        }
        req.getInvtHeadType().setPutrecNo(nemsInvtHeadType.getPutrecNo());
        nemsInvtHeadType.setEtpsInnerInvtNo(req.getEndorsementOrderNo());//企业内部清单编号
        nemsInvtHeadType.setBizopEtpsNm(target.getCustomsName());
        nemsInvtHeadType.setBizopEtpsno(target.getCustomsCode());
        nemsInvtHeadType.setBizopEtpsSccd(target.getSocialCreditCode());
        nemsInvtHeadType.setRcvgdEtpsNm(target.getCustomsName());
        nemsInvtHeadType.setRcvgdEtpsno(target.getCustomsCode());
        nemsInvtHeadType.setRvsngdEtpsSccd(target.getSocialCreditCode());
        nemsInvtHeadType.setDclEtpsno(target.getCustomsCode());
        nemsInvtHeadType.setDclEtpsNm(target.getCustomsName());
        nemsInvtHeadType.setDclEtpsSccd(target.getSocialCreditCode());
        nemsInvtHeadType.setDclPlcCuscd("2924");
        nemsInvtHeadType.setImpexpPortcd(!StringUtils.isEmpty(invtHeadType.getImpexpPortcd()) ? invtHeadType.getImpexpPortcd() : "2924");
        if ("L2923B21A004".equals(target.getCustomsBookCode())) {//义乌优诚
            nemsInvtHeadType.setDclPlcCuscd("2923");
            nemsInvtHeadType.setImpexpPortcd(!StringUtils.isEmpty(invtHeadType.getImpexpPortcd()) ? invtHeadType.getImpexpPortcd() : "2923");
        } else if (CustomsDistrictCons.YW_2925_CUSTOMS_DISTRICT.contains(target.getCustomsBookCode())) {
            nemsInvtHeadType.setDclPlcCuscd("2925");
            nemsInvtHeadType.setImpexpPortcd(!StringUtils.isEmpty(invtHeadType.getImpexpPortcd()) ? invtHeadType.getImpexpPortcd() : "2925");
        }
        // 这里获取下配置的海关关区，如果不为空，则直接使用
        String customsAreaCode = req.getCustomsAreaCode();
        if (StringUtils.hasText(customsAreaCode)) {
            nemsInvtHeadType.setDclPlcCuscd(customsAreaCode);
            nemsInvtHeadType.setImpexpPortcd(!StringUtils.isEmpty(invtHeadType.getImpexpPortcd()) ? invtHeadType.getImpexpPortcd() : customsAreaCode);
        }
        nemsInvtHeadType.setImpexpMarkcd(invtHeadType.getImpexpMarkCd());
        nemsInvtHeadType.setMtpckEndprdMarkcd(StrUtil.isNotBlank(invtHeadType.getMtpckEndprdMarkcd()) ? invtHeadType.getMtpckEndprdMarkcd() : "I");
        nemsInvtHeadType.setSupvModecd(invtHeadType.getSupvModecd());
        nemsInvtHeadType.setTrspModecd(invtHeadType.getTrspModecd());
        nemsInvtHeadType.setDclcusFlag(invtHeadType.getDclcusFlag());
        nemsInvtHeadType.setInputCode(target.getCustomsCode());
        nemsInvtHeadType.setInputCreditCode(target.getSocialCreditCode());
        nemsInvtHeadType.setInputName(target.getCustomsName());
        nemsInvtHeadType.setIcCardNo(target.getIcCard());
        nemsInvtHeadType.setInputTime(new SimpleDateFormat("yyyyMMdd").format(new Date()));
        nemsInvtHeadType.setStshipTrsarvNatcd(invtHeadType.getStshipTrsarvNatcd());
        nemsInvtHeadType.setInvtType(invtHeadType.getInvtType());
        nemsInvtHeadType.setDclTypecd(invtHeadType.getDclTypecd()); // 报关标志填写为“2.非报关”该项不可填。
        nemsInvtHeadType.setGenDecFlag(invtHeadType.getGenDecFlag());
        nemsInvtHeadType.setRmk(invtHeadType.getRemark());
        //关联核注清单编号
        if (!StringUtils.isEmpty(invtHeadType.getRltInvtNo())) {
            nemsInvtHeadType.setRltInvtNo(invtHeadType.getRltInvtNo());
        }
        //关联备案编号（账册编号）
        if (!StringUtils.isEmpty(invtHeadType.getRltPutrecNo())) {
            nemsInvtHeadType.setRltPutrecNo(invtHeadType.getRltPutrecNo());
        }
        //一线入境报关单信息
        if ("ONELINE_IN".equals(req.getBussinessType())
                || "BONDED_ONELINE_IN".equals(req.getBussinessType())
                || (InventoryOrderBusinessEnum.BUSSINESS_SECTION_IN.getCode().equalsIgnoreCase(req.getBussinessType())
                && Inv101TrspModecd.WAY_SPECIAL_BONDED_AREA.getKey().equalsIgnoreCase(invtHeadType.getTrspModecd()))
                || "SUBSEQUENT_TAX".equals(req.getBussinessType())
                || "ONLINE_REFUND".equals(req.getBussinessType())
                || "BONDED_PROCESSING_ONELINE_IN".equalsIgnoreCase(req.getBussinessType())
                || "BONDED_PROCESSING_ONELINE_OUT".equalsIgnoreCase(req.getBussinessType())
        ) {
            nemsInvtHeadType.setDecType(invtHeadType.getDecType());
            nemsInvtHeadType.setDclcusTypecd(invtHeadType.getDclcusTypeCd());
            nemsInvtHeadType.setCorrEntryDclEtpsNm(invtHeadType.getCorrEntryDclEtpsNm());
            nemsInvtHeadType.setCorrEntryDclEtpsNo(invtHeadType.getCorrEntryDclEtpsNo());
            nemsInvtHeadType.setCorrEntryDclEtpsSccd(invtHeadType.getCorrEntryDclEtpsSccd());
        }
        // 保税物流转大贸报关单信息
        if ("BONDED_TO_TRADE".equals(req.getBussinessType())) {
            nemsInvtHeadType.setDecType(invtHeadType.getDecType());
            nemsInvtHeadType.setDclcusTypecd(invtHeadType.getDclcusTypeCd());
            nemsInvtHeadType.setRltEntryDclEtpsNm(invtHeadType.getRltEntryDclEtpsNm());
            nemsInvtHeadType.setRltEntryDclEtpsno(invtHeadType.getRltEntryDclEtpsNo());
            nemsInvtHeadType.setRltEntryDclEtpsSccd(invtHeadType.getRltEntryDclEtpsSccd());
            nemsInvtHeadType.setRltEntryBizopEtpsNm(invtHeadType.getRltEntryBizopEtpsNm());
            nemsInvtHeadType.setRltEntryBizopEtpsno(invtHeadType.getRltEntryBizopEtpsNo());
            nemsInvtHeadType.setRltEntryBizopEtpsSccd(invtHeadType.getRltEntryBizopEtpsSccd());
            nemsInvtHeadType.setRltEntryRcvgdEtpsNm(invtHeadType.getRltEntryRvsngdEtpsNm());
            nemsInvtHeadType.setRltEntryRcvgdEtpsno(invtHeadType.getRltEntryRvsngdEtpsNo());
            nemsInvtHeadType.setRltEntryRvsngdEtpsSccd(invtHeadType.getRltEntryRvsngdEtpsSccd());
        }
        if ("SIMPLE_PROCESSING".equalsIgnoreCase(req.getBussinessType())) {
            nemsInvtHeadType.setApplyNo(invtHeadType.getApplyNo());
        }
        //一线入境区港联动需要传对应报关单号
        if (!StringUtils.isEmpty(invtHeadType.getEntryNo()) && "7".equals(invtHeadType.getInvtType())) {
            nemsInvtHeadType.setEntryNo(invtHeadType.getEntryNo());
        }
        if (StringUtil.isNotEmpty(invtHeadType.getIcCardNo())) {
            nemsInvtHeadType.setIcCardNo(invtHeadType.getIcCardNo());
        }
        return nemsInvtHeadType;
    }

    private EnvelopInfo buildEnvelopInfo(Inv101MessageRequest req) {
        EnvelopInfo envelopInfo = new EnvelopInfo();
        String uuid = UUID.randomUUID().toString();
        envelopInfo.setBusinessId("businessId-" + uuid);
        envelopInfo.setFileName("fileName-" + uuid);
        envelopInfo.setIcCard(target.getIcCard());//ic卡号
        envelopInfo.setMessageId("messageId-" + uuid);
        envelopInfo.setMessageType(CustomsMessageType.INV101.name());
        envelopInfo.setReceiverId("DXPEDCSAS0000001");
        envelopInfo.setSenderId(target.getSenderId());
        envelopInfo.setSendTime(SpecialUtil.dateToXmlDate(new Date()));
        envelopInfo.setVersion("1.0");
        return envelopInfo;
    }

    /**
     * 危化品标志校验
     * 表头备案号以T、H开头或以L开头且第6位字符为‘B’,清单类型不是'简单加工'、监管方式不是‘AAAA’时，必填, 其他情况为空
     *
     * @param bookNo
     * @param invtType
     * @param supvModecd
     * @return
     */
    private boolean checkClyMarkcd(String bookNo, String invtType, String supvModecd) {
        String regex = "^(T|H|L....B).*";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(bookNo);
        boolean matches = matcher.matches();
        return matches && !invtType.equals("4") && !supvModecd.equals("AAAA");
    }
}
