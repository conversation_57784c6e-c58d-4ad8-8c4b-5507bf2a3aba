package com.danding.cds.declare.sdk.enums;
/*
        0	非保税区
        1	监管仓库
        2	江海运输
        3	铁路运输
        4	汽车运输
        5	航空运输
        6	邮件运输
        7	保税区
        8	保税仓库
        9	其它运输
        Z	出口加工
        A	全部运输
        W	物流中心
        X	物流园区
        Y	保税港区
*/
public enum PortTrafModeEnum {
    HANGZHOU_TRAFMODE("HANGZHOU","0", "非保税区"),
    GUANGZHOU__TRAFMODE("GUANGZHOU","1", "监管仓库"),
    NINGBO__TRAFMODE("NINGBO","2", "江海运输"),
    CUSTOMSOFFICE__TRAFMODE("CUSTOMSOFFICE","3", "铁路运输"),
    JINAN__TRAFMODE("JINAN","3", "铁路运输"),
    XIAMEN__TRAFMODE("XIAMEN","5", "航空运输"),
    PINGTAN__TRAFMODE("PINGTAN","7", "保税区"),
    SHATIAN__TRAFMODE("SHATIAN","8", "保税仓库"),
    CHONGQING__TRAFMODE("CHONGQING","9", "其它运输"),
    QINGDAO__TRAFMODE("QINGDAO", "Z","出口加工"),
    GYPORT__TRAFMODE("GYPORT", "A","全部运输"),
    JIANGYIN__TRAFMODE("JIANGYIN","W", "物流中心"),
    NANCHANG__TRAFMODE("NANCHANG","X", "物流园区"),
    ZHENGZHOU__TRAFMODE("ZHENGZHOU","Y", "保税港区"),
    QIANHAI__TRAFMODE("QIANHAI","0", "非保税区"),
    CHENGDU__TRAFMODE("CHENGDU","1", "监管仓库"),
    HEFEI__TRAFMODE("HEFEI","2", "江海运输");
    private String portNid;
    private String trafCode;
    private String desc;
    private PortTrafModeEnum(String portNid,String trafCode,String desc)
    {
        this.portNid = portNid;
        this.trafCode = trafCode;
        this.desc  = desc;
    }
    public String getPortNid() {
        return portNid;
    }

    public void setPortNid(String portNid) {
        this.portNid = portNid;
    }

    public String getTrafCode() {
        return trafCode;
    }

    public void setTrafCode(String trafCode) {
        this.trafCode = trafCode;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static String getTrafCode(String portNid)
    {
        for (PortTrafModeEnum item : PortTrafModeEnum.values()) {
            if (item.portNid.equals(portNid)) {
                return item.trafCode;
            }
        }
        return null;
    }
}
