package com.danding.cds.declare.sdk.model.checklistAuth;

import com.danding.cds.declare.sdk.model.checkList.MessageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
public class Sas122MessageRequest extends MessageRequest implements Serializable {
    private static final long serialVersionUID = 5966037031232029363L;

    /**
     * 核放单调用授权Sn
     */
    private String checklistAuthSn;

    /**
     * 授权信息
     */
    private List<PassportAuthInfo> passportAuthInfoList;

    /**
     * 企业内部编号
     */
    private String etpsPreentNo;

    /**
     * 签名信息
     */
    private SasSign sasSign;

    /**
     * 申报标志
     * 0--暂存；
     * 1--申报；
     */
    private Integer delcareFlag;

    @Data
    public static class PassportAuthInfo implements Serializable {

        /**
         * 授权系统编号
         */
        private String sysId;  // SYS_ID 授权系统编号

        /**
         * 授权企业海关编码
         */
        private String etpsNo;  // ETPS_NO 授权企业海关编码

        /**
         * 授权企业社会信用代码
         */
        private String etpsSccd;  // ETPS_SCCD 授权企业社会信用代码

        /**
         * 授权企业名称
         */
        private String etpsName;  // ETPS_NAME 授权企业名称

        /**
         * 业务单据类型
         * “1”-核注清单，“2”-出入库单
         */
        private String billType;  // BLS_TYPE 业务单据类型

        /**
         * 业务单据编号
         */
        private String billNo;  // BLS_NO 业务单据编号

        /**
         * 被授权企业海关编码
         */
        private String syEtpsNo;  // SY_ETPS_NO 被授权企业海关编码

        /**
         * 被授权企业社会信用代码
         */
        private String syEtpsSccd;  // SY_ETPS_SCCD 被授权企业社会信用代码

        /**
         * 被授权企业名称
         */
        private String syEtpsName;  // SY_ETPS_NAME 被授权企业名称

        /**
         * 权限类型
         * “1”-授予权限，“0”-取消授权
         */
        private String authType;  // AUTH_TYPE 权限类型
    }

    @Data
    public static class SasSign implements Serializable {
        /**
         * 预录入统一编号
         */
        private String seqNo;  // SEQ_NO 预录入统一编号

        /**
         * 经营单位编码
         */
        private String bizopEtpsNo;  // BIZOP_ETPS_NO 经营单位编码

        /**
         * 经营企业社会信用代码
         */
        private String bizopEtpsSccd;  // BIZOP_ETPS_SCCD 经营企业社会信用代码

        /**
         * 加工单位编码
         */
        private String ownerEtpsNo;  // OWNER_ETPS_NO 加工单位编码

        /**
         * 加工企业社会信用代码
         */
        private String ownerEtpsSccd;  // OWNER_ETPS_SCCD 加工企业社会信用代码

        /**
         * 签名卡号
         */
        private String icCode;  // IC_CODE 签名卡号

        /**
         * 签名信息
         */
        private String signInfo;  // SIGN_INFO 签名信息

        /**
         * 签名日期
         */
        private Date signDate;  // SIGN_DATE 签名日期

        /**
         * 证书号
         */
        private String certNo;  // CERT_NO 证书号
    }
}
