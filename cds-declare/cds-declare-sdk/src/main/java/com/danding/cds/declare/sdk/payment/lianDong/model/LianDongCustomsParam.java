package com.danding.cds.declare.sdk.payment.lianDong.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.umf.api.payments.enums.CitizenIdType;
import com.umf.api.payments.enums.CustomsDeclarationState;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: shenhui
 * @Date: 2019/12/18 10:14
 */
@Data
public class LianDongCustomsParam implements Serializable {
    /**
     * id(请求报关时无需上送)
     */
    private String id;

    /**
     * 表示订单号. 商户唯一订单号.
     */
    @JSONField ( name = "mer_reference_id" )
    private String merReferenceId;

    /**
     * 订单金额
     */
    @JSONField ( name = "order_amount" )
    private Amount orderAmount;

    /**
     * 订单金额
     */
    @JSONField ( name = "sub_order_amount" )
    private Amount subOrderAmount;

    /**
     * 必须加密. 支付人姓名.
     */
    @JSONField ( name = "name" )
    private String payerName;

    /**
     * 公民当前身份类型. 枚举值为IDENTITY_CARD
     */
    @JSONField ( name = "citizen_id_type" )
    private CitizenIdType citizenIdType;

    /**
     * 身份证号码.. 加密.
     */
    @JSONField ( name = "citizen_id_number" )
    private String citizenIdNumber;

    /**
     * 商户订单支付日期 YYYYMMDD
     */
    @JSONField ( name = "mer_pay_date" )
    private String merPayDate;

    /**
     * 商户订单支付时间.HHMMSS
     */
    @JSONField ( name = "mer_pay_time" )
    private String merPayTime;

    /**
     * 支付人手机号.
     */
    @JSONField ( name = "phone" )
    private String phone;

    /**
     * 海关申报时的海关编号(如 HZHG, NB 和 GZHG 分别代表杭州, 宁波 和广州海关).
     */
    @JSONField ( name = "customs_id" )
    private String customsId;

    /**
     * 海关商户号
     */
    @JSONField ( name = "mer_customs_code" )
    private String merCustomsCode;

    /**
     * 海关系统电子商务平台帐号
     */
    @JSONField ( name = "ec_plat_id" )
    private String ecPlatId;

    /**
     * 支付运费
     */
    @JSONField ( name = "freight_amount" )
    private Amount freightAmount;

    /**
     * 海关税
     */
    @JSONField ( name = "tax_amount" )
    private Amount taxAmount;

    private CustomsDeclarationState state;

    /**
     * 请求海关日期
     * 请求UMF报关后返回，无需上送 YYYYMMDD
     */
    @JSONField ( name = "customs_clearance_date" )
    private String customsClearanceDate;

    /**
     * 原支付流水号.非必填
     */
    @JSONField ( name = "pay_trace" )
    private String payTrace;

    /**
     * 异步通知报关结果地址.
     */
    @JSONField ( name = "notify_url" )
    private String notifyUrl;
    @JSONField ( name = "paye_code" )
    private String payeCode;

    /**
     * 货物信息 字段传入要求：商品描述+规格+数量 如有多个货物，用 ;隔开，如：飞利浦电动牙刷HX6730*1;Estee Lauder精华眼霜 15ml*1
     */
    @JSONField ( name = "goods_info" )
    private String goodsInfo;
    @JSONField ( name = "tracking_number" )
    private String tracking_number;

    /**
     * 验核机构 验核机构包括：
     * 银联-UnionPay
     * 网联-NetsUnion
     * 其他-Others（银行卡快捷，网银）
     */
    @JSONField ( name = "verify_department" )
    private String verify_department;


    /**
     * 验核机构流水号 交易流水号，来自验核机构，如银联侧的交易流水号，供商户报备海关
     */
    @JSONField ( name = "verify_department_pay_trace" )
    private String verify_department_pay_trace;

    /**
     * 原订单时间
     */
    @JSONField ( name = "mer_date" )
    private String mer_date;

    /**
     * 子订单
     */
    @JSONField(name = "sub_order")
    private LianDongSubOrder sub_order;

}
