package com.danding.cds.declare.sdk.clear.chongqing.model;

import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;
import java.util.List;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(propOrder = { "ORDER_HEAD", "ORDER_DETAIL"})
@XmlRootElement(name = "ORDER_XT2WMS")
public class CQDeliverModel implements Serializable {

    @XmlElement(name = "ORDER_HEAD")
    private CQDeliverHead ORDER_HEAD;

    @XmlElement(name = "ORDER_DETAIL")
    private List<CQDeliverItem> ORDER_DETAIL;
}
