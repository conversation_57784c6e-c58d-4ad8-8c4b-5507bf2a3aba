package com.danding.cds.declare.sdk.model.refund;

import com.danding.cds.declare.sdk.model.WrapBeanInfo;
import com.danding.cds.declare.sdk.model.company.CompanyInfo;
import com.danding.cds.declare.sdk.model.inventory.CustomsInventoryInfo;
import com.danding.cds.declare.sdk.model.inventory.CustomsInventoryItemInfo;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class WarpRefundOrderInfo extends WrapBeanInfo {
    public WarpRefundOrderInfo()
    {

    }
    public WarpRefundOrderInfo(RefundOrderInfo refundOrderInfoDto,
                               CustomsInventoryInfo customsInventoryDto,
                               List<CustomsInventoryItemInfo> listCustomsInventoryItemInfo,
                               CompanyInfo internalAreaCompany,
                               CompanyInfo logisticsCompanyDTO,
                               CompanyInfo ebpCompanyDTO,
                               CompanyInfo ebcCompanyDTO,
                               CompanyInfo declareCompanyDTO
                               )
    {
        super(ebpCompanyDTO,ebcCompanyDTO,declareCompanyDTO,logisticsCompanyDTO);
        this.refundOrderInfoDto = refundOrderInfoDto;
        this.customsInventoryDto = customsInventoryDto;
        this.listCustomsInventoryItemInfo = listCustomsInventoryItemInfo;
        this.internalAreaCompany = internalAreaCompany;
        this.logisticsCompanyDTO = logisticsCompanyDTO;
    }
    /**
     * 退货单信息
     */
    protected RefundOrderInfo refundOrderInfoDto;
    /**
     * 相关清单对象信息
     */
    protected CustomsInventoryInfo customsInventoryDto;

    /**
     * 清单物流明细
     */
    protected List<CustomsInventoryItemInfo> listCustomsInventoryItemInfo;
    /**
     * 区内企业
     */
    protected CompanyInfo internalAreaCompany ;

    /**
     * 物流公司
     */
    protected CompanyInfo logisticsCompanyDTO;

    private String dxpId;

    @Override
    public String getDeclareNos() {
        return customsInventoryDto == null ? "" : customsInventoryDto.getOrderNo();
    }
}
