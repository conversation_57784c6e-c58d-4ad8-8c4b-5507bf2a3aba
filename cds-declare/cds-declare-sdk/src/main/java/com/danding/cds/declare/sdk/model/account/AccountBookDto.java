package com.danding.cds.declare.sdk.model.account;

import lombok.Data;

import java.io.Serializable;

/**
 * @program: cds-center
 * @description: 账册
 * @author: 潘本乐（Belep）
 * @create: 2021-11-29 13:44
 **/
@Data
public class AccountBookDto implements Serializable {

    private Long id;
    /**
     * 海关账册编码
     */
    private String bookNo;
    /**
     * 区内企业ID
     */
    private Long areaCompanyId;
    /**
     * 口岸编码
     */
    private String customsDistrictCode;
    /**
     * 关区编码
     */
    private String customsAreaCode;
    /**
     * 关区名称
     */
    private String customsAreaName;
    /**
     * 备注
     */
    private String remark;
}
