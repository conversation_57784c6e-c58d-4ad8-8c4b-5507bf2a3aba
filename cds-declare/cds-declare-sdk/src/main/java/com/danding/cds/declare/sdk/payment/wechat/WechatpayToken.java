package com.danding.cds.declare.sdk.payment.wechat;


import com.danding.cds.declare.sdk.payment.base.BaseToken;
import lombok.Data;

import java.io.Serializable;

@Data
public class WechatpayToken extends BaseToken implements Serializable {

    private static final long serialVersionUID = 4241240454224578672L;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 秘钥
     */
    private String secret;

    /**
     * 商户号
     */
    private String mchId;

    /**
     * 商户秘钥
     */
    private String partnerKey;

    /**
     * todo:??加密文件路径
     */
    private String caFilePath;

    /**
     * todo:??解密文件路径
     */
    private String certFilePath;
}
