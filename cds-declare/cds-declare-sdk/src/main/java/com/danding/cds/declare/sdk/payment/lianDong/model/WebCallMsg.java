package com.danding.cds.declare.sdk.payment.lianDong.model;


import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
public class WebCallMsg implements Serializable {
	private boolean flag;
	private String sn;
	private Integer endStatus;
	private String action;
	private String outSn;
	private String errorMsg;
	private String returnCode;
	private String returnMsg;
	private String handleTime;
	
	private String requestMsg;
	private String responseMsg;
	
	private List<StockReturnInfoQuery> list;
	
	public WebCallMsg() {
	}
	
	public WebCallMsg(boolean flag, String errorMsg, String returnCode, String returnMsg) {
		this.flag = flag;
		this.errorMsg = errorMsg;
		this.returnCode = returnCode;
		this.returnMsg = returnMsg;
	}


	@Override
	public String toString() {
		return "WebCallMsg [sn=" + sn + ", errorMsg=" + errorMsg + ", returnCode=" + returnCode + ", returnMsg="
				+ returnMsg + ", handleTime=" + handleTime + ", requestMsg=" + requestMsg + ", responseMsg="
				+ responseMsg + "]";
	}
	
}
