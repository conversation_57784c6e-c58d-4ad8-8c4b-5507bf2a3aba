package com.danding.cds.declare.sdk.bean;


import com.danding.cds.declare.ceb.internal.utils.XMLUtil;
import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/6/12 14:13
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(propOrder = {"success", "result", "errorcode", "errormsg"})
@XmlRootElement(name = "response")
public class JdCustomsDataSignResponse implements Serializable {
    @XmlElement
    private boolean success;
    @XmlElement
    private String result;// Changed type to Element to hold XML content directly
    @XmlElement
    private Integer errorcode;
    @XmlElement
    private String errormsg;


    public static void main(String[] args) throws Exception {
        success("<foo>111</foo>");
    }

    public static String success(String signedData) throws Exception {
        JdCustomsDataSignResponse response = new JdCustomsDataSignResponse();
        response.setSuccess(true);
        response.setErrorcode(null);
        response.setErrormsg("");
        response.setResult("SIGN_DATA");
        String string = XMLUtil.convertToXml(response);
        return string.replace("SIGN_DATA", signedData);
    }

    public static String error(Integer errorcode, String errormsg) throws Exception {
        JdCustomsDataSignResponse response = new JdCustomsDataSignResponse();
        response.setSuccess(false);
        response.setErrorcode(errorcode);
        response.setErrormsg(errormsg);
        response.setResult("");
        return XMLUtil.convertToXml(response);
    }


}