package com.danding.cds.declare.sdk;

import com.alibaba.fastjson.JSON;
import lombok.Data;

import java.io.Serializable;

/**
 * 海关动作报告
 */
@Data
public class CustomsReport implements Serializable {
    public static final String TYPE_SEND = "SEND";
    public static final String TYPE_ACCEPT = "ACCEPT";

    public static final String SYSTEM_PAY_CHANNEL = "PAY_CHANNEL";
    public static final String SYSTEM_CUSTOMS_CLIENT = "CUSTOMS_CLIENT";
    public static final String SYSTEM_SPECIAL_CLIENT = "SPECIAL_CLIENT";

    private String businessKey; // 标识字段

    private String type; // SEND:发送动作 ACCEPT:接收动作

    private String system; // 系统类型


    // 处理报文这里需注意，报文分明文和密文，看这里要哪种。
    // 请求必填
    private String requestMsg;

    // 回执必填
    private String responseMsg;


    /**
     * 业务处理类型
     */
    private String process;

    /**
     * 通用化后的数据
     */
    private String processData;

    /**
     * 发送方
     */
    private String sender;


    // 定义需要特殊处理process
    public static final String PROCESS_DEFAULT = "PROCESS_DEFAULT"; // 默认process


    public static final String PROCESS_CLEAR_CALLBACK_UN_KNOW = "CLEAR_CALLBACK_UN_KNOW"; // 清关-回执-未知

    public static final String PROCESS_CLEAR_CALLBACK_ORDER = "CLEAR_CALLBACK_ORDER"; // 清关-回执-订单
    public static final String PROCESS_CLEAR_CALLBACK_INVENTORY = "CLEAR_CALLBACK_INVENTORY"; // 清关-回执-清单
    public static final String PROCESS_CLEAR_CALLBACK_SHIPMENT = "CLEAR_CALLBACK_SHIPMENT"; // 清关-回执-运单
    public static final String PROCESS_CLEAR_CALLBACK_TAX = "CLEAR_CALLBACK_TAX"; // 清关-回执-税单
    public static final String PROCESS_CLEAR_CALLBACK_TAX_STATUS = "CLEAR_CALLBACK_TAX_STATUS"; // 清关-回执-税单汇总
    public static final String PROCESS_CLEAR_CALLBACK_INVENTORY_CANCEL = "CLEAR_CALLBACK_INVENTORY_CANCEL"; // 清关-回执-撤单
    public static final String PROCESS_CLEAR_CALLBACK_INVENTORY_REFUND = "CLEAR_CALLBACK_INVENTORY_REFUND"; // 清关-回执-退货
    public static final String PROCESS_CALLBACK_DELIVER = "CALLBACK_DELIVER"; // 回执-发货

    public static final String PROCESS_CLEAR_DECLARE_PAYMENT = "CLEAR_DECLARE_PAYMENT"; // 清关-申报-支付单
    public static final String PROCESS_CLEAR_DECLARE_ORDER = "CLEAR_DECLARE_ORDER"; // 清关-申报-订单
    public static final String PROCESS_CLEAR_DECLARE_INVENTORY = "CLEAR_DECLARE_INVENTORY"; // 清关-申报-清单
    public static final String PROCESS_CLEAR_DECLARE_SHIPMENT = "CLEAR_DECLARE_SHIPMENT"; // 清关-申报-运单
    public static final String PROCESS_CLEAR_DECLARE_INVENTORY_CANCEL = "CLEAR_DECLARE_INVENTORY_CANCEL"; // 清关-申报-撤单
    public static final String PROCESS_CLEAR_DECLARE_INVENTORY_REFUND = "CLEAR_DECLARE_INVENTORY_REFUND"; // 清关-申报-退货


    public CustomsReport() {
    }

    public CustomsReport(String type, String system) {
        this.type = type;
        this.system = system;
        this.process = CustomsReport.PROCESS_DEFAULT;
    }

    public CustomsReport(String type, String system, String process) {
        this.type = type;
        this.system = system;
        this.process = process;
    }

    public void buildBusinessKey(String businessKey){
        setBusinessKey(businessKey);
    }

    public void buildRequest(String requestMsg){
        setRequestMsg(requestMsg);
    }

    public void buildResponse(String responseMsg){
        setResponseMsg(responseMsg);
    }

    public void buildProcessData(Object object){
        setProcessData(JSON.toJSONString(object));
    }
}

