package com.danding.cds.declare.sdk.clear.zhejiang.builder;

import com.danding.cds.declare.ceb.domain.base.BaseTransfer;
import com.danding.cds.declare.ceb.domain.ceb623.CEB623Message;
import com.danding.cds.declare.ceb.domain.ceb623.InvtCancel;
import com.danding.cds.declare.ceb.internal.enums.CebDeclareType;
import com.danding.cds.declare.ceb.internal.utils.CebDateUtil;
import com.danding.cds.declare.sdk.constant.CustomsDistrictCons;
import com.danding.cds.declare.sdk.enums.BusinessTypeEnums;
import com.danding.cds.declare.sdk.model.cancel.WarpCancelOrderInfo;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;
import java.util.UUID;

public class ZJCebInventoryCancelBuilder implements Serializable {

    private WarpCancelOrderInfo info;
    private String dxpId;


    public ZJCebInventoryCancelBuilder(WarpCancelOrderInfo info, String dxpId) {
        this.info = info;
        this.dxpId = dxpId;

    }

    public CEB623Message build() {
        return buildOrderMessage();
    }

    private CEB623Message buildOrderMessage() {
        CEB623Message ceb623Message = new CEB623Message();
        ceb623Message.setInvtCancel(buildInvtCancel());
        ceb623Message.setBaseTransfer(buildBaseTransfer());
        ceb623Message.setGuid(ceb623Message.getInvtCancel().getGuid());
        return ceb623Message;
    }

    private BaseTransfer buildBaseTransfer() {
        BaseTransfer baseTransfer = new BaseTransfer();
        String declareCompanyCode = info.getDeclareCompanyDTO().getCebCode();
        String declareCompanyName = info.getDeclareCompanyDTO().getCebName();
        baseTransfer.setCopCode(declareCompanyCode);
        baseTransfer.setCopName(declareCompanyName);
        baseTransfer.setDxpMode("DXP");
        baseTransfer.setDxpId(dxpId);
        return baseTransfer;
    }

    private InvtCancel buildInvtCancel() {

        InvtCancel invtCancel = new InvtCancel();
        invtCancel.setGuid(UUID.randomUUID().toString().toUpperCase());
        invtCancel.setAppType(CebDeclareType.CREATE.getType());
        invtCancel.setAppTime(CebDateUtil.format(new Date(), CebDateUtil.defaultCebDateStringFormat));
        invtCancel.setAppStatus("2");
        //申报海关代码
        invtCancel.setCustomsCode("2924");
        if ("L2923B21A004".equals(info.getCustomsInventoryDto().getBookNo())) {
            invtCancel.setCustomsCode("2923");
        } else if (CustomsDistrictCons.YW_2925_CUSTOMS_DISTRICT.contains(info.getCustomsInventoryDto().getBookNo())) {
            invtCancel.setCustomsCode("2925");
        }
        // 这里获取下账册维护的海关关区，如果不为空，则直接使用
        String customsAreaCode = info.getCustomsAreaCode();
        if (StringUtils.isNotBlank(customsAreaCode)) {
            invtCancel.setCustomsCode(customsAreaCode);
        }
        //订单编号
        invtCancel.setOrderNo(this.info.getCustomsInventoryDto().getOrderNo());

        //电商平台代码、电商平台名称、电商企业代码、电商企业名称

        invtCancel.setEbpCode(this.info.getEbpCompanyDTO().getCebCode());
        invtCancel.setEbpName(this.info.getEbpCompanyDTO().getCebName());
        invtCancel.setEbcCode(this.info.getEbcCompanyDTO().getCebCode());
        invtCancel.setEbcName(this.info.getEbcCompanyDTO().getCebName());

        //物流运单编号、物流企业代码、物流企业名称
        invtCancel.setLogisticsNo(this.info.getCustomsInventoryDto().getLogisticsNo());
        invtCancel.setLogisticsCode(this.info.getLogisticsCompanyDTO().getCebCode());
        invtCancel.setLogisticsName(this.info.getLogisticsCompanyDTO().getCebName());


        //企业内部标识单证的编号 、担保企业编号 TODO:要改成SN
        //如果type为芥舟则用清单sn赋值CopNo
        if (Objects.equals(info.getBusinessType(), BusinessTypeEnums.JIE_ZHOU.getCode()) && !StringUtils.isEmpty(this.info.getCustomsInventoryDto().getSn())) {
            invtCancel.setCopNo(this.info.getCustomsInventoryDto().getSn());
        } else {
            invtCancel.setCopNo(String.valueOf(this.info.getCustomsInventoryCancelInfo().getId()));
        }
        invtCancel.setInvtNo(this.info.getCustomsInventoryDto().getInventoryNo());
        invtCancel.setPreNo(this.info.getCustomsInventoryDto().getPreNo());

        //订购人信息
        invtCancel.setBuyerIdType("1");
        invtCancel.setBuyerIdNumber(this.info.getCustomsInventoryDto().getBuyerIdNumber());
        invtCancel.setBuyerTelephone(this.info.getCustomsInventoryDto().getBuyerTelNumber());
        invtCancel.setBuyerName(this.info.getCustomsInventoryDto().getBuyerName());

        //申报企业代码、名称
        String declareCompanyCode = this.info.getDeclareCompanyDTO().getCebCode();
        String declareCompanyName = this.info.getDeclareCompanyDTO().getCebName();
        invtCancel.setAgentCode(declareCompanyCode);
        invtCancel.setAgentName(declareCompanyName);
        invtCancel.setReason("交易订单取消,买家申请退款");
        return invtCancel;
    }
}
