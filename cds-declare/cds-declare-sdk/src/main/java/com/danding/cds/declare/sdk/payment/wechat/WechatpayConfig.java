package com.danding.cds.declare.sdk.payment.wechat;


import com.danding.cds.declare.sdk.payment.base.BaseConfig;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Data
public class WechatpayConfig extends BaseConfig {

    public WechatpayConfig() {
        setCustomDeclareOrderGateway("https://api.mch.weixin.qq.com/cgi-bin/mch/customs/customdeclareorder");
        setCustomDeclareQueryGateway("https://api.mch.weixin.qq.com/cgi-bin/mch/customs/customdeclarequery");
        setCustomDeclareRestGateway("https://api.mch.weixin.qq.com/cgi-bin/mch/newcustoms/customdeclareredeclare");
    }
}
