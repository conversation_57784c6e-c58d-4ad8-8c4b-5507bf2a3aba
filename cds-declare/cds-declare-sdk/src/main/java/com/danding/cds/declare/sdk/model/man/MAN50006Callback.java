package com.danding.cds.declare.sdk.model.man;

import lombok.Data;

import java.io.Serializable;

/**
 * 非保核放单 过卡回执
 */
@Data
public class MAN50006Callback implements Serializable {

    private ManSign manSign;

    /**
     * 浙电：核放单编号
     * 新浙电：核放单预录入编号
     */
    private String jobFormId;

    /**
     * 老版浙电
     */

    /**
     * 状态 0-未过卡, 1-已过卡
     */
    private String status;

    /**
     * 过卡失败原因
     */
    private String desc;

    /**
     * 新版浙电
     */

    /**
     * 非保税核放单编号
     */
    private String jobFormNo;

    /**
     * 处理结果
     * 0:海关端校验不通过
     * 1:海关端入库成功
     * 2:海关端审核通过
     * 3:海关端审核不通过
     * 4.核放单已过卡
     */
    private String processResult;

    /**
     * 处理意见
     * processResult为0或者3时，该值不为空
     */
    private String processComment;

    /**
     * 过卡时间
     * yyyy-MM-dd HH:mm:ss
     * processResult为4时，该值不为空
     */
    private String passTime;

    /**
     * 海关十位数代码
     */
    private String companyCode;



}
