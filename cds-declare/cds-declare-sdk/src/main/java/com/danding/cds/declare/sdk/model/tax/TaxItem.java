package com.danding.cds.declare.sdk.model.tax;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class TaxItem implements Serializable {

    /**
     * 商品项号,从1开始连续序号
     */
    protected int gnum;

    /**
     * 海关商品编码（10位） HS?
     */
    protected String gcode;

    /**
     * 完税总价格
     */
    protected BigDecimal taxPrice;

    /**
     * 应征关税
     */
    protected BigDecimal customsTax;

    /**
     * 应征增值税
     */
    protected BigDecimal valueAddedTax;

    /**
     * 应征消费税
     */
    protected BigDecimal consumptionTax;
}
