package com.danding.cds.declare.sdk.swxa;

import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.w3c.dom.Document;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.xml.crypto.dsig.*;
import javax.xml.crypto.dsig.dom.DOMSignContext;
import javax.xml.crypto.dsig.keyinfo.KeyInfo;
import javax.xml.crypto.dsig.keyinfo.KeyInfoFactory;
import javax.xml.crypto.dsig.keyinfo.KeyName;
import javax.xml.crypto.dsig.keyinfo.X509Data;
import javax.xml.crypto.dsig.spec.C14NMethodParameterSpec;
import javax.xml.crypto.dsig.spec.TransformParameterSpec;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.*;
import java.security.*;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @author: create by wujinlong
 * @version: v1.0
 * @description: sw.jce.func.test
 * @date:2020/2/11
 */
public class SwxaSignUtil {

    static {
        Security.addProvider(new BouncyCastleProvider());
    }

    private static final Logger logger = LoggerFactory.getLogger(SwxaSignUtil.class);

    public static byte[] signXml(byte[] xmldata, X509Certificate x509cert, int index) {
        try {
            XMLSignatureFactory fac = XMLSignatureFactory.getInstance("DOM", "XMLDSig");

            Reference ref = fac.newReference("", fac.newDigestMethod(DigestMethod.SHA1, null),
                    Collections.singletonList(fac.newTransform(Transform.ENVELOPED, (TransformParameterSpec) null)), null, null);

            SignedInfo si = fac.newSignedInfo(fac.newCanonicalizationMethod(CanonicalizationMethod.INCLUSIVE_WITH_COMMENTS,
                    (C14NMethodParameterSpec) null), fac.newSignatureMethod(SignatureMethod.RSA_SHA1, null),
                    Collections.singletonList(ref));
            // get key from VSM
            KeyPair kp = getKeyPair(index);
            // Create a KeyValue containing the RSA PublicKey/Certificate that was
            // generated
            KeyInfoFactory kif = fac.getKeyInfoFactory();

            X509Data kd = kif.newX509Data(Collections.singletonList(x509cert));
            KeyName keyName = kif.newKeyName(x509cert.getSerialNumber()
                    .toString());
            List<Object> contet = new ArrayList<>();
            contet.add(keyName);
            contet.add(kd);
            // Create a KeyInfo and add the KeyValue to it
            KeyInfo ki = kif.newKeyInfo(contet);
            // ki = kif.newKeyInfo(Collections.singletonList(kd));
            // Instantiate the document to be signed
            DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
            dbf.setNamespaceAware(true);
            // Document doc = dbf.newDocumentBuilder().parse(new
            // FileInputStream(dataFile));
            Document doc = dbf.newDocumentBuilder().parse(new ByteArrayInputStream(xmldata));
            // Create a DOMSignContext and specify the DSA PrivateKey and
            // location of the resulting XMLSignature's parent element
            DOMSignContext dsc = new DOMSignContext(kp.getPrivate(), doc.getDocumentElement());
            dsc.putNamespacePrefix("http://www.w3.org/2000/09/xmldsig#", "ds");
            // Create the XMLSignature (but don't sign it yet)
            XMLSignature signature = fac.newXMLSignature(si, ki);
            // Marshal, generate (and sign) the enveloped signature
            signature.sign(dsc);
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            TransformerFactory tf = TransformerFactory.newInstance();
            Transformer trans = tf.newTransformer();
            trans.transform(new DOMSource(doc), new StreamResult(out));
            return out.toByteArray();
        } catch (Exception e) {
            logger.error("三未信安加签调用异常 ：{}", e.getMessage(), e);
            throw new RuntimeException("三未信安加签调用异常：" + e.getMessage());
        }
    }


    public static String rsaSign(int index, String sourceData) {
        Signature signatue = null;
        byte[] out;
        byte[] dataInput = sourceData.getBytes();
        try {
            String algorithm = "SHA1WithRSA";
            signatue = Signature.getInstance(algorithm, "SwxaJCE");
            KeyPair kp = getKeyPair(index);
            PrivateKey privateKey = kp.getPrivate();
            //签名
            signatue.initSign(privateKey);
            signatue.update(dataInput);
            out = signatue.sign();
            return new BASE64Encoder().encode(out);
        } catch (Exception e) {
            logger.error("三未信安加签调用异常 ：{}", e.getMessage(), e);
            throw new RuntimeException("三未信安加签调用异常：" + e.getMessage());
        }
//        List<String> alg = new ArrayList<String>();
//        alg.add("SHA1WithRSA");
//        alg.add("SHA224WithRSA");
//        alg.add("SHA256WithRSA");
//        alg.add("SHA384WithRSA");
//        alg.add("SHA512WithRSA");
//        alg.add("SHA1/RSA");
//        System.out.println("Source Data : " + new String(dataInput));
//        try {
//            KeyPair kp = getKeyPair(index);
//            PrivateKey privateKey = kp.getPrivate();
//            PublicKey publicKey = kp.getPublic();
//            for(int i=0; i<alg.size(); i++) {
//                System.out.println("Sign Algorithm [ "+alg.get(i)+" ]");
//                signatue = Signature.getInstance(alg.get(i), "SwxaJCE");
//                //签名
//                signatue.initSign(privateKey);
//                signatue.update(dataInput);
//                out = signatue.sign();
//                System.out.println("Sign Value : "+new BASE64Encoder().encode(out));
//                //验签
//                signatue.initVerify(publicKey);
//                signatue.update(dataInput);
//                boolean flag = signatue.verify(out);
//
//                System.out.println("Verify Result: "+flag);
//                System.out.println();
//            }
//        } catch (Exception e) {
//            log.warn("处理异常：{}", e.getMessage(), e);
//        }
//        return "";
    }

    private static KeyPair getKeyPair(int index) throws NoSuchAlgorithmException, NoSuchProviderException {
        KeyPairGenerator kpg = KeyPairGenerator.getInstance("RSA", "SwxaJCE");
        int keyIndex = index << 16;
        kpg.initialize(keyIndex);
        return kpg.genKeyPair();
    }

    public static X509Certificate d2i_X509Cerificate(String certName) {
        ByteArrayOutputStream out = getByteArrayOutputStream(certName);
        if (out == null) {
            return null;
        }
        byte[] cert = out.toByteArray();
        if (cert == null || cert.length <= 1) {
            logger.error("读证书" + certName + "失败");
            return null;
        }
        byte[] derCert = null;
        // 判断是否是BASE64编码
        if (cert[0] != 0x30) {
            BASE64Decoder decoder = new BASE64Decoder();
            try {
                String certstr = new String(cert);
                certstr = certstr.replace("-----BEGIN CERTIFICATE-----", "");
                certstr = certstr.replace("-----END CERTIFICATE-----", "");
                derCert = decoder.decodeBuffer(certstr);
            } catch (IOException e) {
                logger.error("证书编码失败:{}", e.getMessage(), e);
                return null;
            }
        } else {
            derCert = cert;
        }
        if (derCert == null) {
            return null;
        }
        X509Certificate x509cert = null;
        InputStream ins = new ByteArrayInputStream(derCert);
        CertificateFactory cf = null;
        try {
            // todo 这里判断下证书是否是sm2,后缀区分下吧
            if (certName.contains("-sm2") || certName.contains("hzhyds")
                    || certName.contains("jytx-sm2")) {
                cf = CertificateFactory.getInstance("X.509", "BC");
            } else {
                cf = CertificateFactory.getInstance("X.509");
            }
            x509cert = (X509Certificate) cf.generateCertificate(ins);
            if (x509cert != null) {
                return x509cert;
            }
        } catch (Exception e) {
            logger.error("生成证书失败：" + e);
            return null;
        }
        return null;
    }

    private static ByteArrayOutputStream getByteArrayOutputStream(String certName) {
        ByteArrayOutputStream out;
        try {
            // InputStream in = SwxaSignUtil.class.getClassLoader().getResourceAsStream(certName);
            File file = new File(certName);
            FileInputStream in = new FileInputStream(file);
            int len = 0;
            out = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            while ((len = in.read(buffer)) > 0) {
                out.write(buffer, 0, len);
            }
            in.close();
        } catch (IOException e) {
            logger.error("读取证书异常：" + e);
            return null;
        }
        return out;
    }
}
