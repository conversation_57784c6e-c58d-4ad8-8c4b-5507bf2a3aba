package com.danding.cds.declare.sdk.clear.base;

import lombok.Data;

import java.io.Serializable;

@Data
public class CustomsResult implements Serializable {

    /**
     * 动作是否成功
     */
    private Boolean success;

    /**
     * 失败时的错误信息
     */
    private String errorMessage;

    public static CustomsResult success(){
        CustomsResult result = new CustomsResult();
        result.setSuccess(true);
        result.setErrorMessage("");
        return result;
    }

    public static CustomsResult fail(String errorMessage){
        CustomsResult result = new CustomsResult();
        result.setSuccess(false);
        result.setErrorMessage(errorMessage);
        return result;
    }
}
