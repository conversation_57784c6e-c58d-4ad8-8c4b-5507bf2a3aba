package com.danding.cds.declare.sdk.clear.chongqing.builder;


import com.danding.cds.declare.cqport.domain.*;
import com.danding.cds.declare.cqport.internal.CQCustomsConfig;
import com.danding.cds.declare.sdk.model.inventory.CustomsInventoryItemInfo;
import com.danding.cds.declare.sdk.model.inventory.WrapInventoryOrderInfo;
import com.danding.cds.declare.sdk.utils.DateUtil;
import com.danding.cds.declare.sdk.utils.NumberUtil;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CQInventoryBuilder implements Serializable {

    private WrapInventoryOrderInfo info;
    private CQCustomsConfig config;

    public CQInventoryBuilder(CQCustomsConfig config, WrapInventoryOrderInfo info) {
        this.info = info;
        this.config = config;
    }

    public CQZHGMOrderMessage build(){
        return buildCQZHGMOrderMessage();
    }

    private CQZHGMOrderMessage buildCQZHGMOrderMessage(){
        Date currentTime = new Date();
        CQZHGMOrderMessage message = new CQZHGMOrderMessage();
        CQZHGMOrderMessageHead messageHead = new CQZHGMOrderMessageHead();
        messageHead.setMessageType("ORDER_INFO");
        messageHead.setMessageId("CDS_".concat(String.valueOf(currentTime.getTime())));
        if ("DOCP202012151923500001036258".equals(this.info.getCustomsInventoryDto().getOrderNo())
        ){
            messageHead.setActionType("2");
        }else {
            messageHead.setActionType("1");
        }
        messageHead.setMessageTime(DateUtil.formatDateStr(currentTime, DateUtil.DEF_PATTERN));
        //messageHead.setSenderId(this.info.getDeclareCompanyDTO().getCebCode());
        messageHead.setSenderId(this.config.getUserNo());
        messageHead.setReceiverId("CQITC");
        messageHead.setUserNo(this.config.getUserNo());
        messageHead.setPassword(this.config.getPassword());
        message.setMessageHead(messageHead);
        CQZHGMOrderMessageBody messageBody = new CQZHGMOrderMessageBody();
        CQZHGMOrderMessageBodyDTCFlow dTCFlow = new CQZHGMOrderMessageBodyDTCFlow();
        CQZHGMOrderMessageBodyDTCFlowOrderHead orderHead = new CQZHGMOrderMessageBodyDTCFlowOrderHead();
        orderHead.setCUSTOMS_CODE("8013");
        orderHead.setBIZ_TYPE_CODE("I20");
        orderHead.setORIGINAL_ORDER_NO(this.info.getCustomsInventoryDto().getOrderNo());
        orderHead.setESHOP_ENT_CODE(this.info.getEbcCompanyDTO().getCebCode());
        orderHead.setESHOP_ENT_NAME(this.info.getEbcCompanyDTO().getCebName());
        orderHead.setDESP_ARRI_COUNTRY_CODE("142");
        orderHead.setSHIP_TOOL_CODE("Y");
        orderHead.setRECEIVER_ID_NO(this.info.getCustomsInventoryDto().getBuyerIdNumber());
        orderHead.setRECEIVER_NAME(this.info.getCustomsInventoryDto().getBuyerName());
        orderHead.setRECEIVER_ADDRESS(this.info.getCustomsInventoryDto().getConsigneeAddress());
        orderHead.setRECEIVER_TEL(this.info.getCustomsInventoryDto().getBuyerTelNumber());
        orderHead.setPROXY_ENT_CODE("");
        orderHead.setPROXY_ENT_NAME("");
        orderHead.setSORTLINE_ID("SORTLINE03");
        orderHead.setCC_TRADE_CODE(this.info.getInternalAreaCompany().getCebCode());
        orderHead.setCC_TRADE_NAME(this.info.getInternalAreaCompany().getCebName());
        //orderHead.setUNI_ESHOP_ENT_CODE(this.info.getLogisticsCompanyDTO().getCebCode());
        //orderHead.setUNI_ESHOP_ENT_NAME(this.info.getLogisticsCompanyDTO().getCebName());
//        orderHead.setUNI_ESHOP_ENT_CODE("50122604QT");
//        orderHead.setUNI_ESHOP_ENT_NAME("重庆圆通快递有限公司");

//        orderHead.setUNI_ESHOP_ENT_CODE(info.getLogisticsCompanyDTO().getCebCode());
//        orderHead.setUNI_ESHOP_ENT_NAME(info.getLogisticsCompanyDTO().getCebName());
        // todo:20210204过年期间使用申通
        orderHead.setUNI_ESHOP_ENT_CODE("500696082C");
        orderHead.setUNI_ESHOP_ENT_NAME("重庆欧东快递服务有限公司");
        orderHead.setCHECK_TYPE("R");
        //orderHead.setSEND_ENT_CODE(this.info.getDeclareCompanyDTO().getCebCode());
        orderHead.setSEND_ENT_CODE(this.config.getUserNo());

        orderHead.setBUYER_REG_NO(this.info.getCustomsInventoryDto().getBuyerTelNumber());
        orderHead.setBUYER_NAME(this.info.getCustomsInventoryDto().getBuyerName());
        orderHead.setBUYER_ID_TYPE("1");
        orderHead.setBUYER_ID(this.info.getCustomsInventoryDto().getBuyerIdNumber());
        orderHead.setBUYER_TELEPHONE(this.info.getCustomsInventoryDto().getBuyerTelNumber());
        //String.valueOf(customsInventoryItemInfo.getUnitPrice().floatValue()* customsInventoryItemInfo.getCount().floatValue())

        orderHead.setTRANSPORT_FEE(NumberUtil.defaultParsePriceFeng2Yuan(this.info.getCustomsInventoryDto().getFreight()));

        //float _total  = this.info.getCustomsInventoryDto().getInsureAmount().floatValue()+this.info.getCustomsInventoryDto().getFreight().floatValue();
        //orderHead.setACTUAL_PAID(NumberUtil.defaultParsePriceFeng2Yuan(_total));
        orderHead.setINSURED_FEE("0");
        orderHead.setEBP_CODE(this.info.getEbpCompanyDTO().getCebCode());
        orderHead.setEBP_NAME(this.info.getEbpCompanyDTO().getCebName());
        orderHead.setPAY_CODE(this.info.getPayCompanyDTO().getCebCode());
        orderHead.setPAY_NAME(this.info.getPayCompanyDTO().getCebName());
        orderHead.setBATCH_NUMBERS("");
        orderHead.setCONSIGNEE_DISTRICT("");
        orderHead.setNOTE("");
        orderHead.setBILL_NO("");
        orderHead.setTRANSACTION_ID(StringUtils.isEmpty(this.info.getCustomsInventoryDto().getPayTransactionId())?"":
                this.info.getCustomsInventoryDto().getPayTransactionId());
        orderHead.setTRAF_NO("");
        orderHead.setVOYAGE_NO("");
        orderHead.setLOCT_NO("");
        orderHead.setLICENSE_NO("");
        orderHead.setASSURE_CODE(this.info.getAssureCompanyDTO().getCebCode());
        orderHead.setGOODSINFO("");
        orderHead.setORG_CODE("");
        List<CQZHGMOrderMessageBodyDTCFlowOrderDetail> orderDetails = new ArrayList<CQZHGMOrderMessageBodyDTCFlowOrderDetail>();
        List<CustomsInventoryItemInfo> itemList = info.getListCustomsInventoryItemInfo();
        float netWeightTotal = info.getCustomsInventoryDto().getNetWeight().floatValue();
        float grossWeightTotal = info.getCustomsInventoryDto().getGrossWeight().floatValue();
        BigDecimal total = new BigDecimal(0);

        for(int i =0; i < itemList.size(); i++)
        {
            CustomsInventoryItemInfo declareOrderItem = itemList.get(i);
            CQZHGMOrderMessageBodyDTCFlowOrderDetail detailItem = new CQZHGMOrderMessageBodyDTCFlowOrderDetail();
            detailItem.setSKU(declareOrderItem.getItemNo());
            detailItem.setGOODS_SPEC(declareOrderItem.getItemName());
            detailItem.setCURRENCY_CODE("142");
            detailItem.setPRICE(declareOrderItem.getUnitPrice().setScale(4,BigDecimal.ROUND_HALF_UP).toString());
            detailItem.setQTY(String.valueOf(declareOrderItem.getCount()));
            BigDecimal unitPrice = new BigDecimal(declareOrderItem.getUnitPrice().floatValue());
            //detailItem.setGOODS_FEE(NumberUtil.defaultParsePriceFeng2Yuan(declareOrderItem.getUnitPrice().floatValue()*declareOrderItem.getCount()));
            detailItem.setGOODS_FEE(unitPrice.multiply(new BigDecimal(declareOrderItem.getCount())).setScale(4,BigDecimal.ROUND_HALF_UP).toString());

            total = total.add(unitPrice.multiply(new BigDecimal(declareOrderItem.getCount())));
            detailItem.setTAX_FEE("0.00"); // TODO
            detailItem.setCOUNTRY(declareOrderItem.getCountry());
            detailItem.setG_NUM(String.valueOf(i+1));
            detailItem.setUNIT_CODE(declareOrderItem.getUnit());
            detailItem.setNOTE("");
            detailItem.setGOODS_NAME(declareOrderItem.getItemName());
            detailItem.setHS_CODE(declareOrderItem.getHsCode());
            detailItem.setUNIT2(declareOrderItem.getUnit2());
            //detailItem.setQTY2(String.valueOf(declareOrderItem.getSecondCount()));
            detailItem.setQTY2("0");
            detailItem.setTRADE_COUNTRY("");
            orderDetails.add(detailItem);
        }

        orderHead.setGOODS_FEE(total.setScale(4,BigDecimal.ROUND_HALF_UP).toString());

        total = total.add(this.info.getCustomsInventoryDto().getInsureAmount()==null?new BigDecimal(0):this.info.getCustomsInventoryDto().getInsureAmount());
        total = total.add(this.info.getCustomsInventoryDto().getFreight()==null?new BigDecimal(0):this.info.getCustomsInventoryDto().getFreight());
        BigDecimal taxFee = this.info.getCustomsInventoryDto().getTaxFee()==null?new BigDecimal(0):this.info.getCustomsInventoryDto().getTaxFee();
        total = total.add(taxFee);
        BigDecimal discountFee = this.info.getCustomsInventoryDto().getDiscountFee()==null?new BigDecimal(0):this.info.getCustomsInventoryDto().getDiscountFee();
        total = total.subtract(discountFee);
        //if(total.longValue()<0)
        //orderHead.setACTUAL_PAID(NumberUtil.defaultParsePriceFeng2Yuan(total));
        orderHead.setACTUAL_PAID(total.setScale(4, BigDecimal.ROUND_HALF_UP).toString());
        if (netWeightTotal == 0) {
            netWeightTotal = 1;
        }
        orderHead.setDISCOUNT(discountFee.setScale(4,BigDecimal.ROUND_HALF_UP).toString());
//		if (grossWeightTotal < netWeightTotal) {
//			grossWeightTotal = netWeightTotal;
//		}
        //orderHead.setNET_WEIGHT(NumberUtil.defaultParseWeightG2KG(netWeightTotal));

		/*
		if (grossWeightTotal == netWeightTotal) {
			orderHead.setGROSS_WEIGHT(new BigDecimal(orderHead.getNET_WEIGHT()).divide(new BigDecimal("0.85"), 3, RoundingMode.FLOOR).toString()); // 0.85为净重和毛重的大致比例
		} else {
			orderHead.setGROSS_WEIGHT(new BigDecimal(grossWeightTotal).setScale(4, BigDecimal.ROUND_HALF_UP).toString());
		}
		*/
        BigDecimal _netWeightTotal = new BigDecimal(netWeightTotal);
        BigDecimal _grossWeightTotal=  new BigDecimal(grossWeightTotal);
        if(_netWeightTotal.compareTo(_grossWeightTotal)!=-1)
        {
            _grossWeightTotal = _netWeightTotal.add(new BigDecimal(0.01));
        }
        orderHead.setNET_WEIGHT(_netWeightTotal.setScale(4, BigDecimal.ROUND_HALF_UP).toString());
        orderHead.setGROSS_WEIGHT(_grossWeightTotal.setScale(4, BigDecimal.ROUND_HALF_UP).toString());
        orderHead.setTAX_FEE(taxFee.setScale(4,BigDecimal.ROUND_HALF_UP).toString()); // TODO
        orderHead.setOrderDetails(orderDetails);
        dTCFlow.setOrderHead(orderHead);
        messageBody.setDTCFlow(dTCFlow);
        message.setMessageBody(messageBody);
        return message;
    }
}
