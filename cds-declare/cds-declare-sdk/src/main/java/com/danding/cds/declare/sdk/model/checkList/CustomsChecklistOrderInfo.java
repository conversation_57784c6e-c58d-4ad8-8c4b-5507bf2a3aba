package com.danding.cds.declare.sdk.model.checkList;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class CustomsChecklistOrderInfo implements Serializable {

    private static final long serialVersionUID = -5052921526172240309L;
    /**
     * 核放单号（系统内）
     */
    private String checklistOrderNo;
    /**
     * 核放单编号
     */
    private String realChecklistOrderNo;
    /**
     * 预录入编号
     */
    private String preChecklistOrderNo;
    /**
     * 报关单号
     */
    private String declareOrderNo;
    /**
     * IC卡
     */
    private String vehicleIcNo;
    /**
     * 车牌号
     */
    private String licensePlate;
    /**
     * 车自重
     */
    private String vehicleWeight;
    /**
     * 车架号
     */
    private String vehicleFrameNo;
    /**
     * 车架重
     */
    private String vehicleFrameWeight;
    /**
     * 申请人
     */
    private String applicant;
    /**
     * 是否需要称重（1：需要称重，0:不要称重）
     */
    private String weightRequired;
    /**
     * 是否开启子母单校验(1:开启，0：不开启)
     */
    private String enableCPOrderValid;
    /**
     * 进出区标志
     */
    private String checklistType;
    /**
     * 1：先入区后报关(不支持)，2.一线一体化进出区，3：二线进出区，4：非报关进出区(不支持)，5：卡口登记货物(不支持)，6：空车进出区
     */
    private String passPortTypeCd;
    /**
     * 1：一车多票（一车必须将多票货物全部拉完），2：一票一车（一车必须将一票货物全部拉完），3：一票多车（一票货物可以分多车拉）
     * 卡口登记货物、空车进出区类型的核放单不可填写，其他类型为必填。
     */
    private String bindTypeCd;
    /**
     * 1：核注清单，2：出入库单，3：提运单。一个核放单只能对应一种类型的单证，不允许多种类型单证拼车。
     */
    private String rltTbTypeCd;
    /**
     * 总毛重
     */
    private String totalGrossWt;
    /**
     * 总净重
     */
    private String totalNetWt;
    /**
     * 总重量
     */
    private BigDecimal totalWt;

    private String remark;
    /**
     * 集装箱箱型
     */
    private String containerType;

    /**
     * 集装箱重（千克）
     */
    private BigDecimal containerWt;
}
