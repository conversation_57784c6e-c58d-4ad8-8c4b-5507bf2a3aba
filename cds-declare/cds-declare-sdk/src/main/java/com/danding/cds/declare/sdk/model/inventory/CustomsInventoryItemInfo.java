package com.danding.cds.declare.sdk.model.inventory;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Setter
public class CustomsInventoryItemInfo implements Serializable {
    private Long id;
    /**
     * 企业商品货号
     */
    private String itemNo;
    /**
     * 企业商品品名
     */
    private String itemName;
    /**
     * 数量
     */
    private Integer count;
    /**
     * 单价
     */
    private BigDecimal unitPrice;

    //------------- 这边字段是王响平根据调试后续增加的

    /**
     * 账册备案物料编码
     */
    private String itemRecordNo;
    /**
     * 备案物料型号
     */
    private String gmodle;
    /**
     * hs 编码
     */
    private String hsCode;
    /**
     * 源产国
     */
    private String country;
    /**
     * 第一法定数量
     */
    private String firstCount="1";
    /**
     * 单位
     */
    private String  unit;
    /**
     * 法定计量单位
     */
    private String unit1;
    /**
     * 第二计量单位
     */
    private String unit2;
    /**
     * 第二法定数量
     */
    private String secondCount="1";
    /**
     * 单位重量
     */
    private Float weight;
    /**
     * 条形码
     */
    private String barCode;
    /**
     * 天津
     */
    private String goodsRegNo;
    private String declIINo;
    private String ioGoodsSerialNo;
    private String originCountryCode;

    /**
     * 退货序号
     */
    private Integer gnum;
}