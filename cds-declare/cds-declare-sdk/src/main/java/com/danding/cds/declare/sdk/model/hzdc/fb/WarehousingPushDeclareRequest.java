package com.danding.cds.declare.sdk.model.hzdc.fb;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 非保出入库单推送申请
 */
@Data
@NoArgsConstructor
public class WarehousingPushDeclareRequest implements Serializable {
    private String messageId; // 消息id,uuid (必填)
    private BoStockInOut head; // 出入库单信息表头 (必填)
    private List<BoStockInOutDetail> details; // 出入库单明细 (必填)

    @Data
    @NoArgsConstructor
    public static class BoStockInOut implements Serializable {
        private String inOutSeq; // 出入库单号 (非必填)
        private String regulatorySites; // 监管场所 (必填)
        private String customsCode; // 关区代码 (必填)
        private String companyName; // 企业名称 (必填)
        private String companyCode; // 企业海关编码 (必填)
        private String jobFormId; // 核放单编号,出入库类型为正常进库(非保税入区)、正常出库(非保税出区)、残次品出库时必填 (非必填)
        private String manualId; // 账册编号 (必填)
        private int inOutFlag; // 出入库类型（参见2.7所定义） (必填)
        private int manualType; // 账册类型（1-非保物流账册，2-非保一般纳税人账册，一期只有1） (必填)
        private Integer businessType; // 业务类型（参见2.5所定义）进行保税转非保业务时必填，businessType=5，且inOutFlag=1；当进行非保转保税业务时必填businessType=6，且inOutFlag=5 (非必填)
        private String inOutTime; // 申请时间yyyy-MM-dd HH:mm:ss (必填)
        private String remark; // 备注 (非必填)
    }

    @Data
    @NoArgsConstructor
    public static class BoStockInOutDetail implements Serializable {
        private String sourceNo; // 料号 (必填)
        private String goodsNo; // 商品编码 (必填)
        private String itemNo; // 账册项号 (必填)
        private BigDecimal declareQuantity; // 数量 (必填)
        private String declareUnit; // 数量单位 (必填)
        private int itemType; // 料件性质（参见2.3所定义）
    }
}
