package com.danding.cds.declare.sdk.model.checkList;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Create 2021/4/26  11:18
 * @Describe
 **/
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class Icp101MessageRequest extends MessageRequest implements Serializable {

    /**
     * 申报标志
     *  0--暂存
     *  1--申报
     */
    private Integer delcareFlag = 1;

    /**
     * 核放单申报信息
     */
    private CustomsChecklistOrderInfo customsChecklistOrderInfo;
}
