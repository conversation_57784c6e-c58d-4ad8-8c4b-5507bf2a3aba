package com.danding.cds.declare.sdk.model.payment;

import com.danding.cds.declare.sdk.model.WrapBeanInfo;
import com.umf.api.payments.SubOrder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class WrapPaymentInfo extends WrapBeanInfo {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 报关流水号
     */
    private String outRequestNo;

    private String outOrderNo;

    private String outTradeNo;

    /**
     * 子订单ID
     */
    private String merSubReferenceId;
    /**
     * 子订单商品ID
     */
    private String merItemId;
    /**
     * 海关
     */
    private String customs;

    /**
     * 商户编码
     */
    private String merchantCode;

    /**
     * 	商户海关备案编号
     */
    private String recpAccount;

    /**
     * 支付渠道
     */
    private String channel;

    /**
     * 支付方式
     */
    private String payType;

    /**
     * 第三方支付流水号
     */
    private String tradePayNo;

    /**
     * 商户海关备案号
     */
    private String merchantCustomsCode;

    /**
     * 商户海关备案名称
     */
    private String merchantCustomsName;

    /**
     * 总价格
     */
    private BigDecimal amount;

    /**
     * 物流费
     */
    private BigDecimal transportFee;

    /**
     * 商品费用
     */
    private BigDecimal commodityFee;

    /**
     * 订购人姓名
     */
    private String buyerName;

    /**
     * 订购人身份证号
     */
    private String buyerIdNo;

    /**
     * 订购人证件类别
     */
    private String buyerIdType;

    /**
     * 是否拆单标记
     */
    private Boolean splitFlag = true;

    /**
     * 支付申报单状态
     */
    private Integer status;

    private String tokenJson;
    /**
     * 操作类型
     * add 新增
     * edit 修改重推
     * delete 取消推送
     */
    private String action = "add";

    @Override
    public String getDeclareNos() {
        return this.declareOrderNo;
    }
    /**
     * 税费
     */
    private BigDecimal TaxFee;

    /**
     * 申报单号
     */
    String declareOrderNo;

    /**
     * 商品名称
     */
    String goodsName;

    /**
     * 商品数量
     */
    Integer goodsCount;

    /**
     * 收件人电话
     */
    private String buyerPhoneNumber;

    /**
     * 子订单
     */
    private SubOrder subOrder;

    /**
     * 返回报文
     */
    private String extra;

    /**
     * 支付流水号关联申报单号
     * 通联拆单申报
     */
    private List<String> relDeclareOrderNoList;
}
