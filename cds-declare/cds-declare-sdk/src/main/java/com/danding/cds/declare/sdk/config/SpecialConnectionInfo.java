package com.danding.cds.declare.sdk.config;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @program: cds-center
 * @description: 特殊监管区域连接配置
 * @author: 潘本乐（Belep）
 * @create: 2022-06-12 21:40
 **/
@Data
@NoArgsConstructor
public class SpecialConnectionInfo implements Serializable {
    /**
     * 连接信息编码，区分不同的连接方式
     */
    private String code;
    private String host;
    private String port;
    private String userName;
    private String password;

    public SpecialConnectionInfo(String host, String port, String userName, String password) {
        this.host = host;
        this.port = port;
        this.userName = userName;
        this.password = password;
    }
}
