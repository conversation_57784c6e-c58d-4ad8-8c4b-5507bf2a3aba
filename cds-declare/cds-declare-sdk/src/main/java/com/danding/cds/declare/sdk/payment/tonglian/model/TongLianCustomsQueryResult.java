package com.danding.cds.declare.sdk.payment.tonglian.model;

import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(propOrder = {"head", "body"})
@XmlRootElement(name = "PAYMENT_INFO")
public class TongLianCustomsQueryResult implements Serializable {

    @XmlElement(name = "HEAD")
    private Head head;

    @XmlElement(name = "BODY")
    private Body body;

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(propOrder = {"version", "transDatetime", "charset", "signType", "signMsg", "paymentOrderNo"})
    @XmlRootElement(name = "HEAD")
    public static class Head implements Serializable {
        /**
         * 版本号 v5.6
         */
        @XmlElement(name = "VERSION")
        private String version;
        /**
         * 发送时间 yyyyMMddHH24mmss
         */
        @XmlElement(name = "TRANS_DATETIME")
        private String transDatetime;
        /**
         * 字符集 默认值1，UTF-8
         */
        @XmlElement(name = "CHARSET")
        private String charset;
        /**
         * 签名方式 默认值1,MD5签名
         */
        @XmlElement(name = "SIGN_TYPE")
        private String signType;
        /**
         * 签名密文
         */
        @XmlElement(name = "SIGN_MSG")
        private String signMsg;
        /**
         * 支付流水号 支付系统订单号
         */
        @XmlElement(name = "PAYMENT_ORDER_NO")
        private String paymentOrderNo;
    }


    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement(name = "BODY")
    @XmlType(propOrder = {"returnCode", "returnMsg", "sendStatus", "sendDatetime", "insReceiptStatus",
            "insReceiptTime","customsReceiptStatus", "customsReceiptTime"})
    @Data
    public static class Body implements Serializable {

        /**
         * 应答码
         * 成功应答’0000’，其他为失败应答.报关信息见下方字段
         */
        @XmlElement(name = "RETURN_CODE")
        private String returnCode;
        /**
         * 应答描述
         * 对失败应答的简单描述，应答码为’0000’时留空
         */
        @XmlElement(name = "RETURN_MSG")
        private String returnMsg;
        /**
         * 发送状态 0-待发送 1-发送成功 2-发送失败
         */
        @XmlElement(name = "SEND_STATUS")
        private String sendStatus;
        /**
         * 发送时间 时间格式 yyyyMMddHH24mmss
         */
        @XmlElement(name = "SEND_DATETIME")
        private String sendDatetime;
        /**
         * 平台入库回执状态 0-入库失败 1-入库成功 2-处理中
         */
        @XmlElement(name = "INS_RECEIPT_STATUS")
        private String insReceiptStatus;
        /**
         * 平台入库回执时间 时间格式 yyyyMMddHH24mmss
         */
        @XmlElement(name = "INS_RECEIPT_TIME")
        private String insReceiptTime;
        /**
         * 海关入库回执状态 0-申报失败 1-三单对碰成功 2-申报成功
         */
        @XmlElement(name = "CUSTOMS_RECEIPT_STATUS")
        private String customsReceiptStatus;
        /**
         * 海关入库回执时间 时间格式 yyyyMMddHH24mmss
         */
        @XmlElement(name = "CUSTOMS_RECEIPT_TIME")
        private String customsReceiptTime;

    }

}
