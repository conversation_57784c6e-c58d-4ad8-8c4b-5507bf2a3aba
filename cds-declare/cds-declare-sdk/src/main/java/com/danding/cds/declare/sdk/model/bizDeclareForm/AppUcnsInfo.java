package com.danding.cds.declare.sdk.model.bizDeclareForm;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class AppUcnsInfo {

    /**
     * 申报表预录入编号，详情同表头相关说明。
     */
    private String seqNo;
    /**
     * 申报表编号，备案时为空，变更时填写。
     */
    private String sasDclNo;
    /**
     * 成品备案序号，简单加工和外发加工需要填写单耗信息，须为申报表中料件成品标志为成品的申报序号，业务主建。
     */
    private BigDecimal endprdSeqNo;
    /**
     * 料件备案序号，简单加工和外发加工需要填写单耗信息，须为申报表中料件成品标志为料件的申报序号，业务主建。
     */
    private BigDecimal mtpckSeqNo;
    /**
     * 净耗数量，在相应业务场景下必填且允许变更。
     */
    private BigDecimal netUseupQty;
    /**
     * 损耗率，取值范围为大于等于0，小于1，在相应业务场景下必填且允许变更。
     */
    private BigDecimal lossRate;
    /**
     * 修改标志代码，目前固定取值为3-增加，在相应业务场景下必填且允许变更。
     */
    private String modfMarkCd;
    /**
     * 备用字段1，非必填字段，可按需使用。
     */
    private String col1;
    /**
     * 备用字段2，非必填字段，可按需使用。
     */
    private java.util.Date col2;
    /**
     * 备用字段3，非必填字段，可按需使用。
     */
    private BigDecimal col3;
    /**
     * 备用字段4，非必填字段，可按需使用。
     */
    private String col4;
}
