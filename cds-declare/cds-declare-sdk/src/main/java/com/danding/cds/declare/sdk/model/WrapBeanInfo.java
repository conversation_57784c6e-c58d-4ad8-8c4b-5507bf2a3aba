package com.danding.cds.declare.sdk.model;

import com.danding.cds.common.enums.DeclareEnum;
import com.danding.cds.declare.sdk.model.account.AccountBookDto;
import com.danding.cds.declare.sdk.model.company.CompanyDeclareConfigDto;
import com.danding.cds.declare.sdk.model.company.CompanyInfo;
import com.danding.cds.declare.sdk.model.route.RouteDeclareConfig;
import com.danding.cds.declare.sdk.model.route.RouteExtraDto;
import com.danding.cds.declare.sdk.model.route.RouteInfo;
import com.danding.cds.order.api.dto.OrderSensitivePlainText;
import lombok.Getter;
import lombok.Setter;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Getter
@Setter
public abstract class WrapBeanInfo implements Serializable {
    public WrapBeanInfo() {
    }

    public WrapBeanInfo(CompanyInfo ebpCompanyDTO, CompanyInfo ebcCompanyDTO, CompanyInfo declareCompanyDTO, CompanyInfo logisticsCompanyDTO) {
        this.ebpCompanyDTO = ebpCompanyDTO;
        this.ebcCompanyDTO = ebcCompanyDTO;
        this.declareCompanyDTO = declareCompanyDTO;
        this.logisticsCompanyDTO = logisticsCompanyDTO;
    }

    /**
     * 申报次数
     */
    protected Integer declareTimes;

    /**
     * 申报主表ID
     */
    protected Long mainOrderId;

    /**
     * 申报主表SN
     */
    protected String mainOrderSn;

    /**
     * 申报渠道 - 申报单流入渠道（1:出入库系统 2:pangu 3:京东 4:老支付系统）
     */
    protected Integer mainChannel;

    /**
     * 电商平台
     */
    protected CompanyInfo ebpCompanyDTO;
    /**
     * 电商企业
     */
    protected CompanyInfo ebcCompanyDTO;

    /**
     * 申报单公司
     */
    protected CompanyInfo declareCompanyDTO;
    /**
     * 物流企业
     */
    protected CompanyInfo logisticsCompanyDTO;
    /**
     * 路径
     */
    protected RouteInfo routeInfo;

    /**
     * 账册信息
     */
    protected AccountBookDto accountBookDto;

    /**
     * dxpId
     */
    protected String dxpId;


    /**
     * 区别芥舟和普通的赋值类型
     */
    protected String businessType;

    protected OrderSensitivePlainText plainText;


    /**
     * 获取申报公司的十位总署编码
     *
     * @return 十位总署编码
     */
    public String getDeclareCompanyCebCode() {
        return declareCompanyDTO == null ? "" : declareCompanyDTO.getCebCode();
    }

    /**
     * 申报方式获取
     *
     * @return
     */
    public String getDeclareWay() {
        return routeInfo == null ? "" : routeInfo.getDeclareWay();
    }

    /**
     * 能否动态申报
     * 根据最细话的申报配置灵活切换
     *
     * @return true/false
     */
    public boolean dynamicDeclareEnable() {
        return routeInfo != null && Objects.equals(routeInfo.getDeclareWay(), "auto");
    }

    /**
     * 获取代理申报配置
     *
     * @param declareEnum 申报类型
     * @return 申报的实现方式
     */
    public String getProxyDeclareConfig(DeclareEnum declareEnum) {

        RouteDeclareConfig routeDeclareConfig = getRouteDeclareHighConfig(declareEnum);
        return Optional.ofNullable(routeDeclareConfig).map(RouteDeclareConfig::getProxyImpl).orElse(null);
    }

    /**
     * 获取路径中申报高级配置
     *
     * @param declareEnum 申报类型
     * @return
     */
    public RouteDeclareConfig getRouteDeclareHighConfig(DeclareEnum declareEnum) {
        if (routeInfo == null) {
            return null;
        }
        List<RouteDeclareConfig> routeDeclareConfigList = routeInfo.getRouteDeclareConfigList();
        if (CollectionUtils.isEmpty(routeDeclareConfigList)) {
            return null;
        }
        return routeDeclareConfigList.stream()
                .filter(z -> Objects.equals(z.getType(), declareEnum.getType()))
                .findFirst().orElse(null);
    }

    /**
     * 获取申报企业设置的申报配置
     *
     * @param declareEnum 申报类型
     * @return 申报实现名称
     */
    public String getDeclareCompanyDeclareImpl(DeclareEnum declareEnum) {
        if (declareCompanyDTO == null) {
            return null;
        }
        List<CompanyDeclareConfigDto> declareConfigList = declareCompanyDTO.getDeclareConfigList();
        if (CollectionUtils.isEmpty(declareConfigList)) {
            return null;
        }
        return declareConfigList.stream()
                .filter(z -> Objects.equals(z.getType(), declareEnum.getType()))
                .map(CompanyDeclareConfigDto::getDeclareImpl)
                .filter(Objects::nonNull)
                .findFirst().orElse(null);
    }

    /**
     * 获取申报单号
     *
     * @return 申报单号
     */
    public abstract String getDeclareNos();

    /**
     * 获取账册关区代码
     * eg. 2924/2925
     *
     * @return 关区代码
     */
    public String getCustomsAreaCode() {
        return accountBookDto == null ? null : accountBookDto.getCustomsAreaCode();
    }

    /**
     * 获取账册ID
     *
     * @return
     */
    public Long getCustomsBookId() {
        if (accountBookDto != null) {
            return accountBookDto.getId();
        }
        return Optional.ofNullable(routeInfo).map(RouteInfo::getRouteExtraDto).map(RouteExtraDto::getCustomsBookId).orElse(null);
    }
}
