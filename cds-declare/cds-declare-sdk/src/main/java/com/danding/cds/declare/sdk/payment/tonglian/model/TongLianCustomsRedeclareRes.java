package com.danding.cds.declare.sdk.payment.tonglian.model;

import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(propOrder = {"head", "body"})
@XmlRootElement(name = "PAYMENT_INFO")
public class TongLianCustomsRedeclareRes implements Serializable {

    @XmlElement(name = "HEAD")
    private TongLianCustomsReDeclareXmlHead head;

    @XmlElement(name = "BODY")
    private Body body;


    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(propOrder = {"returnCode", "returnMsg"})
    @XmlRootElement(name = "BODY")
    @Data
    public static class Body implements Serializable {

        /**
         * 应答码
         * 成功应答’0000’，其他为失败应答.报关信息见下方字段
         */
        @XmlElement(name = "RETURN_CODE")
        private String returnCode;
        /**
         * 应答描述
         * 对失败应答的简单描述，应答码为’0000’时留空
         */
        @XmlElement(name = "RETURN_MSG")
        private String returnMsg;
    }

}
