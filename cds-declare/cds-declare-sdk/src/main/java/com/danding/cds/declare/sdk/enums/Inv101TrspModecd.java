package com.danding.cds.declare.sdk.enums;

import org.apache.commons.lang3.StringUtils;

public enum Inv101TrspModecd {
    NULL("","",""),
    WAY_WATER("2","WATER","水路运输"),
    WAY_RAIL("3","RAIL","铁路运输"),
    WAY_ROAD("4","ROAD","公路运输"),
    WAY_AIR("5","AIR","航空运输"),
    WAY_OTHER("9","OTHER","其他运输");
    private String key;

    private String value;

    private String desc;

    Inv101TrspModecd(String key,String value, String desc) {
        this.key = key;
        this.value = value;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static Inv101TrspModecd getEnum(String value){
        if(StringUtils.isEmpty(value)){
            return NULL;
        }
        for (Inv101TrspModecd orderStatus : Inv101TrspModecd.values()) {
            if (orderStatus.getValue().equals(value)){
                return orderStatus;
            }
        }
        return NULL;
    }
}
