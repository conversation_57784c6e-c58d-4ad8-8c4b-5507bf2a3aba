package com.danding.cds.declare.sdk.model.cancel;

import com.danding.cds.declare.sdk.model.WrapBeanInfo;
import com.danding.cds.declare.sdk.model.company.CompanyInfo;
import com.danding.cds.declare.sdk.model.inventory.CustomsInventoryInfo;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class WarpCancelOrderInfo  extends WrapBeanInfo {
    protected CustomsInventoryCancelInfo customsInventoryCancelInfo;

    public WarpCancelOrderInfo() {
    }

    public WarpCancelOrderInfo(
                                CustomsInventoryCancelInfo customsInventoryCancelInfo,
                               CustomsInventoryInfo customsInventoryDto,
                               CompanyInfo internalAreaCompany,
                               CompanyInfo logisticsCompanyDTO,
                               CompanyInfo ebpCompanyDTO,
                               CompanyInfo ebcCompanyDTO,
                               CompanyInfo declareCompanyDTO
    )
    {
        super(ebpCompanyDTO,ebcCompanyDTO,declareCompanyDTO,logisticsCompanyDTO);
        this.customsInventoryCancelInfo = customsInventoryCancelInfo;
        this.customsInventoryDto = customsInventoryDto;
        this.internalAreaCompany = internalAreaCompany;
        this.logisticsCompanyDTO = logisticsCompanyDTO;
    }
    /**
     * 相关清单对象信息
     */
    protected CustomsInventoryInfo customsInventoryDto;


    /**
     * 区内企业
     */
    protected CompanyInfo internalAreaCompany ;

    /**
     * 物流公司
     */
    protected CompanyInfo logisticsCompanyDTO;
    /**
     * 担保企业
     */
    protected CompanyInfo assureCompanyDTO;


    @Override
    public String getDeclareNos() {
        return customsInventoryDto == null ? "" : customsInventoryDto.getOrderNo();
    }
}
