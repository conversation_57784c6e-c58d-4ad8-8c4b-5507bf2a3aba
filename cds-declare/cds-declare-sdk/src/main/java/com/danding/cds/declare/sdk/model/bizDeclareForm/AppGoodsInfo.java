package com.danding.cds.declare.sdk.model.bizDeclareForm;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class AppGoodsInfo {

    /**
     * 申报表预录入编号，详情同表头相关说明
     */
    private String seqNo;
    /**
     * 申报表编号，备案时为空，变更时填写
     */
    private String sasDclNo;
    /**
     * 申报序号，从1开始，按顺序填写
     */
    private BigDecimal sasDclSeqNo;
    /**
     * 底账商品序号，有以下填写规则：
     * 1、分送集报、保税展示交易、简单加工且申报表料件成品标志为“料件”时该字段可录入，其他情况该字段不可录入。
     * 2、分送集报、保税展示交易：
     * 1） 账册类型为加工账册（单耗、工单）的该字段必填；
     * 2） 货物流向为出区的物流账册的该字段必填；
     * 3、简单加工:
     * 1)申报表料件成品标志为料件的简单加工该字段必填。
     * 4、如果填写该字段，则必须从底账信息中调取到商品信息，否则该字段必须为空。
     * 5、该字段录入调出有关底账数据后，该字段不允许再修改。
     */
    private BigDecimal oriActGdsSeqNo;
    /**
     * 料件成品标志代码，I-料件/半成品、E-成品/残次品，含义：对应申报表单耗表体的料件成品标志。外发加工、简单加工专用，外发加工包含以上3种，简单加工只包含I/E; 其他业务类型必须为I。
     */
    private String mtpckEndprdTypeCd;
    /**
     * 商品编码，如果底账商品序号不为空，则从账册反填；否则根据实际情况填写
     */
    private String gdeCd;
    /**
     * 商品名称，如果底账商品序号不为空，则从账册反填；否则根据实际情况填写
     */
    private String gdsNm;
    /**
     * 商品规格型号描述，如果底账商品序号不为空，则从账册反填；否则根据实际情况填写
     */
    private String gdsSpcfModelDesc;
    /**
     * 数量
     */
    private BigDecimal dclQty;
    /**
     * 申报计量单位代码，如果底账商品序号不为空，则从账册反填；否则根据实际情况填写
     */
    private String dclUnitCd;
    /**
     * 单价
     */
    private BigDecimal dclUprcAmt;
    /**
     * 总价
     */
    private BigDecimal dclTotalAmt;
    /**
     * 币制代码，如果底账商品序号不为空，则从账册反填；否则根据实际情况填写
     */
    private String dclCurrCd;
    /**
     * 许可证编号
     */
    private String licenceNo;
    /**
     * 许可证有效期，格式为YYYYMMDD
     */
    private Date licenceValidTime;
    /**
     * 商品标记代码，0-非重点商品 1-重点商品
     */
    private String gdsMarkCd;
    /**
     * 商品备注
     */
    private String gdsRmk;
    /**
     * 修改标志代码，3-增加
     */
    private String modfMarkCd;
    /**
     * 备注
     */
    private String rmk;
    /**
     * 商品料号，如果底账商品序号不为空，则从账册反填；否则根据实际情况填写
     */
    private String gdsMtNo;
    /**
     * 法定计量单位代码，如果底账商品序号不为空，则从账册反填；如果为空，根据录入的商品编码进行反填
     */
    private String lawfUnitCd;
    /**
     * 法定第二计量代码，如果底账商品序号不为空，则从账册反填；如果为空，根据录入的商品编码进行反填
     */
    private String secdLawfUnitCd;
    /**
     * 重点商品标识，0-非重点商品、1-目录重点商品。简单加工的必填，其他类型不允许填写。
     */
    private String col1;
    /**
     * 国别(地区)，代码详情，请参见“单一窗口”门户网站参数查询，简单加工的必填，其他类型不允许填写。
     */
    private String col2;
    /**
     * 备用字段3
     */
    private BigDecimal col3;
    /**
     * 备用字段4
     */
    private String col4;
    /**
     * 成品类型，1-成品，2-残次品，3-边角料，4-副产品。外发加工专用，默认为空
     */
    private String endprdGdsTypeCd;
}
