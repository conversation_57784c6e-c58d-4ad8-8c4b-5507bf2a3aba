package com.danding.cds.declare.sdk.ie;

import cn.hutool.core.date.DateUtil;
import com.danding.cds.declare.sdk.config.CustomsSpecialToken;
import com.danding.cds.declare.sdk.model.bizDeclareForm.AppGoodsInfo;
import com.danding.cds.declare.sdk.model.bizDeclareForm.AppHeadInfo;
import com.danding.cds.declare.sdk.model.bizDeclareForm.AppUcnsInfo;
import com.danding.cds.declare.sdk.model.bizDeclareForm.Sas101MessageRequest;
import com.danding.cds.declare.zjspecial.domain.KeyInfo;
import com.danding.cds.declare.zjspecial.domain.Signature;
import com.danding.cds.declare.zjspecial.domain.SignedInfo;
import com.danding.cds.declare.zjspecial.domain.base.EnvelopInfo;
import com.danding.cds.declare.zjspecial.domain.sas101.*;
import com.danding.cds.declare.zjspecial.internal.enums.CustomsMessageType;
import com.danding.cds.declare.zjspecial.internal.utils.SpecialUtil;
import com.danding.component.common.rpc.common.utils.BeanUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

public class BizDeclareFormBuilder implements Serializable {
    private CustomsSpecialToken target;

    public Signature build(Sas101MessageRequest req, CustomsSpecialToken specialToken) {
        this.target = specialToken;
        Signature signature = new Signature();
        signature.setSignedInfo(buildSignedInfo());
        signature.setSignatureValue("");
        signature.setKeyInfo(buildKeyInfo());
        signature.setObject(buildObject(req));
        return signature;
    }

    private KeyInfo buildKeyInfo() {
        KeyInfo keyInfo = new KeyInfo();
        keyInfo.setKeyName("aa");
        return keyInfo;
    }

    private SignedInfo buildSignedInfo() {
        SignedInfo signedInfo = new SignedInfo();
        return signedInfo;
    }

    private Signature.Object buildObject(Sas101MessageRequest req) {
        Signature.Object object = new Signature.Object();
        object.setPackage(buildPackage(req));
        return object;
    }

    private Sas101Package buildPackage(Sas101MessageRequest req) {
        Sas101Package _package = new Sas101Package();
        _package.setEnvelopInfo(buildEnvelopInfo(req));
        _package.setDataInfo(buildDataInfo(req));
        return _package;
    }

    private EnvelopInfo buildEnvelopInfo(Sas101MessageRequest req) {
        EnvelopInfo envelopInfo = new EnvelopInfo();
        String uuid = UUID.randomUUID().toString();
        envelopInfo.setBusinessId("businessId-" + uuid);
        envelopInfo.setFileName("fileName-" + uuid);
        envelopInfo.setIcCard(target.getIcCard());//ic卡号
        envelopInfo.setMessageId("messageId-" + uuid);
        envelopInfo.setMessageType(CustomsMessageType.SAS101.name());
        envelopInfo.setReceiverId("DXPEDCSAS0000001");
        envelopInfo.setSenderId(target.getSenderId());
        envelopInfo.setSendTime(SpecialUtil.dateToXmlDate(new Date()));
        envelopInfo.setVersion("1.0");
        return envelopInfo;
    }

    private DataInfo buildDataInfo(Sas101MessageRequest req) {
        DataInfo dataInfo = new DataInfo();
        dataInfo.setBussinessData(buildBussinessData(req));
        return dataInfo;
    }

    private DataInfo.BussinessData buildBussinessData(Sas101MessageRequest req) {
        DataInfo.BussinessData bussinessData = new DataInfo.BussinessData();
        bussinessData.setDelcareFlag(req.getDelcareFlag());
        bussinessData.setAppMessage(buildAppMessage(req));
        return bussinessData;
    }

    private AppMessage buildAppMessage(Sas101MessageRequest req) {
        AppMessage appMessage = new AppMessage();
        appMessage.setAppHead(buildAppHead(req.getAppHeadInfo()));
        appMessage.setAppGoodsList(buildAppGoodsInfos(req.getAppGoodsInfos()));
        appMessage.setAppUcnsList(buildAppUcnsInfos(req.getAppUcnsInfos()));
        appMessage.setOperCusRegCode(target.getCustomsCode());
        return appMessage;
    }

    private AppHead buildAppHead(AppHeadInfo appHeadInfo) {
        AppHead appHead = BeanUtils.copyProperties(appHeadInfo, AppHead.class);
        appHead.setValidTime(DateUtil.format(appHeadInfo.getValidTime(), "yyyyMMdd"));
        return appHead;
    }

    private List<AppGoods> buildAppGoodsInfos(List<AppGoodsInfo> appGoodsInfos) {
        if (appGoodsInfos == null) {
            return null;
        }
        List<AppGoods> appGoodsList = new ArrayList<>();
        for (AppGoodsInfo appGoodsInfo : appGoodsInfos) {
            AppGoods appGoods = BeanUtils.copyProperties(appGoodsInfo, AppGoods.class);
            appGoods.setLicenceVaildTime(DateUtil.format(appGoodsInfo.getLicenceValidTime(), "yyyyMMdd"));
            appGoodsList.add(appGoods);
        }
        return appGoodsList;
    }

    private List<AppUcns> buildAppUcnsInfos(List<AppUcnsInfo> appUcnsInfos) {
        if (appUcnsInfos == null) {
            return null;
        }
        List<AppUcns> appUcnsList = new ArrayList<>();
        for (AppUcnsInfo appUcnsInfo : appUcnsInfos) {
            AppUcns appUcns = BeanUtils.copyProperties(appUcnsInfo, AppUcns.class);
            appUcnsList.add(appUcns);
        }
        return appUcnsList;
    }


}
