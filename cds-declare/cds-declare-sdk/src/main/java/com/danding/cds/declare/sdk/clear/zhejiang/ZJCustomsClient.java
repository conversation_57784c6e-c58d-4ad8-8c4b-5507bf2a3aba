package com.danding.cds.declare.sdk.clear.zhejiang;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.cds.common.config.EnvironmentConfig;
import com.danding.cds.common.enums.TrackLogConstantMixAll;
import com.danding.cds.common.model.MapExt;
import com.danding.cds.common.utils.FormatUtil;
import com.danding.cds.customs.inventory.api.enums.CustomsStat;
import com.danding.cds.customs.inventory.api.enums.InventoryOrderType;
import com.danding.cds.declare.ceb.domain.ceb311.CEB311Message;
import com.danding.cds.declare.ceb.domain.ceb312.CEB312Message;
import com.danding.cds.declare.ceb.domain.ceb511.CEB511Message;
import com.danding.cds.declare.ceb.domain.ceb512.CEB512Message;
import com.danding.cds.declare.ceb.domain.ceb621.CEB621Message;
import com.danding.cds.declare.ceb.domain.ceb622.CEB622Message;
import com.danding.cds.declare.ceb.domain.ceb623.CEB623Message;
import com.danding.cds.declare.ceb.domain.ceb624.CEB624Message;
import com.danding.cds.declare.ceb.domain.ceb624.InvtCancelReturn;
import com.danding.cds.declare.ceb.domain.ceb625.CEB625Message;
import com.danding.cds.declare.ceb.domain.ceb626.CEB626Message;
import com.danding.cds.declare.ceb.domain.ceb626.InvtRefundReturn;
import com.danding.cds.declare.ceb.domain.ceb816.CEB816Message;
import com.danding.cds.declare.ceb.domain.ceb816.Tax;
import com.danding.cds.declare.ceb.domain.ceb816.TaxListRd;
import com.danding.cds.declare.ceb.domain.ceb818.CEB818Message;
import com.danding.cds.declare.ceb.domain.ceb818.TaxHeadStatus;
import com.danding.cds.declare.ceb.domain.dxpmsg.DxpMsg;
import com.danding.cds.declare.ceb.domain.dxpmsg.ReceiverIds;
import com.danding.cds.declare.ceb.domain.dxpmsg.TransInfo;
import com.danding.cds.declare.ceb.internal.enums.CebMessageV2Enum;
import com.danding.cds.declare.ceb.internal.utils.CebDateUtil;
import com.danding.cds.declare.ceb.internal.utils.XMLUtil;
import com.danding.cds.declare.sdk.CustomsReport;
import com.danding.cds.declare.sdk.CustomsSupport;
import com.danding.cds.declare.sdk.clear.base.CustomsClient;
import com.danding.cds.declare.sdk.clear.base.CustomsClientRegistry;
import com.danding.cds.declare.sdk.clear.base.callback.CallbackResult;
import com.danding.cds.declare.sdk.clear.base.callback.CallbackType;
import com.danding.cds.declare.sdk.clear.base.callback.module.*;
import com.danding.cds.declare.sdk.clear.base.result.*;
import com.danding.cds.declare.sdk.clear.zhejiang.builder.*;
import com.danding.cds.declare.sdk.clear.zhejiang.encry.KeyInfo;
import com.danding.cds.declare.sdk.constant.CustomsDistrictCons;
import com.danding.cds.declare.sdk.enums.CustomsType;
import com.danding.cds.declare.sdk.model.WrapBeanInfo;
import com.danding.cds.declare.sdk.model.cancel.WarpCancelOrderInfo;
import com.danding.cds.declare.sdk.model.check.CustomsDataPayExInfoDTO;
import com.danding.cds.declare.sdk.model.check.CustomsDataSendResult;
import com.danding.cds.declare.sdk.model.check.WrapDataPayExInfo;
import com.danding.cds.declare.sdk.model.inventory.WrapInventoryOrderInfo;
import com.danding.cds.declare.sdk.model.order.WrapOrderDeclareInfo;
import com.danding.cds.declare.sdk.model.refund.WarpRefundOrderInfo;
import com.danding.cds.declare.sdk.model.shipment.WrapShipmentInfo;
import com.danding.cds.declare.sdk.model.tax.TaxItem;
import com.danding.cds.declare.sdk.utils.AesUtil;
import com.danding.cds.declare.sdk.utils.CebSignUtil;
import com.danding.cds.declare.sdk.utils.ChangeDeclareUtil;
import com.danding.cds.declare.sdk.utils.NCUtils;
import com.danding.cds.declare.zjport.domain.base.Head;
import com.danding.cds.declare.zjport.domain.request.Request;
import com.danding.cds.declare.zjport.domain.response.Response;
import com.danding.cds.declare.zjport.domain.response.ResponseBody;
import com.danding.cds.declare.zjport.domain.response.ReturnMessage;
import com.danding.cds.declare.zjport.domain.response.jkfResult.JKFResult;
import com.danding.cds.declare.zjport.domain.response.jkfResult.JKFResultDetail;
import com.danding.cds.declare.zjport.internal.HzPortAESUtil;
import com.danding.cds.declare.zjport.internal.enums.HzPortBusinessType;
import com.danding.cds.declare.zjport.internal.utils.HzPortRSAUtil;
import com.danding.cds.declare.zjport.internal.utils.WebServiceRequest;
import com.danding.cds.log.api.dto.TrackLogDTO;
import com.danding.cds.log.api.enums.TrackLogEnums;
import com.danding.cds.order.api.enums.OrderStatus;
import com.danding.component.http.SaasHttpConstant;
import com.danding.core.tenant.SimpleTenantHelper;
import com.github.kevinsawicki.http.HttpRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class ZJCustomsClient extends CustomsClient {


    public static String interfaceUrl;
    public static Map<String, ZJAgentToken> tokenMap;

    /**
     * 解码密钥
     */
    public static String jmKey;


    public void addToken(ZJAgentToken zjAgentToken) {
        if (tokenMap == null) {
            tokenMap = new HashMap<>();
        }
        tokenMap.put(zjAgentToken.getCode(), zjAgentToken);
    }

    public ZJCustomsClient(CustomsSupport support, String env) {
        super(support, env);
    }

    /**
     * 初始化注入
     *
     * @param support
     * @param registry
     * @param env
     */
    public ZJCustomsClient(CustomsSupport support, CustomsClientRegistry registry, String env) {
        super(support, registry, env);
        if (CustomsSupport.ENV_ONLINE.equals(env)) {
            interfaceUrl = "http://api.kjeport.com";
        } else {
            interfaceUrl = "http://122.224.230.4:18003";
        }
        jmKey = "qZe60QZFxuirub2ey4+7+Q==";
    }

    @Override
    public CustomsType getSystem() {
        return CustomsType.ZHE_JIANG;
    }

    @Override
    public OrderDeclareResult orderDeclare(WrapOrderDeclareInfo declareInfo) {
        CustomsReport report = new CustomsReport(CustomsReport.TYPE_SEND, CustomsReport.SYSTEM_CUSTOMS_CLIENT, CustomsReport.PROCESS_CLEAR_DECLARE_ORDER);
        report.buildBusinessKey(declareInfo.getBusinessKey());
        ZJAgentToken encryInfo = tokenMap.get(declareInfo.getDeclareCompanyDTO().getCebCode());
        if (encryInfo == null) {
            log.info("[op:ZJCustomsClient-orderDeclare]订单申报-浙江电子口岸 找不到对应的口岸密钥");
            throw new RuntimeException("找不到对应的口岸密钥");
        }

        // Step::组装报文
        Request request = new ZJPortOrderBuilder(declareInfo, encryInfo.getMode()).build();

        OrderDeclareResult result = new OrderDeclareResult();
        result.setOrderNo(declareInfo.getDeclareOrderNo());
        result.setSn(declareInfo.getSn());
        result.setStatus(declareInfo.getStatus());
        report.buildProcessData(result);

        String importOrderInfo;
        try {
            importOrderInfo = XMLUtil.convertToXml(request);
            if (StringUtils.isEmpty(importOrderInfo)) {
                log.error("[op:ZJCustomsClient-orderDeclare]订单申报-浙江电子口岸 XML转换数据为空={}", JSON.toJSONString(request));
                throw new RuntimeException("XML转换数据为空");
            }
            log.info("[op:ZJCustomsClient-orderDeclare]订单申报-浙江电子口岸 明文={}", importOrderInfo);
            report.setRequestMsg(importOrderInfo);
        } catch (Exception e) {
            log.info("[op:ZJCustomsClient-orderDeclare]订单申报-浙江电子口岸 报文组装异常, cause={}", e.getMessage(), e);
            throw new RuntimeException("浙江电子口岸报文组装异常", e);
        }
        // Step::调用
        String responseString;
        if ("mock".equals(env)) {
            responseString = this.mockResult();
        } else {
//            String customsCode = this.findCustomsCode(null);
//            // 义乌优诚关区代码特殊处理
//            if ("3318W6K01A".equals(declareInfo.getDeclareCompanyDTO().getCebCode())) {
//                customsCode = "2923";
//            }
            String customsCode = declareInfo.getCustomsAreaCode();
            // 浙江跨境一步达
            responseString = invokeCrossBorderOneStep(encryInfo, importOrderInfo, declareInfo.getDeclareOrderNo(), declareInfo.getDeclareCompanyDTO().getCebCode(), customsCode, HzPortBusinessType.IMPORTORDER, declareInfo);
        }
        log.info("[op:ZJCustomsClient-orderDeclare]订单申报- 浙江电子口岸报文 下发订单申报指令成功 回复报文={}", responseString);
        report.setResponseMsg(responseString);
        support.accept(report);
        if ("ceb".equals(encryInfo.getMode())) {
            this.orderDeclareCeb(declareInfo);
        }
        return result;
    }

    private void orderDeclareCeb(WrapOrderDeclareInfo declareInfo) {
        CustomsReport report = new CustomsReport(CustomsReport.TYPE_SEND, CustomsReport.SYSTEM_CUSTOMS_CLIENT, CustomsReport.PROCESS_CLEAR_DECLARE_ORDER);
        report.buildBusinessKey(declareInfo.getBusinessKey());
        ZJAgentToken encryInfo = tokenMap.get(declareInfo.getDeclareCompanyDTO().getCebCode());
        CEB311Message ceb311Message = new ZJCebOrderBuilder(declareInfo, encryInfo.getCebSignInfo().getDxpId()).build();

        OrderDeclareResult result = new OrderDeclareResult();
        result.setOrderNo(declareInfo.getDeclareOrderNo());
        result.setSn(declareInfo.getSn());
        result.setCustomsAreaCode(declareInfo.getCustomsAreaCode());
        report.buildProcessData(result);

        this.cebSend(report, encryInfo, ceb311Message, "CEB311Message", "订单申报");
        support.accept(report);
    }

    @Override
    public OrderCallback handelOrderMsg(CallbackResult callbackResult) {
        CustomsReport report = new CustomsReport(
                CustomsReport.TYPE_ACCEPT,
                CustomsReport.SYSTEM_CUSTOMS_CLIENT,
                CustomsReport.PROCESS_CLEAR_CALLBACK_ORDER);

        log.info("[op:ZJCustomsClient-handelOrderMsg] CallbackResult={}", JSON.toJSONString(callbackResult));
        JSONObject origMap = JSON.parseObject(callbackResult.getOrigMsg());
        OrderCallback orderCallback = new OrderCallback();
        String msgType = origMap.getString("msgType");
        try {
            if ("CUSTOMS_CEB_CALLBACK".equals(msgType)) {
                // Module::总署回执
                String requestXmlString = origMap.getString("content");
                report.setResponseMsg(requestXmlString);
                requestXmlString = requestXmlString.replace("xmlns=\"http://www.chinaport.gov.cn/ceb\"", "");
                CEB312Message response = XMLUtil.converyToJavaBean(requestXmlString, CEB312Message.class);
                orderCallback.setEbpCode(response.getOrderReturn().getEbpCode());
                orderCallback.setOrderNo(response.getOrderReturn().getOrderNo());
                Date returnDate = DateUtils.parseDate(response.getOrderReturn().getReturnTime(), "yyyyMMddHHmmssSSS");
                orderCallback.setReturnTime(returnDate);
                orderCallback.setReturnStatus(response.getOrderReturn().getReturnStatus());
                orderCallback.setEbcCode(response.getOrderReturn().getEbcCode());
                orderCallback.setReturnInfo(response.getOrderReturn().getReturnInfo());
            } else if ("CUSTOMS_DECLARE_IMPORT_CALLBACK".equals(msgType)) {
                // Module::浙电回执
                String requestXmlString = origMap.getString("content");
                report.setResponseMsg(requestXmlString);
                ReturnMessage response = XMLUtil.converyToJavaBean(requestXmlString, ReturnMessage.class);
                ReturnMessage.Head head = response.getHead();
                List<ReturnMessage.ReturnMsg> returnMsgs = response.getBody().getReturnList().getReturnMsgs();

                // 过滤非失败的和业务单号为空的报文
                List<ReturnMessage.ReturnMsg> collect = returnMsgs.stream()
                        .filter(msg -> !StringUtils.isEmpty(msg.getBusinessNo()))
                        .filter(msg -> !"0".equals(msg.getReturnStatus())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(collect)) {
                    log.warn("浙电回执报文无异常: {}", requestXmlString);
                    // 无异常信息，不处理
                    return orderCallback;
                }
                report.setSender(TrackLogConstantMixAll.ZJ_PORT);
                for (ReturnMessage.ReturnMsg returnMsg : collect) {
                    Date returnDate = DateUtils.parseDate(head.getCreateTime(), "yyyyMMddHHmmss");
                    orderCallback.setOrderNo(returnMsg.getBusinessNo());
                    orderCallback.setEbpCode(head.getCompanyCode());
                    orderCallback.setReturnTime(returnDate);
                    orderCallback.setReturnStatus(CustomsStat.ZJ_PORT_EXCEPTION.getValue());
                    orderCallback.setReturnInfo(returnMsg.getReturnInfo());

                    report.setProcessData(JSON.toJSONString(orderCallback));
                    support.accept(report);
                }
                return orderCallback;
            } else {
                // Module::口岸回执
                String requestXmlString = origMap.getString("content");
                report.setResponseMsg(requestXmlString);
                Response response = XMLUtil.converyToJavaBean(requestXmlString, Response.class);
                List<JKFResult> jkfResultList = response.getBody().getList();
                // Step::当前仅取第一个
                JKFResult jkfResult = jkfResultList.get(0);
                String companyCode = jkfResult.getCompanyCode();
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-ddHH:mm");
                Date portAcceptTime = simpleDateFormat.parse(jkfResult.getNoticeDate().concat(jkfResult.getNoticeTime()));
                orderCallback.setOrderNo(jkfResult.getBusinessNo());
                //这里从生产观察来看 应该是ebcCode
                orderCallback.setEbpCode(companyCode);
                orderCallback.setReturnTime(portAcceptTime);
                orderCallback.setReturnStatus(jkfResult.getChkMark());
                orderCallback.setReturnInfo(NCUtils.getTopOneResultDetail(jkfResult.getJkfResultDetail()));
                log.info("ZJCustomsClient handelOrderMsg 浙电口岸回执暂不处理 callback={}", orderCallback);
                return orderCallback;
            }
            report.setSender(TrackLogConstantMixAll.ZJ_PORT);
            report.setProcessData(JSON.toJSONString(orderCallback));
            support.accept(report);
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
        }
        log.info("[op:ZJCustomsClient-handelOrderMsg] result={}", JSON.toJSONString(orderCallback));
        return orderCallback;
    }

    @Override
    public InventoryDeclareResult inventoryDeclare(WrapInventoryOrderInfo info) {
        String cebCode = info.getDeclareCompanyDTO().getCebCode();
        String orderNo = info.getCustomsInventoryDto().getOrderNo();
        ZJAgentToken token = tokenMap.get(cebCode);
        CustomsReport report = new CustomsReport(CustomsReport.TYPE_SEND, CustomsReport.SYSTEM_CUSTOMS_CLIENT, CustomsReport.PROCESS_CLEAR_DECLARE_INVENTORY);
        report.buildBusinessKey(info.getBusinessKey());
        InventoryDeclareResult result = new InventoryDeclareResult();
        result.setOrderNo(info.getCustomsInventoryDto().getOrderNo());
        result.setSn(info.getCustomsInventoryDto().getSn());
        result.setStatus(info.getCustomsInventoryDto().getStatus());
        report.buildProcessData(result);

        ZJPortInventoryBuilder builder = new ZJPortInventoryBuilder(info);
        Request request = builder.build();
        String importOrderInfo;
        try {
            importOrderInfo = XMLUtil.convertToXml(request);
            if (StringUtils.isEmpty(importOrderInfo)) {
                throw new RuntimeException("申报组装数据为空");
            }
            report.buildRequest(importOrderInfo);
        } catch (Exception e) {
            log.info("[供应链报文-杭州口岸-清单申报] 异常，cause={}", e.getMessage(), e);
            throw new RuntimeException("报文组装异常", e);
        }
        log.info("[供应链报文-杭州口岸-清单申报],清单报文原始数据{}", importOrderInfo);
        String responseString;
        if ("mock".equals(env)) {
            responseString = this.mockResult();
        } else {
            String customsCode = info.getCustomsAreaCode();
            // 浙江跨境一步达
            responseString = invokeCrossBorderOneStep(token, importOrderInfo, orderNo, cebCode, customsCode, HzPortBusinessType.PERSONAL_GOODS_DECLAR, info);
        }

        this.writeTrackLog(orderNo, importOrderInfo);

        log.info("[op:ZJCustomsClient-inventoryDeclare]清单申报- 浙江电子口岸报文 下发清单申报指令成功 回复报文={}", responseString);
        report.setResponseMsg(responseString);
        support.accept(report);
        if ("ceb".equals(token.getMode())) {
            this.inventoryDeclareCeb(info);
        }
        return result;
    }

    /**
     * 查找海关编码
     *
     * @param accountBookNo 根据账册查找海关编码
     * @return
     */
    private String findCustomsCode(String accountBookNo) {
        String _defaultCustomsCode = "2924";
        if (Objects.equals("L2923B21A004", accountBookNo)) {
            _defaultCustomsCode = "2923";
        } else if (CustomsDistrictCons.YW_2925_CUSTOMS_DISTRICT.contains(accountBookNo)) {
            _defaultCustomsCode = "2925";
        }
        return _defaultCustomsCode;
    }

    /**
     * 调用跨境一步达
     *
     * @param token       token
     * @param orignXml    原始xml
     * @param declareNo   申报单号
     * @param cebCode     海关十位编码
     * @param declareType 申报类型
     * @param info
     * @return
     */
    protected String invokeCrossBorderOneStep(ZJAgentToken token, String orignXml, String declareNo, String cebCode, String customsAreaCode, HzPortBusinessType declareType, WrapBeanInfo info) {

        // Step::加密&签名
        KeyInfo keyInfo = token.getZjPortKeyInfo();
        String AESKey = keyInfo.getAesKey();
        String context;
        String dataDigest;
        try {
            byte[] inputContent = orignXml.getBytes("utf-8");
            byte[] aesKeyCode = Base64.decodeBase64(AESKey.getBytes("utf-8"));
            context = new String(Base64.encodeBase64(HzPortAESUtil.encrypt(inputContent, aesKeyCode)), "utf-8");
            byte[] privateKeyCode = Base64.decodeBase64(keyInfo.getPrivateKey().getBytes("utf-8"));
            dataDigest = new String(Base64.encodeBase64(HzPortRSAUtil.sign(inputContent, privateKeyCode)), "utf-8");
        } catch (Exception e) {
            throw new RuntimeException("浙江电子口岸报文加密&签名异常", e);
        }
        log.info("[op:ZJCustomsClient-inventoryDeclare]供应链报文- 浙江电子口岸报文 -申报-下发{}申报指令  interfaceUrl={}, AesEncrypt={},加密报文={},签名信息={}",
                declareType.getDescription(), interfaceUrl, AESKey, context, dataDigest);
        String responseString = WebServiceRequest.webServiceRequestOfPort(
                interfaceUrl,
                context,
                declareType.getType(),
                dataDigest,
                cebCode);
        return FormatUtil.removeCrlf(responseString);
    }

    /**
     * 日志记录
     *
     * @param orderNo  订单No
     * @param orginXml 原始报文
     */
    private void writeTrackLog(String orderNo, String orginXml) {
        try {
            TrackLogDTO trackLogDTO = new TrackLogDTO();
            trackLogDTO.setOrderType(InventoryOrderType.INVENTORY_ORDER.getCode());
            trackLogDTO.setDeclareOrderNo(orderNo)
                    .setOldStatus(OrderStatus.DEC_WAIT.getValue()).setNewStatus(OrderStatus.DEC_ING.getValue())
                    .setOperateDes(TrackLogEnums.INVENTORY_DECLARE.getCode()).setLogDes("杭州口岸报文已推送").setContent(orginXml).setHasXmlMessage(1);
            support.messageSender.sendMsg(JSON.toJSONString(trackLogDTO), "ccs-trackLog-topic");
        } catch (Exception e) {
            log.info("[op:ZJCustomsClient-inventoryDeclare orderNo={}， 轨迹日志记录错误 trackLog error={}]", orderNo, e.getMessage(), e);
        }
    }

    private void inventoryDeclareCeb(WrapInventoryOrderInfo declareInfo) {
        CustomsReport report = new CustomsReport(CustomsReport.TYPE_SEND, CustomsReport.SYSTEM_CUSTOMS_CLIENT, CustomsReport.PROCESS_CLEAR_DECLARE_INVENTORY);
        report.buildBusinessKey(declareInfo.getBusinessKey());
        InventoryDeclareResult result = new InventoryDeclareResult();
        result.setOrderNo(declareInfo.getCustomsInventoryDto().getOrderNo());
        result.setSn(declareInfo.getCustomsInventoryDto().getSn());
        result.setStatus(declareInfo.getCustomsInventoryDto().getStatus());
        result.setAccountBookNo(declareInfo.getCustomsInventoryDto().getBookNo());
        result.setCustomsAreaCode(declareInfo.getCustomsAreaCode());
        report.buildProcessData(result);
        ZJAgentToken encryInfo = tokenMap.get(declareInfo.getDeclareCompanyDTO().getCebCode());
        // 这里设置下变更申报的申报单号，后面会根据此单号作为变更申报依据
        ChangeDeclareUtil.parseAndSetDeclareNo(support.getZjChangeDeclareNo());
        CEB621Message ceb621Message = new ZJCebInventoryBuilder(declareInfo, encryInfo.getCebSignInfo().getDxpId()).build();
        this.cebSend(report, encryInfo, ceb621Message, "CEB621Message", "清单申报");
        support.accept(report);

    }

    private void inventoryModifyDeclareCeb() {

    }

    @Override
    public InventoryCallback handelInventoryMsg(CallbackResult callbackResult) {
        CustomsReport report = new CustomsReport(
                CustomsReport.TYPE_ACCEPT,
                CustomsReport.SYSTEM_CUSTOMS_CLIENT,
                CustomsReport.PROCESS_CLEAR_CALLBACK_INVENTORY);
        report.setRequestMsg("");

        log.info("[op:ZJCustomsClient-handelInventoryMsg] CallbackResult={}", JSON.toJSONString(callbackResult));
        JSONObject origMap = JSON.parseObject(callbackResult.getOrigMsg());
        InventoryCallback inventoryCallback = new InventoryCallback();
        String msgType = origMap.getString("msgType");
        try {
            if ("CUSTOMS_CEB_CALLBACK".equals(msgType)) {
                // Module::总署回执
                String requestXmlString = origMap.getString("content");
                report.setResponseMsg(requestXmlString);
                requestXmlString = requestXmlString.replace("xmlns=\"http://www.chinaport.gov.cn/ceb\"", "");
                CEB622Message response = XMLUtil.converyToJavaBean(requestXmlString, CEB622Message.class);
                inventoryCallback.setEbpCode(response.getInventoryReturn().getEbpCode());
                inventoryCallback.setAgentCode(response.getInventoryReturn().getAgentCode());
                inventoryCallback.setSn(response.getInventoryReturn().getCopNo());
                Date returnDate = DateUtils.parseDate(response.getInventoryReturn().getReturnTime(), "yyyyMMddHHmmssSSS");
                inventoryCallback.setReturnTime(returnDate);
                inventoryCallback.setInvtNo(response.getInventoryReturn().getInvtNo());
                inventoryCallback.setPreNo(response.getInventoryReturn().getPreNo());
                inventoryCallback.setReturnStatus(response.getInventoryReturn().getReturnStatus());
                inventoryCallback.setReturnInfo(response.getInventoryReturn().getReturnInfo());
            } else if ("CUSTOMS_DECLARE_IMPORT_CALLBACK".equals(msgType)) {
                String requestXmlString = origMap.getString("content");
                report.setResponseMsg(requestXmlString);
                ReturnMessage response = XMLUtil.converyToJavaBean(requestXmlString, ReturnMessage.class);

                ReturnMessage.Head head = response.getHead();
                ReturnMessage.Body body = response.getBody();
                ReturnMessage.ReturnList returnList = body.getReturnList();
                List<ReturnMessage.ReturnMsg> returnMsgs = returnList.getReturnMsgs();
                report.setSender(TrackLogConstantMixAll.ZJ_PORT);
                for (ReturnMessage.ReturnMsg returnMsg : returnMsgs) {
                    inventoryCallback.setEbpCode(head.getCompanyCode());
                    inventoryCallback.setSn(returnMsg.getBusinessNo());
                    Date returnDate = DateUtils.parseDate(head.getCreateTime(), "yyyyMMddHHmmss");
                    inventoryCallback.setReturnTime(returnDate);
                    inventoryCallback.setReturnStatus(CustomsStat.ZJ_PORT_EXCEPTION.getValue());
                    inventoryCallback.setReturnInfo(returnMsg.getReturnInfo());

                    report.setProcessData(JSON.toJSONString(inventoryCallback));
                    support.accept(report);
                }
                return inventoryCallback;
            } else {
                // Module::口岸回执
                String requestXmlString = origMap.getString("content");
                report.setResponseMsg(requestXmlString);
                Response response = XMLUtil.converyToJavaBean(requestXmlString, Response.class);
                List<JKFResult> jkfResultList = response.getBody().getList();
                // Step::当前仅取第一个
                JKFResult jkfResult = jkfResultList.get(0);
                String companyCode = jkfResult.getCompanyCode();
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-ddHH:mm");
                Date portAcceptTime = simpleDateFormat.parse(jkfResult.getNoticeDate().concat(jkfResult.getNoticeTime()));
                inventoryCallback.setSn(jkfResult.getBusinessNo());
                inventoryCallback.setEbpCode(companyCode);
                inventoryCallback.setReturnTime(portAcceptTime);
                inventoryCallback.setReturnStatus(jkfResult.getChkMark());
                inventoryCallback.setReturnInfo(NCUtils.getTopOneResultDetail(jkfResult.getJkfResultDetail()));
                log.info("ZJCustomsClient handelInventoryMsg 浙电口岸回执暂不处理 callback={}", inventoryCallback);
                return inventoryCallback;
            }
            report.setSender(TrackLogConstantMixAll.ZJ_PORT);
            report.setProcessData(JSON.toJSONString(inventoryCallback));
            support.accept(report);
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
        }
        log.info("[op:ZJCustomsClient-handelInventoryMsg] result={}", JSON.toJSONString(inventoryCallback));
        return inventoryCallback;
    }

    @Override
    public ShipmentDeclareResult shipmentDeclare(WrapShipmentInfo info) {
        ZJAgentToken token = tokenMap.get(info.getDeclareCompanyDTO().getCebCode());
        CustomsReport report = new CustomsReport(CustomsReport.TYPE_SEND, CustomsReport.SYSTEM_CUSTOMS_CLIENT, CustomsReport.PROCESS_CLEAR_DECLARE_SHIPMENT);
        report.buildBusinessKey(info.getBusinessKey());
        ShipmentDeclareResult result = new ShipmentDeclareResult();
        result.setOrderNo(info.getDeclareOrderNo());
        result.setSn(info.getSn());
        result.setStatus(info.getStatus());
        report.buildProcessData(result);

        ZJPortShipmentBuilder builder = new ZJPortShipmentBuilder(info);
        Request request = builder.build();
        String importOrderInfo;
        try {
            importOrderInfo = XMLUtil.convertToXml(request);
            if (StringUtils.isEmpty(importOrderInfo)) {
                throw new RuntimeException("申报组装数据为空");
            }
            report.buildRequest(importOrderInfo);
        } catch (Exception e) {
            log.info("[供应链报文-杭州口岸-运单申报] 异常，cause={}", e.getMessage(), e);
            throw new RuntimeException("报文组装异常", e);
        }
        log.info("[供应链报文-杭州口岸-运单申报],运单报文原始数据{}", importOrderInfo);
        String responseString;
        if ("mock".equals(env)) {
            responseString = this.mockResult();
        } else {
            String customsCode = info.getCustomsAreaCode();
            // 浙江跨境一步达
            responseString = invokeCrossBorderOneStep(token, importOrderInfo, info.getDeclareOrderNo(), info.getDeclareCompanyDTO().getCebCode(), customsCode, HzPortBusinessType.IMPORTBILL, info);
        }
        log.info("[op:ZJCustomsClient-logisticsDeclare]运单申报 - 浙江电子口岸报文 下发运单申报指令成功 回复报文={}", responseString);
        report.setResponseMsg(responseString);
        support.accept(report);
        if ("ceb".equals(token.getMode())) {
            this.shipmentDeclareCeb(info);
        }
        return result;
    }

    private void shipmentDeclareCeb(WrapShipmentInfo info) {
        CustomsReport report = new CustomsReport(CustomsReport.TYPE_SEND, CustomsReport.SYSTEM_CUSTOMS_CLIENT, CustomsReport.PROCESS_CLEAR_DECLARE_SHIPMENT);
        report.buildBusinessKey(info.getBusinessKey());
        ShipmentDeclareResult result = new ShipmentDeclareResult();
        result.setOrderNo(info.getDeclareOrderNo());
        result.setSn(info.getSn());
        result.setStatus(info.getStatus());
        result.setCustomsAreaCode(info.getCustomsAreaCode());
        report.buildProcessData(result);
        ZJAgentToken encryInfo = tokenMap.get(info.getDeclareCompanyDTO().getCebCode());
        CEB511Message ceb511Message = new ZjCebShipmentBuilder(info, encryInfo.getCebSignInfo().getDxpId()).build();
        this.cebSend(report, encryInfo, ceb511Message, "CEB511Message", "运单申报");
        support.accept(report);
    }

    @Override
    public ShipmentCallback handleShipmentMsg(CallbackResult callbackResult) {
        CustomsReport report = new CustomsReport(
                CustomsReport.TYPE_ACCEPT,
                CustomsReport.SYSTEM_CUSTOMS_CLIENT,
                CustomsReport.PROCESS_CLEAR_CALLBACK_SHIPMENT);
        log.info("[op:ZJCustomsClient-handleShipmentMsg] CallbackResult={}", JSON.toJSONString(callbackResult));
        ShipmentCallback shipmentCallback = new ShipmentCallback();
        JSONObject origMap = JSON.parseObject(callbackResult.getOrigMsg());
        String msgType = origMap.getString("msgType");
        try {
            if ("CUSTOMS_CEB_CALLBACK".equals(msgType)) {
                // Module::总署回执
                String requestXmlString = origMap.getString("content");
                report.setResponseMsg(requestXmlString);
                requestXmlString = requestXmlString.replace("xmlns=\"http://www.chinaport.gov.cn/ceb\"", "");
                CEB512Message response = XMLUtil.converyToJavaBean(requestXmlString, CEB512Message.class);
                shipmentCallback.setLogisticsCode(response.getLogisticsReturn().getLogisticsCode());
                shipmentCallback.setLogisticsNo(response.getLogisticsReturn().getLogisticsNo());
                Date returnDate = DateUtils.parseDate(response.getLogisticsReturn().getReturnTime(), "yyyyMMddHHmmssSSS");
                shipmentCallback.setReturnTime(returnDate);
                shipmentCallback.setReturnStatus(response.getLogisticsReturn().getReturnStatus());
                shipmentCallback.setReturnInfo(response.getLogisticsReturn().getReturnInfo());
            } else if ("CUSTOMS_DECLARE_IMPORT_CALLBACK".equals(msgType)) {
                String requestXmlString = origMap.getString("content");
                report.setResponseMsg(requestXmlString);
                ReturnMessage response = XMLUtil.converyToJavaBean(requestXmlString, ReturnMessage.class);

                ReturnMessage.Head head = response.getHead();
                ReturnMessage.Body body = response.getBody();
                ReturnMessage.ReturnList returnList = body.getReturnList();
                List<ReturnMessage.ReturnMsg> returnMsgs = returnList.getReturnMsgs();
                report.setSender(TrackLogConstantMixAll.ZJ_PORT);
                for (ReturnMessage.ReturnMsg returnMsg : returnMsgs) {
//                    shipmentCallback.setLogisticsCode(returnMsg.getBusinessNo());
                    shipmentCallback.setLogisticsNo(returnMsg.getBusinessNo());
                    Date returnDate = DateUtils.parseDate(head.getCreateTime(), "yyyyMMddHHmmss");
                    shipmentCallback.setReturnTime(returnDate);
                    shipmentCallback.setReturnStatus(CustomsStat.ZJ_PORT_EXCEPTION.getValue());
                    shipmentCallback.setReturnInfo(returnMsg.getReturnInfo());

                    report.setProcessData(JSON.toJSONString(shipmentCallback));
                    support.accept(report);
                }
                return shipmentCallback;
            } else {
                // Module::口岸回执
                String requestXmlString = origMap.getString("content");
                report.setResponseMsg(requestXmlString);
                Response response = XMLUtil.converyToJavaBean(requestXmlString, Response.class);
                List<JKFResult> jkfResultList = response.getBody().getList();
                // Step::当前仅取第一个
                JKFResult jkfResult = jkfResultList.get(0);
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-ddHH:mm");
                Date portAcceptTime = simpleDateFormat.parse(jkfResult.getNoticeDate().concat(jkfResult.getNoticeTime()));
                shipmentCallback.setSn(jkfResult.getBusinessNo());
                shipmentCallback.setReturnTime(portAcceptTime);
                shipmentCallback.setReturnStatus(jkfResult.getChkMark());
                shipmentCallback.setReturnInfo(NCUtils.getResultDetail(jkfResult.getJkfResultDetail()));
                log.info("ZJCustomsClient handleShipmentMsg 浙电口岸回执暂不处理 callback={}", shipmentCallback);
                return shipmentCallback;
            }
            report.setSender(TrackLogConstantMixAll.ZJ_PORT);
            report.setProcessData(JSON.toJSONString(shipmentCallback));
            support.accept(report);
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
        }
        log.info("[op:ZJCustomsClient-handleShipmentMsg] result={}", JSON.toJSONString(shipmentCallback));
        return shipmentCallback;
    }

    @Override
    public CallbackResult parserCallback(Object request) {
        ZJCallbackResponse response = (ZJCallbackResponse) request;
        String content = response.getContent();
        String msgType = response.getMsg_type();
        String dataDigest = response.getData_digest();
        // 该字段用于测试环境自己模拟不加密报文时使用
        Boolean decipher = response.getDecipher();
        // Step::报文解密
        String originContent;
        if (!decipher) {
            originContent = content;
        } else {
            if (StringUtils.isEmpty(content)) {
                log.error("[op:ZJPortCallbackParser] content empty");
                throw new RuntimeException("未分配返回值");
            }
            StringBuffer contentBuffer = new StringBuffer();
            if (handDecipher(content, contentBuffer)) ;
            originContent = contentBuffer.toString();
        }
        log.info("[op:ZJPortCallbackParser] 浙江电子口岸海关回执原始报文 origContent={}", FormatUtil.removeCrlf(originContent));
        CallbackResult result = new CallbackResult();
        Map<String, String> origMap = new HashMap<>();
        origMap.put("content", originContent);
        origMap.put("msgType", msgType);
        result.setOrigMsg(JSON.toJSONString(origMap));
        result.setCallbackType(getCallbackType(originContent, msgType));
        if (Objects.equals(response.getSender(), "EWTP")) {
            result.setSenderCode("EWTP");
            result.setSender(TrackLogConstantMixAll.EWTP);
        } else {
            result.setSender(TrackLogConstantMixAll.ZJ_PORT);
        }
        if (CallbackType.NULL.equals(result.getCallbackType())) {
            CustomsReport report = new CustomsReport(
                    CustomsReport.TYPE_ACCEPT,
                    CustomsReport.SYSTEM_CUSTOMS_CLIENT,
                    CustomsReport.PROCESS_CLEAR_CALLBACK_UN_KNOW);
            report.setResponseMsg(JSON.toJSONString(response));
            report.setProcessData(JSON.toJSONString(result));
            support.accept(report);
        }
        return result;
    }

    @Override
    public List<TaxResult> handelTaxMsg(CallbackResult callbackResult) {
        CustomsReport report = new CustomsReport(
                CustomsReport.TYPE_ACCEPT,
                CustomsReport.SYSTEM_CUSTOMS_CLIENT,
                CustomsReport.PROCESS_CLEAR_CALLBACK_TAX);
        log.info("[op:ZJCustomsClient-handelTaxMsg] CallbackResult={}", JSON.toJSONString(callbackResult));
        JSONObject origMap = JSON.parseObject(callbackResult.getOrigMsg());
        List<TaxResult> taxResultList = new ArrayList<>();
        try {
            // Module::总署回执
            String requestXmlString = origMap.getString("content");
            report.buildResponse(requestXmlString);
            requestXmlString = requestXmlString.replace("xmlns=\"http://www.chinaport.gov.cn/ceb\"", "");
            CEB816Message response;
            response = XMLUtil.converyToJavaBean(requestXmlString, CEB816Message.class);
            for (Tax tax : response.getTax()) {
                TaxResult taxResult = new TaxResult();
                taxResult.setGuid(tax.getTaxHeadRd().getGuid());
                taxResult.setOrderNo(tax.getTaxHeadRd().getOrderNo());
                taxResult.setLogisticsNo(tax.getTaxHeadRd().getLogisticsNo());
                Date returnDate = DateUtils.parseDate(tax.getTaxHeadRd().getReturnTime(), "yyyyMMddHHmmssSSS");
                taxResult.setReturnTime(returnDate);
                taxResult.setInvtNo(tax.getTaxHeadRd().getInvtNo());
                taxResult.setTaxNo(tax.getTaxHeadRd().getTaxNo());
                taxResult.setCustomsTax(tax.getTaxHeadRd().getCustomsTax());
                taxResult.setValueAddedTax(tax.getTaxHeadRd().getValueAddedTax());
                taxResult.setConsumptionTax(tax.getTaxHeadRd().getConsumptionTax());
                taxResult.setStatus(tax.getTaxHeadRd().getStatus());
                taxResult.setEntDutyNo(tax.getTaxHeadRd().getEntDutyNo());
                taxResult.setNote(tax.getTaxHeadRd().getNote());
                taxResult.setEbcCode(tax.getTaxHeadRd().getEbcCode());
                taxResult.setAssureCode(tax.getTaxHeadRd().getAssureCode());
                taxResult.setLogisticsCode(tax.getTaxHeadRd().getLogisticsCode());
                taxResult.setAgentCode(tax.getTaxHeadRd().getAgentCode());
                taxResult.setCustomsCode(tax.getTaxHeadRd().getCustomsCode());
                List<TaxItem> itemList = new ArrayList<>();
                List<TaxListRd> taxListRdList = tax.getTaxListRd();
                if (!CollectionUtils.isEmpty(taxListRdList)) {
                    for (TaxListRd taxListRd : taxListRdList) {
                        TaxItem item = new TaxItem();
                        item.setGnum(taxListRd.getGnum());
                        item.setGcode(taxListRd.getGcode());
                        item.setTaxPrice(taxListRd.getTaxPrice());
                        item.setCustomsTax(taxListRd.getCustomsTax());
                        item.setValueAddedTax(taxListRd.getValueAddedTax());
                        item.setConsumptionTax(taxListRd.getConsumptionTax());
                        itemList.add(item);
                    }
                }
                taxResult.setItemList(itemList);
                taxResult.setSender(callbackResult.getSender());
                taxResultList.add(taxResult);
            }
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
        }
        report.setSender(TrackLogConstantMixAll.ZJ_PORT);
        report.buildProcessData(taxResultList);
        support.accept(report);
        return taxResultList;
    }


    @Override
    public InventoryCancelResult inventoryCancel(WarpCancelOrderInfo declareInfo) {
        CustomsReport report = new CustomsReport(
                CustomsReport.TYPE_ACCEPT,
                CustomsReport.SYSTEM_CUSTOMS_CLIENT,
                CustomsReport.PROCESS_CLEAR_DECLARE_INVENTORY_CANCEL
        );

        InventoryCancelResult result = new InventoryCancelResult();
        report.setSender(TrackLogConstantMixAll.ZJ_PORT);
        result.setOrderNo(declareInfo.getCustomsInventoryDto().getOrderNo());
        result.setSn(declareInfo.getCustomsInventoryCancelInfo().getSn());
        result.setStatus(declareInfo.getCustomsInventoryCancelInfo().getStatus());
        result.setAccountBookNo(declareInfo.getAccountBookDto().getBookNo());
        result.setCustomsAreaCode(declareInfo.getCustomsAreaCode());
        report.buildProcessData(result);

        ZJAgentToken encryInfo = tokenMap.get(declareInfo.getDeclareCompanyDTO().getCebCode());
        CEB623Message ceb623Message = new ZJCebInventoryCancelBuilder(declareInfo, encryInfo.getCebSignInfo().getDxpId()).build();
        this.cebSend(report, encryInfo, ceb623Message, "CEB623Message", "清单撤单");
        support.accept(report);
        return result;
    }

    @Override
    public InventoryRefundResult inventoryRefund(WarpRefundOrderInfo declareInfo) {
        CustomsReport report = new CustomsReport(
                CustomsReport.TYPE_ACCEPT,
                CustomsReport.SYSTEM_CUSTOMS_CLIENT,
                CustomsReport.PROCESS_CLEAR_DECLARE_INVENTORY_REFUND
        );
        InventoryRefundResult result = new InventoryRefundResult();
        report.setSender(TrackLogConstantMixAll.ZJ_PORT);
        result.setOrderNo(declareInfo.getCustomsInventoryDto().getOrderNo());
        result.setSn(declareInfo.getRefundOrderInfoDto().getRefundNo());
        result.setStatus(declareInfo.getRefundOrderInfoDto().getRefundCheckStatus());
        result.setAccountBookNo(declareInfo.getAccountBookDto().getBookNo());
        result.setCustomsAreaCode(declareInfo.getCustomsAreaCode());
        report.buildProcessData(result);
        ZJAgentToken encryInfo = tokenMap.get(declareInfo.getDeclareCompanyDTO().getCebCode());
        CEB625Message ceb625Message = new ZJCebInventoryRefundBuilder(declareInfo, encryInfo.getCebSignInfo().getDxpId()).build();
        this.cebSend(report, encryInfo, ceb625Message, "CEB625Message", "清单退货");
        support.accept(report);
        return result;
    }

    private String cebSend(CustomsReport report, ZJAgentToken token, Object model, String tag, String detail) {
        // Step::组装报文
        String xmlFormat;
        try {
            xmlFormat = XMLUtil.convertToXml(model);
            if (xmlFormat == null) {
                throw new RuntimeException("XML转换数据为空");
            }
            report.buildRequest(xmlFormat);
            log.info("[op:ZJCustomsClient] 总署CEB报文 {} 原始业务报文{}", detail, xmlFormat);
        } catch (Exception e) {
            log.info("[op:ZJCustomsClient] 总署CEB报文 {} XML报文组装异常, cause={}", detail, e.getMessage(), e);
            throw new RuntimeException("XML报文组装异常", e);
        }
        MapExt processDataMap = JSON.parseObject(report.getProcessData(), MapExt.class);
        String responseString;
        if ("mock".equals(env)) {
            responseString = this.mockResult();
            log.info("[op:ZJCustomsClient] 模拟数据 总署{} 请求业务报文= {},回复报文={}", detail, xmlFormat, responseString);
            try {
                // TODO：提交至测试应用
                Map<String, Object> param = new HashMap<>();
                param.put("type", tag);
                param.put("data", JSON.toJSONString(model));
                HttpRequest httpRequest = HttpRequest.post("http://oms.backend.daily.yang800.com/xhr/ccs/test/customs/mock")
                        .header(SaasHttpConstant.HEADER_TENANT_ID, SimpleTenantHelper.getTenantIdStr())
                        .form(param);
                httpRequest.ok();
            } catch (HttpRequest.HttpRequestException e) {
                log.warn("处理异常：{}", e.getMessage(), e);
            }
        } else {
            // Step::加密
            String customsAreaCode = processDataMap.getCustomsAreaCode();
            responseString = invokeCeb(token, xmlFormat, processDataMap.getOrderNo(), customsAreaCode, tag, detail, model);
        }

//        this.writeCebMessageLog(processDataMap.getOrderNo(), xmlFormat);
        report.buildResponse(responseString);
        return responseString;
    }


    /**
     * CEB报文调用
     *
     * @param token
     * @param orginXml
     * @param tag
     * @param detail
     * @param model
     * @return
     */
    protected String invokeCeb(ZJAgentToken token, String orginXml, String declareNo, String customsAreaCode, String tag, String detail, Object model) {

        String content;
        try {
            String signStr = CebSignUtil.signXml(token.getCebSignInfo(), orginXml);
            content = buildDxpMsg(signStr.getBytes(), token.getCebSignInfo().getDxpId(), tag);
        } catch (Exception e) {
            log.error("[op:signError]exception={}", e.getMessage(), e);
            throw new RuntimeException("总署报文加密异常", e);
        }
        if (StringUtils.isBlank(content)) {
            log.error("[op:signError]content empty", content);
        }

        String responseString = WebServiceRequest.webServiceRequestOfCeb(interfaceUrl, content);
        String request = FormatUtil.removeCrlf(orginXml);
        String dxpMessage = FormatUtil.removeCrlf(content);
        String response = FormatUtil.removeCrlf(responseString);
        log.info("[op:ZJCustomsClient] 总署CEB报文 {} 请求业务报文= {},请求终端节点报文={},回复报文={}", detail, request, dxpMessage, response);
        return responseString;
    }

    private void writeCebMessageLog(String declareNo, String orignXml) {
        try {
            TrackLogDTO trackLogDTO = new TrackLogDTO();
            trackLogDTO.setOrderType(InventoryOrderType.INVENTORY_ORDER.getCode());
            trackLogDTO.setDeclareOrderNo(declareNo)
                    .setOldStatus(OrderStatus.DEC_WAIT.getValue()).setNewStatus(OrderStatus.DEC_ING.getValue())
                    .setOperateDes(TrackLogEnums.INVENTORY_DECLARE.getCode()).setLogDes("总署报文已推送").setContent(orignXml).setHasXmlMessage(1);
            support.messageSender.sendMsg(JSON.toJSONString(trackLogDTO), "ccs-trackLog-topic");
        } catch (Exception e) {
            log.info("[op:ZJCustomsClient-cebSend trackLog error={}]", e.getMessage());
        }
    }


    public void dataCheckSubmit(WrapDataPayExInfo infoDTO) {
        String indata = "\"sessionID\":\"" + infoDTO.getExInfoDTO().getSessionID()
                + "\"||\"payExchangeInfoHead\":\"" + JSON.toJSONString(infoDTO.getExInfoDTO().getPayExchangeInfoHead()) +
                "\"||\"payExchangeInfoLists\":\"" + JSON.toJSONString(infoDTO.getExInfoDTO().getPayExchangeInfoLists())
                + "\"||\"serviceTime\":\"" + infoDTO.getReceiveTime() + "\"";
        log.info("[op:ZJCustomsClient-dataCheckSubmit] inData={}", indata);
        try {
            ZJAgentToken encryInfo = tokenMap.get(infoDTO.getExInfoDTO().getPayExchangeInfoHead().getEbpCode());
            String signed = CebSignUtil.signRsa(encryInfo.getCebSignInfo(), indata);
            CustomsDataPayExInfoDTO payExInfoDTO = infoDTO.getExInfoDTO();
            payExInfoDTO.setSignValue(signed);
            payExInfoDTO.setServiceTime(infoDTO.getReceiveTime() + "");
            payExInfoDTO.setCertNo(encryInfo.getCebSignInfo().getCertNo());
            String payExInfoStr = JSON.toJSONString(payExInfoDTO);
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("payExInfoStr", payExInfoStr);
            log.info("[op:ZJCustomsClient-dataCheckSubmit] send={}", JSON.toJSONString(paramMap));
            String url;
            if (EnvironmentConfig.isOnline()) {
                url = "http://ccs.backend.yang800.com/xhr/proxy/dataCheckSubmit";
            } else if (EnvironmentConfig.isTest()) {
                url = "http://ccs.backend.daily.yang800.com/xhr/proxy/dataCheckSubmit";
            } else {
                url = "http://127.0.0.1:8083/xhr/proxy/dataCheckSubmit";
            }
            HttpRequest request = HttpRequest.post(url)
                    .header(SaasHttpConstant.HEADER_TENANT_ID, SimpleTenantHelper.getTenantIdStr())
                    .form(paramMap);
            if (request.ok()) {
                String response = request.body();
                CustomsDataSendResult customsResponseVO = JSONObject.toJavaObject(JSON.parseObject(response), CustomsDataSendResult.class);
                if (null != customsResponseVO && customsResponseVO.getCode().equals("10000")) {
                    log.info("[op: ZJCustomsClient-dataCheckSubmit] success, params={}, result={}", JSON.toJSONString(paramMap), response);
                    // 提交成功
                } else {
                    // 提交失败
                    log.info("[op: ZJCustomsClient-dataCheckSubmit] fail, params={}, result={}", JSON.toJSONString(paramMap), response);
                }
            } else {
                log.error("[op: ZJCustomsClient-dataCheckSubmit] fail. code={}, body={}", request.code(), request.body());
            }
            // }
        } catch (Exception e) {
            log.error("[op: ZJCustomsClient-dataCheckSubmit] exception. code={}, body={}", e.getMessage(), e);
        }
    }

    /**
     * 回执报文解密
     */
    private boolean handDecipher(String content, StringBuffer contentBuffer) {
        return doHandDecipher(content, contentBuffer, this.jmKey);
    }

    public static boolean doHandDecipher(String content, StringBuffer contentBuffer, String jmKey) {
        try {
            byte[] contentByte = Base64.decodeBase64(content.getBytes("utf-8"));
            byte[] jmKeyByte = Base64.decodeBase64(jmKey);
            AesUtil aesUtil = new AesUtil(jmKeyByte, null);
            aesUtil.setUseECB(true);
            String originContent = new String(aesUtil.encrypt(contentByte), "UTF-8");
            contentBuffer.setLength(0);
            contentBuffer.append(originContent);
        } catch (Exception e) {
            throw new RuntimeException("加解密异常", e);
        }
        return true;
    }

    /**
     * 分辨回执报文类型
     */
    public static CallbackType getCallbackType(String content, String msgType) {
        if (StringUtils.equals(msgType, "CUSTOMS_CEB_CALLBACK")) {
            // Module::总署回执
            // Type::运单回执
            if (content.contains("CEB512Message")) {
                return CallbackType.SHIPMENT;
            }
            // Type::清单回执
            if (content.contains("CEB622Message")) {
                return CallbackType.INVENTORY;
            }
            // Type::退货回执
            if (content.contains("CEB626Message")) {
                return CallbackType.REFUND;
            }
            // Type::撤单回执
            if (content.contains("CEB624Message")) {
                return CallbackType.CANCEL;
            }
            // Type::撤单回执
            if (content.contains("CEB312Message")) {
                return CallbackType.ORDER;
            }
            // Type::撤单回执
            if (content.contains("CEB816Message")) {
                return CallbackType.TAX;
            }
            if (content.contains("CEB818Message")) {
                return CallbackType.TAXSTATUS;
            }
            return CallbackType.NULL;
        } else if ("CUSTOMS_DECLARE_IMPORT_CALLBACK".equals(msgType)) {
            // Module::浙电新接口回执
            ReturnMessage returnMessage = null;
            try {
                returnMessage = XMLUtil.converyToJavaBean(content, ReturnMessage.class);
            } catch (Exception e) {
                throw new RuntimeException("ReturnMessage xml解析错误", e);
            }
            String bizCode = returnMessage.getHead().getBizCode();

            CebMessageV2Enum cebMessageV2Enum = CebMessageV2Enum.getEnumByCode(bizCode);
            if (Objects.isNull(cebMessageV2Enum)) {
                return CallbackType.NULL;
            }
            switch (cebMessageV2Enum) {
                case CEB_INVENTORY:
                    return CallbackType.INVENTORY;
                case CEB_ORDER:
                    return CallbackType.ORDER;
                case CEB_SHIPMENT:
                    return CallbackType.SHIPMENT;
                case CEB_INVENTORY_CANCEL:
                    return CallbackType.CANCEL;
                case CEB_INVENTORY_REFUND:
                    return CallbackType.REFUND;
            }
            return CallbackType.NULL;
        } else {
            // Module::口岸回执
            // Step::报文结构解析
            Response response;
            try {
                response = XMLUtil.converyToJavaBean(content, Response.class);
            } catch (Exception ex) {
                throw new RuntimeException("response xml解析错误", ex);
            }
            CallbackType callbackType = getCallbackByPort(response.getHead().getBusinessType());
            if (!CallbackType.NULL.equals(callbackType)) {
                return callbackType;
            }
            //如果是未名回执，则根据JKFResult中的BusinessType再找一次handler
            if (response.getHead().getBusinessType().equalsIgnoreCase(HzPortBusinessType.RESULT.getType())) {
                callbackType = getCallbackByPort(response.getBody().getList().get(0).getBusinessType());
            }
            return callbackType;
        }
    }

    private static CallbackType getCallbackByPort(String type) {
        if (StringUtils.isEmpty(type)) {
            return CallbackType.NULL;
        }
        // Type::清单回执
        if (type.equalsIgnoreCase(HzPortBusinessType.PERSONAL_GOODS_DECLAR.getType())) {
            return CallbackType.INVENTORY;

        }
        // Type::商品备案审单回执
        if (type.equalsIgnoreCase(HzPortBusinessType.PRODUCT_RECORD.getType())) {
            return CallbackType.PRODUCT;
        }
        // Type::运单审单回执
        if (type.equalsIgnoreCase(HzPortBusinessType.IMPORTBILL.getType())) {
            return CallbackType.SHIPMENT;
        }
        // Type::订单审单回执
        if (type.equalsIgnoreCase(HzPortBusinessType.IMPORTORDER.getType())) {
            return CallbackType.ORDER;
        }
        return CallbackType.NULL;
    }

    protected String buildDxpMsg(byte[] bs, String dxpId, String msgType) throws Exception {
        DxpMsg dxpMsg = new DxpMsg();
        dxpMsg.setVer("1.0");
        dxpMsg.setData(java.util.Base64.getEncoder().encodeToString(bs));
        TransInfo transInfo = new TransInfo();
        transInfo.setCopMsgId(UUID.randomUUID().toString());
        ReceiverIds receiverIds = new ReceiverIds();
        receiverIds.setReceiverId("DXPEDCCEB0000002");
        transInfo.setReceiverIds(Collections.singletonList(receiverIds));
        transInfo.setCreatTime(new Date());
        transInfo.setMsgType(msgType);
        transInfo.setSenderId(dxpId);
        dxpMsg.setTransInfo(transInfo);
        return XMLUtil.convertToXml(dxpMsg);
    }

    private String mockResult() {
        Response response = new Response();
        Head head = new Head();
        head.setBusinessType("RESULT");
        response.setHead(head);
        ResponseBody body = new ResponseBody();
        List<JKFResult> list = new ArrayList<>();
        JKFResult jkfResult = new JKFResult();
        jkfResult.setChkMark("1");
        jkfResult.setNoticeDate(CebDateUtil.getCurrentYearDateForDirName());
        jkfResult.setNoticeTime(CebDateUtil.getCurrentHourMinusForDirName());
        List<JKFResultDetail> jkfResultDetail = new ArrayList<>();
        JKFResultDetail detail = new JKFResultDetail();
        detail.setResultInfo("处理成功");
        jkfResultDetail.add(detail);
        jkfResult.setJkfResultDetail(jkfResultDetail);
        list.add(jkfResult);
        body.setList(list);
        response.setBody(body);
        try {
            String xml = XMLUtil.convertToXml(response);
            return xml;
        } catch (Exception e) {
            return null;
        }
    }


    @Override
    public List<TaxStatus> handelTaxStatusMsg(CallbackResult callbackResult) {
        CustomsReport report = new CustomsReport(
                CustomsReport.TYPE_ACCEPT,
                CustomsReport.SYSTEM_CUSTOMS_CLIENT,
                CustomsReport.PROCESS_CLEAR_CALLBACK_TAX_STATUS);

        log.info("[op:ZJCustomsClient-handelTaxStatusMsg] CallbackResult={}", JSON.toJSONString(callbackResult));
        JSONObject origMap = JSON.parseObject(callbackResult.getOrigMsg());
        List<TaxStatus> taxStatusList = new ArrayList<TaxStatus>();
        try {
            // Module::总署回执
            String requestXmlString = origMap.getString("content");
            report.buildResponse(requestXmlString);
            requestXmlString = requestXmlString.replace("xmlns=\"http://www.chinaport.gov.cn/ceb\"", "");
            CEB818Message response = XMLUtil.converyToJavaBean(requestXmlString, CEB818Message.class);
            for (com.danding.cds.declare.ceb.domain.ceb818.Tax tax : response.getTax()) {
                TaxStatus taxResult = new TaxStatus();
                TaxHeadStatus taxHeadStatus = tax.getTaxHeadStatus();
                taxResult.setStatus(taxHeadStatus.getStatus());
                taxResult.setInvtNo(taxHeadStatus.getInvtNo());
                if (taxHeadStatus.getReturnTime() != null) {
                    taxResult.setReturnTime(DateUtils.parseDate(taxHeadStatus.getReturnTime(), "yyyyMMddHHmmss"));
                }
                taxResult.setTaxNo(taxHeadStatus.getTaxNo());
                taxResult.setEntDutyNo(taxHeadStatus.getEntDutyNo());
                taxResult.setAssureCode(taxHeadStatus.getAssureCode());
                taxStatusList.add(taxResult);
            }
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
        }
        report.setProcessData(JSON.toJSONString(taxStatusList));
        support.accept(report);
        return taxStatusList;
    }

    @Override
    public List<InventoryCancel> handelInventoryCancel(CallbackResult callbackResult) {
        List<InventoryCancel> cancelList = new ArrayList<>();
        JSONObject origMap = JSON.parseObject(callbackResult.getOrigMsg());
        CustomsReport report = new CustomsReport(
                CustomsReport.TYPE_ACCEPT,
                CustomsReport.SYSTEM_CUSTOMS_CLIENT,
                CustomsReport.PROCESS_CLEAR_CALLBACK_INVENTORY_CANCEL);
        String msgType = origMap.getString("msgType");
        try {
            if ("CUSTOMS_DECLARE_IMPORT_CALLBACK".equals(msgType)) {
                String requestXmlString = origMap.getString("content");
                report.setResponseMsg(requestXmlString);
                ReturnMessage response = XMLUtil.converyToJavaBean(requestXmlString, ReturnMessage.class);

                ReturnMessage.Head head = response.getHead();
                ReturnMessage.Body body = response.getBody();
                ReturnMessage.ReturnList returnList = body.getReturnList();
                List<ReturnMessage.ReturnMsg> returnMsgs = returnList.getReturnMsgs();
                report.setSender(TrackLogConstantMixAll.ZJ_PORT);
                for (ReturnMessage.ReturnMsg returnMsg : returnMsgs) {
                    InventoryCancel cancel = new InventoryCancel();
                    cancel.setEbpCode(head.getCompanyCode());
                    cancel.setID(returnMsg.getBusinessNo());
                    Date returnDate = DateUtils.parseDate(head.getCreateTime(), "yyyyMMddHHmmss");
                    cancel.setReturnTime(returnDate);
                    cancel.setReturnStatus(CustomsStat.ZJ_PORT_EXCEPTION.getValue());
                    cancel.setReturnInfo(returnMsg.getReturnInfo());
                    cancelList.add(cancel);
                }
            } else {
                // 总署回执
                String requestXmlString = origMap.getString("content");
                report.buildResponse(requestXmlString);
                requestXmlString = requestXmlString.replace("xmlns=\"http://www.chinaport.gov.cn/ceb\"", "");
                CEB624Message response = XMLUtil.converyToJavaBean(requestXmlString, CEB624Message.class);
                for (InvtCancelReturn invtCancelReturn : response.getInvtCancelReturn()) {
                    InventoryCancel cancel = new InventoryCancel();
                    cancel.setEbpCode(invtCancelReturn.getEbpCode());
                    cancel.setID(invtCancelReturn.getCopNo());
                    cancel.setInvtNo(invtCancelReturn.getInvtNo());
                    Date returnDate = DateUtils.parseDate(invtCancelReturn.getReturnTime(), "yyyyMMddHHmmssSSS");
                    cancel.setReturnTime(returnDate);
                    cancel.setReturnStatus(invtCancelReturn.getReturnStatus());
                    cancel.setReturnInfo(invtCancelReturn.getReturnInfo());
                    cancelList.add(cancel);
                }
            }
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
        }
        if (Objects.equals(callbackResult.getSenderCode(), "EWTP")) {
            report.setSender(TrackLogConstantMixAll.EWTP);
        } else {
            report.setSender(TrackLogConstantMixAll.ZJ_PORT);
        }
        report.setProcessData(JSON.toJSONString(cancelList));
        support.accept(report);
        return cancelList;
    }

    @Override
    public List<InventoryRefund> handelInventoryRefund(CallbackResult callbackResult) {
        List<InventoryRefund> refundList = new ArrayList<>();
        JSONObject origMap = JSON.parseObject(callbackResult.getOrigMsg());
        CustomsReport report = new CustomsReport(
                CustomsReport.TYPE_ACCEPT,
                CustomsReport.SYSTEM_CUSTOMS_CLIENT,
                CustomsReport.PROCESS_CLEAR_CALLBACK_INVENTORY_REFUND);
        try {
            String msgType = origMap.getString("msgType");

            if ("CUSTOMS_DECLARE_IMPORT_CALLBACK".equals(msgType)) {
                String requestXmlString = origMap.getString("content");
                report.buildResponse(requestXmlString);
                ReturnMessage response = XMLUtil.converyToJavaBean(requestXmlString, ReturnMessage.class);

                ReturnMessage.Head head = response.getHead();
                ReturnMessage.Body body = response.getBody();
                ReturnMessage.ReturnList returnList = body.getReturnList();
                List<ReturnMessage.ReturnMsg> returnMsgs = returnList.getReturnMsgs();
                report.setSender(TrackLogConstantMixAll.ZJ_PORT);

                for (ReturnMessage.ReturnMsg returnMsg : returnMsgs) {
                    InventoryRefund refund = new InventoryRefund();
                    refund.setEbpCode(head.getCompanyCode());
                    refund.setID(returnMsg.getBusinessNo());
                    Date returnDate = DateUtils.parseDate(head.getCreateTime(), "yyyyMMddHHmmss");
                    refund.setReturnTime(returnDate);
                    refund.setReturnStatus(CustomsStat.ZJ_PORT_EXCEPTION.getValue());
                    refund.setReturnInfo(returnMsg.getReturnInfo());
                    refundList.add(refund);
                }
            } else {
                String requestXmlString = origMap.getString("content");
                report.buildResponse(requestXmlString);
                requestXmlString = requestXmlString.replace("xmlns=\"http://www.chinaport.gov.cn/ceb\"", "");
                CEB626Message response = XMLUtil.converyToJavaBean(requestXmlString, CEB626Message.class);
                for (InvtRefundReturn invtRefundReturn : response.getInvtRefundReturn()) {
                    InventoryRefund refund = new InventoryRefund();
                    refund.setEbpCode(invtRefundReturn.getEbpCode());
                    refund.setID(invtRefundReturn.getCopNo());
                    refund.setInvtNo(invtRefundReturn.getInvtNo());
                    Date returnDate = DateUtils.parseDate(invtRefundReturn.getReturnTime(), "yyyyMMddHHmmssSSS");
                    refund.setReturnTime(returnDate);
                    refund.setReturnStatus(invtRefundReturn.getReturnStatus());
                    refund.setReturnInfo(invtRefundReturn.getReturnInfo());
                    refundList.add(refund);
                }
            }
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
        }
        if (Objects.equals(callbackResult.getSenderCode(), "EWTP")) {
            report.setSender(TrackLogConstantMixAll.EWTP);
        } else {
            report.setSender(TrackLogConstantMixAll.ZJ_PORT);
        }
        report.setProcessData(JSON.toJSONString(refundList));
        support.accept(report);
        return refundList;
    }
}
