package com.danding.cds.declare.sdk.model.man;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/2/23 17:38
 */
@Data
public class MAN00003Request implements Serializable {
    private ManSign manSign;

    private ManFbStockDelete manFbStockDelete;

    private List<ManFbStockDeleteDetail> manFbStockDeleteDetailList;

    @Data
    public static class ManFbStockDelete implements Serializable {
        /**
         * 删单申请单编号
         */
        private String stockDeleteId;

        /**
         * 账册编码
         */
        private String manualId;

        /**
         * WMS系统权限代码
         */
        private String roleCode;

        /**
         * 企业海关十位编码
         */
        private String companyCode;

        /**
         * 关区代码
         */
        private String customsCode;

        /**
         * 出入库类型
         */
        private String inOutFlag;

        /**
         * 删单原因
         */
        private String reason;
    }

    @Data
    public static class ManFbStockDeleteDetail implements Serializable {
        private String stockDeleteId;

        private String inOutSeq;
    }
}
