package com.danding.cds.declare.sdk.payment.wechat.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * @Auther: Dante-Gxj
 * @Date: 2019/12/13 14:00
 * @Description: 微信海关查询
 */
@Data
public class WxCustomsQueryModel implements Serializable {
    /**
     * 商户订单号
     * 共通 选填
     */
    @JSONField(name = "out_trade_no")
    private String outTradeNo;

    /**
     * 微信支付订单号
     * 报关 选填
     */
    @JSONField(name = "transaction_id")
    private String transactionId;

    /**
     * 子订单号
     * 报关 选填
     */
    @JSONField(name = "sub_order_no")
    private String subOrderNo;

    /**
     * 微信子订单号
     * 报关 选填
     */
    @JSONField(name = "sub_order_id")
    private String subOrderId;

    /**
     * 海关
     * 报关 必填
     * HANGZHOU_ZS 杭州
     * GUANGZHOU_ZS 广州（总署版）
     * TIANJIN 天津
     * GUANGZHOU_NS_GJ 广州南沙国检（需推送订单至南沙国检的订单需分别推送广州（总署版）和广州南沙国检，即需要请求两次报关接口）
     */
    @JSONField(name = "customs")
    private String customs;
}
