package com.danding.cds.declare.sdk.utils;

import org.joda.time.LocalDateTime;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2021/11/26
 */
public class DateUtils {

//    public static SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
//    public static SimpleDateFormat format1 = new SimpleDateFormat("yyyyMMdd HH:mm:ss");

    /**
     * 日期字符串，例：20160228
     */
    public static String dateStr() {
        SimpleDateFormat dateSdf = new SimpleDateFormat("yyyyMMdd");
        return dateSdf.format(new Date());
    }

    /**
     * yyyyMMddHHmmss
     */
    public static String secondsStr(long timeMillis) {
        SimpleDateFormat secondSdf = new SimpleDateFormat("yyyyMMddHHmmss");
        return secondSdf.format(timeMillisToDate(timeMillis));
    }

    /**时间毫秒格式转成日期时间格式*/
    public static Date timeMillisToDate(long timeMillis) {
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(timeMillis);
        return cal.getTime();
    }

    /**时间毫秒格式转成日期时间格式*/
    public static String timeMillisToString(Long timeMillis) {
        SimpleDateFormat dateSdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return  dateSdf.format(new Date(timeMillis));
    }

    /**
     * 时间毫秒格式转成日期时间格式
     */
    public static Long dateToTimeMillis(Date date) {
        try {
            SimpleDateFormat dateSdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return dateSdf.parse(dateSdf.format(date)).getTime() / 1000;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return 0L;
    }

    /**
     * 得到指定日期的一天的的最后时刻23:59:59
     *
     * @param date
     * @return
     */
    public static Date getFinallyDate(Date date) {
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        String temp = format.format(date);
        temp += " 23:59:59";

        try {
            SimpleDateFormat format1 = new SimpleDateFormat("yyyyMMdd HH:mm:ss");
            return format1.parse(temp);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 得到指定日期的一天的开始时刻00:00:00
     *
     * @param date
     * @return
     */
    public static Date getStartDate(Date date) {
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        String temp = format.format(date);
        temp += " 00:00:00";

        try {
            SimpleDateFormat format1 = new SimpleDateFormat("yyyyMMdd HH:mm:ss");
            return format1.parse(temp);
        } catch (Exception e) {
            return null;
        }
    }

    public static Date getYearFirstDay(Date date) {
        LocalDateTime localDateTime = new LocalDateTime(date);
        int year = localDateTime.getYear();
        return getYearFirstDay(year);
    }


    public static Date getYearLastDay(Date date) {
        LocalDateTime localDateTime = new LocalDateTime(date);
        int year = localDateTime.getYear();
        return getYearLastDay(year);
    }

    /**
     * 获取某年第一天日期开始时刻
     *
     * @param year 年份
     * @return Date
     */
    public static Date getYearFirstDay(int year) {
        Calendar cal = Calendar.getInstance();
        cal.clear();
        cal.set(Calendar.YEAR, year);
        Date yearFirstDay = cal.getTime();
        return getStartDate(yearFirstDay);
    }

    /**
     * 获取某年最后一天日期最后时刻
     *
     * @param year 年份
     * @return Date
     */
    public static Date getYearLastDay(int year) {
        Calendar cal = Calendar.getInstance();
        cal.clear();
        cal.set(Calendar.YEAR, year);
        cal.roll(Calendar.DAY_OF_YEAR, -1);
        Date yearLastDay = cal.getTime();
        return getFinallyDate(yearLastDay);
    }


}
