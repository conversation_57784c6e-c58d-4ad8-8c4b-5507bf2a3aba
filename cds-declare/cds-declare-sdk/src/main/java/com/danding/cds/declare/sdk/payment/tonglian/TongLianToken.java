package com.danding.cds.declare.sdk.payment.tonglian;

import com.danding.cds.declare.sdk.payment.base.BaseToken;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2021/11/26
 */
@Data
public class TongLianToken extends BaseToken implements Serializable {

    private static final long serialVersionUID = 4598974360361523669L;

    private String access_token;
    private long createTimeStamp;
    private long expires_in;
}
