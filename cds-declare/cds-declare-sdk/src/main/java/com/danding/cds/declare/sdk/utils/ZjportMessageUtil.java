package com.danding.cds.declare.sdk.utils;

import com.alibaba.fastjson.JSON;
import com.danding.cds.declare.ceb.internal.utils.CebDateUtil;
import com.danding.cds.declare.ceb.internal.utils.XMLUtil;
import com.danding.cds.declare.sdk.bean.dto.ZjportMsgDto;
import com.danding.cds.declare.sdk.clear.zhejiang.ZJAgentToken;
import com.danding.cds.declare.sdk.clear.zhejiang.builder.ZJPortInventoryBuilder;
import com.danding.cds.declare.sdk.clear.zhejiang.builder.ZJPortOrderBuilder;
import com.danding.cds.declare.sdk.clear.zhejiang.builder.ZJPortShipmentBuilder;
import com.danding.cds.declare.sdk.clear.zhejiang.encry.KeyInfo;
import com.danding.cds.declare.sdk.model.WrapBeanInfo;
import com.danding.cds.declare.sdk.model.inventory.WrapInventoryOrderInfo;
import com.danding.cds.declare.sdk.model.order.WrapOrderDeclareInfo;
import com.danding.cds.declare.sdk.model.shipment.WrapShipmentInfo;
import com.danding.cds.declare.zjport.domain.base.Head;
import com.danding.cds.declare.zjport.domain.request.Request;
import com.danding.cds.declare.zjport.domain.response.Response;
import com.danding.cds.declare.zjport.domain.response.ResponseBody;
import com.danding.cds.declare.zjport.domain.response.jkfResult.JKFResult;
import com.danding.cds.declare.zjport.domain.response.jkfResult.JKFResultDetail;
import com.danding.cds.declare.zjport.internal.HzPortAESUtil;
import com.danding.cds.declare.zjport.internal.enums.HzPortBusinessType;
import com.danding.cds.declare.zjport.internal.utils.HzPortRSAUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @program: cds-center
 * @description: zjport message
 * @author: 潘本乐（Belep）
 * @create: 2021-11-18 14:41
 **/
@Slf4j
public class ZjportMessageUtil {

    public static String mockResult() {
        Response response = new Response();
        Head head = new Head();
        head.setBusinessType("RESULT");
        response.setHead(head);
        ResponseBody body = new ResponseBody();
        List<JKFResult> list = new ArrayList<>();
        JKFResult jkfResult = new JKFResult();
        jkfResult.setChkMark("1");
        jkfResult.setNoticeDate(CebDateUtil.getCurrentYearDateForDirName());
        jkfResult.setNoticeTime(CebDateUtil.getCurrentHourMinusForDirName());
        List<JKFResultDetail> jkfResultDetail = new ArrayList<>();
        JKFResultDetail detail = new JKFResultDetail();
        detail.setResultInfo("处理成功");
        jkfResultDetail.add(detail);
        jkfResult.setJkfResultDetail(jkfResultDetail);
        list.add(jkfResult);
        body.setList(list);
        response.setBody(body);
        try {
            String xml = XMLUtil.convertToXml(response);
            return xml;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 创建浙江电子口岸报文信息
     *
     * @param info 申报信息
     * @return 浙江申报信息封装
     */
    public static ZjportMsgDto buildZjPortMessage(WrapBeanInfo info) {

        String cebCode = info.getDeclareCompanyCebCode();
        ZJAgentToken token = TokenUtil.getZjPortToken(cebCode);
        if (token == null) {
            log.error("找不到对应的口岸密钥，海关编码：{},浙江电子口岸", cebCode);
            String message = String.format("根据海关编码:%s,未找到浙江电子口岸密钥的配置", cebCode);
            throw new RuntimeException(message);
        }
        // Step::组装报文
        try {
            ZjportMsgDto zjportMsgDto = buildZjPortMsg(info, token.getMode());
            Request request = zjportMsgDto.getRequest();
            String zjportDeclareXml = XMLUtil.convertToXml(request);
            if (StringUtils.isEmpty(zjportDeclareXml)) {
                log.error("订单申报-浙江电子口岸 XML转换数据为空={}", JSON.toJSONString(request));
                throw new RuntimeException("XML转换数据为空");
            }
            zjportMsgDto.setOrigMsg(zjportDeclareXml);
            log.info("订单申报-浙江电子口岸 明文={}", zjportDeclareXml);
            KeyInfo keyInfo = token.getZjPortKeyInfo();
            if (keyInfo == null) {
                String message = String.format("根据海关编码:%s,未找到浙江电子口岸密钥的配置", cebCode);
                throw new RuntimeException(message);
            }
            String AESKey = keyInfo.getAesKey();
            byte[] inputContent = zjportDeclareXml.getBytes("utf-8");
            byte[] aesKeyCode = Base64.decodeBase64(AESKey.getBytes("utf-8"));
            String encryptMsg = new String(Base64.encodeBase64(HzPortAESUtil.encrypt(inputContent, aesKeyCode)), "utf-8");
            byte[] privateKeyCode = Base64.decodeBase64(keyInfo.getPrivateKey().getBytes("utf-8"));
            String signMsg = new String(Base64.encodeBase64(HzPortRSAUtil.sign(inputContent, privateKeyCode)), "utf-8");
            zjportMsgDto.setEncryptMsg(encryptMsg);
            zjportMsgDto.setSignMsg(signMsg);
            return zjportMsgDto;
        } catch (Exception e) {
            log.error("申报单号: {} ,浙江电子口岸报文加密&签名异常：{}", info.getDeclareNos(), e.getMessage(), e);
            throw new RuntimeException("浙江电子口岸报文加密&签名异常:[" + e.getMessage() + "]");
        }
    }

    private static ZjportMsgDto buildZjPortMsg(WrapBeanInfo declareInfo, String mode) {

        ZjportMsgDto zjportMsgDto = new ZjportMsgDto();
        if (declareInfo instanceof WrapOrderDeclareInfo) {

            WrapOrderDeclareInfo orderDeclareInfo = (WrapOrderDeclareInfo) declareInfo;
            Request request = new ZJPortOrderBuilder(orderDeclareInfo, mode).build();
            zjportMsgDto.setRequest(request);
            zjportMsgDto.setBusinessType(HzPortBusinessType.IMPORTORDER);
        } else if (declareInfo instanceof WrapInventoryOrderInfo) {

            Request request = new ZJPortInventoryBuilder((WrapInventoryOrderInfo) declareInfo).build();
            zjportMsgDto.setRequest(request);
            zjportMsgDto.setBusinessType(HzPortBusinessType.PERSONAL_GOODS_DECLAR);
        } else if (declareInfo instanceof WrapShipmentInfo) {

            Request request = new ZJPortShipmentBuilder((WrapShipmentInfo) declareInfo).build();
            zjportMsgDto.setRequest(request);
            zjportMsgDto.setBusinessType(HzPortBusinessType.IMPORTBILL);
        }
        return zjportMsgDto;
    }
}
