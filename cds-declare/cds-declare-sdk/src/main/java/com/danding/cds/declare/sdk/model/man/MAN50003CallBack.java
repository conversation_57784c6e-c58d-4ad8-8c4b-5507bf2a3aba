package com.danding.cds.declare.sdk.model.man;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/3/1 13:21
 */
@Data
public class MAN50003CallBack implements Serializable {
    private ManSign manSign;

    private ManFbDeleteResult manDeleteResult;
    private ManFbDeleteResultDetail manDeleteResultDetailList;

    @Data
    public static class ManFbDeleteResult implements Serializable {
        private String stockDeleteId;

        /**
         * 处理时间
         * yyyy-MM-ddHH:mm:ss
         */
        private String processTime;

        /**
         * S:处理成功
         * F:处理失败
         */
        private String processResult;

        private String processComment;
    }


    @Data
    public static class ManFbDeleteResultDetail implements Serializable {
        private String information;
    }
}
