package com.danding.cds.declare.sdk.payment.base;

import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 渠道注册中心, 对外提供支付渠道
 *
 * DATE: 16/5/25 下午6:11 <br>
 * MAIL: <EMAIL> <br>
 * AUTHOR: zhanghecheng
 */
@Slf4j
public class ChannelRegistry implements Serializable {
    /**
     * 支付渠道标识 到支付渠道的映射
     */
    private final Map<String, BaseChannel> channels;

    public ChannelRegistry() {
        channels = new ConcurrentHashMap<>();
    }

    /**
     * 注册支付渠道
     *
     * @param channelId  支付渠道标识
     * @param baseChannel  支付渠道
     */
    public void register(String channelId, BaseChannel baseChannel){
        channels.put(channelId, baseChannel);
    }

    /**
     * 获取支付渠道
     *
     * @param channelId  支付渠道标识
     * @return  对应的支付渠道, 如果不存在则抛出异常
     */
    public BaseChannel findChannel(String channelId){
        final BaseChannel baseChannel = channels.get(channelId);
        if(baseChannel == null){
            log.error("no payment channel (id={}) configured", channelId);
            throw new PayException("channel.not.exist");
        }
        return baseChannel;
    }

    /**
     * 获取已经注册的支付渠道列表
     *
     * @return 支付渠道列表
     */
    public Set<String> getChannelsRegistered(){
        return new HashSet<>(channels.keySet());
    }
}

