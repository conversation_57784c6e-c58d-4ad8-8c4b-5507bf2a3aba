package com.danding.cds.declare.sdk.utils;

import com.danding.cds.declare.zjspecial.internal.mq.HzDataCenterProducer;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

/**
 * @program: cds-center
 * @description: 消息工具
 * @author: 潘本乐（Belep）
 * @create: 2021-09-26 15:26
 **/
@Slf4j
public class RabbitMqUtil {

    /**
     * 杭州数据中心消息发送
     *
     * @param message   消息
     * @param sendQueue 发送的队列名称
     */
    public static void hzDataCenterSendMsg(String message, String sendQueue) {

        HzDataCenterProducer centerProducer = new HzDataCenterProducer(
                "**************", "5672",
                "EPORT_HZ", "password", sendQueue);
        try {
            centerProducer.send(message);
        } catch (Exception ex) {
            log.error("杭州数据中心申报消息发送异常：{}", ex.getMessage(), ex);
            throw new RuntimeException("杭州数据中心申报消息发送异常:" + ex.getMessage(), ex);
        }
        log.info("杭州数据中心消息发送成功：队列：{}，消息为：{}", sendQueue, message);
    }


    @Test
    public void send() {
//        String message = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?><DxpMsg ver=\"1.0\" xmlns:dxp=\"http://www.chinaport.gov.cn/dxp\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:ds=\"http://www.w3.org/2000/09/xmldsig#\"><TransInfo><CopMsgId>9a490a6c-d166-4e0f-a404-d7a2e188198e</CopMsgId><SenderId>DXPENT0000479166</SenderId><ReceiverIds><ReceiverId>DXPEDCCEB0000002</ReceiverId></ReceiverIds><CreatTime>2024-03-14T13:04:42.104+08:00</CreatTime><MsgType>CEB621Message</MsgType></TransInfo><Data>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</Data></DxpMsg>";
        String message = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?><DxpMsg ver=\"1.0\" xmlns:dxp=\"http://www.chinaport.gov.cn/dxp\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:ds=\"http://www.w3.org/2000/09/xmldsig#\"><TransInfo><CopMsgId>9a490a6c-d166-4e0f-a404-d7a2e188198e</CopMsgId><SenderId>DXPENT0000479166</SenderId><ReceiverIds><ReceiverId>DXPEDCCEB0000002</ReceiverId></ReceiverIds><CreatTime>2024-03-14T13:04:42.104+08:00</CreatTime><MsgType>CEB621Message</MsgType></TransInfo><Data>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</Data></DxpMsg>";
        String sendQueue = "DXPENT0000479166";
        hzDataCenterSendMsg(message, sendQueue);
    }

    @Test
    public void icCardSign() {

        String url = "http://122.224.240.58:9010/newyorkTransferWebapps/rest/transferDeclare";
        String data = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?><ceb:CEB625Message guid=\"F0B398AD-89BC-490C-8DB3-A9ED846DC605\" version=\"1.0\" xmlns:ceb=\"http://www.chinaport.gov.cn/ceb\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"><ceb:InvtRefund><ceb:InvtRefundHead><ceb:guid>F0B398AD-89BC-490C-8DB3-A9ED846DC605</ceb:guid><ceb:appType>1</ceb:appType><ceb:appTime>20230928175238</ceb:appTime><ceb:appStatus>2</ceb:appStatus><ceb:customsCode>2991</ceb:customsCode><ceb:orderNo>************</ceb:orderNo><ceb:ebpCode>31149679BZ</ceb:ebpCode><ceb:ebpName>上海京东才奥电子商务有限公司</ceb:ebpName><ceb:ebcCode>4401460817</ceb:ebcCode><ceb:ebcName>广州卓普贸易有限公司</ceb:ebcName><ceb:logisticsNo>JD0116117420067</ceb:logisticsNo><ceb:logisticsCode>11089609XE</ceb:logisticsCode><ceb:logisticsName>北京京邦达贸易有限公司</ceb:logisticsName><ceb:copNo>2309281752301517</ceb:copNo><ceb:preNo>B20230906625342095</ceb:preNo><ceb:invtNo>29242023I184292618</ceb:invtNo><ceb:buyerIdType>1</ceb:buyerIdType><ceb:buyerIdNumber>440682198909165014</ceb:buyerIdNumber><ceb:buyerName>黎嘉彬</ceb:buyerName><ceb:buyerTelephone>15919059823</ceb:buyerTelephone><ceb:agentCode>330766K009</ceb:agentCode><ceb:agentName>金华代塔供应链管理有限公司</ceb:agentName><ceb:reason>退货关区2991-测试</ceb:reason></ceb:InvtRefundHead><ceb:InvtRefundList><ceb:gnum>1</ceb:gnum><ceb:gcode>9503008900</ceb:gcode><ceb:gname>乐高积木 42149 烈焰飞龙</ceb:gname><ceb:qty>1</ceb:qty><ceb:unit>140</ceb:unit><ceb:note></ceb:note></ceb:InvtRefundList></ceb:InvtRefund><ceb:BaseTransfer><ceb:copCode>330766K009</ceb:copCode><ceb:copName>金华代塔供应链管理有限公司</ceb:copName><ceb:dxpMode>DXP</ceb:dxpMode><ceb:dxpId>DXPENT0000477639</ceb:dxpId></ceb:BaseTransfer></ceb:CEB625Message>";
        String body = CebSignUtil.icCardSign(url, null, data);
        System.out.println(body);
    }
}
