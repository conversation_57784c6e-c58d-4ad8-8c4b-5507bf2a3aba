package com.danding.cds.declare.sdk.clear.zhejiang.builder;

import com.danding.cds.declare.ceb.internal.enums.CebDeclareType;
import com.danding.cds.declare.sdk.model.inventory.CustomsInventoryItemInfo;
import com.danding.cds.declare.sdk.model.inventory.WrapInventoryOrderInfo;
import com.danding.cds.declare.sdk.utils.DateUtil;
import com.danding.cds.declare.sdk.utils.NumberUtil;
import com.danding.cds.declare.zjport.domain.base.Head;
import com.danding.cds.declare.zjport.domain.base.JKFSign;
import com.danding.cds.declare.zjport.domain.request.Body;
import com.danding.cds.declare.zjport.domain.request.Request;
import com.danding.cds.declare.zjport.domain.request.goodsDeclareModule.GoodsDeclare;
import com.danding.cds.declare.zjport.domain.request.goodsDeclareModule.GoodsDeclareDetail;
import com.danding.cds.declare.zjport.domain.request.goodsDeclareModule.GoodsDeclareModule;
import com.danding.cds.declare.zjport.internal.enums.DeclareCompanyType;
import com.danding.cds.declare.zjport.internal.enums.HzPortBusinessType;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class ZJPortInventoryBuilder implements Serializable {
    
    private WrapInventoryOrderInfo info;

    public ZJPortInventoryBuilder(WrapInventoryOrderInfo info) {
        this.info = info;
    }

    public Request build(){
        return this.buildRequest();
    }


    private Request buildRequest() {
        Request request = new Request();
        request.setHead(buildHead());
        request.setBody(buildBody());
        return request;
    }

    private Head buildHead() {
        Head head = new Head();
        head.setBusinessType(HzPortBusinessType.PERSONAL_GOODS_DECLAR.getType());
        return head;
    }

    private Body buildBody(){
        Body body = new Body();
        body.setGoodsDeclareModuleList(buildGoodsDeclareModuleList());
        return body;
    }

    private List<GoodsDeclareModule> buildGoodsDeclareModuleList(){
        List<GoodsDeclareModule> goodsDeclareModules = new ArrayList<>();
        goodsDeclareModules.add(buildDeclareModule());
        return goodsDeclareModules;
    }

    private GoodsDeclareModule buildDeclareModule(){
        GoodsDeclareModule goodsDeclareModule = new GoodsDeclareModule();
        goodsDeclareModule.setJkfSign(buildJkfSign());
        goodsDeclareModule.setGoodsDeclare(buildGoodsDeclare());
        goodsDeclareModule.setGoodsDeclareDetails(buildGoodsDeclareDetails(goodsDeclareModule.getGoodsDeclare()));
        return goodsDeclareModule;
    }

    private JKFSign buildJkfSign() {
        JKFSign jkfSign = new JKFSign();
        //<companyCode> 	发送方备案编号	VARCHAR2(20)	必填	发送方备案编号,不可随意填写
        String companyCode = info.getDeclareCompanyDTO().getCode();
        jkfSign.setCompanyCode(companyCode);
        //<businessNo>	业务编码	VARCHAR2(20)	必填	可以是清单预录入号，字段作用是回执给到企业的时候通过这个编号企业能认出对应之前发送的哪个单子
        //jkfSign.setBusinessNo(CcbServiceUtil.getPreEntryNumber(String.valueOf(info.getCustomsInventoryDto().getId())));
        jkfSign.setBusinessNo(info.getCustomsInventoryDto().getSn());
        //<businessType>	业务类型	VARCHAR2(30)	必填	业务类型 PERSONAL_GOODS_DECLAR
        jkfSign.setBusinessType(HzPortBusinessType.PERSONAL_GOODS_DECLAR.getType());
        //<declareType>	申报类型	CHAR(1)	必填	企业报送类型。1-新增 2-变更 3-删除。默认为1。
        jkfSign.setDeclareType(CebDeclareType.CREATE.getType());
        //<cebFlag>			必填	填写或01表示在途在库单证， 02 表示企业采用方案二对接，自行生成加签总署报文， 03表示采用方案一对接，委托平台生成总署报文，回调企业加签服务器加签
        jkfSign.setCebFlag("02");
        //<note>	备注	VARCHAR2(256)	可空
        jkfSign.setNote("");
        return jkfSign;
    }

    private GoodsDeclare buildGoodsDeclare() {
        GoodsDeclare goodsDeclare = new GoodsDeclare();
        String iePort = "2924";//PortCodeEnum.getPortCode(info.getCustomsInventoryDto().getCustoms());
        String destinationPort = "142";//PortCodeEnum.getDestinationPortCode(info.getCustomsInventoryDto().getCustoms());

        //String iePort = LogisticsDynamicConfig.getLP().getRule(providerNid, "iePort");
        //String destinationPort = LogisticsDynamicConfig.getLP().getRule(providerNid, "destinationPort");
        String trafMode = "7";//PortTrafModeEnum.getTrafCode(this.portNid);

        String declareCompanyCode = info.getDeclareCompanyDTO().getCode();
        String declareCompanyName = info.getDeclareCompanyDTO().getName();
        String internalAreaCompanyNo = info.getInternalAreaCompany().getCode();
        String internalAreaCompanyName = info.getInternalAreaCompany().getName();
        String customsField  = "292401";//info.getCustomsInventoryDto().getCustomsField();

//        CompanyAndEcommerce cae = CustomsEnterpriseUtils.getCompanyAndEcommerceInfo(lineEntity.portEntity.portNid,
//                stockoutOrderDO.getEnterpriseNid(), stockoutOrderDO.getBizType(), providerNid);
        //<accountBookNo>	账册编号	VARCHAR2(50)	否	一线转入的对应账册编号，保税模式必填，直邮模式不填
        // 就是海关监管仓储企业记账的账册编号
        // 得问你们合作仓储企业。
        goodsDeclare.setAccountBookNo(info.getCustomsInventoryDto().getBookNo());
        //<ieFlag>	进出口标记	CHAR(1)	是	必须为I
        goodsDeclare.setIeFlag("I");
        //<preEntryNumber>	预录入编号	CHAR(18)	是	4位电商编号+14位企业流水，电商平台/物流企业生成后发送服务平台，与运单号一一对应，同个运单重新申报时，保持不变
//        goodsDeclare.setPreEntryNumber(CcbServiceUtil.getPreEntryNumber(stockoutOrderDO.getMasterBizId(), stockoutOrderDO.getBizType()));
        //goodsDeclare.setPreEntryNumber(CcbServiceUtil.getPreEntryNumber(String.valueOf(info.getCustomsInventoryDto().getId())));
        //goodsDeclare.setPreEntryNumber(CcbServiceUtil.getPreEntryNumber(String.valueOf(info.getCustomsInventoryDto().getId())));
        goodsDeclare.setPreEntryNumber(info.getCustomsInventoryDto().getSn()); // TODO:业务意义不明确

        //<importType>	监管方式	CHAR(1)	是	填写0：对应9610直邮进口 填写1：对应1210保税进口 填写2：对应1239保税进口
        goodsDeclare.setImportType("1");
        //<inOutDateStr>	进口日期	DATE	是	格式：2014-02-18 20:33:33
        goodsDeclare.setInOutDateStr(DateUtil.dateToString(info.getCustomsInventoryDto().getCreateTime(), DateUtil.DEF_PATTERN));
        //<iePort>	进口口岸代码	VARCHAR2(5)	是	口岸代码表 4019
        goodsDeclare.setIePort(iePort);
        //<destinationPort>	指运港(抵运港)	VARCHAR2(5)	是	对应参数表 401901
        goodsDeclare.setDestinationPort(destinationPort);
        //<trafName> 	运输工具名称	NVARCHAR2(100)	否	包括字母和数字 可以填写中文 转关时填写@+16位转关单号
        //<voyageNo> 	航班航次号	VARCHAR2(32)	否	直邮必填,包括字母和数字，可以有中文
        //<trafNo>	运输工具编号	VARCHAR2(100)	否	直邮必填
        //<trafMode> 	运输方式	VARCHAR2(30)	是	参照运输方式代码表(TRANSF)
        goodsDeclare.setTrafMode(trafMode);
        //<declareCompanyType>	申报单位类别	VARCHAR2(30)	是	个人委托电商企业申报 个人委托物流企业申报 个人委托第三方申报
        goodsDeclare.setDeclareCompanyType(DeclareCompanyType.LOGISTICS_COMPANY.getType());
        //<declareCompanyCode>	申报企业代码	VARCHAR2(20)	是	指委托申报单位代码，需在海关注册，具有报关资质
        goodsDeclare.setDeclareCompanyCode(declareCompanyCode);
        //<declareCompanyName>	申报企业名称	NVARCHAR2(200)	是	指委托申报单位名称，需在海关注册，具有报关资质
        goodsDeclare.setDeclareCompanyName(declareCompanyName);
        //<companyCode> 电商平台代码  VARCHAR(20) 是   电商平台在跨境电商通关服务的备案编号

        goodsDeclare.setCompanyCode(info.getEbpCompanyDTO().getCode());
        //<companyName> 电商平台名称  VARCHAR2(200)   是   电商平台在跨境电商综合服务平台的备案名称
        goodsDeclare.setCompanyName(info.getEbpCompanyDTO().getName());

        //<eCommerceCode>   电商企业代码  VARCHAR2(20)    是   电商企业在跨境平台备案编码
        goodsDeclare.setECommerceCode(info.getEbcCompanyDTO().getCode());
        //<eCommerceName>   电商企业名称  NVARCHAR2(200)  是   电商企业在跨境平台备案的企业名称
        goodsDeclare.setECommerceName(info.getEbcCompanyDTO().getName());
        //<logisCompanyName>	物流企业名称	VARCHAR2(200)	是	物流企业在跨境平台备案的企业名称
        //获取物流企业信息
        if (info.isDeclareLogisticsInSystem()){
            goodsDeclare.setLogisCompanyName(info.getLogisticDeclareCompany().getName());
            //<logisCompanyCode>	物流企业代码	VARCHAR2(20)	是	物流企业在跨境平台备案编码
            goodsDeclare.setLogisCompanyCode(info.getLogisticDeclareCompany().getCode());
        }else {
            goodsDeclare.setLogisCompanyName(info.getLogisticsCompanyDTO().getName());
            //<logisCompanyCode>	物流企业代码	VARCHAR2(20)	是	物流企业在跨境平台备案编码
            goodsDeclare.setLogisCompanyCode(info.getLogisticsCompanyDTO().getCode());
        }
        //<orderNo>	订单编号	VARCHAR2(50)	是
        goodsDeclare.setOrderNo(info.getCustomsInventoryDto().getOrderNo());
        //<wayBill>	物流运单编号	VARCHAR2(50)	是
        goodsDeclare.setWayBill(info.getCustomsInventoryDto().getLogisticsNo());
        //<billNo>	提运单号	VARCHAR2(37)	否	直邮必填
        //<tradeCountry> 	启运国（地区）	NVARCHAR2(20)	是	参照国别代码表(COUNTRY)
        goodsDeclare.setTradeCountry("142"); // TODO
        //<packNo>	件数	NUMBER(14,4)	是	表体独立包装的物品总数，不考虑物品计量单位，大于0
        goodsDeclare.setPackNo("1"); // TODO
        //<grossWeight>	毛重（公斤）	NUMBER(14,4)	是	大于0
        goodsDeclare.setGrossWeight("1"); // TODO
        //<netWeight>	净重（公斤）	NUMBER(14,4)	是	大于0
        goodsDeclare.setNetWeight("1"); // TODO
        //<warpType>	包装种类代码	VARCHAR2(20)	否	参照包装种类代码表
        //<remark>	备注	NVARCHAR2(200)	否	可以数字和字母或者中文
        //<declPort>	申报地海关代码	VARCHAR2(5)	是	对应参数表
        goodsDeclare.setDeclPort(iePort);
        //<enteringPerson>	录入人	NVARCHAR2(20)	是	默认9999
        goodsDeclare.setEnteringPerson("9999");
        //<enteringCompanyName>	录入单位名称	NVARCHAR2(30)	是	默认9999
        goodsDeclare.setEnteringCompanyName("9999");
        //<declarantNo>	报关员代码	VARCHAR2(20)	否
        //<customsField> 	监管场所代码	VARCHAR2(20)	是	对应参数表 292801	下城园区 299102	下沙园区  299201	萧山园区
        goodsDeclare.setCustomsField(customsField);
        //<senderName>  发件人 NVARCHAR2(20)   是   可以数字和字母，可以有中文和英文
        goodsDeclare.setSenderName(info.getEbpCompanyDTO().getName());
        //<consignee>	收件人	NVARCHAR2(20)	 是	可以数字和字母，可以有中文和英文
        goodsDeclare.setConsignee(info.getCustomsInventoryDto().getBuyerName());
        //<senderCountry>	发件人国别	NVARCHAR2(20)	是	参数表
        goodsDeclare.setSenderCountry("142");
        //<senderCity> 	发件人城市	NVARCHAR2(20)	否	可以数字和字母，可以有中文和英文
        //<paperType> 	收件人证件类型	CHAR(1)	否	1-身份证（试点期间只能是身份证）2-护照 3-其他
        //<paperNumber>	收件人证件号	VARCHAR2(50)	否	可以有数字和字母
        //<consigneeAddress>	收件人地址	VARCHAR2(255)	是	对应订单中的收件人地址
        goodsDeclare.setConsigneeAddress(info.getCustomsInventoryDto().getConsigneeAddress());
        //<purchaserTelNumber>	购买人电话	VARCHAR2(30)	是	海关监管对象的电话,对应订单中的购买人联系电话
        goodsDeclare.setPurchaserTelNumber(info.getCustomsInventoryDto().getBuyerTelNumber());
        //<buyerIdType>	订购人证件类型	VARCHAR2(1)	是	1-身份证；2-其它
        goodsDeclare.setBuyerIdType(info.getCustomsInventoryDto().getBuyerIdType());
        //<buyerIdNumber>	订购人证件号码	VARCHAR2(60)	是	海关监控对象的身份证号,对应订单购买人证件号码
        goodsDeclare.setBuyerIdNumber(info.getCustomsInventoryDto().getBuyerIdNumber());
        //<buyerName>	订购人姓名	VARCHAR2(60)	是	海关监控对象的姓名,对应订单购买人人姓名
        goodsDeclare.setBuyerName(info.getCustomsInventoryDto().getBuyerName());
        //<worth>	价值	NUMBER(14,4)	是	表体所有商品总价的和+运费+保费
        BigDecimal worth = info.getCustomsInventoryDto().getFreight();
        if(worth==null)worth=new BigDecimal(0);
        for (CustomsInventoryItemInfo customsInventoryItemInfo : info.getListCustomsInventoryItemInfo()) {
            worth = worth.add(customsInventoryItemInfo.getUnitPrice().multiply(new BigDecimal( customsInventoryItemInfo.getCount())));
        }
        goodsDeclare.setWorth(worth.toString());

        //<feeAmount>	运费	NUMBER(12,4)	是	交易过程中商家向用户征收的运费，免邮模式填写0
        String feeAmount = "0";
        if(info.getCustomsInventoryDto().getFreight()!=null)
            feeAmount = info.getCustomsInventoryDto().getFreight().toString();
        goodsDeclare.setFeeAmount(feeAmount);
        //<insureAmount>	保费	NUMBER(12,4)	是	商家向用户征收的保价费用，无保费可填写0
        goodsDeclare.setInsureAmount("0");
        //<currCode>	币制	VARCHAR2(18)	是	对应参数表
        goodsDeclare.setCurrCode("142");
        //<mainGName>	主要货物名称	NVARCHAR2(255)	是	可以数字和字母或者中文
        String mainGName  = info.getCustomsInventoryDto().getMainGName();
        goodsDeclare.setMainGName(mainGName);
        goodsDeclare.setTradeCountry("142");
//        <internalAreaCompanyNo>   区内企业代码  VARCHAR2(50)    否   保税进口必填，填报仓储企业编码
        goodsDeclare.setInternalAreaCompanyNo(info.getInternalAreaCompany().getCode());
        //<internalAreaCompanyName> 区内企业名称  NVARCHAR2(200)  否   保税进口必填，填报仓储企业名称
        goodsDeclare.setInternalAreaCompanyName(info.getInternalAreaCompany().getName());
//        goodsDeclare.setInternalAreaCompanyName(internalAreaCompanyName);
//        goodsDeclare.setInternalAreaCompanyNo(internalAreaCompanyNo);
        //<assureCode>  担保企业编号  VARCHAR2(50)    是
        goodsDeclare.setAssureCode(info.getAssureCompanyDTO().getCode());
        //<applicationFormNo>	申请单编号	VARCHAR2(30)	否	指仓储企业事先在辅助系统申请的分送集报申请单编号。金二账册为空
        //<isAuthorize>	是否授权	CHAR(1)	是	 代表是否个人买家授权电商申报数据，填写0或1，0代表否，1代表是
        goodsDeclare.setIsAuthorize("1");
        //<licenseNo>	许可证号		否
        if ("L2923B21A004".equals(info.getCustomsInventoryDto().getBookNo())){
            goodsDeclare.setIePort("2923");
            goodsDeclare.setDeclPort("2923");
            goodsDeclare.setCustomsField("292301");
        }
        return goodsDeclare;
    }

    private List<GoodsDeclareDetail> buildGoodsDeclareDetails(GoodsDeclare goodsDeclare) {
        List<GoodsDeclareDetail> goodsDeclareDetails = new ArrayList<GoodsDeclareDetail>();
        // 净重（千克）总和
        // float netWeightTotal = 0;
        // 毛重（千克）总和
        // float grossWeightTotal = 0;
        BigDecimal grossWeight = new BigDecimal("0");
        BigDecimal netWeight = new BigDecimal("0");
        for (int index = 0; index < info.getListCustomsInventoryItemInfo().size(); index++) {
            GoodsDeclareDetail goodsDeclareDetail = new GoodsDeclareDetail();
            CustomsInventoryItemInfo customsInventoryItemInfo = info.getListCustomsInventoryItemInfo().get(index);
            if (null != customsInventoryItemInfo.getWeight()) {
                grossWeight = grossWeight.add(BigDecimal.valueOf(customsInventoryItemInfo.getWeight() * customsInventoryItemInfo.getCount()));
                netWeight = netWeight.add(BigDecimal.valueOf(Float.valueOf(customsInventoryItemInfo.getFirstCount()) * customsInventoryItemInfo.getCount()));
                // netWeightTotal = netWeightTotal + (Float.valueOf(customsInventoryItemInfo.getFirstCount()) * customsInventoryItemInfo.getCount());
                // grossWeightTotal = grossWeightTotal + (customsInventoryItemInfo.getWeight() * customsInventoryItemInfo.getCount());
            }
            // JSONObject spcesJSONObj = JSON.parseObject(skuDO.getSpecs());
            //1 <goodsOrder>    序号  NUMBER 是    只能有数字，外网自动生成大于0小于50
            goodsDeclareDetail.setGoodsOrder(index + 1);
            //2 <codeTs>    商品编码    VARCHAR2(50)    是   填写商品对应的HS编码
            goodsDeclareDetail.setCodeTs(customsInventoryItemInfo.getHsCode());
            //3 <goodsItemNo>   企业商品货号  VARCHAR2(30)    否
            goodsDeclareDetail.setGoodsItemNo(customsInventoryItemInfo.getItemNo());
            //4 <itemRecordNo>  账册备案料号  VARCHAR2(30)    否   保税必填，与仓储企业备案的电子账册中料号数据一致；金二账册填写备案序号。
            goodsDeclareDetail.setItemRecordNo(customsInventoryItemInfo.getItemRecordNo());
            //5 <itemName>  企业商品品名  NVARCHAR2(250)  否
            goodsDeclareDetail.setItemName(customsInventoryItemInfo.getItemName());
            //6 <goodsName> 商品名称    NVARCHAR2(255)  是
            goodsDeclareDetail.setGoodsName(customsInventoryItemInfo.getItemName());
            //7 <goodsModel>    商品规格型号  NVARCHAR2(510)  是   填写品名、牌名、规格、型号、成份、含量、等级等，满足海关归类、审价、监管要求
            goodsDeclareDetail.setGoodsModel(customsInventoryItemInfo.getGmodle());
            goodsDeclareDetail.setOriginCountry(customsInventoryItemInfo.getCountry());
            //9 <tradeCurr> 币制  NVARCHAR2(20)   是   参照币制代码表(CURR)
            goodsDeclareDetail.setTradeCurr("142");
            //10 <tradeTotal> 成交总价 NUMBER(14,4) 否 与申报总价一致
            //11 <declPrice>    单价  NUMBER(14,4)    是   只能是数字

            goodsDeclareDetail.setDeclPrice(customsInventoryItemInfo.getUnitPrice().toString());
            //12 <declTotalPrice>   总价  NUMBER(14,4)    是   申报数量乘以申报单价
            goodsDeclareDetail.setDeclTotalPrice(String.valueOf(customsInventoryItemInfo.getUnitPrice().floatValue() * customsInventoryItemInfo.getCount().floatValue()));
            //13 <useTo>    用途  NVARCHAR2(200)  否   08-赠品、10-试用装
            //14 <declareCount> 数量  NUMBER(14,4)    是   只能是数字，大于0
            goodsDeclareDetail.setDeclareCount(customsInventoryItemInfo.getCount());
            //15 <goodsUnit>    计量单位    NVARCHAR2(20)   是   参照计量单位代码表(UNIT)
//                    goodsDeclareDetail.setGoodsUnit(LogisticsBO.getInstance().getPortParamCode(
//                        lineEntity.portEntity.id, PortParamType.UNIT.getValue(), "件", true));
            goodsDeclareDetail.setGoodsUnit(customsInventoryItemInfo.getUnit());
            //16 <goodsGrossWeight>     商品毛重    NUMBER(14,4)    否   只能是数字
            //17 <firstUnit>    法定计量单位  NVARCHAR2(15)   是   填写商品HS编码对应的第一单位
//                    goodsDeclareDetail.setFirstUnit(LogisticsBO.getInstance().getPortParamCode(
//                        lineEntity.portEntity.id, PortParamType.UNIT.getValue(), "件", true));

            goodsDeclareDetail.setFirstUnit(customsInventoryItemInfo.getUnit1());
            //18 <firstCount>   法定数量    NUMBER(14,4)    是   根据第一单位填写对应数量
            goodsDeclareDetail.setFirstCount(NumberUtil.parseForDeclareValue(customsInventoryItemInfo.getFirstCount(), customsInventoryItemInfo.getCount()));
            if (StringUtils.isNotBlank(customsInventoryItemInfo.getUnit2())) {
                //19 <secondUnit>   第二计量单位  NVARCHAR2(15)   否   填写商品HS编码对应的第二单位
                goodsDeclareDetail.setSecondUnit(customsInventoryItemInfo.getUnit2());
                //20 <secondCount>  第二数量    NUMBER(14,4)    否   根据第二单位填写对应数量
                goodsDeclareDetail.setSecondCount(NumberUtil.parseForDeclareValue(customsInventoryItemInfo.getSecondCount(), customsInventoryItemInfo.getCount()));
            }
            //21 <productRecordNo>  产品国检备案编号    VARCHAR2 (18)   是   通过向国检备案获取
            // goodsDeclareDetail.setProductRecordNo("TODO"); // TODO
            //22 <webSite>  商品网址    VARCHAR2 (100)  否   商品网址
            //23 <barCode>  条形码 VARCHAR2 (50)   否   条形码
            goodsDeclareDetail.setBarCode(customsInventoryItemInfo.getBarCode());
            //24 <note> 备注  VARCHAR2 (1000) 否   备注
            //25 <tradeCountry> 贸易国 VARCHAR2 (3)        按海关规定的《国别（地区）代码表》选择填报相应的贸易国（地区）代码。
//                    goodsDeclareDetail.setTradeCountry(LogisticsBO.getInstance().getPortParamCode(lineEntity.portEntity.id,
//                        PortParamType.ORIGIN.getValue(), "中国", true));
            goodsDeclareDetail.setTradeCountry("142");
            goodsDeclareDetails.add(goodsDeclareDetail);
        }
//        //<netWeight>	净重（公斤）	NUMBER(14,4)	是	大于0
//        BigDecimal grossWeight = new BigDecimal(grossWeightTotal);
//        BigDecimal netWeight = new BigDecimal(netWeightTotal);
        //<netWeight>	净重（公斤）	NUMBER(14,4)	是	大于0
        goodsDeclare.setNetWeight(netWeight.setScale(4, BigDecimal.ROUND_HALF_UP).toString()); // TODO
        //<grossWeight>	毛重（公斤）	NUMBER(14,4)	是	大于0
        goodsDeclare.setGrossWeight(grossWeight.setScale(4, BigDecimal.ROUND_HALF_UP).toString()); // TODO

        return goodsDeclareDetails;
    }
}
