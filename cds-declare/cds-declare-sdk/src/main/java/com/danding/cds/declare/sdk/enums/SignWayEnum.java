package com.danding.cds.declare.sdk.enums;

import java.util.Objects;

/**
 * @program: cds-center
 * @description: 签名方式
 * @author: 潘本乐（Belep）
 * @create: 2022-10-20 13:11
 **/
public enum SignWayEnum {

    sign_null(null, "unknown"),
    sign_1(1, "swxa"),
    sign_2(2, "swxa"),
    sign_3(3, "swxa"),
    sign_4(4, "icCard");

    private Integer type;

    private String signDesc;

    SignWayEnum(Integer type, String sign) {
        this.type = type;
        this.signDesc = sign;
    }

    public Integer getType() {
        return type;
    }

    public String getSignDesc() {
        return signDesc;
    }

    public static SignWayEnum getEnum(Integer type) {
        for (SignWayEnum signWay : SignWayEnum.values()) {
            if (Objects.equals(signWay.getType(), type)) {
                return signWay;
            }
        }
        return SignWayEnum.sign_null;
    }

    public static void main(String[] args) {
        SignWayEnum signWayEnum = getEnum(null);
        System.out.println(signWayEnum.getType());

    }
}
