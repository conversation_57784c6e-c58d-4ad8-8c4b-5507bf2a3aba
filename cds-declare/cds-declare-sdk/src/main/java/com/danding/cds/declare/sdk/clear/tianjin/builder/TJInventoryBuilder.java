package com.danding.cds.declare.sdk.clear.tianjin.builder;

import com.danding.cds.declare.ceb.internal.enums.CebDeclareType;
import com.danding.cds.declare.ceb.internal.utils.CebDateUtil;
import com.danding.cds.declare.sdk.model.inventory.CustomsInventoryItemInfo;
import com.danding.cds.declare.sdk.model.inventory.WrapInventoryOrderInfo;
import com.danding.cds.declare.sdk.utils.DateUtil;
import com.danding.cds.declare.sdk.utils.NumberUtil;
import com.danding.cds.declare.tjport.domain.ENTBaseTransfer;
import com.danding.cds.declare.tjport.domain.ent621.*;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;

public class TJInventoryBuilder implements Serializable {

    private WrapInventoryOrderInfo info;

    public TJInventoryBuilder(WrapInventoryOrderInfo info) {
        this.info = info;
    }

    public ENT621Message build() {
        return buildOrderMessage();
    }

    /**
     * 构造ENT621Message
     *
     * @return
     */
    private ENT621Message buildOrderMessage() {
        ENT621Message ent621Message = new ENT621Message();
        ENT621Inventory inventory = buildInventory();
        ENTBaseTransfer baseTransfer = buildBaseTransfer();
        ent621Message.setGuid(inventory.getInventoryHead().getGuid());
        ent621Message.setReciptCode("121500");
        ent621Message.setSendCode("Q121500201907000422");
        ent621Message.setBaseTransfer(baseTransfer);
        ent621Message.setInventory(inventory);
        return ent621Message;
    }


    private ENT621Inventory buildInventory() {
        ENT621Inventory inventory = new ENT621Inventory();
        ENT621InventoryHead inventoryHead = buildInventoryHead();
        inventory.setInventoryList(buildInventoryList(inventoryHead));
        inventory.setInventoryHead(inventoryHead);
        inventory.setIODeclContainerList(buildIODeclContainerList());
        inventory.setIODeclOrderRelationList(buildIODeclOrderRelationList());
        return inventory;
    }

    private static List<String> editDeclareList = Arrays.asList(
            "AAAA21010001477931057600",
            "AAAA21010003094912240400",
            "AAAA21010001764444332300",
            "AAAA21010001885604096900",
            "AAAA21010001475901072500",
            "AAAA21010001690287036200",
            "AAAA21010001685590238600",
            "AAAA21010001742170238300",
            "AAAA21010001592425029600",
            "AAAA21010002168674131600",
            "AAAA21010001341877304900",
            "AAAA21010001544215004500",
            "AAAA21010001642076292400",
            "AAAA21010001699583408900",
            "AAAA21010001531739294000",
            "AAAA21010001687482362300",
            "AAAA21010001579080345200",
            "AAAA21010001577724372200",
            "AAAA21010001597945329800",
            "AAAA21010001593128042700",
            "AAAA21010001635633394800",
            "AAAA21010001515953197600",
            "AAAA21010001559739171600",
            "AAAA21010001924063313300",
            "AAAA21010001444059167800",
            "AAAA21010001650830358700",
            "AAAA21010001428482152800",
            "AAAA21010001954045400400",
            "AAAA21010001532173306000",
            "AAAA21010001473984050300",
            "AAAA21010001486641338100",
            "AAAA21010001710877096400",
            "AAAA21010001657943318900",
            "AAAA21010001653358024200",
            "AAAA21010001559514055600",
            "AAAA21010001507787339300",
            "AAAA21010001557382105300",
            "AAAA21010001563332067100",
            "AAAA21010001390782334400",
            "AAAA21010001439656009600",
            "AAAA21010001659796165000",
            "AAAA21010001476092373400",
            "AAAA21010001435613185900",
            "AAAA21010001434057186200",
            "AAAA21010001782148360600",
            "AAAA21010001463327003900",
            "AAAA21010001501347107300",
            "AAAA21010001344440199200",
            "AAAA21010001425746235700",
            "AAAA21010001649967074200",
            "AAAA21010001565827082200",
            "AAAA21010001438102315700",
            "AAAA21010001743532217700",
            "AAAA21010001549477185500",
            "AAAA21010001459591315800",
            "AAAA21010001655196315100",
            "AAAA21010001822163098600",
            "AAAA21010001415649217600",
            "AAAA21010001544393278300",
            "AAAA21010001694413014800",
            "AAAA21010001524542395900",
            "AAAA21010001589459071600",
            "AAAA21010001433383007500",
            "AAAA21010003209262260000",
            "AAAA21010001400271404100",
            "AAAA21010001648490119500",
            "AAAA21010001788033134600",
            "AAAA21010001788035134600",
            "AAAA21010001546549266600",
            "AAAA21010001800199131800",
            "AAAA21010001399117215500",
            "AAAA21010001625563014100",
            "AAAA21010001436980218600",
            "AAAA21010001623672059400",
            "AAAA21010001286688070900",
            "AAAA21010001722157150300",
            "AAAA21010001371357051900",
            "AAAA21010001582093056300",
            "AAAA21010001635052097000",
            "AAAA21010001526431058800",
            "AAAA21010001514143111300",
            "AAAA21010001278382160700",
            "AAAA21010001328247123200",
            "AAAA21010001494461215200",
            "AAAA21010001688610257000",
            "AAAA21010001632041031500",
            "AAAA21010001474789195600",
            "AAAA21010001331484320900",
            "AAAA21010001491249179400",
            "AAAA21010001559026138600",
            "AAAA21010001707391224900",
            "AAAA21010005244005293300",
            "AAAA21010001469115187300",
            "AAAA21010001452545150400",
            "AAAA21010001452935150400",
            "AAAA21010001365169039800",
            "AAAA21010001443357009600",
            "AAAA21010001444510180800",
            "AAAA21010001533901083000",
            "AAAA21010001532969372500",
            "AAAA21010001732863167400",
            "AAAA21010001424499291200",
            "AAAA21010001575798214500",
            "AAAA21010001475182230700",
            "AAAA21010001660153365200",
            "AAAA21010001590132403400",
            "AAAA21010001357693186300",
            "AAAA21010001743199402500",
            "AAAA21010001367860364900",
            "AAAA21010002336575131300",
            "AAAA21010001661497263900",
            "AAAA21010001468235230700",
            "AAAA21010001527367331500",
            "AAAA21010001522218357700",
            "AAAA21010001429442342500",
            "AAAA21010001438886191800",
            "AAAA21010002269773266900",
            "AAAA21010001590740269200",
            "AAAA21010001439435014400",
            "AAAA21010001438296014400",
            "AAAA21010001516424297600",
            "AAAA21010001687541115400",
            "AAAA21010001503451342200",
            "AAAA21010001512766187400",
            "AAAA21010001331882289500",
            "AAAA21010001710413067700",
            "AAAA21010001468363004700",
            "AAAA21010001623973367300",
            "AAAA21010001281638061600",
            "AAAA21010001442031108600",
            "AAAA21010001774358385000",
            "AAAA21010002866479249500",
            "AAAA21010001597231199500",
            "AAAA21010001470836120400",
            "AAAA21010001701792193100",
            "AAAA21010001455101180700",
            "AAAA21010001626428079200",
            "AAAA21010001617246015500",
            "AAAA21010001670310202100",
            "AAAA21010001515401367100",
            "AAAA21010001562632312800",
            "AAAA21010002623744262600",
            "AAAA21010001627277079200",
            "AAAA21010001531762209100",
            "AAAA21010002043211133400",
            "AAAA21010001517526005200",
            "AAAA21010001517528005200",
            "AAAA21010001718220409400",
            "AAAA21010001475079120400",
            "AAAA21010001490618287400",
            "AAAA21010001278017033400",
            "AAAA21010001615836124200",
            "AAAA21010001717692409400",
            "AAAA21010002147964134500",
            "AAAA21010001899376109700",
            "AAAA21010001589898247300",
            "AAAA21010001568605094800",
            "AAAA21010001563662234500",
            "AAAA21010001598309080700",
            "AAAA21010001255977307800",
            "AAAA21010001631900069500",
            "AAAA21010001361443043100",
            "AAAA21010001765418045600",
            "AAAA21010001533154375400",
            "AAAA21010001422936103700",
            "AAAA21010001537291093500",
            "AAAA21010001603334015300",
            "AAAA21010001529214181600",
            "AAAA21010001771201284300",
            "AAAA21010001470246043000",
            "AAAA21010001472799015100",
            "AAAA21010001426772189300",
            "AAAA21010001755862233100",
            "AAAA21010001529881000300",
            "AAAA21010001572668381100",
            "AAAA21010002042570133400",
            "AAAA21010001649653396900",
            "AAAA21010001551193156700",
            "AAAA21010001673237346000",
            "AAAA21010001532654093500",
            "AAAA21010001465874346100",
            "AAAA21010001493124346500",
            "AAAA21010001669366153100",
            "AAAA21010001706739292100",
            "AAAA21010001535158245700",
            "AAAA21010001450966200000",
            "AAAA21010001530741245700",
            "AAAA21010001394285049400",
            "AAAA21010001517289194600",
            "AAAA21010001624947035400",
            "AAAA21010001508769265500",
            "AAAA21010001449270188400",
            "AAAA21010002170053256300",
            "AAAA21010001430836253600",
            "AAAA21010001472203050800",
            "AAAA21010001434800185900",
            "AAAA21010001435247185900",
            "AAAA21010001352458098400",
            "AAAA21010001474888072500",
            "AAAA21010001399258370600",
            "AAAA21010003926157252000",
            "AAAA21010001450340390100",
            "AAAA21010001652457087600",
            "AAAA21010001575829401600",
            "AAAA21010001658845319100",
            "AAAA21010001401851309600",
            "AAAA21010001678101138300",
            "AAAA21010001709901052700",
            "AAAA21010001536343051500",
            "AAAA21010002068591113000",
            "AAAA21010001340878247800",
            "AAAA21010001278679384000",
            "AAAA21010001604639208500",
            "AAAA21010001707273162800",
            "AAAA21010001593080062000",
            "AAAA21010001463104195000",
            "AAAA21010001513209286500",
            "AAAA21010003007903272400",
            "AAAA21010001433531003500",
            "AAAA21010001587900253000",
            "AAAA21010001577902398000",
            "AAAA21010001405120308800",
            "AAAA21010001442574244600",
            "AAAA21010001713058068700",
            "AAAA21010001616581033500",
            "AAAA21010001401679060600",
            "AAAA21010001376084347100",
            "AAAA21010001404631385100",
            "AAAA21010001435243185900",
            "AAAA21010001329925306100",
            "AAAA21010001615868079000",
            "AAAA21010001629306080100",
            "AAAA21010001478222355600",
            "AAAA21010001552367013900",
            "AAAA21010001507241214000",
            "AAAA21010001425966368700",
            "AAAA21010001544340220700",
            "AAAA21010001618685014100",
            "AAAA21010001575139406300",
            "AAAA21010001335388299000",
            "AAAA21010002170988133900",
            "AAAA21010001656041069100",
            "AAAA21010002220688133000",
            "AAAA21010001593890405800",
            "AAAA21010001377244003800",
            "AAAA21010001736872201100",
            "AAAA21010001241823339900",
            "AAAA21010001670603346600",
            "AAAA21010001800005024000",
            "AAAA21010001441403107800",
            "AAAA21010002777666261200",
            "AAAA21010001608241011200",
            "AAAA21010001503026082100",
            "AAAA21010001540671100700",
            "AAAA21010001415325384700",
            "AAAA21010001503037082100",
            "AAAA21010001699420293800",
            "AAAA21010001540883008600",
            "AAAA21010001419640051700",
            "AAAA21010001546479052300",
            "AAAA21010001568441220100",
            "AAAA21010001564382128100",
            "AAAA21010002708906267100",
            "AAAA21010001393775405200",
            "AAAA21010001524652380000",
            "AAAA21010003082240247100",
            "AAAA21010001409705126800",
            "AAAA21010001594896302700",
            "AAAA21010001551857306500",
            "AAAA21010001512251281500",
            "AAAA21010001543100360500",
            "AAAA21010001687520240300",
            "AAAA21010001460559094400",
            "AAAA21010001469804349200",
            "AAAA21010001389706405200",
            "AAAA21010001593646213700",
            "AAAA21010001491404350900",
            "AAAA21010001393916023500",
            "AAAA21010001501632033800",
            "AAAA21010001442518148400",
            "AAAA21010001524295318100",
            "AAAA21010001589728188300",
            "AAAA21010001922644134800",
            "AAAA21010001277518102300",
            "AAAA21010002429762309500",
            "AAAA21010001542084116100",
            "AAAA21010001553079393500",
            "AAAA21010001603015263300",
            "AAAA21010001561361115100",
            "AAAA21010001541328040200",
            "AAAA21010001548288386800",
            "AAAA21010001626905378700",
            "AAAA21010001451431387100",
            "AAAA21010002790896271500",
            "AAAA21010001292051107700",
            "AAAA21010002230840277500",
            "AAAA21010001412858408600",
            "AAAA21010001700572222700",
            "AAAA21010001430485317100",
            "AAAA21010001411521111700",
            "AAAA21010001456761388600",
            "AAAA21010001646103280900",
            "AAAA21010001621412234200",
            "AAAA21010001541897318700",
            "AAAA21010001456650165200",
            "AAAA21010001351003158900",
            "AAAA21010001386627116500",
            "AAAA21010001386546116500",
            "AAAA21010001769403393000",
            "AAAA21010001433889059100",
            "AAAA21010001386901116500",
            "AAAA21010001445960087400",
            "AAAA21010001450105301400",
            "AAAA21010001485669385400",
            "AAAA21010001920923370700",
            "AAAA21010001392135146700",
            "AAAA21010001288420070900",
            "AAAA21010001348263196000",
            "AAAA21010001472110356100",
            "AAAA21010001623608370500",
            "AAAA21010001461564219000",
            "AAAA21010001528659041500",
            "AAAA21010001440489077100",
            "AAAA21010001621200014500",
            "AAAA21010001350742317700",
            "AAAA21010001484735378400",
            "AAAA21010001639287163500",
            "AAAA21010001462138277300",
            "AAAA21010001381019391100",
            "AAAA21010001731835075800",
            "AAAA21010001526318336100",
            "AAAA21010001521856017900",
            "AAAA21010004798994266800",
            "AAAA21010001632086326700",
            "AAAA21010001594015029600",
            "AAAA21010001455522075900",
            "AAAA21010001489155193500",
            "AAAA21010001502461259200",
            "AAAA21010001502413189200",
            "AAAA21010001408284112600",
            "AAAA21010001327813202900",
            "AAAA21010001643116046100",
            "AAAA21010001394401018100",
            "AAAA21010001452794227000",
            "AAAA21010001631731062700",
            "AAAA21010001564030406900",
            "AAAA21010001489603259800",
            "AAAA21010001440590077100",
            "AAAA21010002843114257300",
            "AAAA21010001595191018000",
            "AAAA21010001911308152000",
            "AAAA21010001577556327300",
            "AAAA21010001829702067500",
            "AAAA21010001443209327200",
            "AAAA21010001706061279100",
            "AAAA21010001609413335200",
            "AAAA21010001627703100300",
            "AAAA21010001549979067200",
            "AAAA21010001530479317400",
            "AAAA21010001587515303400",
            "AAAA21010001558586033200",
            "AAAA21010001484513279000",
            "AAAA21010001460543037300",
            "AAAA21010001421909043800",
            "AAAA21010001355292335000",
            "AAAA21010001470072335800",
            "AAAA21010001543011227300",
            "AAAA21010001726810182000",
            "AAAA21010001703253173000",
            "AAAA21010001525579171500",
            "AAAA21010001433708059100",
            "AAAA21010001444445355200",
            "AAAA21010001678143105100",
            "AAAA21010001601853196900",
            "AAAA21010001292402148700",
            "AAAA21010001231709179200",
            "AAAA21010001630644041600",
            "AAAA21010001515846319300",
            "AAAA21010001714536206100",
            "AAAA21010001513599012500",
            "AAAA21010001666965131400",
            "AAAA21010001590203188300",
            "AAAA21010001613787044900",
            "AAAA21010001463827399200",
            "AAAA21010001584549002200",
            "AAAA21010001576562178300",
            "AAAA21010001714457206100",
            "AAAA21010001471272054500",
            "AAAA21010001417186111700",
            "AAAA21010001419156394200",
            "AAAA21010001501611239900",
            "AAAA21010001410921115800",
            "AAAA21010001331728352500",
            "AAAA21010002041150137100",
            "AAAA21010001507046295400",
            "AAAA21010001444995322300",
            "AAAA21010001745611110700",
            "AAAA21010001829240379000",
            "AAAA21010001645558043300",
            "AAAA21010001500832186600",
            "AAAA21010001925257298800",
            "AAAA21010001626998378100",
            "AAAA21010001418181068500",
            "AAAA21010001542294234000",
            "AAAA21010001457054404500",
            "AAAA21010001274901409300",
            "AAAA21010001634171335600",
            "AAAA21010001588491381900",
            "AAAA21010001616000350700",
            "AAAA21010001631874326700",
            "AAAA21010001462249040600",
            "AAAA21010001595448207900",
            "AAAA21010001477647117500",
            "AAAA21010001761251332300",
            "AAAA21010001524065180300",
            "AAAA21010001366504327900",
            "AAAA21010001409668226900",
            "AAAA21010001521115398100",
            "AAAA21010001580587196600",
            "AAAA21010001395512405200",
            "AAAA21010001572877381100",
            "AAAA21010001512942124500",
            "AAAA21010001226030087200"
    );

    private ENT621InventoryHead buildInventoryHead() {
        ENT621InventoryHead inventoryHead = new ENT621InventoryHead();
        //系统唯一序号、报送类型、报送时间、业务状态
        inventoryHead.setGuid(UUID.randomUUID().toString().toUpperCase());
        inventoryHead.setAppType(CebDeclareType.CREATE.getType());
        if (editDeclareList.contains(info.getCustomsInventoryDto().getOrderNo())) {
            inventoryHead.setAppType(CebDeclareType.EDIT.getType());
            inventoryHead.setInvtNo(info.getCustomsInventoryDto().getInventoryNo());
            inventoryHead.setPreNo(info.getCustomsInventoryDto().getPreNo());
        }
        inventoryHead.setAppTime(CebDateUtil.format(new Date(), CebDateUtil.defaultCebDateStringFormat));
        inventoryHead.setAppStatus("2");
        //订单编号  交易平台的订单编号，同一交易平台的订单编号应唯一。订单编号长度不能超过60位。
        inventoryHead.setOrderNo(info.getCustomsInventoryDto().getOrderNo());
        //电商平台代码、电商平台名称、电商企业代码、电商企业名称
        inventoryHead.setEbpCode(info.getEbpCompanyDTO().getCebCode());
        inventoryHead.setEbpName(info.getEbpCompanyDTO().getCebName());
        inventoryHead.setEbcCode(info.getEbcCompanyDTO().getCebCode());
        inventoryHead.setEbcName(info.getEbcCompanyDTO().getCebName());
        //物流运单编号、物流企业代码、物流企业名称
        inventoryHead.setLogisticsNo(info.getCustomsInventoryDto().getLogisticsNo());
        inventoryHead.setLogisticsCode(info.getLogisticsCompanyDTO().getCebCode());
        inventoryHead.setLogisticsName(info.getLogisticsCompanyDTO().getCebName());
        //企业内部标识单证的编号 、担保企业编号
        inventoryHead.setCopNo(info.getCustomsInventoryDto().getSn());
        inventoryHead.setAssureCode(info.getAssureCompanyDTO().getCebCode());
        //账册编号
        inventoryHead.setEmsNo(info.getCustomsInventoryDto().getBookNo());
        //进出口标记、申报日期、申报海关代码、口岸海关代码、进口日期
        inventoryHead.setIeFlag("I");
        inventoryHead.setDeclTime(DateUtil.getCDateString("yyyyMMdd"));
        inventoryHead.setCustomsCode("0213");
        inventoryHead.setPortCode("0213");
        inventoryHead.setIeDate(DateUtil.getTCurrentDate("yyyyMMdd"));
        //订购人信息 固定身份证 收件地址
        inventoryHead.setBuyerIdType(info.getCustomsInventoryDto().getBuyerIdType());
        inventoryHead.setBuyerIdNumber(info.getCustomsInventoryDto().getBuyerIdNumber());
        inventoryHead.setBuyerTelephone(info.getCustomsInventoryDto().getBuyerTelNumber());
        inventoryHead.setBuyerName(info.getCustomsInventoryDto().getBuyerName());
        inventoryHead.setConsigneeAddress(info.getCustomsInventoryDto().getConsigneeAddress());
        //申报企业代码、名称、区内企业代码、区内企业名称
        String declareCompanyCode = info.getDeclareCompanyDTO().getCebCode();
        String declareCompanyName = info.getDeclareCompanyDTO().getCebName();
        String internalAreaCompanyNo = info.getInternalAreaCompany().getCebCode();
        String internalAreaCompanyName = info.getInternalAreaCompany().getCebName();
        inventoryHead.setAgentCode(declareCompanyCode);
        inventoryHead.setAgentName(declareCompanyName);
        inventoryHead.setAreaCode(internalAreaCompanyNo);
        inventoryHead.setAreaName(internalAreaCompanyName);
        //贸易方式、运输方式
        inventoryHead.setTradeMode("1210");
        inventoryHead.setTrafMode("Y");
        //起运国、运费、保费、币种、件数
        inventoryHead.setCountry("142");
        if (info.getCustomsInventoryDto().getFreight() != null) {
            inventoryHead.setFreight(info.getCustomsInventoryDto().getFreight().toString());
        } else {
            inventoryHead.setFreight("0");
        }
        inventoryHead.setInsuredFee("0");
        inventoryHead.setCurrency("142");
        inventoryHead.setPackNo("1");
        //出区进口流水号、申报号、申报人名称、申报人联系方式
        inventoryHead.setIoSerialNo(info.getCustomsInventoryDto().getOrderNo());//2.0传的出库单号
        inventoryHead.setDeclNo(info.getCustomsInventoryDto().getOrderNo());//2.0传的出库单号
        inventoryHead.setDeclPerson(info.getInternalAreaCompany().getCebName());
        inventoryHead.setDeclTel("18268853606");
        //支付交易号、申报类型代码、检验检疫申报单位代码、检验检疫电商企业代码、检验检疫物流企业编号、施检机构代码
        inventoryHead.setPaymentNo(info.getCustomsInventoryDto().getPayTransactionId());
        inventoryHead.setDeclTypeCode("SI");
        inventoryHead.setEntCode("Q120000201808000039");
        inventoryHead.setCbecode("Q120000201808000039");
        inventoryHead.setCiqLogisticsCode("Q12150016000027");
        inventoryHead.setCheckOrgCode("121500");
        //发货人证件类型编号(默认身份证)、集货/备货标识
        inventoryHead.setIdType("1");
        inventoryHead.setStockFlag("C");
        //收货人信息
        inventoryHead.setConsigneeCname(info.getCustomsInventoryDto().getBuyerName());
        inventoryHead.setConsigneeTel(info.getCustomsInventoryDto().getBuyerTelNumber());
        //发货人信息
        inventoryHead.setConsignorCname("海外购自营");
        inventoryHead.setConsignorTel("18268853606");
        inventoryHead.setConsignorAddress("天津自贸试验区（东疆保税港区）洛阳道601号海丰物流园9号库一层3-4单元");
        inventoryHead.setIdCard("91120118MA05JG4M4J");//天津代塔统一社会信用代码
        return inventoryHead;
    }

    private List<ENT621InventoryList> buildInventoryList(ENT621InventoryHead inventoryHead) {
        BigDecimal grossWeight = new BigDecimal("0");
        BigDecimal netWeight = new BigDecimal("0");
        BigDecimal totalValues = new BigDecimal("0");
        List<ENT621InventoryList> inventoryLists = new ArrayList<>();
        int num = 1;
        for (CustomsInventoryItemInfo inventoryItemInfo : info.getListCustomsInventoryItemInfo()) {
            ENT621InventoryList inventoryList = new ENT621InventoryList();
            inventoryList.setGnum(num + "");
            inventoryList.setItemRecordNo(inventoryItemInfo.getItemRecordNo());
            inventoryList.setItemNo(inventoryItemInfo.getItemNo());
            inventoryList.setItemName(inventoryItemInfo.getItemName());
            inventoryList.setGcode(inventoryItemInfo.getHsCode());
            inventoryList.setGname(inventoryItemInfo.getItemName());
            inventoryList.setGmodel(inventoryItemInfo.getGmodle());
            inventoryList.setCountry(inventoryItemInfo.getCountry());
            inventoryList.setCurrency("142");
            inventoryList.setCiqcurrency("156");
            inventoryList.setQty("" + inventoryItemInfo.getCount());

            inventoryList.setQty1(NumberUtil.parseForDeclareValue(inventoryItemInfo.getFirstCount(), inventoryItemInfo.getCount()));
            inventoryList.setUnit1(inventoryItemInfo.getUnit1());
            inventoryList.setUnit(inventoryItemInfo.getUnit());
            if (StringUtils.isNotBlank(inventoryItemInfo.getUnit2())) {
                inventoryList.setUnit2(inventoryItemInfo.getUnit2().trim());
                inventoryList.setQty2(NumberUtil.parseForDeclareValue(inventoryItemInfo.getSecondCount().trim(), inventoryItemInfo.getCount()));
            }
            if (inventoryItemInfo.getUnitPrice() != null) {
                inventoryList.setPrice(inventoryItemInfo.getUnitPrice().toString());
            } else {
                inventoryList.setPrice("0.00");
            }
            if (inventoryItemInfo.getUnitPrice() != null && inventoryItemInfo.getCount() != null) {
                inventoryList.setTotalPrice((new BigDecimal(inventoryItemInfo.getCount()).multiply(inventoryItemInfo.getUnitPrice())).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            } else {
                inventoryList.setTotalPrice("0.00");
            }
            inventoryList.setIoGoodsSerialNo(inventoryItemInfo.getIoGoodsSerialNo());
            inventoryList.setEntCode("Q120000201808000039");
            inventoryList.setGoodsRegNo(inventoryItemInfo.getGoodsRegNo());
            inventoryList.setDeclIINo(inventoryItemInfo.getDeclIINo());
            inventoryList.setLogisticsNo(info.getCustomsInventoryDto().getLogisticsNo());
            inventoryList.setOrderNo(info.getCustomsInventoryDto().getOrderNo());
            inventoryList.setPaymentNo(info.getCustomsInventoryDto().getPayTransactionId());
            inventoryList.setEntCname(info.getDeclareCompanyDTO().getCebName());
            inventoryList.setConsigneeCname(info.getCustomsInventoryDto().getBuyerName());
            inventoryList.setConsigneeIdType("1");
            inventoryList.setOriginCountryCode(inventoryItemInfo.getOriginCountryCode());//疑问  国检代码
            inventoryList.setQtyUnitCode(inventoryItemInfo.getUnit());
            inventoryList.setConsigneeNo("SuNingAPI_TJ");
            num++;
            inventoryLists.add(inventoryList);
            if (null != inventoryItemInfo.getWeight()) {
                grossWeight = grossWeight.add(BigDecimal.valueOf(inventoryItemInfo.getWeight() * inventoryItemInfo.getCount()));
                netWeight = netWeight.add(BigDecimal.valueOf(Float.parseFloat(inventoryItemInfo.getFirstCount()) * inventoryItemInfo.getCount()));
                totalValues = totalValues.add((new BigDecimal(inventoryItemInfo.getCount()).multiply(inventoryItemInfo.getUnitPrice())).setScale(2, BigDecimal.ROUND_HALF_UP));
            }
        }
        inventoryHead.setGrossWeight(grossWeight.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
        inventoryHead.setNetWeight(netWeight.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
        inventoryHead.setTotalValues(totalValues.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
        return inventoryLists;
    }

    private List<ENT621IODeclContainerList> buildIODeclContainerList() {
        List<ENT621IODeclContainerList> iODeclContainerLists = new ArrayList<>();
        ENT621IODeclContainerList ioDeclContainerList = new ENT621IODeclContainerList();
        //先跟范例报文保持一致
        ioDeclContainerList.setBizType("2");
        ioDeclContainerList.setConModel("12*32");
        ioDeclContainerList.setConNo("3221");
        ioDeclContainerList.setConNum("1");
        iODeclContainerLists.add(ioDeclContainerList);
        return iODeclContainerLists;
    }

    private List<ENT621IODeclOrderRelationList> buildIODeclOrderRelationList() {
        List<ENT621IODeclOrderRelationList> iODeclOrderRelationLists = new ArrayList<>();
        ENT621IODeclOrderRelationList ioDeclOrderRelationList = new ENT621IODeclOrderRelationList();
        ioDeclOrderRelationList.setBizType("2");
        ioDeclOrderRelationList.setDeclNo(info.getCustomsInventoryDto().getOrderNo());//2.0传的出库单号
        ioDeclOrderRelationList.setOrderNo(info.getCustomsInventoryDto().getOrderNo());
        iODeclOrderRelationLists.add(ioDeclOrderRelationList);
        return iODeclOrderRelationLists;
    }

    private ENTBaseTransfer buildBaseTransfer() {
        ENTBaseTransfer baseTransfer = new ENTBaseTransfer();
        baseTransfer.setCopCode(info.getDeclareCompanyDTO().getCebCode());
        baseTransfer.setCopName(info.getDeclareCompanyDTO().getCebName());
        baseTransfer.setDxpId(info.getDeclareCompanyDTO().getDxpId());
        baseTransfer.setDxpMode("DXP");
        return baseTransfer;
    }
}
