package com.danding.cds.declare.sdk.clear.base;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

public class CustomsClientRegistry implements Serializable {

    private Map<String, CustomsClient> clientLib = new HashMap<>();

    /**
     * 仓库客户端注册
     * @param client
     */
    public void register(CustomsClient client){
        clientLib.put(client.getSystem().getValue() + client.getVersion(), client);
    }

    /**
     * 获取客户端，默认1.0版本
     * @param system
     * @return
     */
    public CustomsClient findClient(String system){
        return findClient(system,"1.0");
    }

    public CustomsClient findClient(String system, String version){
        return clientLib.get(system + version);
    }
}
