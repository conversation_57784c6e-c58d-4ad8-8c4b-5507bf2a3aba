package com.danding.cds.declare.sdk.utils;

import com.danding.cds.common.utils.ObjectMapperUtil;
import com.danding.cds.declare.sdk.bean.vo.ShipmentDeclareReqVo;
import com.danding.cds.declare.sdk.bean.vo.ShipmentDeclareResVo;
import com.danding.component.http.SaasHttpConstant;
import com.danding.core.tenant.SimpleTenantHelper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.kevinsawicki.http.HttpRequest;
import lombok.extern.slf4j.Slf4j;

/**
 * @program: cds-center
 * @description: TMS INVOKE
 * @author: 潘本乐（Belep）
 * @create: 2021-11-22 17:23
 **/
@Slf4j
public class ThirdShipmentDeclareUtil {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static final String URL = "http://tms.yang800.com:8090/v2/api/uploadwaybill";

    /**
     * 第三方运单申报
     *
     * @param declareReqVo 运单申报信息
     * @return
     */
    public static ShipmentDeclareResVo thirdShipmentDeclare(ShipmentDeclareReqVo declareReqVo) {
        String requestBody = null;
        try {
            requestBody = objectMapper.writeValueAsString(declareReqVo);
            log.info("第三方运单申报 请求报文 : {}", requestBody);
        } catch (JsonProcessingException e) {
            log.error("第三方运单申报异常: {}", e.getMessage());
            ShipmentDeclareResVo shipmentDeclareResVo = new ShipmentDeclareResVo();
            shipmentDeclareResVo.setSuccess(false);
            shipmentDeclareResVo.setMessage(e.getMessage());
            return shipmentDeclareResVo;
        }
        HttpRequest httpRequest = HttpRequest.post(URL)
                .header(SaasHttpConstant.HEADER_TENANT_ID, SimpleTenantHelper.getTenantIdStr())
                .header("Content-Type", HttpRequest.CONTENT_TYPE_JSON)
                .connectTimeout(15000)
                .readTimeout(15000)
                .send(requestBody);
        String body = httpRequest.body();
        log.info("第三方运单申报 响应报文 : {}", body);
        ShipmentDeclareResVo shipmentDeclareResVo = new ShipmentDeclareResVo();
        if (httpRequest.ok()) {
            try {
                return ObjectMapperUtil.readValue(body, ShipmentDeclareResVo.class);
            } catch (Exception e) {
                log.error("第三方运单申报异常 响应信息反序列化异常：{} , 原始报文：{}", body, requestBody);
                shipmentDeclareResVo.setSuccess(false);
                shipmentDeclareResVo.setMessage(e.getMessage());
                return shipmentDeclareResVo;
            }
        } else {
            log.error("第三方运单申报异常 响应报文：{} , 原始报文：{}", body, requestBody);
            shipmentDeclareResVo.setSuccess(false);
            shipmentDeclareResVo.setMessage(body);
        }
        return shipmentDeclareResVo;
    }
}
