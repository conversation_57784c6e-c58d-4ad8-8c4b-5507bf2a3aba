package com.danding.cds.declare.sdk.model.callback;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 最终输出结果
 */
@Getter
@Setter
public class ResultInfo implements java.io.Serializable{
    protected String UUID;//UUID

    private String ebpCode;//电商平台
    private String orderNo;//申报单号
    private Date   callBack;//回执时间
    private String chkMarck; // 回执状态编码
    private String chkMarckDesc; // 回执状态描述

    private String checkCode; //审核状态码，多个审核只取一个
    private String checkCodeDesc;   //审核描述多个审核只取一个
 }
