package com.danding.cds.declare.sdk.bean.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @program: cds-center
 * @description: yun
 * @author: 潘本乐（Belep）
 * @create: 2021-11-22 18:23
 **/
@Data
public class ShipmentDeclareInfoResVo implements Serializable {

    @JsonProperty("order_no")
    private String orderNo;
    @JsonProperty("short_address")
    private String shortAddress;
    @JsonProperty("waybill_code")
    private String waybillCode;
    @JsonProperty("package_center_code")
    private String packageCenterCode;
    @JsonProperty("package_center_name")
    private String packageCenterName;
    @JsonProperty("one_section_code")
    private String oneSectionCode;
    @JsonProperty("two_section_code")
    private String twoSectionCode;
    @JsonProperty("three_section_code")
    private String threeSectionCode;
}
