package com.danding.cds.declare.sdk.payment.tonglian.model;

import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;

/**
 * 通联支付申报请求参数
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(propOrder = {"head", "body"})
@XmlRootElement(name = "PAYMENT_INFO")
public class TongLianCustomsQueryRequest implements Serializable {

    @XmlElement(name = "HEAD")
    private Head head;

    @XmlElement(name = "BODY")
    private Body body;

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(propOrder = {"version", "transDatetime", "charset", "signType", "signMsg"})
    @XmlRootElement(name = "HEAD")
    public static class Head implements Serializable {
        /**
         * 版本号 v5.6
         */
        @XmlElement(name = "VERSION")
        private String version;
        /**
         * 发送时间 yyyyMMddHH24mmss
         */
        @XmlElement(name = "TRANS_DATETIME")
        private String transDatetime;
        /**
         * 字符集 默认值1，UTF-8
         */
        @XmlElement(name = "CHARSET")
        private String charset;
        /**
         * 签名方式 默认值1,MD5签名
         */
        @XmlElement(name = "SIGN_TYPE")
        private String signType;
        /**
         * 签名密文
         */
        @XmlElement(name = "SIGN_MSG")
        private String signMsg;
    }

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(propOrder = {
            "visitorId", "customsCode", "paymentMchtId", "paymentOrderNo", "mchtOrderNo"
    })
    @XmlRootElement(name = "BODY")
    public static class Body implements Serializable {
        /**
         * 接入方ID 接入方编号，商户送‘MCT’
         */
        @XmlElement(name = "VISITOR_ID")
        private String visitorId;
        /**
         * 海关类别
         */
        @XmlElement(name = "CUSTOMS_CODE")
        private String customsCode;
        /**
         * 报关用的商户号
         */
        @XmlElement(name = "PAYMENT_MCHT_ID")
        private String paymentMchtId;
        /**
         * 报关流水号
         */
        @XmlElement(name = "PAYMENT_ORDER_NO")
        private String paymentOrderNo;
        /**
         * 商户订单号 商户平台订单号
         */
        @XmlElement(name = "MCHT_ORDER_NO")
        private String mchtOrderNo;
    }
}