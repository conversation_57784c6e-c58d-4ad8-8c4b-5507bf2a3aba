package com.danding.cds.declare.sdk.utils;

import cn.hutool.core.util.ArrayUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;

/**
 * @program: cds-center
 * @description: 变更申报工具
 * @author: 潘本乐（Belep）
 * @create: 2021-07-28 10:14
 **/
@Slf4j
public class ChangeDeclareUtil {

    /**
     * 变更申报单号
     */
    public static volatile List<String> declareNoList;

    /**
     * 申报单号解析
     *
     * @param declareNos 申报单号，多个用逗号隔开
     * @return
     */
    public static void parseAndSetDeclareNo(String[] declareNos) {

        if (ArrayUtil.isEmpty(declareNos)) {
            return;
        }
        declareNoList = Arrays.asList(declareNos);
        log.info("变更申报单号列表：{}", declareNoList);
    }

}
