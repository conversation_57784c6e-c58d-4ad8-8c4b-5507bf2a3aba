package com.danding.cds.declare.sdk.clear.base;

import com.alibaba.fastjson.JSON;
import com.danding.cds.declare.sdk.CustomsSupport;
import com.danding.cds.declare.sdk.clear.base.callback.CallbackResult;
import com.danding.cds.declare.sdk.clear.base.callback.module.*;
import com.danding.cds.declare.sdk.clear.base.result.*;
import com.danding.cds.declare.sdk.enums.CustomsType;
import com.danding.cds.declare.sdk.model.cancel.WarpCancelOrderInfo;
import com.danding.cds.declare.sdk.model.check.WrapDataPayExInfo;
import com.danding.cds.declare.sdk.model.inventory.WrapInventoryOrderInfo;
import com.danding.cds.declare.sdk.model.order.WrapOrderDeclareInfo;
import com.danding.cds.declare.sdk.model.payment.WrapPaymentInfo;
import com.danding.cds.declare.sdk.model.refund.WarpRefundOrderInfo;
import com.danding.cds.declare.sdk.model.shipment.WrapShipmentInfo;
import com.danding.cds.log.api.dto.TrackLogDTO;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;

/**
 * 海关客户端
 */
@Slf4j
public abstract class CustomsClient implements CustomsOption{

    public abstract CustomsType getSystem();
    protected CustomsSupport support;

    protected String env;
    public String getVersion(){
        return "1.0";
    }

    public CustomsClient(CustomsSupport support, String env) {
        this.support = support;
        this.env = env;
    }

        /**
         * 初始化注入
         * @param registry
         */
    public CustomsClient(CustomsSupport support, CustomsClientRegistry registry, String env) {
        this.support = support;
        this.env = env;
        registry.register(this);
    }

    /**
     * 发送轨迹日志
     *
     * @param trackLogDTO
     */
    public void sendTrackLog(TrackLogDTO trackLogDTO) {
        try {
            support.messageSender.sendMsg(JSON.toJSONString(trackLogDTO), "ccs-trackLog-topic");
        } catch (Exception ex) {
            log.info("轨迹日志记录错误： orderNo={}， 轨迹日志记录错误 trackLog error={}]", trackLogDTO.getDeclareOrderNo(), ex.getMessage(), ex);
        }
    }

    @Override
    public void paymentDeclare(WrapPaymentInfo info) {
    }

    /**
     * 运单申报
     * @param info
     * @return
     */
    @Override
    public ShipmentDeclareResult shipmentDeclare(WrapShipmentInfo info) {
        return null;
    }

    @Override
    public void dataCheckSubmit(WrapDataPayExInfo infoDTO){}
    /**
     * 订单申报
     * @param declareInfo
     */
    @Override
    public OrderDeclareResult orderDeclare(WrapOrderDeclareInfo declareInfo){
        return null;
    }

    /**
     * 清单申报
     * @param info
     */
    @Override
    public InventoryDeclareResult inventoryDeclare(WrapInventoryOrderInfo info){
        return null;
    }

    /**
     * 清单取消
     * @param info
     */
    @Override
    public InventoryCancelResult inventoryCancel(WarpCancelOrderInfo info){
        return null;
    };

    /**
     * 清单退货
     * @param info
     */
    @Override
    public InventoryRefundResult inventoryRefund(WarpRefundOrderInfo info){
        return null;
    };

    /**
     * 解析回执的HTTP请求 获得回执类型和明文
     * @param request
     * @return
     */
    @Override
    public CallbackResult parserCallback(Object request){
        return null;
    }

    /**
     * 解析订单回执
     * @param callbackResult
     * @return
     */
    @Override
    public OrderCallback handelOrderMsg(CallbackResult callbackResult){
        return  null;
    }

    /**
     * 税单回执
     * @param callbackResult
     * @return
     */
    @Override
    public List<TaxResult> handelTaxMsg(CallbackResult callbackResult)
    {
        return Collections.emptyList();
    }

    /**
     * 税单状态回执
     * @param callbackResult
     * @return
     */
    @Override
    public List<TaxStatus> handelTaxStatusMsg(CallbackResult callbackResult)
    {
        return Collections.emptyList();
    }

    @Override
    public InventoryCallback handelInventoryMsg(CallbackResult callbackResult) {
        return null;
    }

    @Override
    public ShipmentCallback handleShipmentMsg(CallbackResult callbackResult) {
        return null;
    }

    @Override
    public List<InventoryCancel> handelInventoryCancel(CallbackResult callbackResult) {
        return null;
    }

    @Override
    public List<InventoryRefund> handelInventoryRefund(CallbackResult callbackResult) {
        return null;
    }

    @Override
    public DeliverResult handelDeliverMsg(CallbackResult callbackResult) {
        return null;
    }
}
