package com.danding.cds.declare.sdk.enums;

public enum CustomsType {
    NULL("", "空"),
    ZHE_JIANG("zhejiang", "浙江"),
    HANG_ZHOU_DATA_CENTER("HZDC", "杭州数据中心"),
    CHONG_QING("chongqing", "重庆"),
    TIAN_JIN("tianjin", "天津"),
    DT_CCS("dt_ccs", "ccs");
    private String value;

    private String desc;

    CustomsType(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static CustomsType getEnum(String value) {
        for (CustomsType type : CustomsType.values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        return NULL;
    }
}
