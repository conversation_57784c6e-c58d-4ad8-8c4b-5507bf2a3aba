package com.danding.cds.declare.sdk.model.check;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CustomsDataPayExInfoDTO implements Serializable {
    @JSONField(ordinal=1)
    private String sessionID;
    @JSONField(ordinal=2)
    private CustomsDataPayExchangeInfoHead payExchangeInfoHead;
    @JSONField(ordinal=3)
    private List<CustomsDataPayExchangeInfoItem> payExchangeInfoLists;
    @JSONField(ordinal=4)
    private String serviceTime;
    @JSONField(ordinal=5)
    private String certNo;
    @JSONField(ordinal=6)
    private String signValue;
}
