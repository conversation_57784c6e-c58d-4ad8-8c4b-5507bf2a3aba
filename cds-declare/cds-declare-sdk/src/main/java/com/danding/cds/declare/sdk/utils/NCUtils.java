package com.danding.cds.declare.sdk.utils;

import com.alibaba.fastjson.JSONObject;
import com.danding.cds.declare.zjport.domain.response.jkfGoodsDeclar.ResponsePersonalGoodsDeclar;
import com.danding.cds.declare.zjport.domain.response.jkfResult.JKFResultDetail;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> by xp
 * @version v1.0
 * @description com.sfebiz.logistics.provider.command.send.ccb.nanchang
 * @ClassName NCUtils
 * @date 2019/11/4 18:40
 */

public class NCUtils {

    public static Map<String ,String> getOneResultDetail(ResponsePersonalGoodsDeclar jkfResultDetails) {
        java.util.Map<String,String> oretMap  = new java.util.HashMap<String,String>();
        oretMap.put("code","");
        oretMap.put("desc","");
        oretMap.put("oret","");
        if (jkfResultDetails == null || StringUtils.isEmpty(jkfResultDetails.getApproveComment())) {
            return oretMap;
        } else {
           // JKFResultDetail detail = jkfResultDetails.get(0);
            String resultInfo = jkfResultDetails.getApproveComment();
            String str[] = StringUtils.split(resultInfo,":");
            if(str!= null&&str.length>=2)
            {
                oretMap.put("code",str[0]);
                oretMap.put("desc",str[1]);
                oretMap.put("oret",str[1]);
            }
            String desc  = formatResultDesc(oretMap);
            oretMap.put("desc",desc);
            return oretMap;
        }
    }

    public static Map<String ,String> getOneResultDetail(List<JKFResultDetail> jkfResultDetails,String code) {
        java.util.Map<String,String> oretMap  = new java.util.HashMap<String,String>();
        oretMap.put("code","");
        oretMap.put("desc","");
        oretMap.put("oret","");
        if (jkfResultDetails == null || jkfResultDetails.size() == 0) {
            return oretMap;
        } else {
            JKFResultDetail detail = jkfResultDetails.get(0);
            String resultInfo = detail.getResultInfo();
            if("处理成功".equalsIgnoreCase(resultInfo))
            {
                oretMap.put("code",code);
                oretMap.put("desc","处理成功");
                oretMap.put("oret","处理成功");
            }else {
                String str[] = StringUtils.split(resultInfo, ";");
                if (str != null && str.length >= 2) {
                    oretMap.put("code", code);
                    oretMap.put("desc", str[1]);
                    oretMap.put("oret", str[1]);
                }
            }
            String desc  = formatResultDesc(oretMap);
            oretMap.put("desc",desc);
            return oretMap;
        }
    }
    public static String formatResultDesc(Map<String ,String> oretMap)
    {
        String code = oretMap.get("code");
        if(code==null)code="";
        String desc = oretMap.get("desc");
        if(desc==null)desc="";
        return "[Code:"+code+";Desc:"+desc+"]";
    }


    /**
     * 拼接响应结果详情
     *
     * @param jkfResultDetails
     * @return
     */
    public static String getResultDetail(List<JKFResultDetail> jkfResultDetails) {
        if (jkfResultDetails == null || jkfResultDetails.size() == 0) {
            return "";
        } else {
            StringBuilder resultDetail = new StringBuilder();
            for (JKFResultDetail jkfResultDetail : jkfResultDetails) {
                resultDetail.append(jkfResultDetail.getResultInfo());
                resultDetail.append(";");
            }
            return resultDetail.toString();
        }
    }
    public static String getTopOneResultDetail(List<JKFResultDetail> jkfResultDetails) {
        if (jkfResultDetails == null || jkfResultDetails.size() == 0) {
            return "";
        } else {
            return jkfResultDetails.get(0).getResultInfo();
        }
    }

    public static String getResultDetailJson(ResponsePersonalGoodsDeclar jkfResultDetails) {
        java.util.HashMap<String,Object> map  = new java.util.HashMap<String,Object>();
        Object result = "";
        if (jkfResultDetails == null ) {
            result =  "";
        } else {
            result = jkfResultDetails;
        }
        map.put("result",result);
        JSONObject jsonObj=new JSONObject(map);
        return jsonObj.toJSONString();
    }

    public static String getResultDetailJson(List<JKFResultDetail> jkfResultDetails) {
        java.util.HashMap<String,Object> map  = new java.util.HashMap<String,Object>();
        String result = "";
        if (jkfResultDetails == null || jkfResultDetails.size() == 0) {
            result =  "";
        } else {
            StringBuilder resultDetail = new StringBuilder();
            for (JKFResultDetail jkfResultDetail : jkfResultDetails) {
                resultDetail.append(jkfResultDetail.getResultInfo());
                resultDetail.append(";");
            }
            result=resultDetail.toString();
        }
        map.put("result",result);
        JSONObject jsonObj=new JSONObject(map);
        return jsonObj.toJSONString();
    }

}
