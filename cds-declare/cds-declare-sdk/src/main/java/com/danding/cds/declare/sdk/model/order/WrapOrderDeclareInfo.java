package com.danding.cds.declare.sdk.model.order;

import com.danding.cds.declare.sdk.model.WrapBeanInfo;
import com.danding.cds.declare.sdk.model.company.CompanyInfo;
import lombok.Data;

import java.util.List;

@Data
public class WrapOrderDeclareInfo extends WrapBeanInfo {

    private String businessKey;

    /**
     * 单据编号
     */
    private String sn;

    /**
     * 海关
     */
    private String customs;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 支付企业信息
     */
    private CompanyInfo payCompany;

    /**
     * 支付单号
     */
    private String payTransactionId;

    /**
     * 申报订单号
     */
    private String declareOrderNo;

    /**
     * 订单税款
     */
    private String orderTaxAmount;

    /**
     * 折扣
     */
    private String discount;

    /**
     * 订单运费
     */
    private String freight;

    /**
     * 成交时间
     */
    private Long tradeTime;

    /**
     * 收件人信息
     */
    private String consigneeEmail;
    private String consigneeTel;
    private String consignee;
    private String consigneeAddress;

    /**
     * 发件信息
     * TODO:这里需确定name是否是海关发件人[境外|境内公司主体名称]
     */
    private String senderName;

    /**
     * 物流企业
     * PS:海关好像不校验，可以随便固定填一个
     */
    private CompanyInfo logisticsCompany;

    /**
     * 商品详情
     */
    private List<DeclareOrderItem> itemList;

    /**
     * 支付人|订购人信息
     */
    private String payerIdNumber;
    private String payerName;
    private String payerTelNumber;


    private String signWay = "swxa"; // 默认三未信安加密机方式

    private String dxpId = "";

    @Override
    public String getDeclareNos() {
        return this.declareOrderNo;
    }
}
