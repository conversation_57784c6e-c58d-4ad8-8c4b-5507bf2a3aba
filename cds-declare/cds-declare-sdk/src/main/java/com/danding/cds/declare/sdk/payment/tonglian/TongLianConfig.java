package com.danding.cds.declare.sdk.payment.tonglian;

import com.danding.cds.declare.sdk.payment.base.BaseConfig;

/**
 * <AUTHOR>
 * @description
 * @date 2021/11/26
 */
public class TongLianConfig extends BaseConfig {

    String tongLianCustomsDeclareUrl;
    String tongLianCustomsReDeclareUrl;

    public TongLianConfig() {
//        lianDongCustomsDeclareUrl = "https://uatfx.soopay.net/cberest/v1";
        tongLianCustomsDeclareUrl = "https://service.allinpay.com/customs/pvcapply";

        tongLianCustomsReDeclareUrl = "https://service.allinpay.com/customs/access";
    }


}
