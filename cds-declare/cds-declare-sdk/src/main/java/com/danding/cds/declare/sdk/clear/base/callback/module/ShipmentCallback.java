package com.danding.cds.declare.sdk.clear.base.callback.module;

import com.danding.cds.http.saas.annotation.TenantHttpField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ShipmentCallback implements Serializable {
    /**
     * 物流企业代码
     */
    private String logisticsCode;

    /**
     * 物流运单编号
     */
    @TenantHttpField(alias = "logisticsNo")
    private String logisticsNo;
    /**
     * 回执时间
     */
    private Date returnTime;

    /**
     * 回执状态
     */
    private String returnStatus;

    /**
     * 回执信息
     */
    private String returnInfo;

    private String sn;
}
