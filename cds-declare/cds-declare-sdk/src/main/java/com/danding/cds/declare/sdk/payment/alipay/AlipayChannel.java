package com.danding.cds.declare.sdk.payment.alipay;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.cds.declare.sdk.CustomsReport;
import com.danding.cds.declare.sdk.CustomsSupport;
import com.danding.cds.declare.sdk.enums.PayCustoms;
import com.danding.cds.declare.sdk.enums.PayCustomsChannel;
import com.danding.cds.declare.sdk.model.payment.CustomsPayDeclareResult;
import com.danding.cds.declare.sdk.model.payment.WrapPaymentInfo;
import com.danding.cds.declare.sdk.payment.alipay.model.AliCustomsPayPushRequestModel;
import com.danding.cds.declare.sdk.payment.base.BaseChannel;
import com.danding.cds.declare.sdk.payment.base.PayException;
import com.danding.component.http.SaasHttpConstant;
import com.danding.core.tenant.SimpleTenantHelper;
import com.github.kevinsawicki.http.HttpRequest;
import com.google.common.base.Charsets;
import com.google.common.hash.Hashing;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import java.util.*;

/**
 * 支付渠道
 * 支付宝
 */
@Slf4j
public class AlipayChannel extends BaseChannel<AlipayToken> {

    protected AlipayConfig alipayConfig;

    public AlipayChannel(CustomsSupport support) {
        super(support);
        alipayConfig = new AlipayConfig();
    }


    @Override
    public PayCustomsChannel getPayChannel(){
        return PayCustomsChannel.ALIPAY;
    }

    @Override
    public AlipayToken makeToken(String tokenJson){
        return JSON.parseObject(tokenJson,AlipayToken.class);
    }

    @Override
    public CustomsPayDeclareResult customDeclare(WrapPaymentInfo customDeclareParams, String tokenJson)
        throws PayException {
        CustomsReport customsReport = new CustomsReport(CustomsReport.TYPE_SEND,CustomsReport.SYSTEM_PAY_CHANNEL,CustomsReport.PROCESS_CLEAR_DECLARE_PAYMENT);
        try{
            log.info("[op:AlipayChannel-customsDeclare] CustomsDeclareParams={}, tokenJson={}", JSON.toJSONString(customDeclareParams),tokenJson);
            AlipayToken token = makeToken(tokenJson);
            AliCustomsPayPushRequestModel model = new AliCustomsPayPushRequestModel();
            // 公共参数
            model.setService("alipay.acquire.customs");
            model.setPartner(token.getPartner());
            model.setInputCharset("UTF-8");
            // 订单信息
            model.setOutRequestNo(customDeclareParams.getOutOrderNo());
            model.setTradeNo(customDeclareParams.getTradePayNo());
            model.setAmount(customDeclareParams.getAmount().toString());
            // 商户备案信息
            model.setMerchantCustomsCode(customDeclareParams.getMerchantCustomsCode());
            model.setMerchantCustomsName(customDeclareParams.getMerchantCustomsName());
            // 海关
            switch (PayCustoms.getEnum(customDeclareParams.getCustoms())){
                case JINYI:
                case HANGZHOU:
                case DEQING:
                    model.setCustomsPlace("zongshu");
                    break;
                case GUANGZHOU_NS:
                case GUANGZHOU_NS_GJ:
                    model.setCustomsPlace("zongshu");
                    break;
                case TIANJIN:
                    model.setCustomsPlace("zongshu");
                    break;
                case ZONGSHU:
                    model.setCustomsPlace("zongshu");
                    break;
                case SHANGHAI:
                    model.setCustomsPlace("shanghai_cbt");
                    break;
                default:
                    throw new PayException("支付宝申报海关未找到匹配项");
            }
            // 拆单
            if (customDeclareParams.getSplitFlag()){
                model.setIsSplit("T");
                if (CustomsSupport.ENV_ONLINE.equals(CustomsSupport.env)){
                    model.setSubOutBizNo(customDeclareParams.getOutOrderNo());
                }else {
                    model.setSubOutBizNo(CustomsSupport.env + customDeclareParams.getOutRequestNo());
                }
            }else {
                model.setIsSplit("F");
            }
            // 订购人信息
            model.setBuyerIdNo(customDeclareParams.getBuyerIdNo());
            model.setBuyerName(customDeclareParams.getBuyerName());

            JSONObject paramMap = JSON.parseObject(JSON.toJSONString(model));
            Map<String, Object> sortedParams = new TreeMap<>();
            sortedParams.putAll(paramMap);
            String md5Sign = Hashing
                .md5().newHasher().putString(createLinkString(sortedParams)+token.getKey(), Charsets.UTF_8).hash().toString();
            paramMap.put("sign_type","MD5");
            paramMap.put("sign",md5Sign);
            log.info("[op:alipayCustomsDeclare] request url:{},param={}",alipayConfig.getOldGateway(), JSON.toJSONString(paramMap));
            customsReport.buildRequest(JSON.toJSONString(paramMap));
            String content = "";
            if (CustomsSupport.ENV_ONLINE.equals(CustomsSupport.env)){
                HttpRequest httpRequest = HttpRequest.get(alipayConfig.getOldGateway(),paramMap,true)
                        .header(SaasHttpConstant.HEADER_TENANT_ID, SimpleTenantHelper.getTenantIdStr());
                if (httpRequest.ok()){
                    content = httpRequest.body();
                    log.info("[op:alipayCustomsDeclare] response={}",content);
                }else {
                    throw new PayException("alipay.connect.error");
                }
            }else {
                content =
                        "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                        "<alipay>\n" +
                        "    <is_success>T</is_success>\n" +
                        "    <request>\n" +
                        "        <param name=\"merchant_customs_name\">杭州但丁云科技有限公司</param>\n" +
                        "        <param name=\"amount\">9.90</param>\n" +
                        "        <param name=\"_input_charset\">UTF-8</param>\n" +
                        "        <param name=\"is_split\">T</param>\n" +
                        "        <param name=\"sub_out_biz_no\">DOCP202009031426560001031309</param>\n" +
                        "        <param name=\"sign\">cf98d9badec16df20e8dace49cc60b38</param>\n" +
                        "        <param name=\"buyer_name\">郭献杰</param>\n" +
                        "        <param name=\"partner\">2088621610828354</param>\n" +
                        "        <param name=\"service\">alipay.acquire.customs</param>\n" +
                        "        <param name=\"trade_no\">2020090322001492301435258200</param>\n" +
                        "        <param name=\"customs_place\">hangzhou_zongshu</param>\n" +
                        "        <param name=\"merchant_customs_code\">3301964J31</param>\n" +
                        "        <param name=\"buyer_id_no\">330322199701093214</param>\n" +
                        "        <param name=\"out_request_no\">DOCP202009031426560001031309</param>\n" +
                        "        <param name=\"sign_type\">MD5</param>\n" +
                        "    </request>\n" +
                        "    <response>\n" +
                        "        <alipay>\n" +
                        "            <alipay_declare_no>2020090311082308643309748</alipay_declare_no>\n" +
                        "            <currency>142</currency>\n" +
                        "            <identity_check>T</identity_check>\n" +
                        "            <out_request_no>DOCP202009031426560001031309</out_request_no>\n" +
                        "            <pay_code>ZF14021901</pay_code>\n" +
                        "            <pay_transaction_id>2020090322001492301435258200</pay_transaction_id>\n" +
                        "            <result_code>SUCCESS</result_code>\n" +
                        "            <total_amount>9.90</total_amount>\n" +
                        "            <trade_no>2020090311082308643309748</trade_no>\n" +
                        "            <ver_dept>3</ver_dept>\n" +
                        "        </alipay>\n" +
                        "    </response>\n" +
                        "    <sign>c1d1990ccb48ec4ae215f07d45b0d940</sign>\n" +
                        "    <sign_type>MD5</sign_type>\n" +
                        "</alipay>";
            }
            if(StringUtils.isBlank(content)){
                throw new PayException("alipay.response.is.empty");
            }
            customsReport.buildResponse(content);
            CustomsPayDeclareResult result = null;
            Document document =null;
            try {
                document = DocumentHelper.parseText(content);
            } catch (DocumentException e) {
                log.warn("处理异常：{}", e.getMessage(), e);
            }
            if(document!=null){
                Element rootElement = document.getRootElement();
                Element is_success = rootElement.element("is_success");
                String isSuccess = is_success.getTextTrim();
                if(isSuccess.equals("T")){
                    Element response = rootElement.element("response");
                    Element alipay_response = response.element("alipay");
                    Element result_code = alipay_response.element("result_code");
                    Element identity_check = alipay_response.element("identity_check");
                    String resultCode = result_code.getTextTrim();
                    if("SUCCESS".equals(resultCode)){
                        Element trade_no = alipay_response.element("trade_no");
                        String tradeNo = trade_no.getTextTrim();
                        Element alipay_declare_no = alipay_response.element("alipay_declare_no");
                        String alipayDeclareNo = alipay_declare_no.getTextTrim();
                        Element ver_dept = alipay_response.element("ver_dept");
                        String verDept = ver_dept.getTextTrim();
                        Element pay_transaction_id = alipay_response.element("pay_transaction_id");
                        String payTransactionId = pay_transaction_id.getTextTrim();
                        result = CustomsPayDeclareResult.ok(verDept,payTransactionId);
                        result.setSubBankNo(tradeNo);
                        // 支付宝的verDept与海关一致，不需要额外转换
                        log.debug("[op:alipayPush] verDept={}, payTransactionId={}",verDept, payTransactionId);
                        if ("T".equals(identity_check.getTextTrim())){
                            result.setIdentityCheck(true);
                        }else {
                            result.setIdentityCheck(false);
                        }
                    }else{
                        Element detail_error_code = alipay_response.element("detail_error_code");
                        String detailErrorCode = detail_error_code.getTextTrim();
                        Element detail_error_des = alipay_response.element("detail_error_des");
                        String detailErrorDes = detail_error_des.getTextTrim();
                        result = CustomsPayDeclareResult.fail(detail_error_des.getTextTrim());
                        result.setIdentityCheck(true);
                    }
                }else {
                    Element error = rootElement.element("error");
                    String errorStr = error.getTextTrim();
                    result = CustomsPayDeclareResult.fail(errorStr);
                }
            }
            result.setExtra(content);
            result.setPostMsg(JSON.toJSONString(paramMap));
            result.setCustoms(customDeclareParams.getCustoms());
            result.setOutRequestNo(customDeclareParams.getOutRequestNo());
            customsReport.buildProcessData(result);
            support.accept(customsReport);
            return result;
        }catch (Exception e){
            return CustomsPayDeclareResult.fail("系统异常" + e.getMessage());
        }
    }

    public static void main(String[] args) {
        JSONObject paramMap = JSON.parseObject("{\n" +
                "    \"merchant_customs_name\":\"杭州但丁云科技有限公司\",\n" +
                "    \"amount\":\"180.00\",\n" +
                "    \"_input_charset\":\"UTF-8\",\n" +
                "    \"is_split\":\"T\",\n" +
                "    \"sub_out_biz_no\":\"DOCP202001071131320001013178\",\n" +
                "    \"buyer_name\":\"苏亦菲\",\n" +
                "    \"partner\":\"2088621610828354\",\n" +
                "    \"service\":\"alipay.acquire.customs\",\n" +
                "    \"trade_no\":\"2020010722001438910565800001\",\n" +
                "    \"customs_place\":\"NANSHAGJ\",\n" +
                "    \"merchant_customs_code\":\"1509011052\",\n" +
                "    \"buyer_id_no\":\"310110200211292021\",\n" +
                "    \"out_request_no\":\"DOCP202001071131320001013178\"\n" +
                "}");
        Map<String, Object> sortedParams = new TreeMap<>();
        sortedParams.putAll(paramMap);
        String key = "pil5t34m4qsdor9ujuffkqfvrgfjt3mp";
        System.out.println(JSON.toJSONString(sortedParams));
        String md5Sign = Hashing
                .md5().newHasher().putString(createLinkString(sortedParams) + key, Charsets.UTF_8).hash().toString();
        paramMap.put("sign_type","MD5");
        paramMap.put("sign",md5Sign);
        HttpRequest httpRequest = HttpRequest.get("https://mapi.alipay.com/gateway.do",paramMap, true).header(SaasHttpConstant.HEADER_TENANT_ID, SimpleTenantHelper.getTenantIdStr());
        if (httpRequest.ok()){
            System.out.println(httpRequest.body());
        }
    }

    public static String createLinkString(Map<String, Object> params) {

        List<String> keys = new ArrayList<String>(params.keySet());
        Collections.sort(keys);

        String prestr = "";

        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            String value = params.get(key).toString();

            if (i == keys.size() - 1) {//拼接时，不包括最后一个&字符
                prestr = prestr + key + "=" + value;
            } else {
                prestr = prestr + key + "=" + value + "&";
            }
        }
        return prestr;
    }
}
