package com.danding.cds.declare.sdk.payment.alipay.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * @Auther: Dante-GXJ
 * @Date: 2020/10/23 10:14
 * @Description:
 */
@Data
public class AliCustomsPayQueryModel implements Serializable {
    /**
     * 接口名称
     */
    @JSONField(name = "service")
    private String service;
    /**
     * 合作者身份ID;
     * 签约的支付宝账号对应的支付宝唯一用户号。以2088开头的16位纯数字组成
     */
    @JSONField(name = "partner")
    private String partner;
    /**
     *参数编码字符集
     * 商户网站使用的编码格式，如UTF-8、GBK、GB2312等
     */
    @JSONField(name = "_input_charset")
    private String InputCharset;

    /**
     * 报关流水号
     * 商户生成的用于唯一标识一次报关操作的业务编号。
     * 建议生成规则：yyyymmmdd型8位日期拼接4位序列号。
     * 该参数长度为6~32位。
     */
    @JSONField(name = "out_request_nos")
    private String outRequestNos;
}
