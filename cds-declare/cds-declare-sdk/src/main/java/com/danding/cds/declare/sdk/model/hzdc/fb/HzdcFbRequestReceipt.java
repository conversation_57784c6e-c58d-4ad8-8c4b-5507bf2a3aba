package com.danding.cds.declare.sdk.model.hzdc.fb;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: 非保进出区核放单申报审批结果
 * @date 2023/7/13 10:38
 */
@Data
@NoArgsConstructor
public class HzdcFbRequestReceipt implements Serializable {

    private String messageId; // 消息id,uuid (必填)

    private int approvalStatus; //审批意见

    /**
     * 审批备注，非必填，不通过时必填，
     * 自动审核的不通过需提示企业备案无效（停用/超有效期/无备案）
     * 或申请的核放单账册编号错误/料号错误/数量对不上等。
     */
    private String approvalDesc;

    private String approvalDate; //审批时间 yyyy-MM-dd HH:mm:ss

    private String approvalData;
}
