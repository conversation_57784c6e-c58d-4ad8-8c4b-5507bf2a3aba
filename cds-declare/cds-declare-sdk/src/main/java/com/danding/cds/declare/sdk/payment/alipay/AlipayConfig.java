package com.danding.cds.declare.sdk.payment.alipay;

import com.danding.cds.declare.sdk.payment.base.BaseConfig;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: gxj
 * @Date: 2019/03/07
 * @Description: 支付宝配置类
 */
@Slf4j
@Data
public class AlipayConfig extends BaseConfig {

    /**
     * 旧接口网关
     */
    private String oldGateway;

    public AlipayConfig() {
        oldGateway = "https://mapi.alipay.com/gateway.do";
    }

}
