package com.danding.cds.declare.sdk.model.bizDeclareForm;

import com.danding.cds.declare.sdk.model.checkList.MessageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class Sas101MessageRequest extends MessageRequest implements Serializable {

    private String bizDeclareFormSn;

    private AppHeadInfo appHeadInfo;

    private List<AppGoodsInfo> appGoodsInfos;

    private List<AppUcnsInfo> appUcnsInfos;

    private int DelcareFlag;
}
