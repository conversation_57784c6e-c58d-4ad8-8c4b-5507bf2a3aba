package com.danding.cds.declare.sdk.clear.hangzhou;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.cds.common.enums.TrackLogConstantMixAll;
import com.danding.cds.common.model.MapExt;
import com.danding.cds.common.utils.FormatUtil;
import com.danding.cds.customs.inventory.api.enums.InventoryOrderType;
import com.danding.cds.declare.ceb.domain.ceb511.CEB511Message;
import com.danding.cds.declare.ceb.domain.ceb624.CEB624Message;
import com.danding.cds.declare.ceb.domain.ceb624.InvtCancelReturn;
import com.danding.cds.declare.ceb.domain.ceb626.CEB626Message;
import com.danding.cds.declare.ceb.domain.ceb626.InvtRefundReturn;
import com.danding.cds.declare.ceb.domain.ceb816.CEB816Message;
import com.danding.cds.declare.ceb.domain.ceb816.Tax;
import com.danding.cds.declare.ceb.domain.ceb816.TaxListRd;
import com.danding.cds.declare.ceb.domain.ceb818.CEB818Message;
import com.danding.cds.declare.ceb.domain.ceb818.TaxHeadStatus;
import com.danding.cds.declare.ceb.internal.utils.XMLUtil;
import com.danding.cds.declare.sdk.CustomsReport;
import com.danding.cds.declare.sdk.CustomsSupport;
import com.danding.cds.declare.sdk.bean.dto.CustomsMsgDto;
import com.danding.cds.declare.sdk.clear.base.CustomsClient;
import com.danding.cds.declare.sdk.clear.base.CustomsClientRegistry;
import com.danding.cds.declare.sdk.clear.base.callback.CallbackResult;
import com.danding.cds.declare.sdk.clear.base.callback.CallbackType;
import com.danding.cds.declare.sdk.clear.base.callback.module.*;
import com.danding.cds.declare.sdk.clear.base.result.*;
import com.danding.cds.declare.sdk.clear.zhejiang.ZJAgentToken;
import com.danding.cds.declare.sdk.clear.zhejiang.builder.ZJPortShipmentBuilder;
import com.danding.cds.declare.sdk.clear.zhejiang.builder.ZjCebShipmentBuilder;
import com.danding.cds.declare.sdk.clear.zhejiang.encry.KeyInfo;
import com.danding.cds.declare.sdk.config.CustomsSpecialToken;
import com.danding.cds.declare.sdk.constant.CustomsDistrictCons;
import com.danding.cds.declare.sdk.enums.CustomsType;
import com.danding.cds.declare.sdk.model.WrapBeanInfo;
import com.danding.cds.declare.sdk.model.cancel.WarpCancelOrderInfo;
import com.danding.cds.declare.sdk.model.inventory.WrapInventoryOrderInfo;
import com.danding.cds.declare.sdk.model.order.WrapOrderDeclareInfo;
import com.danding.cds.declare.sdk.model.refund.WarpRefundOrderInfo;
import com.danding.cds.declare.sdk.model.shipment.WrapShipmentInfo;
import com.danding.cds.declare.sdk.model.tax.TaxItem;
import com.danding.cds.declare.sdk.utils.CebMessageUtil;
import com.danding.cds.declare.sdk.utils.CebSignUtil;
import com.danding.cds.declare.sdk.utils.RabbitMqUtil;
import com.danding.cds.declare.zjport.domain.request.Request;
import com.danding.cds.declare.zjport.internal.HzPortAESUtil;
import com.danding.cds.declare.zjport.internal.enums.HzPortBusinessType;
import com.danding.cds.declare.zjport.internal.utils.HzPortRSAUtil;
import com.danding.cds.declare.zjport.internal.utils.WebServiceRequest;
import com.danding.cds.log.api.dto.TrackLogDTO;
import com.danding.cds.log.api.enums.TrackLogEnums;
import com.danding.cds.order.api.enums.OrderStatus;
import com.danding.component.http.SaasHttpConstant;
import com.danding.core.tenant.SimpleTenantHelper;
import com.github.kevinsawicki.http.HttpRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * @program: cds-center
 * @description: 杭州数据中心
 * @author: 潘本乐（Belep）
 * @create: 2021-09-25 14:41
 **/
@Slf4j
public class HzDataCenterCustomsClient extends CustomsClient {

    private String interfaceUrl = "http://api.kjeport.com";

    public HzDataCenterCustomsClient(CustomsSupport support, String env) {
        super(support, env);
    }

    public HzDataCenterCustomsClient(CustomsSupport support, CustomsClientRegistry registry, String env) {
        super(support, registry, env);
    }

    @Override
    public CustomsType getSystem() {
        return CustomsType.HANG_ZHOU_DATA_CENTER;
    }


    @Override
    public ShipmentDeclareResult shipmentDeclare(WrapShipmentInfo info) {

        log.info("开始处理，申报单：{}，浙江电子口岸【运单申报】", info.getDeclareOrderNo());
        CustomsReport report = new CustomsReport(CustomsReport.TYPE_SEND, CustomsReport.SYSTEM_CUSTOMS_CLIENT, CustomsReport.PROCESS_CLEAR_DECLARE_SHIPMENT);
        report.buildBusinessKey(info.getBusinessKey());
        ShipmentDeclareResult result = new ShipmentDeclareResult();
        result.setOrderNo(info.getDeclareOrderNo());
        result.setSn(info.getSn());
        result.setStatus(info.getStatus());
        report.buildProcessData(result);

        // 不是线上环境直接mock
        if (!CustomsSupport.ENV_ONLINE.equalsIgnoreCase(env)) {
            log.info("【测试】申报单：{}，杭州数据中心【运单申报】发往总署CEB报文-MOCK", info.getDeclareOrderNo());
            return result;
        }
//        /**
//         * 组装DXP报文，发送到杭州数据中心
//         */
//        String cebCode = info.getDeclareCompanyCebCode();
//        final CustomsSpecialToken customsSpecialToken = CebMessageUtil.getCustomsSpecialToken(cebCode);
//        CustomsMsgDto customsMsgDto = CebMessageUtil.buildCustomsMessage(info);
//        String cebMsg = customsMsgDto.getCebMsg();
//        String dxpMsg = customsMsgDto.getDxpMsg();
//        RabbitMqUtil.hzDataCenterSendMsg(dxpMsg, customsSpecialToken.getSenderId());
//        report.setRequestMsg(customsMsgDto.getCebMsg());
//        /**
//         * 记录日志
//         */
//        TrackLogDTO trackLogDTO = new TrackLogDTO();
//        trackLogDTO.setDeclareOrderNo(result.getOrderNo())
//                .setOldStatus(OrderStatus.DEC_WAIT.getValue()).setNewStatus(OrderStatus.DEC_ING.getValue())
//                .setOperateDes(TrackLogEnums.INVENTORY_DECLARE.getCode())
//                .setLogDes("杭州数据中心运单报文已推送").setContent(cebMsg).setHasXmlMessage(1);
//        super.sendTrackLog(trackLogDTO);
//        support.accept(report);
//        log.info("处理完成，申报单：{}，杭州数据中心【运单申报】发往总署CEB报文", info.getDeclareOrderNo());
//        return result;


        ZJAgentToken token = CebMessageUtil.getZJAgentToken(info.getDeclareCompanyDTO().getCebCode());
        ZJPortShipmentBuilder builder = new ZJPortShipmentBuilder(info);
        Request request = builder.build();
        String importOrderInfo;
        try {
            importOrderInfo = XMLUtil.convertToXml(request);
            if (StringUtils.isEmpty(importOrderInfo)) {
                throw new RuntimeException("申报组装数据为空");
            }
            report.buildRequest(importOrderInfo);
        } catch (Exception e) {
            log.info("[供应链报文-杭州口岸-运单申报] 异常，cause={}", e.getMessage(), e);
            throw new RuntimeException("报文组装异常", e);
        }
        log.info("[供应链报文-杭州口岸-运单申报],运单报文原始数据{}", importOrderInfo);
        String responseString = "";
        if ("mock".equals(env)) {
//            responseString = this.mockResult();
        } else {
            String customsCode = this.findCustomsCode(null);
            // 浙江跨境一步达
            responseString = invokeCrossBorderOneStep(token, importOrderInfo, info.getDeclareOrderNo(), info.getDeclareCompanyDTO().getCebCode(), customsCode, HzPortBusinessType.IMPORTBILL, info);
        }
        log.info("[op:ZJCustomsClient-logisticsDeclare]运单申报 - 浙江电子口岸报文 下发运单申报指令成功 回复报文={}", responseString);
        report.setResponseMsg(responseString);
        support.accept(report);
        if ("ceb".equals(token.getMode())) {
            this.shipmentDeclareCeb(info);
        }
        return result;
    }

    /**
     * 查找海关编码
     *
     * @param accountBookNo 根据账册查找海关编码
     * @return
     */
    private String findCustomsCode(String accountBookNo) {
        String _defaultCustomsCode = "2924";
        if (Objects.equals("L2923B21A004", accountBookNo)) {
            _defaultCustomsCode = "2923";
        } else if (CustomsDistrictCons.YW_2925_CUSTOMS_DISTRICT.contains(accountBookNo)) {
            _defaultCustomsCode = "2925";
        }
        return _defaultCustomsCode;
    }

    /**
     * 调用跨境一步达
     *
     * @param token       token
     * @param orignXml    原始xml
     * @param declareNo   申报单号
     * @param cebCode     海关十位编码
     * @param declareType 申报类型
     * @param info
     * @return
     */
    protected String invokeCrossBorderOneStep(ZJAgentToken token, String orignXml, String declareNo, String cebCode, String customsAreaCode, HzPortBusinessType declareType, WrapBeanInfo info) {

        // Step::加密&签名
        KeyInfo keyInfo = token.getZjPortKeyInfo();
        String AESKey = keyInfo.getAesKey();
        String context;
        String dataDigest;
        try {
            byte[] inputContent = orignXml.getBytes("utf-8");
            byte[] aesKeyCode = Base64.decodeBase64(AESKey.getBytes("utf-8"));
            context = new String(Base64.encodeBase64(HzPortAESUtil.encrypt(inputContent, aesKeyCode)), "utf-8");
            byte[] privateKeyCode = Base64.decodeBase64(keyInfo.getPrivateKey().getBytes("utf-8"));
            dataDigest = new String(Base64.encodeBase64(HzPortRSAUtil.sign(inputContent, privateKeyCode)), "utf-8");
        } catch (Exception e) {
            log.error("浙江电子口岸报文加密&签名异常:{}",e.getMessage(),e);
            throw new RuntimeException("浙江电子口岸报文加密&签名异常", e);
        }
        log.info("[op:ZJCustomsClient-inventoryDeclare]供应链报文- 浙江电子口岸报文 -申报-下发{}申报指令  interfaceUrl={}, AesEncrypt={},加密报文={},签名信息={}",
                declareType.getDescription(), interfaceUrl, AESKey, context, dataDigest);
        String responseString = WebServiceRequest.webServiceRequestOfPort(
                interfaceUrl,
                context,
                declareType.getType(),
                dataDigest,
                cebCode);
        return FormatUtil.removeCrlf(responseString);
    }

    private void shipmentDeclareCeb(WrapShipmentInfo info) {
        CustomsReport report = new CustomsReport(CustomsReport.TYPE_SEND, CustomsReport.SYSTEM_CUSTOMS_CLIENT, CustomsReport.PROCESS_CLEAR_DECLARE_SHIPMENT);
        report.buildBusinessKey(info.getBusinessKey());
        ShipmentDeclareResult result = new ShipmentDeclareResult();
        result.setOrderNo(info.getDeclareOrderNo());
        result.setSn(info.getSn());
        result.setStatus(info.getStatus());
        report.buildProcessData(result);
        ZJAgentToken encryInfo = CebMessageUtil.getZJAgentToken(info.getDeclareCompanyDTO().getCebCode());
        CEB511Message ceb511Message = new ZjCebShipmentBuilder(info, encryInfo.getCebSignInfo().getDxpId()).build();
        this.cebSend(report, encryInfo, ceb511Message, "CEB511Message", "运单申报");
        support.accept(report);
    }

    private String cebSend(CustomsReport report, ZJAgentToken token, Object model, String tag, String detail) {
        // Step::组装报文
        String xmlFormat;
        try {
            xmlFormat = XMLUtil.convertToXml(model);
            if (xmlFormat == null) {
                throw new RuntimeException("XML转换数据为空");
            }
            report.buildRequest(xmlFormat);
            log.info("[op:ZJCustomsClient] 总署CEB报文 {} 原始业务报文{}", detail, xmlFormat);
        } catch (Exception e) {
            log.error("[op:ZJCustomsClient] 总署CEB报文 {} XML报文组装异常, cause={}", detail, e.getMessage(), e);
            throw new RuntimeException("XML报文组装异常", e);
        }
        MapExt processDataMap = JSON.parseObject(report.getProcessData(), MapExt.class);
        String responseString = "";
        if ("mock".equals(env)) {
//            responseString = this.mockResult();
//            log.info("[op:ZJCustomsClient] 模拟数据 总署{} 请求业务报文= {},回复报文={}", detail, xmlFormat, responseString);
            try {
                // TODO：提交至测试应用
                Map<String, Object> param = new HashMap<>();
                param.put("type", tag);
                param.put("data", JSON.toJSONString(model));
                HttpRequest httpRequest = HttpRequest.post("http://oms.backend.daily.yang800.com/xhr/ccs/test/customs/mock")
                        .header(SaasHttpConstant.HEADER_TENANT_ID, SimpleTenantHelper.getTenantIdStr())
                        .form(param);
                httpRequest.ok();
            } catch (HttpRequest.HttpRequestException e) {
                log.warn("处理异常：{}", e.getMessage(), e);
            }
        } else {
            // Step::加密
            String customsAreaCode = this.findCustomsCode(processDataMap.getAccountBookNo());
            responseString = invokeCeb(token, xmlFormat, processDataMap.getOrderNo(), customsAreaCode, tag, detail, model);
        }

//        this.writeCebMessageLog(processDataMap.getOrderNo(), xmlFormat);
        report.buildResponse(responseString);
        return responseString;
    }


    /**
     * CEB报文调用
     *
     * @param token
     * @param orginXml
     * @param tag
     * @param detail
     * @param model
     * @return
     */
    protected String invokeCeb(ZJAgentToken token, String orginXml, String declareNo, String customsAreaCode, String tag, String detail, Object model) {

        String content;
        try {
            String signStr = CebSignUtil.signXml(token.getCebSignInfo(), orginXml);
            content = CebMessageUtil.buildCustomsMsg(signStr.getBytes(), token.getCebSignInfo().getDxpId(), tag);
        } catch (Exception e) {
            log.error("[op:signError]exception={}", e.getMessage(), e);
            throw new RuntimeException("总署报文加密异常", e);
        }
        if (StringUtils.isBlank(content)) {
            log.error("[op:signError]content empty", content);
        }

        String responseString = WebServiceRequest.webServiceRequestOfCeb(interfaceUrl, content);
        String request = FormatUtil.removeCrlf(orginXml);
        String dxpMessage = FormatUtil.removeCrlf(content);
        String response = FormatUtil.removeCrlf(responseString);
        log.info("[op:ZJCustomsClient] 总署CEB报文 {} 请求业务报文= {},请求终端节点报文={},回复报文={}", detail, request, dxpMessage, response);
        return responseString;
    }

    private void writeCebMessageLog(String declareNo, String orignXml) {
        try {
            TrackLogDTO trackLogDTO = new TrackLogDTO();
            trackLogDTO.setOrderType(InventoryOrderType.INVENTORY_ORDER.getCode());
            trackLogDTO.setDeclareOrderNo(declareNo)
                    .setOldStatus(OrderStatus.DEC_WAIT.getValue()).setNewStatus(OrderStatus.DEC_ING.getValue())
                    .setOperateDes(TrackLogEnums.INVENTORY_DECLARE.getCode()).setLogDes("总署报文已推送").setContent(orignXml).setHasXmlMessage(1);
            support.messageSender.sendMsg(JSON.toJSONString(trackLogDTO), "ccs-trackLog-topic");
        } catch (Exception e) {
            log.info("[op:ZJCustomsClient-cebSend trackLog error={}]", e.getMessage());
        }
    }

    /**
     * 订单申报
     *
     * @param declareInfo
     */
    @Override
    public OrderDeclareResult orderDeclare(WrapOrderDeclareInfo declareInfo) {

        log.info("开始处理，申报单：{}，杭州数据中心【订单申报】发往总署CEB报文封装", declareInfo.getDeclareOrderNo());
        CustomsReport report = new CustomsReport(CustomsReport.TYPE_SEND, CustomsReport.SYSTEM_CUSTOMS_CLIENT, CustomsReport.PROCESS_CLEAR_DECLARE_ORDER);
        report.buildBusinessKey(declareInfo.getBusinessKey());

        OrderDeclareResult result = new OrderDeclareResult();
        result.setOrderNo(declareInfo.getDeclareOrderNo());
        result.setSn(declareInfo.getSn());
        result.setStatus(declareInfo.getStatus());
        report.buildProcessData(result);

        // 不是线上环境直接mock
        if (!CustomsSupport.ENV_ONLINE.equalsIgnoreCase(env)) {
            log.info("【测试】申报单：{}，杭州数据中心【订单申报】发往总署CEB报文-MOCK", declareInfo.getDeclareOrderNo());
            return result;
        }
        /**
         * 组装DXP报文，发送到杭州数据中心
         */
        String cebCode = declareInfo.getDeclareCompanyCebCode();
        final CustomsSpecialToken customsSpecialToken = CebMessageUtil.getCustomsSpecialToken(cebCode);
        CustomsMsgDto customsMsgDto = CebMessageUtil.buildCustomsMessage(declareInfo);
        String cebMsg = customsMsgDto.getCebMsg();
        String dxpMsg = customsMsgDto.getDxpMsg();
        RabbitMqUtil.hzDataCenterSendMsg(dxpMsg, customsSpecialToken.getSenderId());
        report.setRequestMsg(customsMsgDto.getCebMsg());
        /**
         * 记录日志
         */
        TrackLogDTO trackLogDTO = new TrackLogDTO();
        trackLogDTO.setDeclareOrderNo(result.getOrderNo())
                .setOldStatus(OrderStatus.DEC_WAIT.getValue()).setNewStatus(OrderStatus.DEC_ING.getValue())
                .setOperateDes(TrackLogEnums.INVENTORY_DECLARE.getCode())
                .setLogDes("杭州数据中心订单报文已推送").setContent(cebMsg).setHasXmlMessage(1);
        trackLogDTO.setOrderType(InventoryOrderType.ORDER.getCode());
        super.sendTrackLog(trackLogDTO);
        support.accept(report);
        log.info("处理完成，申报单：{}，杭州数据中心【订单申报】发往总署CEB报文", declareInfo.getDeclareOrderNo());
        return result;
    }

    /**
     * 清单申报
     *
     * @param info
     */
    @Override
    public InventoryDeclareResult inventoryDeclare(WrapInventoryOrderInfo info) {

        log.info("开始处理，申报单：{}，杭州数据中心【清单申报】发往总署CEB报文封装", info.getCustomsInventoryDto().getOrderNo());
        CustomsReport report = new CustomsReport(CustomsReport.TYPE_SEND, CustomsReport.SYSTEM_CUSTOMS_CLIENT, CustomsReport.PROCESS_CLEAR_DECLARE_INVENTORY);
        report.buildBusinessKey(info.getBusinessKey());
        InventoryDeclareResult result = new InventoryDeclareResult();
        result.setOrderNo(info.getCustomsInventoryDto().getOrderNo());
        result.setSn(info.getCustomsInventoryDto().getSn());
        result.setStatus(info.getCustomsInventoryDto().getStatus());
        report.buildProcessData(result);

        // 不是线上环境直接mock
        if (!CustomsSupport.ENV_ONLINE.equalsIgnoreCase(env)) {
            log.info("【测试】申报单：{}，杭州数据中心【清单申报】发往总署CEB报文-MOCK", info.getCustomsInventoryDto().getOrderNo());
            return result;
        }
        /**
         * 组装DXP报文，发送到杭州数据中心
         */
        String cebCode = info.getDeclareCompanyCebCode();
        final CustomsSpecialToken customsSpecialToken = CebMessageUtil.getCustomsSpecialToken(cebCode);
        CustomsMsgDto customsMsgDto = CebMessageUtil.buildCustomsMessage(info);
        String dxpMsg = customsMsgDto.getDxpMsg();
        String cebMsg = customsMsgDto.getCebMsg();
        RabbitMqUtil.hzDataCenterSendMsg(dxpMsg, customsSpecialToken.getSenderId());
        report.setRequestMsg(customsMsgDto.getCebMsg());
        /**
         * 记录日志
         */
        TrackLogDTO trackLogDTO = new TrackLogDTO();
        trackLogDTO.setOrderType(InventoryOrderType.INVENTORY_ORDER.getCode());
        trackLogDTO.setDeclareOrderNo(result.getOrderNo())
                .setOldStatus(OrderStatus.DEC_WAIT.getValue()).setNewStatus(OrderStatus.DEC_ING.getValue())
                .setOperateDes(TrackLogEnums.INVENTORY_DECLARE.getCode())
                .setLogDes("杭州数据中心清单报文已推送").setContent(cebMsg).setHasXmlMessage(1);
        super.sendTrackLog(trackLogDTO);
        support.accept(report);
        log.info("处理完成，申报单：{}，杭州数据中心【清单申报】发往总署CEB报文", info.getCustomsInventoryDto().getOrderNo());
        return result;
    }

    /**
     * 清单取消
     *
     * @param info
     */
    @Override
    public InventoryCancelResult inventoryCancel(WarpCancelOrderInfo info) {
        log.info("开始处理，申报单：{}，杭州数据中心【清单取消】发往总署CEB报文封装", info.getCustomsInventoryDto().getOrderNo());
        CustomsReport report = new CustomsReport(
                CustomsReport.TYPE_ACCEPT,
                CustomsReport.SYSTEM_CUSTOMS_CLIENT,
                CustomsReport.PROCESS_CLEAR_DECLARE_INVENTORY_CANCEL
        );

        InventoryCancelResult result = new InventoryCancelResult();
        result.setOrderNo(info.getCustomsInventoryDto().getOrderNo());
        result.setSn(info.getCustomsInventoryCancelInfo().getSn());
        result.setStatus(info.getCustomsInventoryCancelInfo().getStatus());
        report.buildProcessData(result);
        // 不是线上环境直接mock
        if (!CustomsSupport.ENV_ONLINE.equalsIgnoreCase(env)) {
            log.info("【测试】申报单：{}，杭州数据中心【清单取消】发往总署CEB报文-MOCK", info.getCustomsInventoryDto().getOrderNo());
            return result;
        }
        /**
         * 组装DXP报文，发送到杭州数据中心
         */
        String cebCode = info.getDeclareCompanyCebCode();
        final CustomsSpecialToken customsSpecialToken = CebMessageUtil.getCustomsSpecialToken(cebCode);
        CustomsMsgDto customsMsgDto = CebMessageUtil.buildCustomsMessage(info);
        String dxpMsg = customsMsgDto.getDxpMsg();
        String cebMsg = customsMsgDto.getCebMsg();
        RabbitMqUtil.hzDataCenterSendMsg(dxpMsg, customsSpecialToken.getSenderId());
        report.setRequestMsg(cebMsg);
        support.accept(report);
        log.info("处理完成，申报单：{}，杭州数据中心【清单取消】发往总署CEB报文", info.getCustomsInventoryDto().getOrderNo());
        return result;
    }

    /**
     * 清单退货
     *
     * @param info
     */
    @Override
    public InventoryRefundResult inventoryRefund(WarpRefundOrderInfo info) {
        log.info("开始处理，申报单：{}，杭州数据中心【清单退货】发往总署CEB报文封装", info.getCustomsInventoryDto().getOrderNo());

        CustomsReport report = new CustomsReport(
                CustomsReport.TYPE_ACCEPT,
                CustomsReport.SYSTEM_CUSTOMS_CLIENT,
                CustomsReport.PROCESS_CLEAR_DECLARE_INVENTORY_REFUND
        );
        InventoryRefundResult result = new InventoryRefundResult();
        result.setOrderNo(info.getCustomsInventoryDto().getOrderNo());
        result.setSn(info.getRefundOrderInfoDto().getRefundNo());
        result.setStatus(info.getRefundOrderInfoDto().getRefundCheckStatus());
        report.buildProcessData(result);

        // 不是线上环境直接mock
        if (!CustomsSupport.ENV_ONLINE.equalsIgnoreCase(env)) {
            log.info("【测试】申报单：{}，杭州数据中心【清单退货】发往总署CEB报文-MOCK", info.getCustomsInventoryDto().getOrderNo());
            return result;
        }
        /**
         * 组装DXP报文，发送到杭州数据中心
         */
        String cebCode = info.getDeclareCompanyCebCode();
        final CustomsSpecialToken customsSpecialToken = CebMessageUtil.getCustomsSpecialToken(cebCode);
        CustomsMsgDto customsMsgDto = CebMessageUtil.buildCustomsMessage(info);
        RabbitMqUtil.hzDataCenterSendMsg(customsMsgDto.getDxpMsg(), customsSpecialToken.getSenderId());
        report.setRequestMsg(customsMsgDto.getCebMsg());
        support.accept(report);
        log.info("处理完成，申报单：{}，杭州数据中心【清单退货】发往总署CEB报文", info.getCustomsInventoryDto().getOrderNo());
        return result;
    }

    /**
     * 解析订单回执
     *
     * @param callbackResult
     * @return
     */
    @Override
    public OrderCallback handelOrderMsg(CallbackResult callbackResult) {

        log.info("开始处理【订单申报】总署CEB返回封装报文：{}", JSON.toJSONString(callbackResult));
        CustomsReport report = new CustomsReport(
                CustomsReport.TYPE_ACCEPT,
                CustomsReport.SYSTEM_CUSTOMS_CLIENT,
                CustomsReport.PROCESS_CLEAR_CALLBACK_ORDER);
        JSONObject origMap = JSON.parseObject(callbackResult.getOrigMsg());
        OrderCallback orderCallback = null;
        String origMessage = origMap.getString("content");
        report.setSender(TrackLogConstantMixAll.HZ_DATA_CENTER);
        report.setResponseMsg(origMessage);
        try {
            orderCallback = CebMessageUtil.getOrderCallback(origMessage);
            report.setProcessData(JSON.toJSONString(orderCallback));
            support.accept(report);
        } catch (Exception ex) {
            log.error("订单申报总署CEB返回报文处理异常：回执报文：{}，异常：{}", origMessage, ex.getMessage(), ex);
        }
        return orderCallback;
    }

    /**
     * 税单回执
     *
     * @param callbackResult
     * @return
     */
    @Override
    public List<TaxResult> handelTaxMsg(CallbackResult callbackResult) {
        log.info("开始处理【税金】总署CEB返回封装报文：{}", JSON.toJSONString(callbackResult));
        CustomsReport report = new CustomsReport(
                CustomsReport.TYPE_ACCEPT,
                CustomsReport.SYSTEM_CUSTOMS_CLIENT,
                CustomsReport.PROCESS_CLEAR_CALLBACK_TAX);
        log.info("[op:HzDataCenterCustomsClient-handelTaxMsg] CallbackResult={}", JSON.toJSONString(callbackResult));
        JSONObject origMap = JSON.parseObject(callbackResult.getOrigMsg());
        List<TaxResult> taxResultList = new ArrayList<>();
        try {
            // Module::总署回执
            String requestXmlString = origMap.getString("content");
            report.buildResponse(requestXmlString);
            requestXmlString = requestXmlString.replace("xmlns=\"http://www.chinaport.gov.cn/ceb\"", "");
            CEB816Message response;
            response = XMLUtil.converyToJavaBean(requestXmlString, CEB816Message.class);
            for (Tax tax : response.getTax()) {
                TaxResult taxResult = new TaxResult();
                taxResult.setOrderNo(tax.getTaxHeadRd().getOrderNo());
                taxResult.setLogisticsNo(tax.getTaxHeadRd().getLogisticsNo());
                Date returnDate =  DateUtils.parseDate(tax.getTaxHeadRd().getReturnTime(), "yyyyMMddHHmmssSSS");
                taxResult.setReturnTime(returnDate);
                taxResult.setInvtNo(tax.getTaxHeadRd().getInvtNo());
                taxResult.setTaxNo(tax.getTaxHeadRd().getTaxNo());
                taxResult.setCustomsTax(tax.getTaxHeadRd().getCustomsTax());
                taxResult.setValueAddedTax(tax.getTaxHeadRd().getValueAddedTax());
                taxResult.setConsumptionTax(tax.getTaxHeadRd().getConsumptionTax());
                taxResult.setStatus(tax.getTaxHeadRd().getStatus());
                taxResult.setEntDutyNo(tax.getTaxHeadRd().getEntDutyNo());
                taxResult.setNote(tax.getTaxHeadRd().getNote());
                taxResult.setEbcCode(tax.getTaxHeadRd().getEbcCode());
                taxResult.setAssureCode(tax.getTaxHeadRd().getAssureCode());
                taxResult.setLogisticsCode(tax.getTaxHeadRd().getLogisticsCode());
                taxResult.setAgentCode(tax.getTaxHeadRd().getAgentCode());
                taxResult.setCustomsCode(tax.getTaxHeadRd().getCustomsCode());
                taxResult.setGuid(tax.getTaxHeadRd().getGuid());
                List<TaxItem> itemList = new ArrayList<>();
                List<TaxListRd> taxListRdList = tax.getTaxListRd();
                if (!CollectionUtils.isEmpty(taxListRdList)) {
                    for (TaxListRd taxListRd : taxListRdList) {
                        TaxItem item = new TaxItem();
                        item.setGnum(taxListRd.getGnum());
                        item.setGcode(taxListRd.getGcode());
                        item.setTaxPrice(taxListRd.getTaxPrice());
                        item.setCustomsTax(taxListRd.getCustomsTax());
                        item.setValueAddedTax(taxListRd.getValueAddedTax());
                        item.setConsumptionTax(taxListRd.getConsumptionTax());
                        itemList.add(item);
                    }
                }
                taxResult.setItemList(itemList);
                taxResultList.add(taxResult);
            }
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
        }
        report.setSender(TrackLogConstantMixAll.HZ_DATA_CENTER);
        report.buildProcessData(taxResultList);
        support.accept(report);
        return taxResultList;
    }

    @Override
    public List<TaxStatus> handelTaxStatusMsg(CallbackResult callbackResult) {
        log.info("开始处理【税金状态】总署CEB返回封装报文：{}", JSON.toJSONString(callbackResult));
        CustomsReport report = new CustomsReport(
                CustomsReport.TYPE_ACCEPT,
                CustomsReport.SYSTEM_CUSTOMS_CLIENT,
                CustomsReport.PROCESS_CLEAR_CALLBACK_TAX_STATUS);

        log.info("[op:HzDataCenterCustomsClient-handelTaxStatusMsg] CallbackResult={}", JSON.toJSONString(callbackResult));
        JSONObject origMap = JSON.parseObject(callbackResult.getOrigMsg());
        List<TaxStatus> taxStatusList = new ArrayList<TaxStatus>();
        try {
            // Module::总署回执
            String requestXmlString = origMap.getString("content");
            report.buildResponse(requestXmlString);
            requestXmlString = requestXmlString.replace("xmlns=\"http://www.chinaport.gov.cn/ceb\"", "");
            CEB818Message response = XMLUtil.converyToJavaBean(requestXmlString, CEB818Message.class);
            for (com.danding.cds.declare.ceb.domain.ceb818.Tax tax : response.getTax()) {
                TaxStatus taxResult = new TaxStatus();
                TaxHeadStatus taxHeadStatus = tax.getTaxHeadStatus();
                taxResult.setStatus(taxHeadStatus.getStatus());
                taxResult.setInvtNo(taxHeadStatus.getInvtNo());
                if (taxHeadStatus.getReturnTime() != null) {
                    taxResult.setReturnTime(DateUtils.parseDate(taxHeadStatus.getReturnTime(), "yyyyMMddHHmmss"));
                }
                taxResult.setTaxNo(taxHeadStatus.getTaxNo());
                taxResult.setEntDutyNo(taxHeadStatus.getEntDutyNo());
                taxResult.setAssureCode(taxHeadStatus.getAssureCode());
                taxStatusList.add(taxResult);
            }
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
        }
        report.setProcessData(JSON.toJSONString(taxStatusList));
        support.accept(report);
        return taxStatusList;
    }

    @Override
    public InventoryCallback handelInventoryMsg(CallbackResult callbackResult) {

        log.info("处理清单申报总署CEB返回封装报文：{}", JSON.toJSONString(callbackResult));
        CustomsReport report = new CustomsReport(
                CustomsReport.TYPE_ACCEPT,
                CustomsReport.SYSTEM_CUSTOMS_CLIENT,
                CustomsReport.PROCESS_CLEAR_CALLBACK_INVENTORY);
        JSONObject origMap = JSON.parseObject(callbackResult.getOrigMsg());
        InventoryCallback inventoryCallback = null;
        String origMessage = origMap.getString("content");
        report.setSender(TrackLogConstantMixAll.HZ_DATA_CENTER);
        report.setResponseMsg(origMessage);
        try {
            inventoryCallback = CebMessageUtil.getInventoryCallback(origMessage);
            report.setProcessData(JSON.toJSONString(inventoryCallback));
            support.accept(report);
        } catch (Exception ex) {
            log.error("清单申报总署CEB返回报文处理异常：回执报文：{}，异常：{}", origMessage, ex.getMessage(), ex);
        }
        return inventoryCallback;
    }

    @Override
    public ShipmentCallback handleShipmentMsg(CallbackResult callbackResult) {
        log.info("杭州数据中心开始处理运单申报返回封装报文：{}", JSON.toJSONString(callbackResult));
        CustomsReport report = new CustomsReport(
                CustomsReport.TYPE_ACCEPT,
                CustomsReport.SYSTEM_CUSTOMS_CLIENT,
                CustomsReport.PROCESS_CLEAR_CALLBACK_SHIPMENT);
        JSONObject origMap = JSON.parseObject(callbackResult.getOrigMsg());
        ShipmentCallback shipmentCallback = null;
        String origMessage = origMap.getString("content");
        report.setResponseMsg(origMessage);
        try {
            shipmentCallback = CebMessageUtil.getShipmentCallback(origMessage);
            report.setProcessData(JSON.toJSONString(shipmentCallback));
            support.accept(report);
        } catch (Exception ex) {
            log.error("运单申报总署CEB返回报文处理异常：回执报文：{}，异常：{}", origMessage, ex.getMessage(), ex);
        }
        return shipmentCallback;
    }

    @Override
    public List<InventoryCancel> handelInventoryCancel(CallbackResult callbackResult) {

        log.info("处理清单取消总署CEB返回封装后报文：{}", JSON.toJSONString(callbackResult));
        List<InventoryCancel> cancelList = new ArrayList<>();
        JSONObject origMap = JSON.parseObject(callbackResult.getOrigMsg());
        CustomsReport report = new CustomsReport(
                CustomsReport.TYPE_ACCEPT,
                CustomsReport.SYSTEM_CUSTOMS_CLIENT,
                CustomsReport.PROCESS_CLEAR_CALLBACK_INVENTORY_CANCEL);
        try {
            String requestXmlString = origMap.getString("content");
            report.buildResponse(requestXmlString);
            requestXmlString = requestXmlString.replace("xmlns=\"http://www.chinaport.gov.cn/ceb\"", "");
            CEB624Message response = XMLUtil.converyToJavaBean(requestXmlString, CEB624Message.class);
            for (InvtCancelReturn invtCancelReturn : response.getInvtCancelReturn()) {
                InventoryCancel cancel = new InventoryCancel();
                cancel.setEbpCode(invtCancelReturn.getEbpCode());
                cancel.setInvtNo(invtCancelReturn.getInvtNo());
                cancel.setID(invtCancelReturn.getCopNo());
                Date returnDate = DateUtils.parseDate(invtCancelReturn.getReturnTime(), "yyyyMMddHHmmssSSS");
                cancel.setReturnStatus(invtCancelReturn.getReturnStatus());
                cancel.setReturnTime(returnDate);
                cancel.setReturnInfo(invtCancelReturn.getReturnInfo());
                cancelList.add(cancel);
            }
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
        }
        report.setSender(TrackLogConstantMixAll.HZ_DATA_CENTER);
        report.setProcessData(JSON.toJSONString(cancelList));
        support.accept(report);
        return cancelList;
    }

    @Override
    public List<InventoryRefund> handelInventoryRefund(CallbackResult callbackResult) {

        log.info("处理清单退货总署CEB返回封装后报文：{}", JSON.toJSONString(callbackResult));
        List<InventoryRefund> refundList = new ArrayList<>();
        JSONObject origMap = JSON.parseObject(callbackResult.getOrigMsg());
        CustomsReport report = new CustomsReport(
                CustomsReport.TYPE_ACCEPT,
                CustomsReport.SYSTEM_CUSTOMS_CLIENT,
                CustomsReport.PROCESS_CLEAR_CALLBACK_INVENTORY_REFUND);
        try {
            String requestXmlString = origMap.getString("content");
            report.buildResponse(requestXmlString);
            requestXmlString = requestXmlString.replace("xmlns=\"http://www.chinaport.gov.cn/ceb\"", "");
            CEB626Message response = XMLUtil.converyToJavaBean(requestXmlString, CEB626Message.class);
            for (InvtRefundReturn invtRefundReturn : response.getInvtRefundReturn()) {
                InventoryRefund refund = new InventoryRefund();
                refund.setEbpCode(invtRefundReturn.getEbpCode());
                refund.setID(invtRefundReturn.getCopNo());
                refund.setInvtNo(invtRefundReturn.getInvtNo());
                Date returnDate = DateUtils.parseDate(invtRefundReturn.getReturnTime(), "yyyyMMddHHmmssSSS");
                refund.setReturnTime(returnDate);
                refund.setReturnInfo(invtRefundReturn.getReturnInfo());
                refund.setReturnStatus(invtRefundReturn.getReturnStatus());
                refundList.add(refund);
            }
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
        }
        report.setSender(TrackLogConstantMixAll.HZ_DATA_CENTER);
        report.setProcessData(JSON.toJSONString(refundList));
        support.accept(report);
        return refundList;
    }

    /**
     * 解析回执的HTTP请求 获得回执类型和明文
     *
     * @param request
     * @return
     */
    @Override
    public CallbackResult parserCallback(Object request) {
        CallbackResult result = new CallbackResult();
        MapExt origMap = new MapExt();
        origMap.setContent(request.toString());
        origMap.setMsgType("CUSTOMS_CEB_CALLBACK");
        result.setOrigMsg(JSON.toJSONString(origMap));
        result.setCallbackType(CebMessageUtil.getCebCallbackType(request.toString()));
        result.setSender(TrackLogConstantMixAll.HZ_DATA_CENTER);
        if (CallbackType.NULL.equals(result.getCallbackType())) {
            CustomsReport report = new CustomsReport(
                    CustomsReport.TYPE_ACCEPT,
                    CustomsReport.SYSTEM_CUSTOMS_CLIENT,
                    CustomsReport.PROCESS_CLEAR_CALLBACK_UN_KNOW);
            report.setProcessData(JSON.toJSONString(result));
            report.setResponseMsg(JSON.toJSONString(request));
            support.accept(report);
        }
        return result;
    }
}
