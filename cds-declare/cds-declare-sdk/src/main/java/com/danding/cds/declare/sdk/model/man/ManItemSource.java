package com.danding.cds.declare.sdk.model.man;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: 归并关系信息
 * @date 2023/2/18 16:17
 */
@Data
public class ManItemSource implements Serializable {
    /**
     * 账册编号
     */
    private String manualId;
    /**
     * 料件项号
     */
    private Integer itemNo;
    /**
     * 料件性质
     */
    private String itemType;
    /**
     * 料号
     */
    private String sourceNo;
    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 商品编码
     */
    private String goodsNo;
    /**
     * 商品规格、型号
     */
    private String goodsSpec;
    /**
     * 币制
     */
    private String currencyType;
    /**
     * 申报计量单位
     */
    private String declareUnit;
    /**
     * 停用标志
     * 空：正常
     * 0:停用
     * 1:消除
     */
    private String useFlag;
    /**
     * 操作类型
     * C:新增
     * M:修改
     * D:删除
     */
    private String actionType;
    /**
     * 第一单位
     */
    private String unit1;
    /**
     * 第二单位
     */
    private String unit2;
}
