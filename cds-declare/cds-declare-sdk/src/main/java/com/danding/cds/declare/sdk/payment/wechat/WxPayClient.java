package com.danding.cds.declare.sdk.payment.wechat;

import java.io.Serializable;
import java.util.Map;

public class WxPayClient implements Serializable {
	
	private WxPayRequest wxpayRequest = new WxPayRequest();
	

    /**
     * 向 Map 中添加 appid、mch_id、nonce_str、sign_type、sign <br>
     * 该函数适用于商户适用于统一下单等接口，不适用于红包、代金券接口
     *
     * @param reqData
     * @return
     * @throws Exception
     */
    public Map<String, String> fillRequestData(String apppId, String mchId, String key, Map<String, String> reqData) throws Exception {
        reqData.put("appid", apppId);
        reqData.put("mch_id", mchId);
        reqData.put("nonce_str", WXPayUtil.generateUUID());
        reqData.put("sign_type", WXPayConstants.MD5);
        reqData.put("sign", WXPayUtil.generateSignature(reqData, key,  WXPayConstants.SignType.MD5));
        return reqData;
    }

    public Map<String, String> fillRequestDataWithoutNorceStr(String apppId, String mchId, String key, Map<String, String> reqData) throws Exception {
        reqData.put("appid", apppId);
        reqData.put("mch_id", mchId);
        reqData.put("sign", WXPayUtil.generateSignature(reqData, key,  WXPayConstants.SignType.MD5));
        return reqData;
    }

    /**
     * 判断xml数据的sign是否有效，必须包含sign字段，否则返回false。
     *
     * @param reqData 向wxpay post的请求数据
     * @return 签名是否有效
     * @throws Exception
     */
    public boolean isResponseSignatureValid(String key, Map<String, String> reqData) throws Exception {
        // 返回数据的签名方式和请求中给定的签名方式是一致的
        return WXPayUtil.isSignatureValid(reqData, key, WXPayConstants.SignType.MD5);
    }

    /**
     * 判断支付结果通知中的sign是否有效
     *
     * @param reqData 向wxpay post的请求数据
     * @return 签名是否有效
     * @throws Exception
     */
    public boolean isPayResultNotifySignatureValid(String key, Map<String, String> reqData) throws Exception {
        String signTypeInData = reqData.get(WXPayConstants.FIELD_SIGN_TYPE);
        WXPayConstants.SignType signType;
        if (signTypeInData == null) {
            signType = WXPayConstants.SignType.MD5;
        }
        else {
            signTypeInData = signTypeInData.trim();
            if (signTypeInData.length() == 0) {
                signType = WXPayConstants.SignType.MD5;
            }
            else if (WXPayConstants.MD5.equals(signTypeInData)) {
                signType = WXPayConstants.SignType.MD5;
            }
            else if (WXPayConstants.HMACSHA256.equals(signTypeInData)) {
                signType = WXPayConstants.SignType.HMACSHA256;
            }
            else {
                throw new Exception(String.format("Unsupported sign_type: %s", signTypeInData));
            }
        }
        return WXPayUtil.isSignatureValid(reqData, key, signType);
    }


    /**
     * 不需要证书的请求
     * @param url String
     * @param mchId String
     * @param reqData 向wxpay post的请求数据
     * @return API返回数据
     * @throws Exception
     */
    public String requestWithoutCert(String url, String mchId, Map<String, String> reqData) throws Exception {
        String reqBody = WXPayUtil.mapToXml(reqData);
        String resp = this.wxpayRequest.requestWithoutCert(url, mchId, reqBody);
        return resp;
    }


    /**
     * 需要证书的请求
     * @param url String
     * @param mchId
     * @param reqData 向wxpay post的请求数据  Map
     * @return API返回数据
     * @throws Exception
     */
    public String requestWithCert(String url, String mchId, Map<String, String> reqData, String certPath) throws Exception {
        if(reqData.get("nonce_str") == null) {
            reqData.put("nonce_str", WXPayUtil.generateUUID());
        }
        String reqBody = WXPayUtil.mapToXml(reqData);

        String resp = this.wxpayRequest.requestWithCert(url, mchId, reqBody, certPath);
        return resp;
    }

    public Map<String, String> processResponseXml(String key,String xmlStr) throws Exception {
        return this.processResponseXml(key,xmlStr,false);
    }

        /**
         * 处理 HTTPS API返回数据，转换成Map对象。return_code为SUCCESS时，验证签名。
         * @param xmlStr API返回的XML格式数据
         * @return Map类型数据
         * @throws Exception
         */
    public Map<String, String> processResponseXml(String key,String xmlStr,Boolean ignoreSign) throws Exception {
        String RETURN_CODE = "return_code";
        String return_code;
        Map<String, String> respData = WXPayUtil.xmlToMap(xmlStr);
        if (respData.containsKey(RETURN_CODE)) {
            return_code = respData.get(RETURN_CODE);
        }
        else {
            throw new Exception(String.format("No `return_code` in XML: %s", xmlStr));
        }

        if (return_code.equals(WXPayConstants.FAIL)) {
            return respData;
        }
        else if (return_code.equals(WXPayConstants.SUCCESS)) {
           if (this.isResponseSignatureValid(key, respData) || ignoreSign) {
               return respData;
           }
           else {
               throw new Exception(String.format("Invalid sign value in XML: %s", xmlStr));
           }
        }
        else {
            throw new Exception(String.format("return_code value %s is invalid in XML: %s", return_code, xmlStr));
        }
    }


    public Map<String, String> customsDeclare(String url, String appId, String mchId, String key, Map<String, String> reqData) throws Exception {
        String respXml = this.requestWithoutCert(url, mchId, this.fillRequestDataWithoutNorceStr(appId, mchId, key, reqData));
        return this.processResponseXml(key, respXml,true); // TODO:同步响应不校验sign，方便测试模拟数据
    }

    public Map<String, String> customsQuery(String url, String appId, String mchId, String key, Map<String, String> reqData) throws Exception {
        String respXml = this.requestWithoutCert(url, mchId, this.fillRequestDataWithoutNorceStr(appId, mchId, key, reqData));
        return this.processResponseXml(key, respXml,true);
    }
}
