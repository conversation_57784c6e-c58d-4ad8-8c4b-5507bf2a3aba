package com.danding.cds.declare.sdk.enums;

public enum CustomsCallBackResultOperEnum {
    CUSTOMS_179_CHECK("data-check", "海关179公告数据抽查"),
    REFUND_ORDER_CALL_BACK("refund", "退货单申报回执结果"),
    LIST_ORDER_CALL_BACK("inventory", "清单申报回执结果"),
    ORDER_CALL_BACK("order", "订单申报回执结果"),
    TAX_CALL_BACK("tax", "订单申报回执结果"),
    TAX_STATUS_CALL_BACK("tax-status", "税单状态回执结果"),
    PAYMENT_CALL_BACK("payment", "支付申报回执结果"),
    BOOK_ITEM_BACK("book-item", "记账回执"),
    CHECKLIST("checklist", "核放回执"),
    CHECKLIST_AUTH("checklistAuth", "核放单调用授权回执"),
    ENDORSEMENT("endorsement", "核注回执"),
    LIST_ORDER_CANCEL_CALL_BACK("inventory-cancel", "清单撤单申报回执结果"),
    CUSTOMS_DECLARE_CALL_BACK("customs-declare", "报关单申报回执结果"),
    ENDORSEMENT_GENERATE_CUSTOMS_DECLARE_CALL_BACK("endorsement-generate-declare", "核注清单生成报关单回执结果"),
    BIZ_DECLARE_FORM_CALL_BACK("biz-declare-form", "业务申报表回执结果"),
    ;

    private String key;

    private String desc;

    CustomsCallBackResultOperEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public static CustomsCallBackResultOperEnum getEnum(String key){
        for (CustomsCallBackResultOperEnum value : CustomsCallBackResultOperEnum.values()) {
            if (value.getKey().equals(key)){
                return value;
            }
        }
        return null;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
