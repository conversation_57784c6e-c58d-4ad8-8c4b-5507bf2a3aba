package com.danding.cds.declare.sdk.model.check;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CustomsDataPayExchangeInfoItem implements Serializable {

    @JSONField(ordinal=1)
    private String orderNo; //订单编号
    @JSONField(ordinal=2)
    private List<CustomsDataGoodsInfo> goodsInfo; //商品信息
    @JSONField(ordinal=3)
    private String recpAccount; //收款账号
    @JSONField(ordinal=4)
    private String recpCode; //收款企业代码
    @JSONField(ordinal=5)
    private String recpName; //收款企业名称
}
