package com.danding.cds.declare.sdk.model.payment;

import lombok.Data;

import java.io.Serializable;

@Data
public class CustomsPayDeclareResult implements Serializable {

    /**
     * 申报是否成功
     */
    private Boolean success;

    /**
     * 身份证验证是否失败
     */
    private Boolean identityCheck = true;

    /**
     * 验核机构
     */
    private String verDept;

    /**
     * 验核交易流水号
     */
    private String payTransactionId;

    /**
     * 第三方子订单流水号
     */
    private String subBankNo;

    /**
     * 备注信息
     */
    private String extra;

    private String postMsg;

    private String customs;

    private String outRequestNo;

    /**
     * 失败信息
     */
    private String errorMsg;

    public static CustomsPayDeclareResult ok(String verDept, String payTransactionId){
        CustomsPayDeclareResult result = new CustomsPayDeclareResult();
        result.setSuccess(true);
        result.setIdentityCheck(null);
        result.setVerDept(verDept);
        result.setPayTransactionId(payTransactionId);
        return result;
    }

    public static CustomsPayDeclareResult ok(Boolean identityCheck, String verDept, String payTransactionId){
        CustomsPayDeclareResult result = new CustomsPayDeclareResult();
        result.setSuccess(true);
        result.setIdentityCheck(identityCheck);
        result.setVerDept(verDept);
        result.setPayTransactionId(payTransactionId);
        return result;
    }

    public static CustomsPayDeclareResult fail(String errorMsg){
        CustomsPayDeclareResult result = new CustomsPayDeclareResult();
        result.setSuccess(false);
        result.setErrorMsg(errorMsg);
        return result;
    }
}
