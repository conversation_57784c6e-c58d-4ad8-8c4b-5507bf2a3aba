package com.danding.cds.declare.sdk.enums;



public enum CustomsPaymentStatus {
    NULL(0,"空"),
    WAIT_DECLARE(1,"待申报"),
    SUCCESS(2,"申报成功"),
    FAIL(3,"申报失败"),
    WAIT_RE_PUSH(4,"待重推"),
    CANCEL(-1,"取消");

    private Integer value;

    private String desc;

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static CustomsPaymentStatus getEnum(Integer value) {
        for (CustomsPaymentStatus customsPaymentStatus : CustomsPaymentStatus.values()) {
            if (customsPaymentStatus.value.equals(value)){
                return customsPaymentStatus;
            }
        }
        return NULL;
    }

    CustomsPaymentStatus(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

}
