package com.danding.cds.declare.sdk.model.checkList;

import lombok.Data;

import java.io.Serializable;
@Data
public class InvtItemBody implements Serializable {
    private static final long serialVersionUID = -8936042476960372143L;
    /**
     * 商品料号
     */
    private String productId;
    /**
     * 备案序号(对应底账序号）
     */
    private String itemRecordNo;
    /**
     * hs编码
     */
    private String gcode;

    private String gname;

    private String gmodel;

    private String currency;
    /**
     * 申报计量单位代码
     */
    private String unit;
    /**
     * 法定计量单位代码
     */
    private String unit1;
    /**
     * 第二计量单位代码
     */
    private String unit2 = "";
    /**
     * 申报数量
     */
    private String qty;
    /**
     * 法定数量
     */
    private String totalQty1;
    /**
     * 第二法定数量
     */
    private String totalQty2 = "";
    /**
     * 企业申报单价
     */
    private String price;
    /**
     * 企业申报总价
     */
    private String totalPrice;
    /**
     * 原产国（地区）
     */
    private String country;
    /**
     * 最终目的国（地区）
     */
    private String destCountry;
    /**
     * 该项总毛重
     */
    private String totalGrossWeight;
    /**
     * 该项总净重
     */
    private String totalNetWeight;

    /**
     * 危险品标志
     */
    private String clyMarkcd;

    /**
     * 报关单商品序号
     */
    private String entryGdsSeqno;

    /**
     * 流转申报表序号
     */
    private String applyTbSeqno;

    /**
     * 单耗版本号
     */
    protected String ucnsVerno;

    /**
     * 来源标识
     */
    private String param1;
}
