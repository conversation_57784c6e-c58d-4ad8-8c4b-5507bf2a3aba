package com.danding.cds.declare.sdk.clear.tianjin.builder;

import com.danding.cds.declare.ceb.internal.enums.CebDeclareType;
import com.danding.cds.declare.ceb.internal.utils.CebDateUtil;
import com.danding.cds.declare.sdk.model.cancel.WarpCancelOrderInfo;
import com.danding.cds.declare.tjport.domain.ENTBaseTransfer;
import com.danding.cds.declare.tjport.domain.ent623.ENT623InvtCancel;
import com.danding.cds.declare.tjport.domain.ent623.ENT623Message;

import java.io.Serializable;
import java.util.Date;
import java.util.UUID;

public class TJInventoryCancelBuilder implements Serializable {
    private WarpCancelOrderInfo info;

    public TJInventoryCancelBuilder(WarpCancelOrderInfo info) {
        this.info = info;
    }

    public ENT623Message build() {
        return buildOrderMessage();
    }

    private ENT623Message buildOrderMessage() {
        ENT623Message ent623Message = new ENT623Message();
        ENT623InvtCancel invtCancel = buildInvtCancel();
        ENTBaseTransfer baseTransfer = buildBaseTransfer();
        ent623Message.setGuid(invtCancel.getGuid());
        ent623Message.setReciptCode("121500");
        ent623Message.setSendCode("Q121500201907000422");
        ent623Message.setInvtCancel(invtCancel);
        ent623Message.setBaseTransfer(baseTransfer);
        return ent623Message;
    }

    private ENT623InvtCancel buildInvtCancel() {
        ENT623InvtCancel invtCancel = new ENT623InvtCancel();
        invtCancel.setGuid(UUID.randomUUID().toString().toUpperCase());
        invtCancel.setAppType(CebDeclareType.CREATE.getType());
        invtCancel.setAppTime(CebDateUtil.format(new Date(), CebDateUtil.defaultCebDateStringFormat));
        invtCancel.setAppStatus("2");
        invtCancel.setCustomsCode("0213");
        //订单编号
        invtCancel.setOrderNo(this.info.getCustomsInventoryDto().getOrderNo());
        //电商平台代码、电商平台名称、电商企业代码、电商企业名称
        invtCancel.setEbpCode(this.info.getEbpCompanyDTO().getCebCode());
        invtCancel.setEbpName(this.info.getEbpCompanyDTO().getCebName());
        invtCancel.setEbcCode(this.info.getEbcCompanyDTO().getCebCode());
        invtCancel.setEbcName(this.info.getEbcCompanyDTO().getCebName());
        //物流运单编号、物流企业代码、物流企业名称
        invtCancel.setLogisticsNo(this.info.getCustomsInventoryDto().getLogisticsNo());
        invtCancel.setLogisticsCode(this.info.getLogisticsCompanyDTO().getCebCode());
        invtCancel.setLogisticsName(this.info.getLogisticsCompanyDTO().getCebName());
        //企业内部标识单证的编号、预录入编号、清单编号
        invtCancel.setCopNo(this.info.getCustomsInventoryCancelInfo().getId().toString());
        invtCancel.setPreNo(this.info.getCustomsInventoryDto().getPreNo());
        invtCancel.setInvtNo(this.info.getCustomsInventoryDto().getInventoryNo());
        //订购人信息
        invtCancel.setBuyerIdType("1");
        invtCancel.setBuyerIdNumber(this.info.getCustomsInventoryDto().getBuyerIdNumber());
        invtCancel.setBuyerTelephone(this.info.getCustomsInventoryDto().getBuyerTelNumber());
        invtCancel.setBuyerName(this.info.getCustomsInventoryDto().getBuyerName());
        //申报企业代码、名称
        String declareCompanyCode = this.info.getDeclareCompanyDTO().getCebCode();
        String declareCompanyName = this.info.getDeclareCompanyDTO().getCebName();
        invtCancel.setAgentCode(declareCompanyCode);
        invtCancel.setAgentName(declareCompanyName);
        invtCancel.setReason("交易订单取消,买家申请退款");
        return invtCancel;
    }

    private ENTBaseTransfer buildBaseTransfer() {
        ENTBaseTransfer baseTransfer = new ENTBaseTransfer();
        baseTransfer.setCopCode(info.getDeclareCompanyDTO().getCebCode());
        baseTransfer.setCopName(info.getDeclareCompanyDTO().getCebName());
        baseTransfer.setDxpId(info.getDeclareCompanyDTO().getDxpId());
        baseTransfer.setDxpMode("DXP");
        return baseTransfer;
    }
}
