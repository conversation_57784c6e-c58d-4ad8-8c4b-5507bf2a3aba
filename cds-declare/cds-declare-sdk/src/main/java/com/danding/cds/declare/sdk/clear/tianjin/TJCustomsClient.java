package com.danding.cds.declare.sdk.clear.tianjin;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.cds.common.utils.ExceptionJoinUtil;
import com.danding.cds.common.utils.XmlUtil;
import com.danding.cds.customs.inventory.api.enums.InventoryOrderType;
import com.danding.cds.declare.sdk.CustomsReport;
import com.danding.cds.declare.sdk.CustomsSupport;
import com.danding.cds.declare.sdk.clear.base.CustomsClient;
import com.danding.cds.declare.sdk.clear.base.CustomsClientRegistry;
import com.danding.cds.declare.sdk.clear.base.callback.CallbackResult;
import com.danding.cds.declare.sdk.clear.base.callback.CallbackType;
import com.danding.cds.declare.sdk.clear.base.callback.module.InventoryCallback;
import com.danding.cds.declare.sdk.clear.base.callback.module.InventoryCancel;
import com.danding.cds.declare.sdk.clear.base.result.InventoryCancelResult;
import com.danding.cds.declare.sdk.clear.base.result.InventoryDeclareResult;
import com.danding.cds.declare.sdk.clear.tianjin.builder.TJInventoryBuilder;
import com.danding.cds.declare.sdk.clear.tianjin.builder.TJInventoryCancelBuilder;
import com.danding.cds.declare.sdk.enums.CustomsType;
import com.danding.cds.declare.sdk.model.cancel.WarpCancelOrderInfo;
import com.danding.cds.declare.sdk.model.inventory.WrapInventoryOrderInfo;
import com.danding.cds.declare.tjport.domain.ent621.ENT621Message;
import com.danding.cds.declare.tjport.domain.ent622.ENT622Message;
import com.danding.cds.declare.tjport.domain.ent622.ENT622MessageReturn;
import com.danding.cds.declare.tjport.domain.ent623.ENT623Message;
import com.danding.cds.declare.tjport.domain.ent624.ENT624Message;
import com.danding.cds.declare.tjport.domain.ent624.ENT624MessageReturn;
import com.danding.cds.log.api.dto.TrackLogDTO;
import com.danding.cds.log.api.enums.TrackLogEnums;
import com.danding.cds.order.api.enums.OrderStatus;
import com.danding.component.http.SaasHttpConstant;
import com.danding.core.tenant.SimpleTenantHelper;
import com.github.kevinsawicki.http.HttpRequest;
import com.thoughtworks.xstream.XStream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
public class TJCustomsClient extends CustomsClient {

    /**
     * 初始化注入
     *
     * @param support
     * @param registry
     * @param env
     */
    public TJCustomsClient(CustomsSupport support, CustomsClientRegistry registry, String env) {
        super(support, registry, env);
    }

    @Override
    public CustomsType getSystem() {
        return CustomsType.TIAN_JIN;
    }

    @Override
    public InventoryDeclareResult inventoryDeclare(WrapInventoryOrderInfo info) {
        log.info("[op:TJCustomsClient-inventoryDeclare start]  declareOrderNo={},wrapInventoryOrderInfo={}", info.getCustomsInventoryDto().getOrderNo(), JSON.toJSONString(info));
        CustomsReport report = new CustomsReport(CustomsReport.TYPE_SEND, CustomsReport.SYSTEM_CUSTOMS_CLIENT, CustomsReport.PROCESS_CLEAR_DECLARE_INVENTORY);
        InventoryDeclareResult result = new InventoryDeclareResult();
        try {
            result.setOrderNo(info.getCustomsInventoryDto().getOrderNo());
            result.setSn(info.getCustomsInventoryDto().getSn());
            result.setStatus(info.getCustomsInventoryDto().getStatus());
            report.buildProcessData(result);
            log.info("[op:TJCustomsClient-inventoryDeclare-builder-start]  declareOrderNo={}", info.getCustomsInventoryDto().getOrderNo());
            TJInventoryBuilder builder = new TJInventoryBuilder(info);
            ENT621Message ent621Message = builder.build();
            log.info("[op:TJCustomsClient-inventoryDeclare-builder-end]  declareOrderNo={},ent621Message={}", info.getCustomsInventoryDto().getOrderNo(), ent621Message);
            //生成报文 提交到天津海关客户端所在发送文件夹

            String fileName = info.getCustomsInventoryDto().getOrderNo() + "_" + System.currentTimeMillis() + ".xml";
            String content = XmlUtil.buildXml(ent621Message, new Class[]{ENT621Message.class});
            log.info("[op:TJCustomsClient-inventoryDeclare]  declareOrderNo={},ent621Message={}", info.getCustomsInventoryDto().getOrderNo(), content);
            if (CustomsSupport.ENV_ONLINE.equals(env)) {
                String url = "http://115.227.50.133:15000/xhr/tianjin/sendNew";
                JSONObject params = new JSONObject();
                params.put("filePath", "Imp_CebCiq_Send");
                params.put("fileName", fileName);
                params.put("content", content);
                log.info("[op: TJCustomsClient-inventoryDeclare] url={}, params={}", url, JSON.toJSONString(params));
                try {
                    TrackLogDTO trackLogDTO = new TrackLogDTO();
                    trackLogDTO.setOrderType(InventoryOrderType.INVENTORY_ORDER.getCode());
                    trackLogDTO.setDeclareOrderNo(info.getCustomsInventoryDto().getOrderNo())
                            .setOldStatus(OrderStatus.DEC_WAIT.getValue()).setNewStatus(OrderStatus.DEC_ING.getValue())
                            .setOperateDes(TrackLogEnums.INVENTORY_DECLARE.getCode()).setLogDes("天津口岸报文已推送").setContent(content).setHasXmlMessage(1);
                    support.messageSender.sendMsg(JSON.toJSONString(trackLogDTO), "ccs-trackLog-topic");
                } catch (Exception e) {
                    log.info("[op:TJCustomsClient-inventoryDeclare trackLog error={}]", e.getMessage());
                }

                HttpRequest httpRequest = HttpRequest.post(url)
                        .header(SaasHttpConstant.HEADER_TENANT_ID, SimpleTenantHelper.getTenantIdStr())
                        .readTimeout(15000).connectTimeout(15000)
                        .form(params);
                String body = httpRequest.body();
                if (httpRequest.ok()) {
                    if ("SUCCESS".equals(body)) {
                        return result;
                    } else {
                        log.error("[op: OrderDeclarePushOutput] 子客户端文件存储失败, content={}", result);
                    }
                } else {
                    log.error("天津苏宁 - 清单申报异常 : 信息 ：{}", ExceptionJoinUtil.removeCrlf(body));
                }
            } else {
                return result;
            }

        } catch (Exception e) {
            log.error("天津苏宁 - 清单申报异常 : 堆栈 ：{}", ExceptionJoinUtil.exceptionStackTraceAsString(e));
        }
        return result;
    }

    @Override
    public InventoryCallback handelInventoryMsg(CallbackResult callbackResult) {
        CustomsReport report = new CustomsReport(
                CustomsReport.TYPE_ACCEPT,
                CustomsReport.SYSTEM_CUSTOMS_CLIENT,
                CustomsReport.PROCESS_CLEAR_CALLBACK_INVENTORY);
        report.setRequestMsg("");
        log.info("[op:TJCustomsClient-handelInventoryMsg] CallbackResult={}", JSON.toJSONString(callbackResult));
        InventoryCallback inventoryCallback = new InventoryCallback();
        try {
            XStream xStream = new XStream();
            String requestXmlString = callbackResult.getOrigMsg();
            ENT622Message ent622Message = new ENT622Message();
            xStream.alias("InventoryReturn", ENT622MessageReturn.class);
            xStream.alias("ENT622Message", ENT622Message.class);
            xStream.fromXML(requestXmlString, ent622Message);
            ENT622MessageReturn messageReturn = ent622Message.getInventoryReturn();
            inventoryCallback.setEbpCode(messageReturn.getEbpCode());
            inventoryCallback.setSn(messageReturn.getCopNo());
            if (StringUtils.isEmpty(messageReturn.getReturnTime())) {
                inventoryCallback.setReturnTime(new Date());
            } else {
                Date returnDate = DateUtils.parseDate(messageReturn.getReturnTime(), "yyyyMMddHHmmssSSS");
                inventoryCallback.setReturnTime(returnDate);
            }
            inventoryCallback.setInvtNo(messageReturn.getInvtNo());
            inventoryCallback.setPreNo(messageReturn.getPreNo());
            inventoryCallback.setReturnStatus(messageReturn.getReturnStatus());
            inventoryCallback.setReturnInfo(messageReturn.getReturnInfo());

            report.setProcessData(JSON.toJSONString(inventoryCallback));
            support.accept(report);
        } catch (Exception e) {
            log.error("天津苏宁 - 清单回执处理异常 : 堆栈 ：{}", ExceptionJoinUtil.exceptionStackTraceAsString(e));
        }
        log.info("[op:TJCustomsClient-handelInventoryMsg] result={}", JSON.toJSONString(inventoryCallback));
        return inventoryCallback;
    }

    @Override
    public InventoryCancelResult inventoryCancel(WarpCancelOrderInfo declareInfo) {
        log.info("[op:TJCustomsClient-inventoryCancel start]  declareOrderNo={},wrapInventoryOrderInfo={}", declareInfo.getCustomsInventoryDto().getOrderNo(), JSON.toJSONString(declareInfo));
        CustomsReport report = new CustomsReport(
                CustomsReport.TYPE_SEND,
                CustomsReport.SYSTEM_CUSTOMS_CLIENT,
                CustomsReport.PROCESS_CLEAR_DECLARE_INVENTORY_CANCEL
        );
        InventoryCancelResult result = new InventoryCancelResult();
        result.setOrderNo(declareInfo.getCustomsInventoryDto().getOrderNo());
        result.setSn(declareInfo.getCustomsInventoryCancelInfo().getSn());
        result.setStatus(declareInfo.getCustomsInventoryCancelInfo().getStatus());
        report.buildProcessData(result);

        TJInventoryCancelBuilder builder = new TJInventoryCancelBuilder(declareInfo);
        ENT623Message ent623Message = builder.build();
        String fileName = "CD_" + declareInfo.getCustomsInventoryDto().getOrderNo() + "_" + System.currentTimeMillis() + ".xml";
        String content = XmlUtil.buildXml(ent623Message, new Class[]{ENT623Message.class});
        if (CustomsSupport.ENV_ONLINE.equals(env)) {
            String url = "http://115.227.50.133:15000/xhr/tianjin/sendNew";
            JSONObject params = new JSONObject();
            params.put("filePath", "Imp_CebCiq_Send");
            params.put("fileName", fileName);
            params.put("content", content);
            log.info("[op: TJCustomsClient-inventoryCancel] url={}, params={}", url, JSON.toJSONString(params));
            HttpRequest httpRequest = HttpRequest.post(url)
                    .header(SaasHttpConstant.HEADER_TENANT_ID, SimpleTenantHelper.getTenantIdStr())
                    .readTimeout(15000).connectTimeout(15000)
                    .form(params);
            String body = httpRequest.body();
            if (httpRequest.ok()) {
                if ("SUCCESS".equals(body)) {
                    return result;
                } else {
                    log.error("[op: OrderDeclarePushOutput-inventoryCancel] 子客户端文件存储失败, content={}", result);
                }
            } else {
                log.error("天津苏宁 - 清单取消异常 : 信息 ：{}", ExceptionJoinUtil.removeCrlf(body));
            }
        } else {
            return result;
        }
        return result;
    }

    @Override
    public List<InventoryCancel> handelInventoryCancel(CallbackResult callbackResult) {
        List<InventoryCancel> cancelList = new ArrayList<>();
        CustomsReport report = new CustomsReport(
                CustomsReport.TYPE_ACCEPT,
                CustomsReport.SYSTEM_CUSTOMS_CLIENT,
                CustomsReport.PROCESS_CLEAR_CALLBACK_INVENTORY_CANCEL);
        String requestXmlString = callbackResult.getOrigMsg();
        XStream xStream = new XStream();
        ENT624Message ent624Message = new ENT624Message();
        xStream.alias("InvtCancelReturn", ENT624MessageReturn.class);
        xStream.alias("ENT624Message", ENT624Message.class);
        xStream.fromXML(requestXmlString, ent624Message);
        ENT624MessageReturn messageReturn = ent624Message.getInvtCancelReturn();
        InventoryCancel cancel = new InventoryCancel();
        cancel.setEbpCode(messageReturn.getEbpCode());
        cancel.setID(messageReturn.getCopNo());
        Date returnDate = null;
        try {
            returnDate = DateUtils.parseDate(messageReturn.getReturnTime(), "yyyyMMddHHmmssSSS");
        } catch (ParseException e) {
            log.warn("处理异常：{}", e.getMessage(), e);
        }
        cancel.setReturnTime(returnDate);
        cancel.setReturnStatus(messageReturn.getReturnStatus());
        cancel.setReturnInfo(messageReturn.getReturnInfo());
        cancelList.add(cancel);
        report.setProcessData(JSON.toJSONString(cancelList));
        support.accept(report);
        return cancelList;
    }


    @Override
    public CallbackResult parserCallback(Object request) {
        CallbackResult result = new CallbackResult();
        String content = (String) request;
        if (!content.startsWith("<")) {
            content = new String(Base64.decodeBase64(content.getBytes(StandardCharsets.UTF_8)));
        }
        log.info("[OP:TJCustomsClient] 天津解密报文:{}", content);
        CallbackType callbackType = getEventKey(content);
        result.setCallbackType(callbackType);
        result.setOrigMsg(content);
        if (CallbackType.NULL.equals(result.getCallbackType())) {
            CustomsReport report = new CustomsReport(
                    CustomsReport.TYPE_ACCEPT,
                    CustomsReport.SYSTEM_CUSTOMS_CLIENT,
                    CustomsReport.PROCESS_CLEAR_CALLBACK_UN_KNOW);
            report.setResponseMsg(JSON.toJSONString(content));
            report.setProcessData(JSON.toJSONString(result));
            support.accept(report);
        }
        return result;
    }


    public static CallbackType getEventKey(String content) {
        if (content.contains("ENT622Message")) {
            return CallbackType.INVENTORY;
        } else if (content.contains("ENT624Message")) {
            return CallbackType.CANCEL;
        }
        return CallbackType.NULL;
    }
}
