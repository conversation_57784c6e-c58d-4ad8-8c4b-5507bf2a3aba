package com.danding.cds.declare.sdk.listener;

import com.alibaba.fastjson.JSON;
import com.danding.cds.declare.sdk.CustomsReport;
import com.danding.cds.declare.sdk.clear.base.callback.module.*;
import com.danding.cds.declare.sdk.clear.base.result.*;
import com.danding.cds.declare.sdk.model.payment.CustomsPayDeclareResult;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public abstract class CustomsListener {

    public void accept(CustomsReport report){
        this.commonListen(report);

        // Module::支付单同步响应
        if (CustomsReport.SYSTEM_PAY_CHANNEL.equals(report.getSystem())){
            if (CustomsReport.PROCESS_CLEAR_DECLARE_PAYMENT.equals(report.getProcess())){
                CustomsPayDeclareResult resultInfo = JSON.parseObject(report.getProcessData(), CustomsPayDeclareResult.class);
                this.sendClearPaymentDeclare(report,resultInfo);
            }
        }

        // Module::订单申报回执
        if (CustomsReport.SYSTEM_CUSTOMS_CLIENT.equals(report.getSystem())){
            if (CustomsReport.PROCESS_CLEAR_CALLBACK_ORDER.equals(report.getProcess())){
                OrderCallback orderCallback = JSON.parseObject(report.getProcessData(),OrderCallback.class);
                this.acceptOrderCallback(report,orderCallback);
            }
            if (CustomsReport.PROCESS_CLEAR_CALLBACK_INVENTORY.equals(report.getProcess())){
                InventoryCallback callback = JSON.parseObject(report.getProcessData(),InventoryCallback.class);
                this.acceptInventoryCallback(report,callback);
            }
            if (CustomsReport.PROCESS_CLEAR_CALLBACK_SHIPMENT.equals(report.getProcess())){
                ShipmentCallback callback = JSON.parseObject(report.getProcessData(),ShipmentCallback.class);
                this.acceptLogisticsCallback(report,callback);
            }
            if (CustomsReport.PROCESS_CLEAR_CALLBACK_TAX.equals(report.getProcess())){
                List<TaxResult> result = JSON.parseArray(report.getProcessData(),TaxResult.class);
                this.acceptTaxCallback(report,result);
            }
            if (CustomsReport.PROCESS_CLEAR_CALLBACK_TAX_STATUS.equals(report.getProcess())){
                List<TaxStatus> result = JSON.parseArray(report.getProcessData(),TaxStatus.class);
                this.acceptTaxStatusCallback(report,result);
            }
            if (CustomsReport.PROCESS_CLEAR_CALLBACK_INVENTORY_CANCEL.equals(report.getProcess())){
                List<InventoryCancel> result = JSON.parseArray(report.getProcessData(),InventoryCancel.class);
                this.acceptInventoryCancelCallback(report,result);
            }
            if (CustomsReport.PROCESS_CLEAR_CALLBACK_INVENTORY_REFUND.equals(report.getProcess())){
                List<InventoryRefund> result = JSON.parseArray(report.getProcessData(),InventoryRefund.class);
                this.acceptInventoryRefundCallback(report,result);
            }
            if (CustomsReport.PROCESS_CLEAR_DECLARE_ORDER.equals(report.getProcess())){
                OrderDeclareResult result = JSON.parseObject(report.getProcessData(),OrderDeclareResult.class);
                this.sendClearOrderDeclare(report,result);
            }
            if (CustomsReport.PROCESS_CLEAR_DECLARE_INVENTORY.equals(report.getProcess())){
                InventoryDeclareResult result = JSON.parseObject(report.getProcessData(),InventoryDeclareResult.class);
                this.sendClearInventoryDeclare(report,result);
            }
            if (CustomsReport.PROCESS_CLEAR_DECLARE_SHIPMENT.equals(report.getProcess())){
                ShipmentDeclareResult result = JSON.parseObject(report.getProcessData(),ShipmentDeclareResult.class);
                this.sendClearLogisticsDeclare(report,result);
            }
            if (CustomsReport.PROCESS_CLEAR_DECLARE_INVENTORY_CANCEL.equals(report.getProcess())){
                InventoryCancelResult result = JSON.parseObject(report.getProcessData(),InventoryCancelResult.class);
                this.sendInventoryCancelDeclare(report,result);
            }
            if (CustomsReport.PROCESS_CLEAR_DECLARE_INVENTORY_REFUND.equals(report.getProcess())){
                InventoryRefundResult result = JSON.parseObject(report.getProcessData(),InventoryRefundResult.class);
                this.sendInventoryRefundDeclare(report,result);
            }
            if (CustomsReport.PROCESS_CALLBACK_DELIVER.equals(report.getProcess())){
                DeliverResult result = JSON.parseObject(report.getProcessData(),DeliverResult.class);
                this.acceptDeliverCallback(report,result);
            }
        }
    }

    public void commonListen(CustomsReport report){
        log.debug("[op:CustomsListener] 当前Listener未实现监听方法：通用监听-commonListen");
    }

    public void sendClearPaymentDeclare(CustomsReport report, CustomsPayDeclareResult resultInfo){
        log.debug("[op:CustomsListener] 当前Listener未实现监听方法：支付单申报-sendClearPaymentDeclare");
    }

    public void sendClearOrderDeclare(CustomsReport report, OrderDeclareResult result){
        log.debug("[op:CustomsListener] 当前Listener未实现监听方法：订单申报-sendClearOrderDeclare");
    }

    public void sendClearInventoryDeclare(CustomsReport report, InventoryDeclareResult result){
        log.debug("[op:CustomsListener] 当前Listener未实现监听方法：清单申报-sendClearInventoryDeclare");
    }

    public void sendClearLogisticsDeclare(CustomsReport report, ShipmentDeclareResult result){
        log.debug("[op:CustomsListener] 当前Listener未实现监听方法：运单申报-sendClearLogisticsDeclare");
    }

    public void sendInventoryCancelDeclare(CustomsReport report, InventoryCancelResult result){
        log.debug("[op:CustomsListener] 当前Listener未实现监听方法：清单撤单申报-sendInventoryCancelDeclare");
    }

    public void sendInventoryRefundDeclare(CustomsReport report, InventoryRefundResult result){
        log.debug("[op:CustomsListener] 当前Listener未实现监听方法：清单退货申报-sendInventoryRefundDeclare");
    }

    public void acceptOrderCallback(CustomsReport report, OrderCallback callback){
        log.debug("[op:CustomsListener] 当前Listener未实现监听方法：订单回执-acceptOrderCallback");
    }

    public void acceptInventoryCallback(CustomsReport report, InventoryCallback inventoryCallback){
        log.debug("[op:CustomsListener] 当前Listener未实现监听方法：清单回执-acceptInventoryCallback");
    }

    public void acceptLogisticsCallback(CustomsReport report, ShipmentCallback shipmentCallback){
        log.debug("[op:CustomsListener] 当前Listener未实现监听方法：运单回执-acceptLogisticsCallback");
    }

    public void acceptTaxCallback(CustomsReport report, List<TaxResult> taxResults){
        log.debug("[op:CustomsListener] 当前Listener未实现监听方法：税单回执-acceptTaxCallback");
    }

    public void acceptTaxStatusCallback(CustomsReport report, List<TaxStatus> taxStatuses){
        log.debug("[op:CustomsListener] 当前Listener未实现监听方法：税单汇总回执-acceptTaxStatusCallback");
    }

    public void acceptInventoryCancelCallback(CustomsReport report, List<InventoryCancel> cancel){
        log.debug("[op:CustomsListener] 当前Listener未实现监听方法：撤单回执-acceptInventoryCancelCallback");
    }

    public void acceptInventoryRefundCallback(CustomsReport report, List<InventoryRefund> refund){
        log.debug("[op:CustomsListener] 当前Listener未实现监听方法：退货回执-acceptInventoryRefundCallback");
    }

    public void acceptDeliverCallback(CustomsReport report, DeliverResult result){
        log.debug("[op:CustomsListener] 当前Listener未实现监听方法：发货回执-acceptDeliverCallback");
    }
}
