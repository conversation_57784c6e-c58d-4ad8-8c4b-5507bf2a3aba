package com.danding.cds.declare.sdk.model.man;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 非保税库存记录 MAN00002
 * @date 2023/3/1 16:04
 */
@Data
public class MAN00002Request implements Serializable {
    /**
     * 签名信息
     */
    private ManSign manSign;

    /**
     * 非保税库存信息
     */
    private List<ManFbInventoryDetail> manFbInventoryDetailList;

    @Data
    @Accessors(chain = true)
    public static class ManFbInventoryDetail implements Serializable {
        /**
         * 账册编码
         */
        private String manualId;
        /**
         * 料号
         */
        private String sourceNo;
        /**
         * 料件性质
         */
        private String itemType;
        /**
         * 库存数量
         */
        private BigDecimal goodsQuantity;
        /**
         * 单位编码
         */
        private String goodsUnit;
        /**
         * 库位
         */
        private String storageLocation;
        /**
         * 库存申报时间
         */
        private String inventoryTime;
        /**
         * 备注
         */
        private String remark;
    }
}
