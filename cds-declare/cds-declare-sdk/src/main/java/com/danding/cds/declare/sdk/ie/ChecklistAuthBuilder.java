package com.danding.cds.declare.sdk.ie;

import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.declare.sdk.config.CustomsSpecialToken;
import com.danding.cds.declare.sdk.model.checklistAuth.Sas122MessageRequest;
import com.danding.cds.declare.zjspecial.domain.KeyInfo;
import com.danding.cds.declare.zjspecial.domain.Signature;
import com.danding.cds.declare.zjspecial.domain.SignedInfo;
import com.danding.cds.declare.zjspecial.domain.base.EnvelopInfo;
import com.danding.cds.declare.zjspecial.domain.sas122.*;
import com.danding.cds.declare.zjspecial.internal.enums.CustomsMessageType;
import com.danding.cds.declare.zjspecial.internal.utils.SpecialUtil;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.UUID;

public class Checklist<PERSON><PERSON><PERSON>uilder implements Serializable {
    private CustomsSpecialToken target;

    public Signature build(Sas122MessageRequest req, CustomsSpecialToken specialToken) {
        this.target = specialToken;
        Signature signature = new Signature();
        signature.setSignedInfo(buildSignedInfo());
        signature.setSignatureValue("");
        signature.setKeyInfo(buildKeyInfo());
        signature.setObject(buildObject(req));
        return signature;
    }

    private KeyInfo buildKeyInfo() {
        KeyInfo keyInfo = new KeyInfo();
        keyInfo.setKeyName("aa");
        return keyInfo;
    }

    private SignedInfo buildSignedInfo() {
        SignedInfo signedInfo = new SignedInfo();
        return signedInfo;
    }

    private Signature.Object buildObject(Sas122MessageRequest req) {
        Signature.Object object = new Signature.Object();
        object.setPackage(buildPackage(req));
        return object;
    }

    private Sas122Package buildPackage(Sas122MessageRequest req) {
        Sas122Package _package = new Sas122Package();
        _package.setEnvelopInfo(buildEnvelopInfo(req));
        _package.setDataInfo(buildDataInfo(req));
        return _package;
    }

    private EnvelopInfo buildEnvelopInfo(Sas122MessageRequest req) {
        EnvelopInfo envelopInfo = new EnvelopInfo();
        String uuid = UUID.randomUUID().toString();
        envelopInfo.setBusinessId("businessId-" + uuid);
        envelopInfo.setFileName("fileName-" + uuid);
        envelopInfo.setIcCard(target.getIcCard());//ic卡号
        envelopInfo.setMessageId("messageId-" + uuid);
        envelopInfo.setMessageType(CustomsMessageType.SAS122.name());
        envelopInfo.setReceiverId("DXPEDCSAS0000001");
        envelopInfo.setSenderId(target.getSenderId());
        envelopInfo.setSendTime(SpecialUtil.dateToXmlDate(new Date()));
        envelopInfo.setVersion("1.0");
        return envelopInfo;
    }

    private DataInfo buildDataInfo(Sas122MessageRequest req) {
        DataInfo dataInfo = new DataInfo();
        dataInfo.setBussinessData(buildBussinessData(req));
        return dataInfo;
    }

    private DataInfo.BussinessData buildBussinessData(Sas122MessageRequest req) {
        DataInfo.BussinessData bussinessData = new DataInfo.BussinessData();
        bussinessData.setDelcareFlag(req.getDelcareFlag());
        bussinessData.setPassportAuthRequest(buildPassportAuthRequest(req));
        return bussinessData;
    }

    private PassportAuthRequest buildPassportAuthRequest(Sas122MessageRequest req) {
        PassportAuthRequest passportAuthRequest = new PassportAuthRequest();
        passportAuthRequest.setPassportAuthInfo(buildPassportAuthInfo(req.getPassportAuthInfoList()));
        passportAuthRequest.setEtpsPreentNo(req.getEtpsPreentNo());
        passportAuthRequest.setSasSign(buildSasSign(req.getSasSign()));
        return passportAuthRequest;
    }

    private List<PassportAuthInfo> buildPassportAuthInfo(List<Sas122MessageRequest.PassportAuthInfo> passportAuthInfo) {
        return ConvertUtil.listConvert(passportAuthInfo, PassportAuthInfo.class);
    }

    private SasSign buildSasSign(Sas122MessageRequest.SasSign sasSign) {
        return ConvertUtil.beanConvert(sasSign, SasSign.class);
    }
}
