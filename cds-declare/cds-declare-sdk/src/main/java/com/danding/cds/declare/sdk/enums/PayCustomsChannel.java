package com.danding.cds.declare.sdk.enums;


/**
 * 支付申报渠道
 */
public enum PayCustomsChannel {
    NULL("","空"),
    ALIPAY("alipay","支付宝"),
    WECHAT_PAY("wechatpay","微信支付"),
    LIANDONG_PAY("umf", "联动支付"),
    TONGLIAN_PAY("tonglian", "通联支付");

    private String value;

    private String desc;

    PayCustomsChannel(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return this.value;
    }


    public String getDesc() {
        return this.desc;
    }

    public static PayCustomsChannel getEnum(String value) {
        for (PayCustomsChannel payChannel : PayCustomsChannel.values()) {
            if (payChannel.getValue().equalsIgnoreCase(value)){
                return payChannel;
            }
        }
        return NULL;
    }
}
