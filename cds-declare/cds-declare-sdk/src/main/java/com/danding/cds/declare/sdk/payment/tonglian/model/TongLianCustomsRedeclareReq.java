package com.danding.cds.declare.sdk.payment.tonglian.model;

import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(propOrder = {"head", "body"})
@XmlRootElement(name = "PAYMENT_INFO")
public class TongLianCustomsRedeclareReq implements Serializable {

    @XmlElement(name = "HEAD")
    private TongLianCustomsReDeclareXmlHead head;

    @XmlElement(name = "BODY")
    private Body body;


    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(propOrder = {"visitorId", "customsCode", "paymentMchtId", "paymentOrderNo", "mchtOrderNo", "bizType"})
    @XmlRootElement(name = "BODY")
    @Data
    public static class Body implements Serializable {

        /**
         * 接入方ID
         * 接入方编号，包括 'MCT'.'TLT','ETS','BIZ'.'SYB'.'GZF'。商户送 'MCT'
         */
        @XmlElement(name = "VISITOR_ID")
        private String visitorId;
        /**
         * 海关类别
         */
        @XmlElement(name = "CUSTOMS_CODE")
        private String customsCode;
        /**
         * 报关用的商户号
         */
        @XmlElement(name = "PAYMENT_MCHT_ID")
        private String paymentMchtId;
        /**
         * 报关流水号
         */
        @XmlElement(name = "PAYMENT_ORDER_NO")
        private String paymentOrderNo;
        /**
         * 商户订单号 商户平台订单号
         */
        @XmlElement(name = "MCHT_ORDER_NO")
        private String mchtOrderNo;
        /**
         * 业务类型 广州必填 0-海关报关
         */
        @XmlElement(name = "BIZ_TYPE")
        private String bizType;


    }
}
