package com.danding.cds.declare.sdk.clear.chongqing.model;

import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(propOrder = {"CUSTOMS_CODE", "EBP_CODE", "BIZ_TYPE_CODE", "SORTLINE_ID", "ESHOP_ENT_CODE", "ESHOP_ENT_NAME", "RECEIVER_NAME", "RECEIVER_ID_NO",
        "RECEIVER_ADDRESS", "RECEIVER_TEL", "GOODS_FEE", "TAX_FEE", "TRANSPORT_FEE", "GOODS_COUNT", "GOODS_SUM", "CC_TRADE_CODE", "CC_TRADE_NAME",
        "ORDER_NO", "ORIGINAL_ORDER_NO", "BAR_CODE", "BUYER_REG_NO", "BUYER_NAME", "BUYER_ID_TYPE", "BUYER_ID", "DISCOUNT", "ACTUAL_PAID"
        , "INSURED_FEE", "LOGISTICS_NO", "LOGISTICS_CODE", "WL_RETINFO", "PACKAGE_PLACE"})
@XmlRootElement(name = "ORDER_HEAD")
public class CQDeliverHead implements Serializable {

    @XmlElement(name = "CUSTOMS_CODE")
    private String CUSTOMS_CODE;

    @XmlElement(name = "EBP_CODE")
    private String EBP_CODE;

    @XmlElement(name = "BIZ_TYPE_CODE")
    private String BIZ_TYPE_CODE;

    @XmlElement(name = "SORTLINE_ID")
    private String SORTLINE_ID;

    @XmlElement(name = "ESHOP_ENT_CODE")
    private String ESHOP_ENT_CODE;

    @XmlElement(name = "ESHOP_ENT_NAME")
    private String ESHOP_ENT_NAME;

    @XmlElement(name = "RECEIVER_NAME")
    private String RECEIVER_NAME;

    @XmlElement(name = "RECEIVER_ID_NO")
    private String RECEIVER_ID_NO;

    @XmlElement(name = "RECEIVER_ADDRESS")
    private String RECEIVER_ADDRESS;

    @XmlElement(name = "RECEIVER_TEL")
    private String RECEIVER_TEL;

    @XmlElement(name = "GOODS_FEE")
    private String GOODS_FEE;

    @XmlElement(name = "TAX_FEE")
    private String TAX_FEE;

    @XmlElement(name = "TRANSPORT_FEE")
    private String TRANSPORT_FEE;

    @XmlElement(name = "GOODS_COUNT")
    private String GOODS_COUNT;

    @XmlElement(name = "GOODS_SUM")
    private String GOODS_SUM;

    @XmlElement(name = "CC_TRADE_CODE")
    private String CC_TRADE_CODE;

    @XmlElement(name = "CC_TRADE_NAME")
    private String CC_TRADE_NAME;

    @XmlElement(name = "ORDER_NO")
    private String ORDER_NO;

    @XmlElement(name = "ORIGINAL_ORDER_NO")
    private String ORIGINAL_ORDER_NO;

    @XmlElement(name = "BAR_CODE")
    private String BAR_CODE;

    @XmlElement(name = "BUYER_REG_NO")
    private String BUYER_REG_NO;

    @XmlElement(name = "BUYER_NAME")
    private String BUYER_NAME;

    @XmlElement(name = "BUYER_ID_TYPE")
    private String BUYER_ID_TYPE;

    @XmlElement(name = "BUYER_ID")
    private String BUYER_ID;

    @XmlElement(name = "DISCOUNT")
    private String DISCOUNT;

    @XmlElement(name = "ACTUAL_PAID")
    private String ACTUAL_PAID;

    @XmlElement(name = "INSURED_FEE")
    private String INSURED_FEE;

    @XmlElement(name = "LOGISTICS_NO")
    private String LOGISTICS_NO;

    @XmlElement(name = "LOGISTICS_CODE")
    private String LOGISTICS_CODE;

    @XmlElement(name = "WL_RETINFO")
    private String WL_RETINFO;

    @XmlElement(name = "PACKAGE_PLACE")
    private String PACKAGE_PLACE;
}
