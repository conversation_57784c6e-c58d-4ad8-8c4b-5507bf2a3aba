package com.danding.cds.declare.sdk.clear.base.callback.module;

import com.danding.cds.http.saas.annotation.TenantHttpField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class InventoryRefund implements Serializable {

    /**
     * 电商平台
     */
    private String ebpCode;

    /**
     * ID
     */
    private String ID;

    /**
     * 清关单号
     */
    @TenantHttpField(alias = "inventoryNo")
    private String invtNo;

    /**
     * 回执时间
     */
    private Date returnTime;

    /**
     * 回执状态
     */
    private String returnStatus;

    /**
     * 回执信息
     */
    private String returnInfo;
}
