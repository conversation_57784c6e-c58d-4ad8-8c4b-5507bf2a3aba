package com.danding.cds.declare.sdk.bean.dto;

import com.danding.cds.declare.zjport.domain.request.Request;
import com.danding.cds.declare.zjport.internal.enums.HzPortBusinessType;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @program: cds-center
 * @description: zjport
 * @author: 潘本乐（Belep）
 * @create: 2021-11-18 14:45
 **/
@Data
@NoArgsConstructor
public class ZjportMsgDto implements Serializable {
    /**
     * 口岸业务类型
     */
    private HzPortBusinessType businessType;
    /**
     * 申报单号
     */
    private String declareNo;
    /**
     * 原始报文
     */
    private String origMsg;
    /**
     * 原始报文对象
     */
    private Request Request;
    /**
     * 加密报文
     */
    private String encryptMsg;

    /**
     * 签名报文
     */
    private String signMsg;
    /**
     * 推送响应报文
     */
    private String response;
}
