package com.danding.cds.declare.sdk.swxa;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.util.StringUtils;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.xml.sax.InputSource;
import org.xml.sax.SAXException;
import sun.misc.BASE64Encoder;

import javax.xml.crypto.OctetStreamData;
import javax.xml.crypto.dsig.CanonicalizationMethod;
import javax.xml.crypto.dsig.TransformService;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerConfigurationException;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.MessageDigest;
import java.security.Signature;
import java.security.cert.X509Certificate;

/**
 * @program: cds-center
 * @description: 三未信安sm2,  注意：代码是三位信安提供的
 * @author: 潘本乐（Belep）
 * @create: 2022-11-14 17:54
 **/
@Slf4j
public class SwxaSignSm2Util {


    public static boolean isSm2(String certName) {

        if (StringUtils.isEmpty(certName)) {
            return false;
        }
        if (certName.contains("-sm2") || certName.contains("hzhyds")
                || certName.contains("jytx-sm2")) {
            return true;
        }
        return false;
    }


    public static String signXml(byte[] bs, X509Certificate _x509Certificate, int keyIndex) throws Exception {

        //调用密码设备对data.xml做摘要得到DigestValue
        String DigestValue = getDigestValue(bs);
        //创建SignedInfo节点
        String signedInfoString = creatSignedInfo(DigestValue);
        log.info("signedInfoString：" + DigestValue);
        //对SignedInfo节点加签
        String SignatureValue = getSignatureValue(keyIndex, signedInfoString.getBytes("UTF-8"));
        //组装Signature节点:::下面的keyname和cert是三未测试数据，请替换成正式数据
        String keyNameStr = "0" + _x509Certificate.getSerialNumber().toString(16);
        String x509Certificate = Base64.encodeBase64String(_x509Certificate.getEncoded());
        String SignatureStr = creatSignature(signedInfoString, SignatureValue, keyNameStr, x509Certificate);
        //组装XML报文全部
        String signXml = creatXml(new String(bs, StandardCharsets.UTF_8.name()), SignatureStr);
        return signXml;
    }

    public static String signXmlByDigestValue(String digestValue, X509Certificate _x509Certificate, int keyIndex) throws Exception {
        //创建SignedInfo节点
//        String signedInfoString = creatSignedInfo(digestValue);
        //对SignedInfo节点加签
        String SignatureValue = getSignatureValue(keyIndex, digestValue.getBytes("UTF-8"));
        log.info("SignatureValue={}", SignatureValue);
        //组装Signature节点:::下面的keyname和cert是三未测试数据，请替换成正式数据
        String keyNameStr = "0" + _x509Certificate.getSerialNumber().toString(16);
        String x509Certificate = Base64.encodeBase64String(_x509Certificate.getEncoded());
        log.info("x509Certificate={}", x509Certificate);
        String result = creatSignature(digestValue, SignatureValue, keyNameStr, x509Certificate);
        log.info("result={}", result);
        return result;
    }

    public static String getSignatureValue(int keyIndex, String signData) throws Exception {
        return getSignatureValue(keyIndex, signData.getBytes("UTF-8"));
    }

    public static String getSignatureValue(int keyIndex, byte[] signinfo) throws Exception {
        KeyPair kp = null;
        KeyPairGenerator kpg = KeyPairGenerator.getInstance("SM2", "SwxaJCE");
        kpg.initialize(keyIndex << 16);
        kp = kpg.genKeyPair();
        Signature signature = null;
        signature = Signature.getInstance("SM3WithSM2", "SwxaJCE");
        //签名
        signature.initSign(kp.getPrivate());
        signature.update(signinfo);
        byte[] out = signature.sign();
        return new BASE64Encoder().encode(out);
    }

    public static String getDigestValue(byte[] bytes) throws Exception {
        TransformService ts = TransformService.getInstance(CanonicalizationMethod.INCLUSIVE_WITH_COMMENTS, "DOM");
        String result;
        ByteArrayInputStream bais = new ByteArrayInputStream(bytes);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        OctetStreamData data = new OctetStreamData(bais);
        OctetStreamData newData = (OctetStreamData) ts.transform(data, null);
        InputStream is = newData.getOctetStream();
        byte[] buf = new byte[50 * 1024 * 1024];
        int len = is.read(buf);
        while (len != -1) {
            baos.write(buf, 0, len);
            len = is.read(buf);
        }
        MessageDigest md = null;
        byte[] out;
        md = MessageDigest.getInstance("SM3WithoutKey", "SwxaJCE");
        md.update(baos.toByteArray());
        out = md.digest();
        result = new BASE64Encoder().encode(out);
        is.close();
        return result;
    }

    public static String creatSignedInfo(String DigestValue) throws Exception {
        String xmlStr = null;

        Element signedInfoElm = null;
        Element canonicalizationMethodElm = null;
        Element signatureMethodElm = null;
        Element referenceElm = null;
        Element transformsElm = null;
        Element transformElm = null;
        Element digestMethodElm = null;
        Element digestValueElm = null;

        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        try {
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.newDocument();
            signedInfoElm = document.createElement("ds:SignedInfo");
            signedInfoElm.setAttribute("xmlns:ceb", "http://www.chinaport.gov.cn/ceb");
//			signedInfoElm.setAttribute("xmlns","http://www.chinaport.gov.cn/ceb");
            signedInfoElm.setAttribute("xmlns:ds", "http://www.w3.org/2000/09/xmldsig#");
            signedInfoElm.setAttribute("xmlns:xsi", "http://www.w3.org/2001/XMLSchema-instance");

            canonicalizationMethodElm = document.createElement("ds:CanonicalizationMethod");
            canonicalizationMethodElm.setAttribute("Algorithm", "http://www.w3.org/TR/2001/REC-xml-c14n-20010315");
            canonicalizationMethodElm.appendChild(document.createTextNode(""));

            signatureMethodElm = document.createElement("ds:SignatureMethod");
            signatureMethodElm.setAttribute("Algorithm", "http://www.chinaport.gov.cn/2022/04/xmldsig#sm2-sm3");
            signatureMethodElm.appendChild(document.createTextNode(""));

            referenceElm = document.createElement("ds:Reference");
            referenceElm.setAttribute("URI", "");

            transformsElm = document.createElement("ds:Transforms");

            transformElm = document.createElement("ds:Transform");
            transformElm.setAttribute("Algorithm", "http://www.w3.org/2000/09/xmldsig#enveloped-signature");
            transformElm.appendChild(document.createTextNode(""));

            digestMethodElm = document.createElement("ds:DigestMethod");
            digestMethodElm.setAttribute("Algorithm", "http://www.chinaport.gov.cn/2022/04/xmldsig#sm3");
            digestMethodElm.appendChild(document.createTextNode(""));

            digestValueElm = document.createElement("ds:DigestValue");
            digestValueElm.appendChild(document.createTextNode(DigestValue));

            transformsElm.appendChild(transformElm);

            referenceElm.appendChild(transformsElm);
            referenceElm.appendChild(digestMethodElm);
            referenceElm.appendChild(digestValueElm);

            signedInfoElm.appendChild(canonicalizationMethodElm);
            signedInfoElm.appendChild(signatureMethodElm);
            signedInfoElm.appendChild(referenceElm);

            document.appendChild(signedInfoElm);

            TransformerFactory transFactory = TransformerFactory.newInstance();
            Transformer transFormer = transFactory.newTransformer();
            DOMSource domSource = new DOMSource(document);
            //export string
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            transFormer.transform(domSource, new StreamResult(bos));
            xmlStr = bos.toString("UTF-8");
        } catch (Exception e) {
            log.error("三位信安SM2-creatSignedInfo异常:{}", e.getMessage(), e);
        }
        int index = xmlStr.indexOf("<ds:SignedInfo");
        String string = xmlStr.substring(index, xmlStr.length());
        string = c14nT(string);
        return string;
    }

    public static String creatSignature(String SignInfo, String SignatureValue, String KeyName, String X509Certificate) throws Exception {

        String xmlStr = null;

        Element SignatureElm = null;
        Element SignatureValueElm = null;
        Element KeyInfoElm = null;
        Element KeyNameElm = null;
        Element X509DataElm = null;
        Element X509CertificateElm = null;

        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        try {
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.newDocument();
            SignatureElm = document.createElement("ds:Signature");
            SignatureElm.setAttribute("xmlns:ds", "http://www.w3.org/2000/09/xmldsig#");

            SignatureValueElm = document.createElement("ds:SignatureValue");
            SignatureValue = SignatureValue.replace("\r\n", "");
            SignatureValueElm.appendChild(document.createTextNode(SignatureValue));

            KeyInfoElm = document.createElement("ds:KeyInfo");

            KeyNameElm = document.createElement("ds:KeyName");
            KeyNameElm.appendChild(document.createTextNode(KeyName));

            X509DataElm = document.createElement("ds:X509Data");

            X509CertificateElm = document.createElement("ds:X509Certificate");
            X509CertificateElm.appendChild(document.createTextNode(X509Certificate));

            X509DataElm.appendChild(X509CertificateElm);

            KeyInfoElm.appendChild(KeyNameElm);
            KeyInfoElm.appendChild(X509DataElm);

            DocumentBuilderFactory factory1 = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder1;
            Element docEle = null;
            builder1 = factory1.newDocumentBuilder();
            Document doc1 = builder1.parse(new InputSource(new StringReader(SignInfo)));
            docEle = doc1.getDocumentElement();

            Element SignedInfoEle = null;
            SignedInfoEle = (Element) document.importNode(docEle, true);
            SignatureElm.appendChild(SignedInfoEle);
//			SignatureElm.appendChild(document.createTextNode(SignInfo));
            SignatureElm.appendChild(SignatureValueElm);
            SignatureElm.appendChild(KeyInfoElm);

            document.appendChild(SignatureElm);

            TransformerFactory transFactory = TransformerFactory.newInstance();
            Transformer transFormer = transFactory.newTransformer();
            DOMSource domSource = new DOMSource(document);
            //export string
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            transFormer.transform(domSource, new StreamResult(bos));
            xmlStr = bos.toString("UTF-8");
        } catch (ParserConfigurationException e) {
            log.error("SM2-creatSignature异常：{}", e.getMessage(), e);
        }

        int index = xmlStr.indexOf("<ds:Signature");
        return c14nT(xmlStr.substring(index, xmlStr.length()));
    }

    public static byte[] readXml(String sourceXmlData) throws UnsupportedEncodingException {
        String xmlStr = null;
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder;
            builder = factory.newDocumentBuilder();
            Document doc = builder.parse(new InputSource(new StringReader(sourceXmlData)));
            Element docEle = doc.getDocumentElement();

            TransformerFactory transFactory = TransformerFactory.newInstance();
            Transformer transFormer = transFactory.newTransformer();
            DOMSource domSource = new DOMSource(docEle);
            //export string
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            transFormer.transform(domSource, new StreamResult(bos));
            xmlStr = bos.toString("UTF-8");
        } catch (IOException e) {
            e.printStackTrace();
        } catch (ParserConfigurationException e) {
            e.printStackTrace();
        } catch (SAXException e) {
            e.printStackTrace();
        } catch (TransformerConfigurationException e) {
            e.printStackTrace();
        } catch (TransformerException e) {
            e.printStackTrace();
        }
        return xmlStr.getBytes("utf-8");
    }

    public static String creatXml(String sourceXml, String Signature) throws Exception {
        String xmlStr = null;
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder;
            builder = factory.newDocumentBuilder();
            Document doc = builder.parse(new InputSource(new StringReader(sourceXml)));
            Element docEle = doc.getDocumentElement();

            DocumentBuilderFactory factory1 = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder1;
            Element docEle1 = null;
            builder1 = factory1.newDocumentBuilder();
            Document doc1 = builder1.parse(new InputSource(new StringReader(Signature)));
            docEle1 = doc1.getDocumentElement();

            Element SignatureEle = null;
            SignatureEle = (Element) doc.importNode(docEle1, true);
            docEle.appendChild(SignatureEle);

            TransformerFactory transFactory = TransformerFactory.newInstance();
            Transformer transFormer = transFactory.newTransformer();
            DOMSource domSource = new DOMSource(docEle);
            //export string
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            transFormer.transform(domSource, new StreamResult(bos));
            xmlStr = bos.toString("UTF-8");
        } catch (ParserConfigurationException e) {
            log.error("sm2创建xml异常：{}", e.getMessage(), e);
        }
        return xmlStr;
    }

    public static String c14nT(String str) throws Exception {
        byte[] bytes = str.getBytes("UTF-8");
        TransformService ts = TransformService.getInstance(CanonicalizationMethod.INCLUSIVE_WITH_COMMENTS, "DOM");
        String result;
        ByteArrayInputStream bais = new ByteArrayInputStream(bytes);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        OctetStreamData data = new OctetStreamData(bais);
        OctetStreamData newData = (OctetStreamData) ts.transform(data, null);
        InputStream is = newData.getOctetStream();
        byte[] buf = new byte[1024 * 1024];
        int len = is.read(buf);
        while (len != -1) {
            baos.write(buf, 0, len);
            len = is.read(buf);
        }
        result = baos.toString("UTF-8");
        is.close();
        return result;
    }

    public static void write(String filename, byte[] data) {
        try {
            FileOutputStream out = new FileOutputStream(filename);
            out.write(data);
            out.flush();
            out.close();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public static byte[] read(String filename) {
        byte[] data = null;
        try {
            FileInputStream in = new FileInputStream(filename);
            data = new byte[in.available()];
            in.read(data);
            in.close();
        } catch (Exception ex) {
            System.out.println("读文件失败:" + ex.getMessage());
            return null;
        }
        return data;
    }
}
