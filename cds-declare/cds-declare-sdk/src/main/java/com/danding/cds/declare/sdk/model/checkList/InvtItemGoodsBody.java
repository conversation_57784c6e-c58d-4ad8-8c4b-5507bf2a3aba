package com.danding.cds.declare.sdk.model.checkList;

import lombok.Data;

import java.io.Serializable;

@Data
public class InvtItemGoodsBody implements Serializable {

    // 中心统一编号，同表头，非必填，类型为String，长度18
    private String seqNo;
    // 商品序号，按自然数顺序，不允许断号，必填，类型为Decimal，长度19
    private String gdsSeqNo;
    // 备案序号(对应底账序号），必填，类型为Decimal，长度19
    private String putrecSeqNo;
    // 商品料号，必填，类型为String，长度32
    private String gdsMtno;
    // 商品编码，必填，类型为String，长度10
    private String gdecd;
    // 商品名称，必填，类型为String
    private String gdsNm;
    // 商品规格型号，必填，类型为String，长度255
    private String gdsSpcfModelDesc;
    // 申报计量单位，必填，类型为String，长度3
    private String dclUnitcd;
    // 法定计量单位，非必填，类型为String，长度3
    private String lawfUnitcd;
    // 法定第二计量，非必填，类型为String，长度3
    private String secdLawfUnitcd;
    // 原产国(地区)，必填，类型为String，长度3
    private String natcd;
    // 企业申报单价，必填，类型为Decimal，长度19,4
    private String dclUprcAmt;
    // 企业申报总价，必填，类型为Decimal，长度19,2
    private String dclTotalAmt;
    // 美元统计总金额，必填，类型为Decimal，长度25,5
    private String usdStatTotalAmt;
    // 币制，必填，类型为String，长度3
    private String dclCurrcd;
    // 法定数量，必填，类型为Decimal，长度19,5
    private String lawfQty;
    // 第二法定数量，当法定第二计量单位为空时，该项为非必填，类型为Decimal，长度19,5
    private String secdLawfQty;
    // 重量比例因子，非必填，类型为Decimal，长度19,5
    private String wtSfval;
    // 第一比例因子，非必填，类型为Decimal，长度19,5
    private String fstSfval;
    // 第二比例因子，非必填，类型为Decimal，长度19,5
    private String secdSfval;
    // 申报数量，必填，类型为Decimal，长度19,5
    private String dclQty;
    // 毛重，非必填，类型为Decimal，长度19,5
    private String grossWt;
    // 净重，非必填，类型为Decimal，长度19,5
    private String netWt;
    // 用途代码，取消该字段使用；不需要填写，类型为String，长度4
    private String useCd;
    // 征免方式，必填，类型为String，长度6
    private String lvyrlfModecd;
    // 单耗版本号，账册由开关控制是否必填。需看单耗该字段如何定义，类型为String，长度8
    private String ucnsVerno;
    // 报关单商品序号，企业可录入，如果企业不录入，系统自动返填，类型为Decimal，长度19
    private String entryGdsSeqno;
    // 危险品标志，表头备案号以T、H开头或以L开头且第6位字符为‘B’，并且清单类型不是简单加工、监管方式不是‘AAAA’时，必填；其他情况必须为空；参数代码：1-是，0-否，类型为String，长度1
    private String clyMarkcd;
    // 流转申报表序号，流转类专用。用于建立清单商品与流转申请表商品之间的关系，类型为Decimal，长度19
    private String applyTbSeqno;
    // 最终目的国，必填，类型为String，长度3
    private String destinationNatcd;
    // 修改标志，0-未修改 1-修改 2-删除 3-增加，必填，类型为String，长度1
    private String modfMarkcd;
    // 备注，非必填，类型为String，长度255
    private String rmk;
    // 来源表示
    private String param1;
}
