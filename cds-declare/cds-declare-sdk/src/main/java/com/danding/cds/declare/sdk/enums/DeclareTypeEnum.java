package com.danding.cds.declare.sdk.enums;

/**
 * @program: cds-center
 * @description: 申报类型
 * @author: 潘本乐（Belep）
 * @create: 2021-09-13 15:16
 **/
public enum DeclareTypeEnum {

    CEB311("CEB311", "CEB311Message", "订单申报"),
    CEB511("CEB511", "CEB511Message", "运单申报"),
    CEB621("CEB621", "CEB621Message", "清单申报"),
    CEB623("CEB623", "CEB623Message", "清单撤单"),
    ZD_IMPORT_ORDER("ZD_IMPORT_ORDER", "", "浙江电子口岸订单"),
    ZD_PERSONAL_GOODS_DECLARE("ZD_PERSONAL_GOODS_DECLARE", "", "浙江电子口岸清单"),
    ZD_MODIFY_CANCEL("ZD_MODIFY_CANCEL", "", "浙江电子口岸撤销单");

    /**
     * key
     */
    private String key;

    /**
     * message
     */
    private String message;
    /**
     * 描述
     */
    private String desc;

    DeclareTypeEnum(String key, String message, String desc) {
        this.key = key;
        this.desc = desc;
        this.message = message;
    }

    public String getKey() {
        return key;
    }

    public String getMessage() {
        return message;
    }
}
