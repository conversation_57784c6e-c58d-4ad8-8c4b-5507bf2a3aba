package com.danding.cds.declare.sdk.payment.wechat;

import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.DefaultHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.BasicHttpClientConnectionManager;
import org.apache.http.util.EntityUtils;

import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLContext;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.Serializable;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;
import java.security.KeyStore;
import java.security.SecureRandom;

@Slf4j
public class WxPayRequest implements Serializable {

    /**
     * 请求，只请求一次，不做重试
     * @param url
     * @param mchId
     * @param data
     * @param connectTimeoutMs
     * @param readTimeoutMs
     * @param useCert 是否使用证书，针对退款、撤销等操作
     * @return
     * @throws Exception
     */
    private String requestOnce(final String url, String mchId, String data, int connectTimeoutMs, int readTimeoutMs, boolean useCert, String certPath) throws Exception {
        BasicHttpClientConnectionManager connManager;
        if (useCert) {
            // 证书
            char[] password = mchId.toCharArray();
            File file = new File(certPath);
	        InputStream certStream = new FileInputStream(file);
            KeyStore ks = KeyStore.getInstance("PKCS12");
            ks.load(certStream, password);

            // 实例化密钥库 & 初始化密钥工厂
            KeyManagerFactory kmf = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
            kmf.init(ks, password);

            // 创建 SSLContext
            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(kmf.getKeyManagers(), null, new SecureRandom());

            SSLConnectionSocketFactory sslConnectionSocketFactory = new SSLConnectionSocketFactory(
                    sslContext,
                    new String[]{"TLSv1"},
                    null,
                    new DefaultHostnameVerifier());

            connManager = new BasicHttpClientConnectionManager(
                    RegistryBuilder.<ConnectionSocketFactory>create()
                            .register("http", PlainConnectionSocketFactory.getSocketFactory())
                            .register("https", sslConnectionSocketFactory)
                            .build(),
                    null,
                    null,
                    null
            );
        }
        else {
            connManager = new BasicHttpClientConnectionManager(
                    RegistryBuilder.<ConnectionSocketFactory>create()
                            .register("http", PlainConnectionSocketFactory.getSocketFactory())
                            .register("https", SSLConnectionSocketFactory.getSocketFactory())
                            .build(),
                    null,
                    null,
                    null
            );
        }

        HttpClient httpClient = HttpClientBuilder.create()
                .setConnectionManager(connManager)
                .build();

        HttpPost httpPost = new HttpPost(url);

        RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(readTimeoutMs).setConnectTimeout(connectTimeoutMs).build();
        httpPost.setConfig(requestConfig);

        StringEntity postEntity = new StringEntity(data, "UTF-8");
        httpPost.addHeader("Content-Type", "text/xml");
        httpPost.addHeader("User-Agent", "wxpay sdk java v1.0 " + mchId);  // TODO: 很重要，用来检测 sdk 的使用情况，要不要加上商户信息？
        httpPost.setEntity(postEntity);
        log.debug("[op:wxRequest] url={}, data={}", url, data);
        HttpResponse httpResponse = httpClient.execute(httpPost);
        HttpEntity httpEntity = httpResponse.getEntity();
        
        String result = EntityUtils.toString(httpEntity, "UTF-8");
        log.debug("[op:wxRequest] result={},  url={}, data={}", result, url, data);
        return result;
    }


    private String request(String url, String mchId, String data, int connectTimeoutMs, int readTimeoutMs, boolean useCert, String certPath) throws Exception {
        Exception exception = null;
        try {
            String result = requestOnce(url,mchId, data, connectTimeoutMs, readTimeoutMs, useCert, certPath);
            return result;
        }
        catch (UnknownHostException ex) {  // dns 解析错误，或域名不存在
            exception = ex;
            log.error("UnknownHostException for domainInfo {}", url);
        }
        catch (ConnectTimeoutException ex) {
            exception = ex;
            log.error("connect timeout happened for domainInfo {}", url);
        }
        catch (SocketTimeoutException ex) {
            exception = ex;
            log.error("timeout happened for domainInfo {}", url);
        }
        catch (Exception ex) {
            exception = ex;
        }
        throw exception;
    }


    /**
     * 可重试的，非双向认证的请求
     * @param url
     * @param data
     * @return
     */
    public String requestWithoutCert(String url, String mchId, String data) throws Exception {
        return this.request(url, mchId, data, 6000, 8000, false, "");
    }

    /**
     * 可重试的，非双向认证的请求
     * @param url
     * @param mchId
     * @param data
     * @param connectTimeoutMs
     * @param readTimeoutMs
     * @return
     */
    public String requestWithoutCert(String url, String mchId, String data, int connectTimeoutMs, int readTimeoutMs) throws Exception {
        return this.request(url, mchId, data, connectTimeoutMs, readTimeoutMs, false, "");
    }

    /**
     * 可重试的，双向认证的请求
     * @param url
     * @param mchId
     * @param data
     * @return
     */
    public String requestWithCert(String url, String mchId, String data, String certPath) throws Exception {
        return this.request(url, mchId, data, 6000, 8000, true, certPath);
    }

    /**
     * 可重试的，双向认证的请求
     * @param url
     * @param mchId
     * @param data
     * @param connectTimeoutMs
     * @param readTimeoutMs
     * @return
     */
    public String requestWithCert(String url, String mchId, String data, int connectTimeoutMs, int readTimeoutMs, String certPath) throws Exception {
        return this.request(url, mchId, data, connectTimeoutMs, readTimeoutMs, true, certPath);
    }
	
}
