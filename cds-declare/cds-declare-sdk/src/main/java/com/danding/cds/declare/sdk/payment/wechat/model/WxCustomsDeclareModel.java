package com.danding.cds.declare.sdk.payment.wechat.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

@Data
public class WxCustomsDeclareModel implements Serializable {

    /**
     * 新增：公众账号AppId
     */
    @JSONField(name = "appid")
    private String appId;

    /**
     * 新增：商户号
     */
    @JSONField(name = "mch_id")
    private String mchId;

    /**
     * 商户订单号
     * 共通 选填
     */
    @JSONField(name = "out_trade_no")
    private String outTradeNo;

    /**
     * 微信支付订单号
     * 报关 选填
     */
    @JSONField(name = "transaction_id")
    private String transactionId;

    /**
     * 海关
     * 报关 必填
     * HANGZHOU_ZS 杭州
     * GUANGZHOU_ZS 广州（总署版）
     * TIANJIN 天津
     * GUANGZHOU_NS_GJ 广州南沙国检（需推送订单至南沙国检的订单需分别推送广州（总署版）和广州南沙国检，即需要请求两次报关接口）
     */
    @JSONField(name = "customs")
    private String customs;

    /**
     * 商户海关备案号
     */
    @JSONField(name = "mch_customs_no")
    private String mchCustomsNo;

    /**
     * 关税
     */
    @JSONField(name = "duty")
    private String duty;

    /**
     * 报关类型
     */
    @JSONField(name = "action_type")
    private String actionType;

    /**
     * 子订单号
     * 报关 必填
     */
    @JSONField(name = "sub_order_no")
    private String subOrderNo;

    /**
     * 标价币种
     * 共通 非必填
     */
    @JSONField(name = "fee_type")
    private String feeType = "CNY";

    /**
     * 应付金额
     */
    @JSONField(name = "order_fee")
    private String orderFee;

    /**
     * 物流费用
     */
    @JSONField(name = "transport_fee")
    private String transportFee;

    /**
     * 商品价格
     * 报关 必填
     */
    @JSONField(name = "product_fee")
    private String productFee;

    /**
     * 证件类型
     */
    @JSONField(name = "cert_type")
    private String certType = "IDCARD";

    /**
     * 证件号码
     */
    @JSONField(name = "cert_id")
    private String certId;

    /**
     * 姓名
     */
    @JSONField(name = "name")
    private String name;

}
