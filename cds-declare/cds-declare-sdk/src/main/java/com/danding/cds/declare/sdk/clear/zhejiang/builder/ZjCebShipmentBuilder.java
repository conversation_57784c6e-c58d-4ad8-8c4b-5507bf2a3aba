package com.danding.cds.declare.sdk.clear.zhejiang.builder;

import com.danding.cds.declare.ceb.domain.base.BaseTransfer;
import com.danding.cds.declare.ceb.domain.ceb511.CEB511Message;
import com.danding.cds.declare.ceb.domain.ceb511.Logistics;
import com.danding.cds.declare.ceb.domain.ceb511.LogisticsHead;
import com.danding.cds.declare.ceb.internal.enums.CebDeclareType;
import com.danding.cds.declare.ceb.internal.utils.CebDateUtil;
import com.danding.cds.declare.sdk.model.shipment.WrapShipmentInfo;

import java.io.Serializable;
import java.util.Date;
import java.util.UUID;

public class ZjCebShipmentBuilder implements Serializable {

    private String dxpId;

    private WrapShipmentInfo info;

    public ZjCebShipmentBuilder(WrapShipmentInfo info, String dxpId) {
        this.info = info;
        this.dxpId = dxpId;
    }

    public CEB511Message build() {
        return buildShipmentMessage();
    }

    private CEB511Message buildShipmentMessage(){
        CEB511Message ceb511Message = new CEB511Message();
        LogisticsHead logisticsHead = buildLogisticsHead();
        Logistics logistics = new Logistics();
        logistics.setLogisticsHead(logisticsHead);
        ceb511Message.setLogistics(logistics);
        ceb511Message.setBaseTransfer(buildBaseTransfer());
        ceb511Message.setGuid(logisticsHead.getGuid());
        return ceb511Message;
    }

    private LogisticsHead buildLogisticsHead(){
        LogisticsHead logistics = new LogisticsHead();
        logistics.setGuid(UUID.randomUUID().toString().toUpperCase());
        logistics.setAppType(CebDeclareType.CREATE.getType());
        logistics.setAppTime(CebDateUtil.format(new Date(), CebDateUtil.defaultCebDateStringFormat));
        logistics.setAppStatus("2");

        logistics.setLogisticsCode(info.getDeclareCompanyDTO().getCebCode());
        logistics.setLogisticsName(info.getDeclareCompanyDTO().getCebName());
        logistics.setLogisticsNo(info.getLogisticsNo());
        logistics.setOrderNo(info.getDeclareOrderNo());
        logistics.setFreight(info.getFreight());
        logistics.setInsuredFee("0");
        logistics.setCurrency("142");
        logistics.setWeight(info.getWeight());
        logistics.setPackNo(info.getPackNo());
        logistics.setGoodsInfo(info.getGoodsInfo());
        logistics.setConsignee(info.getConsignee());
        logistics.setConsigneeAddress(info.getConsigneeAddress());
        logistics.setConsigneeTelephone(info.getConsigneeTel());
        //logistics.setNote("");
        return logistics;
    }

    private BaseTransfer buildBaseTransfer() {
        BaseTransfer baseTransfer = new BaseTransfer();
        String declareCompanyCode = info.getDeclareCompanyDTO().getCebCode();
        String declareCompanyName = info.getDeclareCompanyDTO().getCebName();
        baseTransfer.setCopCode(declareCompanyCode);
        baseTransfer.setCopName(declareCompanyName);
        baseTransfer.setDxpMode("DXP");
        baseTransfer.setDxpId(dxpId);
        return baseTransfer;
    }
}
