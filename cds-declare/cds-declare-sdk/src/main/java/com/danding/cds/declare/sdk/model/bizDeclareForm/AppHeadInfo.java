package com.danding.cds.declare.sdk.model.bizDeclareForm;

import lombok.Data;

import java.util.Date;

@Data
public class AppHeadInfo {

    /**
     * 申报表预录入编号，第一次导入为空，导入成功后返回预录入编号；第二次导入填写返回的预录入编号
     */
    private String seqNo;
    /**
     * 申报表编号，备案时为空，变更时填写
     */
    private String sasDclNo;
    /**
     * 主管关区代码，与账册保持一致
     */
    private String masterCusCd;
    /**
     * 申报类型代码，1-备案、2-变更、3-结案。备案、变更时，可导入暂存、申报报文；结案时，只可导入申报报文
     */
    private String dclTypeCd;
    /**
     * 业务类型代码，A-分送集报、B-外发加工、C-保税展示交易、D-设备检测、E-设备维修、F-模具外发、G-简单加工、H-其他业务、Y-一纳企业进出区I-保区折料内销X-选择性征税分送集报
     */
    private String businessTypeCd;
    /**
     * 货物流向代码，I-入区、E-出区
     */
    private String directionTypeCd;
    /**
     * 区内账册编号，分送集报、简单加工、保税展示交易、保税折料内销必填
     */
    private String areaInOriActNo;
    /**
     * 区外账册编号
     */
    private String areaOutOriActNo;
    /**
     * 区内企业编码
     */
    private String areaInEtpsNo;
    /**
     * 区内企业名称
     */
    private String areaInEtpsNm;
    /**
     * 区内企业社会信用代码
     */
    private String areaInEtpsSccd;
    /**
     * 区外企业编码
     */
    private String areaOutEtpsNo;
    /**
     * 区外企业名称
     */
    private String areaOutEtpsNm;
    /**
     * 区外企业社会信用代码
     */
    private String areaOutEtpsSccd;
    /**
     * 保证金征收单编号
     */
    private String dpstLevyBlNo;
    /**
     * 有效期，格式为yyyyMMdd
     */
    private Date validTime;
    /**
     * 申请人，申请人姓名
     */
    private String dclEr;
    /**
     * 展示地
     */
    private String exhibitionPlace;
    /**
     * 申报企业编号，申报企业海关十位编号
     */
    private String dclEtpsNo;
    /**
     * 申报企业名称
     */
    private String dclEtpsNm;
    /**
     * 申报企业社会信用代码
     */
    private String dclEtpsSccd;
    /**
     * 录入单位代码，录入单位海关十位编号
     */
    private String inputCode;
    /**
     * 录入单位社会信用代码
     */
    private String inputSccd;
    /**
     * 录入单位名称
     */
    private String inputName;
    /**
     * 企业内部编号
     */
    private String etpsPreentNo;
    /**
     * 底账料件成品标志，含义：表体数据是账册底账中的料件还是成品。分送集报、简单加工、保税展示交易必填，一纳企业进出区默认为成品，其他业务类型不可填。分送集报、简单加工、保税展示交易：1）物流账册，填写I；2）加工账册，根据实际情况填写
     */
    private String mtpckEndprdTypeCd;
    /**
     * 备注
     */
    private String rmk;
    /**
     * 边角料标志，1-是，默认为空。此字段只对出区分送集报申报表有意义，其他类型默认为空
     */
    private String col1;
    /**
     * 备用字段2
     */
    private String col2;
    /**
     * 备用字段3
     */
    private Date col3;
    /**
     * 备用字段4
     */
    private String col4;
    /**
     * 保税区内销标志，Y-是，默认为空。此字段只对出区分送集报申报表有意义，其他类型默认为空
     */
    private String freeDomestic;
}
