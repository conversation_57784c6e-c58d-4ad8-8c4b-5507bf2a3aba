package com.danding.cds.declare.sdk.enums;

/**
 * 海关申报企业类型
 * 
 * <AUTHOR>
 * @version $Id: CustomsDeclareEnterpriseType.java, v 0.1 2019年3月15日 下午6:15:15 matt Exp $
 */
public enum CustomsDeclareEnterpriseType {

    /** 支付企业 */
    E_PAY("E_PAY", "支付企业"),
    /** 物流企业 */
    E_LOGISTICS("E_LOGISTICS", "物流企业"),
    /** 电商平台 */
    P_ECOMMERCE("P_ECOMMERCE", "电商平台"),
    /** 电商企业 */
    E_ECOMMERCE("E_ECOMMERCE", "电商企业")
    ;

    private String type;
    private String description;

    CustomsDeclareEnterpriseType(String type, String description) {
        this.type = type;
        this.description = description;
    }
    
    public static CustomsDeclareEnterpriseType getByType(String type) {
        for (CustomsDeclareEnterpriseType item : CustomsDeclareEnterpriseType.values()) {
            if (item.getType().equals(type)) {
                return item;
            }
        }
        return null;
    }

    public String getDescription() {
        return description;
    }

    public String getType() {
        return type;
    }
}
