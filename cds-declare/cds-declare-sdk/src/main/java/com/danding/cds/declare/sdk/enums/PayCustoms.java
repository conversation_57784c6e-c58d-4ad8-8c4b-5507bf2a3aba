package com.danding.cds.declare.sdk.enums;

public enum PayCustoms {
    NULL("","空"),
    ZONGSHU("ZONGSHU","总署"),
    HANGZHOU("HANGZHOU","杭州"),
    TIANJIN("TIANJIN","天津"),
    GUANGZHOU("GUANGZHOU","广州"),
    GUANGZHOU_NS_GJ("GUANGZHOU_NS_GJ","广州南沙国检"),
    SHANGHAI("SHANGHAI","上海"),
    CHONGQING("CHONGQING","重庆"),


    JINYI("JINYI","金义"),
    YIWU("YIWU","义乌"),
    HAIKOU("HAIKOU","海口"),
    KUNMING("KUNMING","昆明"),
    GUANGZHOU_NS("GUANGZHOU_NS","广州南沙"),
    GUANGZHOU_HP("GUANGZHOU_HP","广州黄埔"),
    SHAOXING("SHAOXING", "绍兴"),
    DEQING("DEQING", "德清B清")
    ;

    private String code;

    private String desc;

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    PayCustoms(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PayCustoms getEnum(String code){
        for (PayCustoms value : PayCustoms.values()) {
            if (value.code.equals(code)){
                return value;
            }
        }
        return NULL;
    }
}
