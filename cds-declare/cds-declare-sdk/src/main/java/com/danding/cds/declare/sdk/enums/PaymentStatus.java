package com.danding.cds.declare.sdk.enums;

public enum PaymentStatus  {
	NULL(0, "空"),
	UNPAY(1,"未支付"),
	PAID(2, "已支付"),
	CLOSE(3,"已关闭");
	private Integer value;

	private String desc;

	PaymentStatus(Integer value, String desc) {
		this.value = value;
		this.desc = desc;
	}

	public Integer getValue() {
		return value;
	}

	public String getDesc() {
		return desc;
	}

	public static PaymentStatus getEnum(String value) {
		for (PaymentStatus type : PaymentStatus.values()) {
			if (type.value.equals(value)) {
				return type;
			}
		}
		return NULL;
	}

}
