package com.danding.cds.declare.sdk.handler;

import com.danding.cds.declare.sdk.CustomsSupport;
import com.danding.cds.declare.sdk.clear.base.CustomsClient;
import com.danding.cds.declare.sdk.clear.base.CustomsResult;
import com.danding.cds.declare.sdk.clear.base.callback.CallbackResult;
import com.danding.cds.declare.sdk.clear.base.callback.CallbackType;
import com.danding.cds.declare.sdk.clear.base.callback.module.*;
import com.danding.cds.declare.sdk.enums.CustomsType;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.List;

@Slf4j
public class CustomsCallbackHandler implements Serializable {

    public CustomsResult handle(CustomsSupport support, Object callbackResponse, CustomsType customsType) {
        CustomsClient customsClient = support.getClientRegistry().findClient(customsType.getValue());
        try {
            CallbackResult result = customsClient.parserCallback(callbackResponse);
            if (result.getCallbackType().equals(CallbackType.ORDER)) {
                // 仅订单回执尝试使用这段逻辑
                // Step::业务参数解析
                OrderCallback orderCallback = customsClient.handelOrderMsg(result);
            } else if (result.getCallbackType().equals(CallbackType.TAX)) {
                List<TaxResult> taxResultList = customsClient.handelTaxMsg(result);
            } else if (result.getCallbackType().equals(CallbackType.TAXSTATUS)) {
                List<TaxStatus> taxStatusList = customsClient.handelTaxStatusMsg(result);
            } else if (result.getCallbackType().equals(CallbackType.INVENTORY)) {
                InventoryCallback inventoryCallback = customsClient.handelInventoryMsg(result);
            } else if (result.getCallbackType().equals(CallbackType.SHIPMENT)) {
                ShipmentCallback shipmentCallback = customsClient.handleShipmentMsg(result);
            } else if (result.getCallbackType().equals(CallbackType.CANCEL)) {
                customsClient.handelInventoryCancel(result);
            } else if (result.getCallbackType().equals(CallbackType.REFUND)) {
                customsClient.handelInventoryRefund(result);
            } else if (result.getCallbackType().equals(CallbackType.DELIVER)) {
                customsClient.handelDeliverMsg(result);
            }
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
        }
        return CustomsResult.success();
    }
}
