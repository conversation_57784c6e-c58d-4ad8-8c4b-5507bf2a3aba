package com.danding.cds.declare.sdk.enums;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 口岸申报业务状态
 * 
 * <AUTHOR>
 * @version $Id: PortBillBusinessState.java, v 0.1 2019年6月18日 下午4:40:03 matt Exp $
 */
public class PortBillBusinessState extends Enumerable4StringValue {
    
    private static final long serialVersionUID = 2504548743721319723L;
    private static final Logger log = LoggerFactory.getLogger(Enumerable4StringValue.class);
    private static volatile transient Map<String, PortBillBusinessState> allbyvalue = new HashMap<String, PortBillBusinessState>();
    private static volatile transient Map<String, PortBillBusinessState> allbyname = new HashMap<String, PortBillBusinessState>();
    private static final Lock lock = new ReentrantLock();

    public static PortBillBusinessState PORT_BIZ_STATE_NULL = PortBillBusinessState.valueOf(null, "未回执");
    public static PortBillBusinessState PORT_BIZ_STATE_0 = PortBillBusinessState.valueOf("0", "申报失败");
    public static PortBillBusinessState PORT_BIZ_STATE_1 = PortBillBusinessState.valueOf("1", "电子口岸已暂存");
    public static PortBillBusinessState PORT_BIZ_STATE_2 = PortBillBusinessState.valueOf("2", "电子口岸申报中");
    public static PortBillBusinessState PORT_BIZ_STATE_3 = PortBillBusinessState.valueOf("3", "发送海关成功");
    public static PortBillBusinessState PORT_BIZ_STATE_4 = PortBillBusinessState.valueOf("4", "发送海关失败");
    
    public static PortBillBusinessState PORT_BIZ_STATE_100 = PortBillBusinessState.valueOf("100", "海关退单");
    public static PortBillBusinessState PORT_BIZ_STATE_120 = PortBillBusinessState.valueOf("120", "海关入库");
    
    public static PortBillBusinessState PORT_BIZ_STATE_300 = PortBillBusinessState.valueOf("300", "人工审核");
    public static PortBillBusinessState PORT_BIZ_STATE_399 = PortBillBusinessState.valueOf("399", "海关审结");
    
    public static PortBillBusinessState PORT_BIZ_STATE_800 = PortBillBusinessState.valueOf("800", "放行");
    public static PortBillBusinessState PORT_BIZ_STATE_899 = PortBillBusinessState.valueOf("899", "结关");
    
    public static PortBillBusinessState PORT_BIZ_STATE_500 = PortBillBusinessState.valueOf("500", "查验");
    public static PortBillBusinessState PORT_BIZ_STATE_501 = PortBillBusinessState.valueOf("501", "扣留移送通关");
    public static PortBillBusinessState PORT_BIZ_STATE_502 = PortBillBusinessState.valueOf("502", "扣留移送缉私");
    public static PortBillBusinessState PORT_BIZ_STATE_503 = PortBillBusinessState.valueOf("503", "扣留移送法规");
    public static PortBillBusinessState PORT_BIZ_STATE_599 = PortBillBusinessState.valueOf("599", "其它扣留");
    
    public static PortBillBusinessState PORT_BIZ_STATE_700 = PortBillBusinessState.valueOf("700", "退运");

    private PortBillBusinessState(String value, String name) {
        super(value, name);
    }

    public static PortBillBusinessState valueOf(String value, String name) {
        PortBillBusinessState e = allbyvalue.get(value);
        if (e != null) {
            if (e.name.equals(name) || undefined.equals(name))
                //undefined可以更新， 其他的name不可以更新？ No, 所有值都可以更新; 但是不能用undefined覆盖已有值
                return e;
            else {
                //命名不相同
                log.warn("Name to be change. value:" + value + ", old name:" + e.name + ", new name:" + name);
            }
        }

        Map<String, PortBillBusinessState> allbyvalue_new = new HashMap<String, PortBillBusinessState>();
        Map<String, PortBillBusinessState> allbyname_new = new HashMap<String, PortBillBusinessState>();
        e = new PortBillBusinessState(value, name);
        lock.lock();
        try {
            allbyvalue_new.putAll(allbyvalue);
            allbyname_new.putAll(allbyname);
            allbyvalue_new.put(value, e);
            allbyname_new.put(name, e);
            allbyvalue = allbyvalue_new;
            allbyname = allbyname_new;
        } finally {
            lock.unlock();
        }
        return e;
    }


    public static PortBillBusinessState valueOf(String value) {
        PortBillBusinessState e = allbyvalue.get(value);
        if (e != null) {
            return e;
        } else {
            return valueOf(value, undefined);
        }
    }

    public static boolean containValue(String value) {
        PortBillBusinessState e = allbyvalue.get(value);
        if (e != null) {
            return true;
        } else {
            return false;
        }
    }

    public static PortBillBusinessState[] values() {
        return allbyvalue.values().toArray(new PortBillBusinessState[0]);
    }

}
