package com.danding.cds.declare.sdk.model.checkList;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ChecklistResult implements Serializable {
    private static final long serialVersionUID = 3232141572257106534L;
    public static final String STATUS_INIT = "STATUS_INIT";

    public static final String DECLARE_STATUS_PASS = "DECLARE_STATUS_PASS"; // 申报成功
    public static final String DECLARE_STATUS_FAIL = "DECLARE_STATUS_FAIL"; // 申报失败

    public static final String DISCARD_STATUS_PASS = "DISCARD_STATUS_PASS"; // 作废成功
    public static final String DISCARD_STATUS_FAIL = "DISCARD_STATUS_FAIL"; // 作废失败

    public static final String OUT_STATUS_FINISH = "OUT_STATUS_FINISH"; // 已过卡


    /**
     * 业务编号
     */
    private String businessNo;

    /**
     * 预录入编号
     */
    private String preNo;

    /**
     * 事件
     */
    private String action;

    /**
     * 真实核放单号
     */
    private String realOrderNo;
    /**
     * 海关状态
     */
    private String customsStatus;
    /**
     * 错误描述
     */
    private List<String> information;

    /**
     * 回执报文
     */
    private String message;

    public ChecklistResult(String businessNo, String preNo, String action, String realOrderNo, String customsStatus, List<String> information, String message) {
        this.businessNo = businessNo;
        this.preNo = preNo;
        this.action = action;
        this.realOrderNo = realOrderNo;
        this.customsStatus = customsStatus;
        this.information = information;
        this.message = message;
    }
}
