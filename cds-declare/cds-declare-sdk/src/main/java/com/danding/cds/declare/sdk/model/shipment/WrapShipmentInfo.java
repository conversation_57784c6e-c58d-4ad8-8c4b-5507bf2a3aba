package com.danding.cds.declare.sdk.model.shipment;

import com.danding.cds.declare.sdk.model.WrapBeanInfo;
import lombok.Data;

@Data
public class WrapShipmentInfo extends WrapBeanInfo {
    private String businessKey;

    /**
     * 海关
     */
    private String customs;
    /**
     * 口岸编码
     */
    private String portCode;
    /**
     * 快递方式
     */
    private String expressCode;

    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * 运费
     */
    private String freight;

    /**
     * 重量
     */
    private String weight;

    /**
     * 件数
     */
    private String packNo;

    /**
     * 主要商品信息
     */
    private String goodsInfo;

    /**
     * 收件人姓名
     */
    private String consignee;
    /**
     * 省
     */
    private String consigneeProvince;

    /**
     * 市
     */
    private String consigneeCity;

    /**
     * 区
     */
    private String consigneeDistrict;

    /**
     * 街道（四级地址）
     */
    private String consigneeStreet;

    /**
     * 收件人地址
     */
    private String consigneeAddress;

    /**
     * 收件人电话
     */
    private String consigneeTel;
    /**
     * 身份证号
     */
    private String idCardNo;
    /**
     * 证件类型
     */
    private String idCardType;
    /**
     * 应与订单货款一致
     */
    private String worth;
    /**
     * 运单系统编号作为businessNo
     */
    private String sn;

    /**
     * 申报单号
     */
    private String declareOrderNo;

    private Integer status;

    @Override
    public String getDeclareNos() {
        return this.declareOrderNo;
    }
}
