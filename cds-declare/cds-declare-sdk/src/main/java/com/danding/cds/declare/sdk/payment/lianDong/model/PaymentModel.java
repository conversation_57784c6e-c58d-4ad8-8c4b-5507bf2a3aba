package com.danding.cds.declare.sdk.payment.lianDong.model;

import com.danding.cds.declare.sdk.enums.PaymentStatus;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 * @date 2021/11/25
 */
@Data
public class PaymentModel implements Serializable {

    private long id;

    /**
     * 支付编号
     */
    private String sn;

    /**
     * 支付方式
     */
    private Integer payWay;

    /**
     * 支付状态
     */
    private PaymentStatus status;

    /**
     * 流水号
     */
    private String bankSn;

    /**
     * 用户id
     */
    private long userId;

    /**
     * 总价
     */
    private BigDecimal totalPrice;

    /**
     * 额外信息
     */
    private String remark;

    /**
     * 创建时间
     */
    private long createTime;

    /**
     * 支付时间
     */
    private long payTime;

    /**
     * 支付原始请求
     */
    private String payRequest;

    /**
     * 支付原始响应
     */
    private String payResponse;

    /**
     * 收款账号
     */
    private String recpAccount;

    /**
     * 收款企业代码
     */
    private String recpCode;

    /**
     * 收款企业名称
     */
    private String recpName;
}
