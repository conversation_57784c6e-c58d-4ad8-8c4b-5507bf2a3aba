package com.danding.cds.declare.sdk.clear.base.callback.module;

import com.danding.cds.declare.sdk.model.tax.Tax;
import com.danding.cds.declare.sdk.model.tax.TaxItem;
import com.danding.cds.http.saas.annotation.TenantHttpField;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class TaxResult extends Tax {
    private String guid;

    /**
     * 申报单号
     */
//    @TenantHttpField(alias = "declareOrderNo")
    private String orderNo;

    /**
     * 物流单号
     */
    @TenantHttpField(alias = "logisticsNo")
    private String logisticsNo;

    /**
     * 回执时间
     */
    private Date returnTime;

//    /**
//     * 清单号
//     */
//    private String invtNo;

    /**
     * 税单号
     */
    private String taxNo;

    /**
     * 应征关税
     */
    private BigDecimal customsTax;

    /**
     * 应征消费税
     */
    private BigDecimal valueAddedTax;

    /**
     * 应征消费税
     */
    private BigDecimal consumptionTax;

    /**
     * 税单状态（(1-已生成，2-已汇总   3-作废)）
     */
    private String status;

    /**
     * 缴款书编号
     */
    private String entDutyNo;

    /**
     * 备注
     */
    private String note;

    /**
     * 担保企业代码
     */
    private String assureCode;

    /**
     * 电商企业代码
     */
    private String ebcCode;

    /**
     * 物流企业代码
     */
    private String logisticsCode;

    /**
     * 申报单位代码,10位海关代码或者18位信用代码
     */
    private String agentCode;

    /**
     * 申报口岸
     */
    private String customsCode;

    /**
     * 海关回执报文
     */
    private String responseMsg;

    private List<TaxItem> itemList;

    private String sender;
}
