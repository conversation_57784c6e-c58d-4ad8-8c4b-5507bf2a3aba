package com.danding.cds.declare.sdk.bean.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @program: cds-center
 * @description: 第三方物流申报
 * @author: 潘本乐（Belep）
 * @create: 2021-11-22 16:17
 **/
@Data
public class ShipmentDeclareReqVo implements Serializable {
    /**
     * stock_code : DT_KMYMBSC0926
     * owner_code :
     * platform_code : YTO
     * cp_code : YTO
     * receive_info : {"province":"重庆市","city":"重庆城区","area":"江北区","town":"","address_detail":"观音桥/红旗河沟洋河三村63-2-2","receiver_eamil":"","receiver_compay":"","receiver_card_no":"500105198709291223","receiver_card_type":"1","receiver_zip_code":"000000","receiver_city_code":"","receive_phone":"18623540929","receive_name":"廖倩"}
     * send_info : {"province":"云南省","city":"昆明市","area":"经开区","address_detail":"综合保税区","send_name":" E宠商城","send_phone":"4008889200","send_eamil":null,"send_compay":null,"send_zip_code":"","send_city_code":""}
     * trade_order_info : {"waybill_no":"8022090806803","apply_type":"1","ec_reg_cid":"530166052A","ec_reg_name":"昆明云免实业有限公司","ec_gj_cid":"3301964J31","ec_gj_name":"330766K00Q","port_code":"2923","customs_code":"HZCUSTOMSNEW","gross_weight":"0.1","net_weight":"0.1","order_no":"JYCK673039911","total_fees":"298.00","insured_fees":"0","curreny_code":"RMB","order_items":[{"item_name":"NATURAL BALANCE/天衡宝特级健乐鸡肉三文鱼配方全猫粮15磅/袋","item_code":"JHYC723633003155","item_price":"298.00","item_qty":"1","item_weight":"0.1"}],"freight":"0.00"}
     */

    @JsonProperty("stock_code")
    private String stockCode;
    @JsonProperty("owner_code")
    private String ownerCode;
    @JsonProperty("platform_code")
    private String platformCode;
    @JsonProperty("cp_code")
    private String cpCode;
    @JsonProperty("receive_info")
    private ShipmentReceiveInfoReqVo receiveInfo;
    @JsonProperty("send_info")
    private ShipmentSendInfoReqVo sendInfo;
    @JsonProperty("trade_order_info")
    private ShipmentTradeOrderInfoReqVo tradeOrderInfo;
}
