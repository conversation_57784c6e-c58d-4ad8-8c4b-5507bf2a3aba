package com.danding.cds.declare.sdk.utils;

import com.danding.cds.declare.sdk.clear.zhejiang.ZJAgentToken;
import com.danding.cds.declare.sdk.config.CustomsSpecialToken;
import com.danding.cds.declare.sdk.config.SpecialConfig;
import com.danding.cds.declare.sdk.config.SpecialConnectionInfo;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @program: cds-center
 * @description: token util
 * @author: 潘本乐（Belep）
 * @create: 2021-11-17 11:57
 **/
public class TokenUtil {

    private static Map<String, ZJAgentToken> zjportConfigMap = Collections.EMPTY_MAP;
    private static Map<String, CustomsSpecialToken> specialConfigMap = Collections.EMPTY_MAP;
    private static Map<String, SpecialConnectionInfo> specialConnectionConfigMap = Collections.EMPTY_MAP;

    /**
     * 初始化浙江电子口岸及CEB加签配置相关信息
     *
     * @param agentTokenList
     */
    public static void initZjportConfig(List<ZJAgentToken> agentTokenList) {

        if (CollectionUtils.isEmpty(agentTokenList)) {
            return;
        }
        Map<String, ZJAgentToken> agentTokenMap = agentTokenList.stream()
                .collect(Collectors.toMap(ZJAgentToken::getCode, Function.identity()));
        zjportConfigMap = Collections.unmodifiableMap(agentTokenMap);
    }

    /**
     * 初始化特殊监管区域配置
     *
     * @param specialTokenList
     */
    public static void initSpecialConfig(List<CustomsSpecialToken> specialTokenList) {

        if (CollectionUtils.isEmpty(specialTokenList)) {
            return;
        }
        Map<String, CustomsSpecialToken> agentTokenMap = specialTokenList.stream()
                .collect(Collectors.toMap(CustomsSpecialToken::getCustomsCode, Function.identity()));
        specialConfigMap = Collections.unmodifiableMap(agentTokenMap);
    }

    /**
     * 初始化特殊监管区域配置
     *
     * @param config
     */
    public static void initSpecialConfig(SpecialConfig config) {

        // 初始下吧
        List<CustomsSpecialToken> specialTokenList = config.getTokens();
        if (!CollectionUtils.isEmpty(specialTokenList)) {
            Map<String, CustomsSpecialToken> agentTokenMap = specialTokenList.stream()
                    .collect(Collectors.toMap(CustomsSpecialToken::getCustomsCode, Function.identity()));
            specialConfigMap = Collections.unmodifiableMap(agentTokenMap);
        }
    }

    /**
     * 默认特殊监管区域连接地址 - 杭州数据中心地址
     *
     * @return
     */
    public static SpecialConnectionInfo getDefaultSpecialConnection() {
        return new SpecialConnectionInfo("115.236.37.155", "5672", "EPORT_HZ", "password");
    }

    /**
     * 根据海关十位编码获取获取特殊监管区域配置
     *
     * @param cebCode
     * @return
     */
    public static CustomsSpecialToken getSpecialToken(String cebCode) {
        return specialConfigMap.get(cebCode);
    }

    /**
     * 根据海关十位编码获取获取浙江电子口岸配置
     *
     * @param cebCode
     * @return
     */
    public static ZJAgentToken getZjPortToken(String cebCode) {
        return zjportConfigMap.get(cebCode);
    }
}
