package com.danding.cds.declare.sdk.clear.simulate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.cds.common.enums.TrackLogConstantMixAll;
import com.danding.cds.common.model.MapExt;
import com.danding.cds.declare.ceb.domain.ceb624.CEB624Message;
import com.danding.cds.declare.ceb.domain.ceb624.InvtCancelReturn;
import com.danding.cds.declare.ceb.domain.ceb626.CEB626Message;
import com.danding.cds.declare.ceb.domain.ceb626.InvtRefundReturn;
import com.danding.cds.declare.ceb.internal.utils.XMLUtil;
import com.danding.cds.declare.sdk.CustomsReport;
import com.danding.cds.declare.sdk.CustomsSupport;
import com.danding.cds.declare.sdk.clear.base.CustomsClient;
import com.danding.cds.declare.sdk.clear.base.CustomsClientRegistry;
import com.danding.cds.declare.sdk.clear.base.callback.CallbackResult;
import com.danding.cds.declare.sdk.clear.base.callback.CallbackType;
import com.danding.cds.declare.sdk.clear.base.callback.module.*;
import com.danding.cds.declare.sdk.clear.zhejiang.ZJCallbackResponse;
import com.danding.cds.declare.sdk.enums.CustomsType;
import com.danding.cds.declare.sdk.utils.CebMessageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
public class SimulateCustomsClient extends CustomsClient {

    public SimulateCustomsClient(CustomsSupport support, String env) {
        super(support, env);
    }

    public SimulateCustomsClient(CustomsSupport support, CustomsClientRegistry registry, String env) {
        super(support, registry, env);
    }

    @Override
    public CustomsType getSystem() {
        return CustomsType.DT_CCS;
    }

    /**
     * 解析订单回执
     *
     * @param callbackResult
     * @return
     */
    @Override
    public OrderCallback handelOrderMsg(CallbackResult callbackResult) {
        log.info("开始处理【订单申报】总署CEB返回封装报文：{}", JSON.toJSONString(callbackResult));
        return super.handelOrderMsg(callbackResult);
    }

    /**
     * 税单回执
     *
     * @param callbackResult
     * @return
     */
    @Override
    public List<TaxResult> handelTaxMsg(CallbackResult callbackResult) {
        log.info("开始处理【税金】总署CEB返回封装报文：{}", JSON.toJSONString(callbackResult));
        return super.handelTaxMsg(callbackResult);
    }

    @Override
    public List<TaxStatus> handelTaxStatusMsg(CallbackResult callbackResult) {
        log.info("开始处理【税金状态】总署CEB返回封装报文：{}", JSON.toJSONString(callbackResult));
        return super.handelTaxStatusMsg(callbackResult);
    }

    @Override
    public InventoryCallback handelInventoryMsg(CallbackResult callbackResult) {
        log.info("处理清单申报总署CEB返回封装报文：{}", JSON.toJSONString(callbackResult));
        CustomsReport report = new CustomsReport(
                CustomsReport.TYPE_ACCEPT,
                CustomsReport.SYSTEM_CUSTOMS_CLIENT,
                CustomsReport.PROCESS_CLEAR_CALLBACK_INVENTORY);
        JSONObject origMap = JSON.parseObject(callbackResult.getOrigMsg());
        InventoryCallback inventoryCallback = null;
        String origMessage = origMap.getString("content");
        report.setSender(TrackLogConstantMixAll.DT_CCS);
        report.setResponseMsg(origMessage);
        try {
            inventoryCallback = CebMessageUtil.getInventoryCallback(origMessage);
            report.setProcessData(JSON.toJSONString(inventoryCallback));
            support.accept(report);
        } catch (Exception ex) {
            log.error("清单申报总署CEB返回报文处理异常：回执报文：{}，异常：{}", origMessage, ex.getMessage(), ex);
        }
        return inventoryCallback;
    }

    @Override
    public ShipmentCallback handleShipmentMsg(CallbackResult callbackResult) {
        log.info("模拟回执开始处理运单申报返回封装报文：{}", JSON.toJSONString(callbackResult));
        return super.handleShipmentMsg(callbackResult);
    }

    @Override
    public List<InventoryCancel> handelInventoryCancel(CallbackResult callbackResult) {
        log.info("处理清单取消总署CEB返回封装后报文：{}", JSON.toJSONString(callbackResult));
        List<InventoryCancel> cancelList = new ArrayList<>();
        JSONObject origMap = JSON.parseObject(callbackResult.getOrigMsg());
        CustomsReport report = new CustomsReport(
                CustomsReport.TYPE_ACCEPT,
                CustomsReport.SYSTEM_CUSTOMS_CLIENT,
                CustomsReport.PROCESS_CLEAR_CALLBACK_INVENTORY_CANCEL);
        try {
            String requestXmlString = origMap.getString("content");
            report.buildResponse(requestXmlString);
            requestXmlString = requestXmlString.replace("xmlns=\"http://www.chinaport.gov.cn/ceb\"", "");
            CEB624Message response = XMLUtil.converyToJavaBean(requestXmlString, CEB624Message.class);
            for (InvtCancelReturn invtCancelReturn : response.getInvtCancelReturn()) {
                InventoryCancel cancel = new InventoryCancel();
                cancel.setEbpCode(invtCancelReturn.getEbpCode());
                cancel.setInvtNo(invtCancelReturn.getInvtNo());
                cancel.setID(invtCancelReturn.getCopNo());
                Date returnDate = DateUtils.parseDate(invtCancelReturn.getReturnTime(), "yyyyMMddHHmmssSSS");
                cancel.setReturnStatus(invtCancelReturn.getReturnStatus());
                cancel.setReturnTime(returnDate);
                cancel.setReturnInfo(invtCancelReturn.getReturnInfo());
                cancelList.add(cancel);
            }
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
        }
        report.setSender(TrackLogConstantMixAll.DT_CCS);
        report.setProcessData(JSON.toJSONString(cancelList));
        support.accept(report);
        return cancelList;
    }

    @Override
    public List<InventoryRefund> handelInventoryRefund(CallbackResult callbackResult) {
        log.info("处理清单退货总署CEB返回封装后报文：{}", JSON.toJSONString(callbackResult));
        List<InventoryRefund> refundList = new ArrayList<>();
        JSONObject origMap = JSON.parseObject(callbackResult.getOrigMsg());
        CustomsReport report = new CustomsReport(
                CustomsReport.TYPE_ACCEPT,
                CustomsReport.SYSTEM_CUSTOMS_CLIENT,
                CustomsReport.PROCESS_CLEAR_CALLBACK_INVENTORY_REFUND);
        try {
            String requestXmlString = origMap.getString("content");
            report.buildResponse(requestXmlString);
            requestXmlString = requestXmlString.replace("xmlns=\"http://www.chinaport.gov.cn/ceb\"", "");
            CEB626Message response = XMLUtil.converyToJavaBean(requestXmlString, CEB626Message.class);
            for (InvtRefundReturn invtRefundReturn : response.getInvtRefundReturn()) {
                InventoryRefund refund = new InventoryRefund();
                refund.setEbpCode(invtRefundReturn.getEbpCode());
                refund.setID(invtRefundReturn.getCopNo());
                refund.setInvtNo(invtRefundReturn.getInvtNo());
                Date returnDate = DateUtils.parseDate(invtRefundReturn.getReturnTime(), "yyyyMMddHHmmssSSS");
                refund.setReturnTime(returnDate);
                refund.setReturnInfo(invtRefundReturn.getReturnInfo());
                refund.setReturnStatus(invtRefundReturn.getReturnStatus());
                refundList.add(refund);
            }
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
        }
        report.setSender(TrackLogConstantMixAll.DT_CCS);
        report.setProcessData(JSON.toJSONString(refundList));
        support.accept(report);
        return refundList;
    }

    /**
     * 解析回执的HTTP请求 获得回执类型和明文
     *
     * @param request
     * @return
     */
    @Override
    public CallbackResult parserCallback(Object request) {
        ZJCallbackResponse response = (ZJCallbackResponse) request;
        CallbackResult result = new CallbackResult();
        MapExt origMap = new MapExt();
        origMap.setContent(response.getContent());
        origMap.setMsgType(response.getMsg_type());
        result.setOrigMsg(JSON.toJSONString(origMap));
        result.setCallbackType(CebMessageUtil.getCebCallbackType(response.getContent()));
        result.setSender(TrackLogConstantMixAll.DT_CCS);
        if (CallbackType.NULL.equals(result.getCallbackType())) {
            CustomsReport report = new CustomsReport(
                    CustomsReport.TYPE_ACCEPT,
                    CustomsReport.SYSTEM_CUSTOMS_CLIENT,
                    CustomsReport.PROCESS_CLEAR_CALLBACK_UN_KNOW);
            report.setProcessData(JSON.toJSONString(result));
            report.setResponseMsg(JSON.toJSONString(request));
            support.accept(report);
        }
        return result;
    }
}
