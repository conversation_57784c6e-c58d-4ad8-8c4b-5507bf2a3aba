package com.danding.cds.declare.sdk.exception;

public class Api<PERSON><PERSON>urnCode extends AbstractReturnCode {

    public final static int _C_EMPTY = Integer.MIN_VALUE-1;
    public final static AbstractReturnCode EMPTY = new ApiReturnCode("空", _C_EMPTY);

    public final static int _C_NO_ASSIGN = Integer.MIN_VALUE;
    public final static AbstractReturnCode NO_ASSIGN = new ApiReturnCode("未分配返回值", _C_NO_ASSIGN);

    public final static int _C_SUCCESS = 0;
    public final static AbstractReturnCode SUCCESS = new ApiReturnCode("成功", _C_SUCCESS);

    public final static int _C_CQ_ORDER_DECLARE_ERROR = -130;
    public final static AbstractReturnCode CQ_ORDER_DECLARE_ERROR = new ApiReturnCode("重庆口岸申报失败", _C_CQ_ORDER_DECLARE_ERROR);

    public final static int _C_CQ_ORDER_CALLBACK_ERROR = -135;
    public final static AbstractReturnCode CQ_ORDER_CALLBACK_ERROR = new ApiReturnCode("重庆口岸回执失败", _C_CQ_ORDER_CALLBACK_ERROR);


    public final static int _C_CEB_INVENTORY_ORDER_CALLBACK_ERROR = -140;
    public final static AbstractReturnCode CEB_INVENTORY_ORDER_CALLBACK_ERROR = new ApiReturnCode("总署清单退货单回执失败", _C_CEB_INVENTORY_ORDER_CALLBACK_ERROR);

    public final static int _C_CEB_INVENTORY_CANCEL_CALLBACK_ERROR = -141;
    public final static AbstractReturnCode CEB_INVENTORY_CANCEL_CALLBACK_ERROR = new ApiReturnCode("总署清单撤单回执失败", _C_CEB_INVENTORY_CANCEL_CALLBACK_ERROR);


    public final static int _C_INVENTORY_ORDER_CALLBACK_ERROR = -145;
    public final static AbstractReturnCode INVENTORY_ORDER_CALLBACK_ERROR = new ApiReturnCode("口岸退货单回执失败", _C_INVENTORY_ORDER_CALLBACK_ERROR);

    public final static int _C_CEB_REFUND_ORDER_CALLBACK_ERROR = -150;
    public final static AbstractReturnCode CEB_REFUND_ORDER_CALLBACK_ERROR = new ApiReturnCode("口岸退货单回执失败", _C_CEB_REFUND_ORDER_CALLBACK_ERROR);

    public final static int _C_CEB_INVENTORY_ORDER_DECLARE_ERROR = -160;
    public final static AbstractReturnCode CEB_INVENTORY_ORDER_DECLARE_ERROR = new ApiReturnCode("总署清单申报失败", _C_CEB_INVENTORY_ORDER_DECLARE_ERROR);

    public final static int _C_CEB_INVENTORY_ORDER_CANCEL_DECLARE_ERROR = -161;
    public final static AbstractReturnCode CEB_INVENTORY_ORDER_CANCEL_DECLARE_ERROR = new ApiReturnCode("总署清单撤单申报失败", _C_CEB_INVENTORY_ORDER_CANCEL_DECLARE_ERROR);


    public final static int _C_INVENTORY_ORDER_DECLARE_ERROR = -165;
    public final static AbstractReturnCode INVENTORY_ORDER_DECLARE_ERROR = new ApiReturnCode("口岸清单申报失败", _C_INVENTORY_ORDER_DECLARE_ERROR);


    public final static int _C_INVENTORY_ORDER_DECLARE_CODE_ERROR = -166;
    public final static AbstractReturnCode INVENTORY_ORDER_DECLARE_CODE_ERROR = new ApiReturnCode("口岸清单申报代码未配置", _C_INVENTORY_ORDER_DECLARE_CODE_ERROR);


    public final static int _C_CEB_REFUND_ORDER_DECLARE_ERROR = -170;
    public final static AbstractReturnCode CEB_REFUND_ORDER_DECLARE_ERROR = new ApiReturnCode("总署退货单申报失败", _C_CEB_REFUND_ORDER_DECLARE_ERROR);



    public final static int _C_SIGNATURE_ERROR = -180;
    public final static AbstractReturnCode SIGNATURE_ERROR = new ApiReturnCode("签名错误", _C_SIGNATURE_ERROR);
    /*
        增加异常错误
     */
    public final static int _C_JCE_LOAD_ERROR = -185;
    public final static AbstractReturnCode JCE_LOAD_ERROR = new ApiReturnCode("加密机加载失败", _C_JCE_LOAD_ERROR);
    /*
        增加异常错误
     */
    public final static int _C_ENCRYPTION_ERROR = -190;
    public final static AbstractReturnCode ENCRYPTION_ERROR = new ApiReturnCode("加解密异常", _C_ENCRYPTION_ERROR);

    public final static int _C_ENCRYPTION_KEY_ERROR = -195;
    public final static AbstractReturnCode ENCRYPTION_KEY_ERROR = new ApiReturnCode("密钥获取为空", _C_ENCRYPTION_KEY_ERROR);


    public final static int _C_RESPONSE_PARSE_ERROR = -200;
    public final static AbstractReturnCode RESPONSE_PARSE_ERROR = new ApiReturnCode("response xml解析错误", _C_RESPONSE_PARSE_ERROR);

    public final static int _C_REQUEST_WEBSERVICE_ERROR = -400;
    public final static AbstractReturnCode REQUEST_WEBSERVICE_ERROR = new ApiReturnCode("请求WebService解析错误", _C_REQUEST_WEBSERVICE_ERROR);

    //记账回执异常
    public final static int _C_INV211_CALLBACK_ERROR = -211;
    public final static AbstractReturnCode INV211_CALLBACK_ERROR = new ApiReturnCode("电子口岸记账回执异常", _C_INV211_CALLBACK_ERROR);

    //记账回执异常
    public final static int _C_BWS201_CALLBACK_ERROR = -201;
    public final static AbstractReturnCode BWS201_CALLBACK_ERROR = new ApiReturnCode("电子口岸物流账册回执异常", _C_BWS201_CALLBACK_ERROR);

    //核注清单回执异常
    public final static int _C_INV201_CALLBACK_ERROR = -211;
    public final static AbstractReturnCode INV201_CALLBACK_ERROR = new ApiReturnCode("电子口岸核注清单回执异常", _C_INV201_CALLBACK_ERROR);

    //核注清单自动生成报关单同一编号报文回执异常
    public final static int _C_INV202_CALLBACK_ERROR = -202;
    public final static AbstractReturnCode INV202_CALLBACK_ERROR = new ApiReturnCode("核注清单自动生成报关单同一编号报文回执异常", _C_INV202_CALLBACK_ERROR);

    //核注清单自动生成报关单同一编号报文回执异常
    public final static int _C_SAS201_CALLBACK_ERROR = -221;
    public final static AbstractReturnCode SAS201_CALLBACK_ERROR = new ApiReturnCode("业务申报表海关回执异常", _C_SAS201_CALLBACK_ERROR);

    //核注清单自动生成报关单同一编号报文回执异常
    public final static int _C_SAS202_CALLBACK_ERROR = -222;
    public final static AbstractReturnCode SAS202_CALLBACK_ERROR = new ApiReturnCode("业务申报表暂停恢复回执异常", _C_SAS202_CALLBACK_ERROR);

    public final static int _C_SAS203_CALLBACK_ERROR = -223;
    public final static AbstractReturnCode SAS203_CALLBACK_ERROR = new ApiReturnCode("业务申报表数据同步回执回执异常", _C_SAS203_CALLBACK_ERROR);

    //开放给新增错误码使用
    public ApiReturnCode(String desc, int code) {
        super(desc, code);
    }

    protected ApiReturnCode(int code, AbstractReturnCode display) {
        super(code, display);
    }

}
