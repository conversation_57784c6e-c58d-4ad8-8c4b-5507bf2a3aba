package com.danding.cds.declare.sdk.model.man;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 非保税核放单暂存
 */
@Data
public class MAN10004Request implements Serializable {
    private ManSign manSign;

    private ManFbChecklistHead head;

    private List<ManFbChecklistItem> list;

    @Data
    public static class ManFbChecklistHead implements Serializable {

        /**
         * 企业核放流水号
         */
        private String seqNo;

        /**
         * 账册编号
         */
        private String manualId;

        /**
         * 经营单位编码
         */
        private String tradeCode;

        /**
         * 收货单位编码
         */
        private String ownerCode;

        /**
         * 货源单位编码
         */
        private String supplyCode;

        /**
         * 核放单类型
         */
        private String type;

        /**
         * 关联报关单号
         */
        private String entryId;

        /**
         * 包装类型
         */
        private String wrapType;

        /**
         * 毛重（kg）
         */
        private BigDecimal grossWt;

        /**
         * 净重（kg）
         */
        private BigDecimal netWt;

        /**
         * 件数
         */
        private Integer packNo;

        /**
         * 集装箱号
         */
        private String containerNos;

        /**
         * 集装箱数量
         */
        private Integer containerNum;

        /**
         * 集装箱自然数量
         */
        private Integer naturalContainerNum;

        /**
         * 车牌号
         */
        private String carCode;

        /**
         * 标记唛码及备注
         */
        private String remark;

        /**
         * 关联保税核放单号
         */
        private String assCheckId;
    }

    @Data
    public static class ManFbChecklistItem implements Serializable {

        /**
         * 单价
         */
        private BigDecimal price;

        /**
         * 料号
         */
        private String sourceNo;

        /**
         * 数量
         */
        private BigDecimal declareQuantity;
    }
}
