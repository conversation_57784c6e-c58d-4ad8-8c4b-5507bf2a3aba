package com.danding.cds.declare.sdk.payment.tonglian;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.crypto.digest.MD5;
import com.alibaba.fastjson.JSONObject;
import com.danding.cds.declare.ceb.internal.utils.XMLUtil;
import com.danding.cds.declare.sdk.CustomsReport;
import com.danding.cds.declare.sdk.CustomsSupport;
import com.danding.cds.declare.sdk.enums.PayCustomsChannel;
import com.danding.cds.declare.sdk.model.company.CompanyInfo;
import com.danding.cds.declare.sdk.model.payment.CustomsPayDeclareResult;
import com.danding.cds.declare.sdk.model.payment.WrapPaymentInfo;
import com.danding.cds.declare.sdk.payment.base.BaseChannel;
import com.danding.cds.declare.sdk.payment.tonglian.model.*;
import com.danding.component.http.SaasHttpConstant;
import com.danding.core.tenant.SimpleTenantHelper;
import com.github.kevinsawicki.http.HttpRequest;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


@Slf4j
public class TongLianPayChannel extends BaseChannel<TongLianToken> {


    private TongLianConfig tongLianConfig;

    public TongLianPayChannel(CustomsSupport support) {
        super(support);
        tongLianConfig = new TongLianConfig();
    }

    @Override
    public PayCustomsChannel getPayChannel() {
        return PayCustomsChannel.TONGLIAN_PAY;
    }

    @Override
    public TongLianToken makeToken(String tokenJson) {

        return null;
    }

    @Override
    public CustomsPayDeclareResult customDeclare(WrapPaymentInfo info, String tokenJson) {
        log.info("[op:tonglianpush] 通联申报-{}-{}", JSONObject.toJSONString(info), tokenJson);
        try {
            log.info("[op:tonglianpush] 通联申报-延迟推动1秒");
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        CustomsPayDeclareResult payDeclareResult = new CustomsPayDeclareResult();
        CustomsReport customsReport = new CustomsReport(CustomsReport.TYPE_SEND, CustomsReport.SYSTEM_PAY_CHANNEL, CustomsReport.PROCESS_CLEAR_DECLARE_PAYMENT);
        if (info.getAction().equalsIgnoreCase("add")) { // 申报
            try {
                payDeclareResult = this.declare(info);
                payDeclareResult.setCustoms(info.getCustoms());
                payDeclareResult.setOutRequestNo(info.getOutRequestNo());
                payDeclareResult.setPayTransactionId(info.getTradePayNo());
                payDeclareResult.setSubBankNo(info.getTradePayNo());
            } catch (Exception e) {
                log.error("申报单号: {} ,通联支付单申报调用失败 ,error = {}", info.getDeclareNos(), e.getMessage(), e);
            }
        } else if (info.getAction().equalsIgnoreCase("edit")) { // 重推
            try {
                String tradePayNo = info.getTradePayNo();
                if (CollUtil.isNotEmpty(info.getRelDeclareOrderNoList())) {
                    // 支付流水子单
                    tradePayNo = info.getTradePayNo() +
                            Strings.padStart("" + (info.getRelDeclareOrderNoList().indexOf(info.getDeclareOrderNo()) + 1), 2, '0');
                }
                TongLianCustomsQueryResult.Body queryResult = this.customsQuery(info.getMerchantCustomsCode(), tradePayNo, info.getDeclareOrderNo());
                if (Objects.nonNull(queryResult) && Objects.equals(queryResult.getReturnCode(), "0000")) { // 查询结果成功(通联第三方已经存在记录) 走重推
                    payDeclareResult = this.reDeclare(info.getOrderNo(), info.getMerchantCustomsCode(), tradePayNo);
                } else { // 采用直接申报
                    payDeclareResult = this.declare(info);
                }
                payDeclareResult.setCustoms(info.getCustoms());
                payDeclareResult.setOutRequestNo(info.getOutRequestNo());
                payDeclareResult.setPayTransactionId(info.getTradePayNo());
                payDeclareResult.setSubBankNo(info.getTradePayNo());
            } catch (Exception e) {
                log.error("申报单号: {} ,通联支付单重推调用失败 ,error = {}", info.getDeclareNos(), e.getMessage(), e);
            }
        }
        customsReport.buildProcessData(payDeclareResult);
        // 走异步回执处理
        support.accept(customsReport);
        return payDeclareResult;
    }

    private CustomsPayDeclareResult declare(WrapPaymentInfo info) throws Exception {
        CustomsPayDeclareResult payDeclareResult = new CustomsPayDeclareResult();
        TongLianCustomsRequest request = this.wrapDeclareRequest(info);
        String requestXml = XMLUtil.convertToXml(request);
        log.info("[op:TongLianPayChannel] 申报单号: {} , 通联支付 请求信息 ={}", info.getDeclareNos(), requestXml);
        String result = sendHttpRequest(requestXml, tongLianConfig.tongLianCustomsDeclareUrl);
        log.info("[op:TongLianPayChannel] 申报单号: {} , 通联支付 返回信息 ={}", info.getDeclareNos(), result);
        TongLianCustomsResult tongLianCustomsResult = XMLUtil.converyToJavaBean(result, TongLianCustomsResult.class);
        TongLianCustomsResult.Body resultBody = tongLianCustomsResult.getBody();
        if (resultBody.getReturnCode().equals("0000")) {
            payDeclareResult.setSuccess(true);
            payDeclareResult.setPostMsg("申报成功");
        } else {
            payDeclareResult.setSuccess(false);
            payDeclareResult.setErrorMsg(resultBody.getReturnMsg());
            log.error("申报单号: {} ,通联支付单申报失败,body:{}", info.getDeclareNos(), result);
        }
        payDeclareResult.setVerDept(resultBody.getVerDept());
        return payDeclareResult;
    }

    public CustomsPayDeclareResult reDeclare(String orderNo, String merchantCode, String tradePayNo) throws Exception {
        CustomsPayDeclareResult payDeclareResult = new CustomsPayDeclareResult();
        TongLianCustomsRedeclareReq request = new TongLianCustomsRedeclareReq();
        String dateStr = DateUtil.format(new Date(), "yyyyMMddHHmmss");
        TongLianCustomsRedeclareReq.Body body = new TongLianCustomsRedeclareReq.Body();
        body.setVisitorId("MCT");
        body.setCustomsCode("HG021"); // 杭州电子口岸（总署版）
        body.setPaymentMchtId(merchantCode);
        body.setPaymentOrderNo(tradePayNo);
        body.setMchtOrderNo(orderNo);
        body.setBizType(""); // 广州口岸必填 0-海关报关
        request.setBody(body);

        TongLianCustomsReDeclareXmlHead head = new TongLianCustomsReDeclareXmlHead();
        head.setVersion("v5.3");
        head.setTransDatetime(dateStr);
        head.setCharset("1");
        head.setSignType("1");
        head.setSignMsg(getSign(XMLUtil.convertToXml(request.getBody())));
        request.setHead(head);
        String requestXml = XMLUtil.convertToXml(request);
        log.info("[op:TongLianPayChannel] 申报单号: {} , 通联支付重推 请求信息 ={}", orderNo, requestXml);
        String httpResult = sendHttpRequest(requestXml, tongLianConfig.tongLianCustomsReDeclareUrl);
        log.info("[op:TongLianPayChannel] 申报单号: {} , 通联支付重推 返回信息 ={}", orderNo, httpResult);
        TongLianCustomsRedeclareRes tongLianCustomsResult = XMLUtil.converyToJavaBean(httpResult, TongLianCustomsRedeclareRes.class);
        TongLianCustomsRedeclareRes.Body resultBody = tongLianCustomsResult.getBody();
        if (resultBody.getReturnCode().equals("0000")) {
            payDeclareResult.setSuccess(true);
            payDeclareResult.setPostMsg("重推成功");
        } else {
            payDeclareResult.setSuccess(false);
            payDeclareResult.setErrorMsg(resultBody.getReturnMsg());
            log.error("申报单号: {} ,通联支付单申报失败,body:{}", orderNo, httpResult);
        }
        return payDeclareResult;
    }


    public TongLianCustomsQueryResult.Body customsQuery(String customsMerchantCode, String tradePayNo, String declareOrderNo) {
        try {
            TongLianCustomsQueryRequest request = new TongLianCustomsQueryRequest();
            TongLianCustomsQueryRequest.Body body = new TongLianCustomsQueryRequest.Body();
            body.setVisitorId("MCT");
            body.setCustomsCode("HG021");
            body.setPaymentMchtId(customsMerchantCode);
            body.setPaymentOrderNo(tradePayNo);
            body.setMchtOrderNo(declareOrderNo);

            TongLianCustomsQueryRequest.Head head = new TongLianCustomsQueryRequest.Head();
            String dateStr = DateUtil.format(new Date(), "yyyyMMddHHmmss");
            head.setVersion("v5.6");
            head.setTransDatetime(dateStr);
            head.setCharset("1");
            head.setSignType("1");
            head.setSignMsg(getSign(XMLUtil.convertToXml(body)));
            request.setHead(head);
            request.setBody(body);

            String requestXml = XMLUtil.convertToXml(request);
            log.info("[op:TongLianPayChannel] 申报单号: {} , 通联支付查询 请求信息 ={}", declareOrderNo, requestXml);
            String result = sendHttpRequest(requestXml, tongLianConfig.tongLianCustomsReDeclareUrl);
            log.info("[op:TongLianPayChannel] 申报单号: {} , 通联支付查询 返回信息 ={}", declareOrderNo, result);
            TongLianCustomsQueryResult tongLianCustomsResult = XMLUtil.converyToJavaBean(result, TongLianCustomsQueryResult.class);
            return tongLianCustomsResult.getBody();
        } catch (Exception e) {
            log.error("申报单号: {} ,通联支付单查询调用失败, error = {}", declareOrderNo, e.getMessage(), e);
        }
        return null;
    }

    private TongLianCustomsRequest wrapDeclareRequest(WrapPaymentInfo info) throws Exception {
        TongLianCustomsRequest request = new TongLianCustomsRequest();
        String dateStr = DateUtil.format(new Date(), "yyyyMMddHHmmss");
        // build body
        TongLianCustomsRequest.Body body = new TongLianCustomsRequest.Body();
        body.setCustomsCode("HG021"); // 杭州电子口岸（总署版）
        body.setPaymentChannel("2");
        body.setCusId(info.getRecpAccount());
        body.setPaymentDatetime(dateStr);
        body.setMchtOrderNo(info.getDeclareOrderNo());
        body.setPaymentOrderNo(info.getTradePayNo());
        body.setPaymentAmount(Objects.nonNull(info.getAmount()) ? info.getAmount().multiply(new BigDecimal(100)).longValue() : 0L);
        body.setCurrency("156"); // 固定人民币 156
        CompanyInfo ebpCompanyDTO = info.getEbpCompanyDTO();
        body.setEshopEntCode(ebpCompanyDTO.getCode());
        body.setEshopEntName(ebpCompanyDTO.getName());
        body.setPayerName(info.getBuyerName());
        body.setPaperType("01"); // 01 : 身份证
        body.setPaperNumber(info.getBuyerIdNo());
        body.setPaperPhone(info.getBuyerPhoneNumber());
        body.setMemo("");
        body.setMainPaymentOrderNo("");
        if (CollUtil.isNotEmpty(info.getRelDeclareOrderNoList())) {
            // 支付流水子单
            String subTradePayNo = info.getTradePayNo() +
                    Strings.padStart("" + (info.getRelDeclareOrderNoList().indexOf(info.getDeclareOrderNo()) + 1), 2, '0');
            body.setPaymentOrderNo(subTradePayNo);
            body.setMainPaymentOrderNo(info.getTradePayNo());
        }
        body.setGoodsFee(Objects.nonNull(info.getCommodityFee()) ? info.getCommodityFee().multiply(new BigDecimal(100)).longValue() : 0L);
        body.setTaxFee(Objects.nonNull(info.getTaxFee()) ? info.getTaxFee().multiply(new BigDecimal(100)).longValue() : 0L);
        body.setFreightFee(Objects.nonNull(info.getTransportFee()) ? info.getTransportFee().multiply(new BigDecimal(100)).longValue() : 0L);
        body.setBizTypeCode("");// 广东关区必填
        body.setOrgCode("");// 广东关区必填
        body.setIsCheck("");// 广东关区必填
        body.setCustomsAreaCode("");// 广东关区必填
        TongLianCustomsXmlHead head = new TongLianCustomsXmlHead();
        // build head
        head.setVersion("v5.6");
        head.setVisitorId("MCT");
        head.setMchtId(info.getMerchantCustomsCode());
        head.setOrderNo(info.getOutRequestNo());
        head.setTransDatetime(dateStr);
        head.setCharset("1");
        head.setSignType("1");
        head.setSignMsg(getSign(XMLUtil.convertToXml(body)));
        request.setHead(head);
        request.setBody(body);
        return request;
    }

    private static String getSign(String xmlMessage) {
        String bodyMessage = extractBodyContent(xmlMessage);
        log.info("bodyMessage={}", bodyMessage);
        if (!StringUtils.isEmpty(bodyMessage)) {
            bodyMessage = "<BODY>" + bodyMessage + "</BODY>" + "<key>1234567890</key>";
            log.info("bodyMessage={}", bodyMessage);
        } else {
            throw new RuntimeException("获取报文body内容失败");
        }
        String sign = MD5.create().digestHex(bodyMessage.getBytes()).toUpperCase();
        log.info("sign={}", sign);
        return sign;
    }

    /**
     * 截取body报文
     *
     * @param xml
     * @return
     */
    public static String extractBodyContent(String xml) {
        String pattern = "<BODY>(.*?)</BODY>";
        Pattern r = Pattern.compile(pattern, Pattern.DOTALL);
        Matcher m = r.matcher(xml);
        if (m.find()) {
            return m.group(1);
        } else {
            return null; // If <BODY> tag is not found in the XML
        }
    }


    private String sendHttpRequest(String requestXml, String url) {
        //base64处理报文
        String encode = Base64.encode(requestXml.getBytes(StandardCharsets.UTF_8));
        Map<String, String> bodyMap = new HashMap<>();
        bodyMap.put("data", encode);
        HttpRequest httpRequest = HttpRequest.post(url)
                .connectTimeout(15000).readTimeout(30000)
                .header(SaasHttpConstant.HEADER_TENANT_ID, SimpleTenantHelper.getTenantIdStr())
                .form(bodyMap);
        String requestBody = httpRequest.body();
        return Base64.decodeStr(requestBody);
    }
}
