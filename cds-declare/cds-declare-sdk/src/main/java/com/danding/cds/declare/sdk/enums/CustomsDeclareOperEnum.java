package com.danding.cds.declare.sdk.enums;

/**
 * @Auther: Dante-Gxj
 * @Date: 2020/5/12 14:42
 * @Description:
 */
public enum CustomsDeclareOperEnum {
    NULL("","空"),
    CUSTOMS_179_CHECK("data-check","海关179数据抽查"),
    REFUND_ORDER_APPLY("refund","退货单申报"),
    REFUND_ORDER_CANCEL("refund-cancel","取消退货单申报"),
    PAY_ORDER_APPLY("payment","支付单申报"),
    LIST_ORDER_APPLY("inventory","清单申报"),
    LIST_ORDER_CANCEL_APPLY("inventory-cancel","清单撤单申报"),
    YUN_ORDER_APPLY("shipment","运单申报"),
    ORDER_APPLY("order","订单申报"),
    CHECKLIST("checklist","核放单申报"),
    ENDORSEMENT("endorsement","核注清单申报"),
    ;


    private String key;

    private String desc;

    CustomsDeclareOperEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public static CustomsDeclareOperEnum getEnum(String key){
        for (CustomsDeclareOperEnum value : CustomsDeclareOperEnum.values()) {
            if (value.getDesc().equals(key)){
                return value;
            }
        }
        return NULL;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
