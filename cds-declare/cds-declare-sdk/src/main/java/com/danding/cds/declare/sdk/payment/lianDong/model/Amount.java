package com.danding.cds.declare.sdk.payment.lianDong.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.umf.api.payments.ExchangeRate;
import com.umf.base.util.StringUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2021/12/7
 */
public class Amount implements Serializable {
    private String total;
    private String currency;
    @JSONField(name = "total_cny")
    private String totalCny;
    @JSONField(name = "exchange_rate")
    private ExchangeRate exchangeRate;

    public Amount() {
    }

    public void setTotal(String total, String currency, String currencyUnitYuan) {
        if (StringUtils.isNotEmpty(currencyUnitYuan) && currencyUnitYuan.contains(currency) && StringUtils.isNotEmpty(total) && total.indexOf(".") != -1) {
            total = total.substring(0, total.indexOf("."));
        }

        this.total = total;
    }

    public String getTotal() {
        return this.total;
    }

    public void setTotal(String total) {
        this.total = total;
    }

    public String getCurrency() {
        return this.currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getTotalCny() {
        return this.totalCny;
    }

    public void setTotalCny(String totalCny) {
        this.totalCny = totalCny;
    }

    public ExchangeRate getExchangeRate() {
        return this.exchangeRate;
    }

    public void setExchangeRate(ExchangeRate exchangeRate) {
        this.exchangeRate = exchangeRate;
    }
}
