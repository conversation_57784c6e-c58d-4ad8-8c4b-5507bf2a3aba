package com.danding.cds.declare.sdk.model.checklistAuth;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ChecklistAuthResult implements Serializable {

    public static final String STATUS_EXCEPTION = "EXCEPTION";
    public static final String STATUS_SUCCESS = "SUCCESS";

    /**
     * 业务编号
     */
    private String businessNo;

    /**
     * 事件
     */
    private String action;

    /**
     * 海关状态
     */
    private String customsStatus;

    /**
     * 错误信息描述
     */
    private List<String> information;

    /**
     * 回执报文
     */
    private String message;


    public ChecklistAuthResult(String businessNo, String action, String customsStatus, List<String> information, String message) {
        this.businessNo = businessNo;
        this.action = action;
        this.customsStatus = customsStatus;
        this.information = information;
        this.message = message;
    }
}
