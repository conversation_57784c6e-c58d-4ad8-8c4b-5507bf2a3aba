package com.danding.cds.declare.sdk.ie;

import com.danding.cds.declare.sdk.config.CustomsSpecialToken;
import com.danding.cds.declare.sdk.model.checkList.CustomsChecklistOrderInfo;
import com.danding.cds.declare.sdk.model.checkList.Icp101MessageRequest;
import com.danding.cds.declare.zjspecial.domain.KeyInfo;
import com.danding.cds.declare.zjspecial.domain.SignedInfo;
import com.danding.cds.declare.zjspecial.domain.base.EnvelopInfo;
import com.danding.cds.declare.zjspecial.domain.icp101.*;
import com.danding.cds.declare.zjspecial.domain.icp101.Package;
import com.danding.cds.declare.zjspecial.internal.enums.CustomsMessageType;
import com.danding.cds.declare.zjspecial.internal.utils.SpecialUtil;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.util.*;

/**
 * <AUTHOR>
 * @Create 2021/4/26  14:32
 * @Describe
 **/
public class CheckListTwoStepBuilder implements Serializable {

    private CustomsSpecialToken target;

    public Signature build(Icp101MessageRequest req, CustomsSpecialToken specialToken) {
        this.target = specialToken;
        Signature signature = new Signature();
        signature.setSignedInfo(buildSignedInfo());
        signature.setSignatureValue("");
        signature.setKeyInfo(buildKeyInfo());
        signature.setObject(buildObject(req));
        return signature;
    }

    private KeyInfo buildKeyInfo() {
        KeyInfo keyInfo = new KeyInfo();
        keyInfo.setKeyName("aa");
        return keyInfo;
    }

    private SignedInfo buildSignedInfo() {
        SignedInfo signedInfo = new SignedInfo();
        return signedInfo;
    }

    private Signature.Object buildObject(Icp101MessageRequest req) {
        Signature.Object object = new Signature.Object();
        object.setPackage(buildPackage(req));
        return object;
    }

    private Package buildPackage(Icp101MessageRequest req) {
        Package _package = new Package();
        _package.setEnvelopInfo(buildEnvelopInfo(req));
        _package.setDataInfo(buildDataInfo(req));
        return _package;
    }

    private DataInfo buildDataInfo(Icp101MessageRequest req) {
        DataInfo dataInfo = new DataInfo();
        dataInfo.setBussinessData(buildBussinessData(req));
        return dataInfo;
    }

    private EnvelopInfo buildEnvelopInfo(Icp101MessageRequest req) {
        EnvelopInfo envelopInfo = new EnvelopInfo();
        envelopInfo.setBusinessId(req.getCustomsChecklistOrderInfo().getChecklistOrderNo());
        envelopInfo.setFileName(req.getCustomsChecklistOrderInfo().getChecklistOrderNo());
        //ic卡号
        envelopInfo.setIcCard(target.getIcCard());
        envelopInfo.setMessageId(UUID.randomUUID().toString());
        envelopInfo.setMessageType(CustomsMessageType.ICP101.name());
        envelopInfo.setReceiverId("DXPEDCSAS0000001");
        envelopInfo.setSenderId(target.getSenderId());
        envelopInfo.setSendTime(SpecialUtil.dateToXmlDate(new Date()));
        envelopInfo.setVersion("1.0");
        return envelopInfo;
    }

    private DataInfo.BussinessData buildBussinessData(Icp101MessageRequest req) {
        DataInfo.BussinessData bussinessData = new DataInfo.BussinessData();
        bussinessData.setDelcareFlag(req.getDelcareFlag());
        bussinessData.setSas2sPassPortMessage(buildSas2sPassPortMessage(req));
        return bussinessData;
    }

    private Sas2sPassPortMessage buildSas2sPassPortMessage(Icp101MessageRequest req) {
        Sas2sPassPortMessage sas2sPassPortMessage = new Sas2sPassPortMessage();
        sas2sPassPortMessage.setSas2sPassportHead(buildSas2sPassportHead(req));
        sas2sPassPortMessage.setSas2sPassportRlt(buildSas2sPassPortList(req));
        sas2sPassPortMessage.setSysId("Z7");
        if ("L2923B21A004".equals(target.getCustomsBookCode())
                || Objects.equals("L2992B22A001", target.getCustomsBookCode())
                || Objects.equals("L2973B24A002", target.getCustomsBookCode())
        ) {
            sas2sPassPortMessage.setSysId("Z8");
        }
        sas2sPassPortMessage.setOperCusRegCode(target.getCustomsCode());
        return sas2sPassPortMessage;
    }


    private Sas2sPassportHead buildSas2sPassportHead(Icp101MessageRequest req) {
        CustomsChecklistOrderInfo checklistOrderInfo = req.getCustomsChecklistOrderInfo();
        Sas2sPassportHead sas2sPassportHead = new Sas2sPassportHead();
        //主管关区
        sas2sPassportHead.setMasterCuscd("2924");
        //义乌优诚
        if ("L2923B21A004".equals(target.getCustomsBookCode())) {
            sas2sPassportHead.setMasterCuscd("2923");
        }
        // 这里获取下配置的海关关区，如果不为空，则直接使用
        String customsAreaCode = req.getCustomsAreaCode();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(customsAreaCode)) {
            sas2sPassportHead.setMasterCuscd(customsAreaCode);
        }
        if (Objects.nonNull(checklistOrderInfo.getRemark())) {
            sas2sPassportHead.setRmk(checklistOrderInfo.getRemark());
        }
        sas2sPassportHead.setDclTypecd("1");
        //区内企业编码
        sas2sPassportHead.setAreainEtpsNo(target.getCustomsCode());
        //区内企业名称
        sas2sPassportHead.setAreainEtpsNm(target.getCustomsName());
        //区内企业社会信用代码
        sas2sPassportHead.setAreainEtpsSccd(target.getSocialCreditCode());
        //承运车车牌号
        sas2sPassportHead.setVehicleNo(checklistOrderInfo.getLicensePlate());
        //申请人及联系方式
        sas2sPassportHead.setDclErConc(checklistOrderInfo.getApplicant());
        //IC卡
        if (!Objects.isNull(checklistOrderInfo.getVehicleIcNo())) {
            sas2sPassportHead.setVehicleIcNo(checklistOrderInfo.getVehicleIcNo());
        }
        //录入企业代码
        sas2sPassportHead.setInputCode(target.getCustomsCode());
        //录入企业名称
        sas2sPassportHead.setInputName(target.getCustomsName());
        //企业内部编号
        sas2sPassportHead.setEtpsPreentNo(req.getCustomsChecklistOrderInfo().getChecklistOrderNo());
//        if (1 == req.getDelcareFlag()) {
        sas2sPassportHead.setDclEtpsNo(target.getCustomsCode());
        sas2sPassportHead.setDclEtpsNm(target.getCustomsName());
//        }
        //车自重
        sas2sPassportHead.setVehicleWt(checklistOrderInfo.getVehicleWeight());
        //车架重
        sas2sPassportHead.setVehicleFrameNo(checklistOrderInfo.getVehicleFrameNo());
        sas2sPassportHead.setVehicleFrameWt(checklistOrderInfo.getVehicleFrameWeight());
        sas2sPassportHead.setTotalWt(String.valueOf(checklistOrderInfo.getTotalWt()));
        sas2sPassportHead.setTotalGrossWt(String.valueOf(checklistOrderInfo.getTotalGrossWt()));
        sas2sPassportHead.setContainerType(checklistOrderInfo.getContainerType());
        if (Objects.nonNull(checklistOrderInfo.getContainerWt())) {
            sas2sPassportHead.setContainerWt(String.valueOf(checklistOrderInfo.getContainerWt()));
        }
        return sas2sPassportHead;
    }


    private List<Sas2sPassportRlt> buildSas2sPassPortList(Icp101MessageRequest req) {
        List<Sas2sPassportRlt> sas2sPassportRlts = new ArrayList<>();
        if (!StringUtils.isEmpty(req.getCustomsChecklistOrderInfo().getDeclareOrderNo())) {
            sas2sPassportRlts.add(new Sas2sPassportRlt(req.getCustomsChecklistOrderInfo().getDeclareOrderNo()));
        }
        return sas2sPassportRlts;
    }

}
