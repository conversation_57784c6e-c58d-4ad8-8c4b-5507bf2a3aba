package com.danding.cds.declare.sdk.exception;

import java.io.Serializable;

/**
 * Created by sunji on 2014/7/24.
 * Add private String service.
 */
public abstract class AbstractReturnCode implements Serializable {

    private       String name;
    private final String desc;
    private final int    code;

    private       String             service;//以后看做domain
    private final AbstractReturnCode display;

    /**
     * 初始化一个对外暴露的ReturnCodeSuper(用于客户端异常处理)
     */
    public AbstractReturnCode(String desc, int code) {
        this.desc = desc;
        this.code = code;
        this.display = this;
    }

    /**
     * 初始化一个不对外暴露的ReturnCodeSuper(仅用于服务端数据分析)
     */
    public AbstractReturnCode(int code, AbstractReturnCode shadow) {
        this.desc = null;
        this.code = code;
        this.display = shadow;
    }

    public String getDesc() {
        return desc;
    }
    public int getCode() {
        return code;
    }
    public AbstractReturnCode getDisplay() {
        return display;
    }
    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }
    public String getService() {
        return service;
    }

    public String getDomain() {
        return service;
    }

    public void setService(String service) {
        this.service = service;
    }

    /**
     * 返回错误id,唯一标示错误码
     * @return
     */
    public String getCodeId() {
        //小于等于零的都是通用错误码
        return genCodeId(service,code);
    }

    public static String genCodeId(String domain, int code) {
        if (code <= 0 || domain == null || domain.length() == 0) {
            return "_." + code;
        }
        return domain + "." + code;
    }
}
