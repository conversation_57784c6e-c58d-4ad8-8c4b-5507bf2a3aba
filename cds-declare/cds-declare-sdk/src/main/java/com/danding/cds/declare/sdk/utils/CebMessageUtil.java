package com.danding.cds.declare.sdk.utils;

import com.danding.cds.declare.ceb.domain.ceb311.CEB311Message;
import com.danding.cds.declare.ceb.domain.ceb312.CEB312Message;
import com.danding.cds.declare.ceb.domain.ceb511.CEB511Message;
import com.danding.cds.declare.ceb.domain.ceb512.CEB512Message;
import com.danding.cds.declare.ceb.domain.ceb621.CEB621Message;
import com.danding.cds.declare.ceb.domain.ceb622.CEB622Message;
import com.danding.cds.declare.ceb.domain.ceb623.CEB623Message;
import com.danding.cds.declare.ceb.domain.ceb625.CEB625Message;
import com.danding.cds.declare.ceb.domain.dxpmsg.DxpMsg;
import com.danding.cds.declare.ceb.domain.dxpmsg.PddDxpMsg;
import com.danding.cds.declare.ceb.domain.dxpmsg.ReceiverIds;
import com.danding.cds.declare.ceb.domain.dxpmsg.TransInfo;
import com.danding.cds.declare.ceb.internal.enums.CebMessageEnum;
import com.danding.cds.declare.ceb.internal.utils.XMLUtil;
import com.danding.cds.declare.sdk.bean.dto.CustomsMsgDto;
import com.danding.cds.declare.sdk.clear.base.callback.CallbackType;
import com.danding.cds.declare.sdk.clear.base.callback.module.InventoryCallback;
import com.danding.cds.declare.sdk.clear.base.callback.module.OrderCallback;
import com.danding.cds.declare.sdk.clear.base.callback.module.ShipmentCallback;
import com.danding.cds.declare.sdk.clear.zhejiang.ZJAgentToken;
import com.danding.cds.declare.sdk.clear.zhejiang.builder.*;
import com.danding.cds.declare.sdk.clear.zhejiang.encry.CebSignInfo;
import com.danding.cds.declare.sdk.config.CustomsSpecialToken;
import com.danding.cds.declare.sdk.model.WrapBeanInfo;
import com.danding.cds.declare.sdk.model.cancel.WarpCancelOrderInfo;
import com.danding.cds.declare.sdk.model.inventory.WrapInventoryOrderInfo;
import com.danding.cds.declare.sdk.model.order.WrapOrderDeclareInfo;
import com.danding.cds.declare.sdk.model.refund.WarpRefundOrderInfo;
import com.danding.cds.declare.sdk.model.shipment.WrapShipmentInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * @program: cds-center
 * @description: ceb-util
 * @author: 潘本乐（Belep）
 * @create: 2021-09-25 15:32
 **/
@Slf4j
public class CebMessageUtil {

    /**
     * 根据海关十位编码获取获取特殊监管区域配置
     *
     * @param cebCode
     * @return
     */
    public static CustomsSpecialToken getCustomsSpecialToken(String cebCode) {
        return TokenUtil.getSpecialToken(cebCode);
    }

    /**
     * 创建海关报文信息DXP-MESAGE
     *
     * @param declareInfo 申报信息
     * @return ceb和dxp信息
     */
    public static CustomsMsgDto buildCustomsMessage(WrapBeanInfo declareInfo) {
        // 海关十位编码
//        String cebCode = declareInfo.getDeclareCompanyCebCode();
//        Tuple<CebMessageEnum, Object> cebData = buildCebMsg(declareInfo);
//        return buildCustomsMsg(cebCode, cebData.getS(), cebData.getF());
        return buildCustomsMessage(declareInfo, declareInfo.getDxpId(), false);
    }

    /**
     * 创建海关报文信息DXP-MESAGE，构建DXP信息时用传入的DXP
     *
     * @param declareInfo 申报信息
     * @param dxpId       传输ID
     * @param setGuid     是否获取ceb报文中的guid
     * @return ceb和dxp信息
     */
    public static CustomsMsgDto buildCustomsMessage(WrapBeanInfo declareInfo, String dxpId, boolean setGuid) {
        // 海关十位编码
        String cebCode = declareInfo.getDeclareCompanyCebCode();
        Tuple<CebMessageEnum, Object> cebData = buildCebMsg(declareInfo, dxpId);
        return buildCustomsMsg(cebCode, cebData.getS(), cebData.getF(), declareInfo.getDxpId(), setGuid);
    }

    /**
     * 创建EWTP海关报文信息DXP-MESAGE，构建DXP信息时用传入的DXP
     *
     * @param declareInfo 申报信息
     * @param dxpId       传输ID
     * @return ceb和dxp信息
     */
    public static CustomsMsgDto buildEwtpCustomsMessage(WrapBeanInfo declareInfo, String dxpId) {
        // 1. 获取数据
        CustomsMsgDto customsMsgDto = buildCustomsMessage(declareInfo, dxpId, false);
        if (customsMsgDto == null) {
            return customsMsgDto;
        }
        String dxpMsg = customsMsgDto.getDxpMsg();
        if (StringUtils.isEmpty(dxpMsg)) {
            return customsMsgDto;
        }
        // 2. 这里需要替换下dxpId  <SenderId>xxxx</SenderId> ---> <SenderId>dxpId</SenderId>
        dxpMsg = XMLUtil.replaceDxpXmlSendId(dxpMsg, dxpId);
        // 3. 转换下DxpMsg，统一前缀dxp:
        String prefix = "dxp:";
        dxpMsg = XMLUtil.xmlElementAddPrefix(dxpMsg, prefix);
        customsMsgDto.setDxpMsg(dxpMsg);
        return customsMsgDto;
    }


    public static CustomsMsgDto buildPddCustomsMessage(WrapBeanInfo declareInfo, String dxpId) {
        // 海关十位编码
        String cebCode = declareInfo.getDeclareCompanyCebCode();
        Tuple<CebMessageEnum, Object> cebData = buildCebMsg(declareInfo, dxpId);
        return buildPddCustomsMsg(cebCode, cebData.getS(), cebData.getF(), declareInfo.getDxpId());
    }

    public static CustomsMsgDto buildPddCustomsMessage(WrapBeanInfo declareInfo) {
        return buildPddCustomsMessage(declareInfo, null);
    }

    /**
     * 创建DXP-MESAGE
     *
     * @param declareInfo 申报信息
     * @return
     */
    public static String buildDxpMsg(WrapBeanInfo declareInfo) {
        // 海关十位编码
        String cebCode = declareInfo.getDeclareCompanyCebCode();
        Tuple<CebMessageEnum, Object> cebData = buildCebMsg(declareInfo);
        CustomsMsgDto customsMsgDto = buildCustomsMsg(cebCode, cebData.getS(), cebData.getF(), declareInfo.getDxpId(), false);
        return customsMsgDto.getDxpMsg();
    }

    public static String buildDxpMsg(byte[] bs, String dxpId, String msgType) throws Exception {
        DxpMsg dxpMsg = new DxpMsg();
        dxpMsg.setVer("1.0");
        dxpMsg.setData(Base64.getEncoder().encodeToString(bs));
        TransInfo transInfo = new TransInfo();
        transInfo.setCopMsgId(UUID.randomUUID().toString());
        ReceiverIds receiverIds = new ReceiverIds();
        receiverIds.setReceiverId("DXPEDCCEB0000002");
        transInfo.setReceiverIds(Collections.singletonList(receiverIds));
        transInfo.setCreatTime(new Date());
        transInfo.setMsgType(msgType);
        transInfo.setSenderId(dxpId);
        dxpMsg.setTransInfo(transInfo);
        return XMLUtil.convertToXml(dxpMsg);
    }

    /**
     * 获取CEB-XML报文，没有签名信息
     *
     * @param declareInfo 申报信息
     * @return
     */
    public static CustomsMsgDto buildCustomsMsgNoSign(WrapBeanInfo declareInfo) {

        Tuple<CebMessageEnum, Object> cebMsgTuple = buildCebMsg(declareInfo);
        if (cebMsgTuple == null) {
            return null;
        }
        try {
            String xmlCebMsg = XMLUtil.convertToXml(cebMsgTuple.getS());
            log.info("{},申报单号: {} ,总署CEB原始业务报文: {}", cebMsgTuple.getF().getDesc(), declareInfo.getDeclareNos(), xmlCebMsg);
            CustomsMsgDto customsMsgDto = new CustomsMsgDto();
            customsMsgDto.setCebMsgEnum(cebMsgTuple.getF());
            customsMsgDto.setCebMsg(xmlCebMsg);
            return customsMsgDto;
        } catch (Exception e) {
            throw new RuntimeException("CEB报文转换XML数据为空", e);
        }
    }

    public static CustomsMsgDto buildCustomsMsgNoSign(WrapBeanInfo declareInfo, String dxpId) {

        Tuple<CebMessageEnum, Object> cebMsgTuple = buildCebMsg(declareInfo, dxpId);
        if (cebMsgTuple == null) {
            return null;
        }
        try {
            String xmlCebMsg = XMLUtil.convertToXml(cebMsgTuple.getS());
            log.info("{},申报单号: {} ,总署CEB原始业务报文: {}", cebMsgTuple.getF().getDesc(), declareInfo.getDeclareNos(), xmlCebMsg);
            CustomsMsgDto customsMsgDto = new CustomsMsgDto();
            customsMsgDto.setCebMsgEnum(cebMsgTuple.getF());
            customsMsgDto.setCebMsg(xmlCebMsg);
            return customsMsgDto;
        } catch (Exception e) {
            throw new RuntimeException("CEB报文转换XML数据为空", e);
        }
    }

    /**
     * 获取CEB-XML报文
     *
     * @param declareInfo 申报信息
     * @param dxpId
     * @return
     */
    public static String getCebXmlMsg(WrapBeanInfo declareInfo, String dxpId) {

        Tuple<CebMessageEnum, Object> cebMsgTuple = buildCebMsg(declareInfo, dxpId);
        if (cebMsgTuple == null) {
            return null;
        }
        try {
            String xmlCebMsg = XMLUtil.convertToXml(cebMsgTuple.getS());
            log.info("{},申报单号: {} ,总署CEB原始业务报文: {}", cebMsgTuple.getF().getDesc(), declareInfo.getDeclareNos(), xmlCebMsg);
            return xmlCebMsg;
        } catch (Exception e) {
            throw new RuntimeException("CEB报文转换XML数据为空", e);
        }
    }


    /**
     * 构建ceb-message
     *
     * @param declareInfo 申报信息
     * @return
     */
    private static Tuple<CebMessageEnum, Object> buildCebMsg(WrapBeanInfo declareInfo) {
        return buildCebMsg(declareInfo, null);
    }

    /**
     * 构建ceb-message
     *
     * @param inputDxpId  传输ID
     * @param declareInfo 申报信息
     * @return
     */
    public static Tuple<CebMessageEnum, Object> buildCebMsg(WrapBeanInfo declareInfo, String inputDxpId) {

        if (declareInfo == null) {
            return null;
        }
        String cebCode = declareInfo.getDeclareCompanyCebCode();
        String dxpId;
        if (StringUtils.hasText(inputDxpId)) {
            dxpId = inputDxpId;
        } else {
            ZJAgentToken agentToken = TokenUtil.getZjPortToken(cebCode);
            dxpId = agentToken.getCebSignInfo().getDxpId();
        }
        if (StringUtils.hasText(dxpId)) {
            declareInfo.setDxpId(dxpId);
        }
        Object cebMsg = null;
        CebMessageEnum cebMessageEnum = null;
        if (declareInfo instanceof WrapInventoryOrderInfo) {
            cebMessageEnum = CebMessageEnum.CEB_INVENTORY;
            cebMsg = new ZJCebInventoryBuilder((WrapInventoryOrderInfo) declareInfo, dxpId).build();
        } else if (declareInfo instanceof WrapOrderDeclareInfo) {
            cebMessageEnum = CebMessageEnum.CEB_ORDER;
            cebMsg = new ZJCebOrderBuilder((WrapOrderDeclareInfo) declareInfo, dxpId).build();
        } else if (declareInfo instanceof WrapShipmentInfo) {
            cebMessageEnum = CebMessageEnum.CEB_SHIPMENT;
            cebMsg = new ZjCebShipmentBuilder((WrapShipmentInfo) declareInfo, dxpId).build();
        } else if (declareInfo instanceof WarpRefundOrderInfo) {
            cebMessageEnum = CebMessageEnum.CEB_INVENTORY_REFUND;
            cebMsg = new ZJCebInventoryRefundBuilder((WarpRefundOrderInfo) declareInfo, dxpId).build();
        } else if (declareInfo instanceof WarpCancelOrderInfo) {
            cebMessageEnum = CebMessageEnum.CEB_INVENTORY_CANCEL;
            cebMsg = new ZJCebInventoryCancelBuilder((WarpCancelOrderInfo) declareInfo, dxpId).build();
        }
        return new Tuple(cebMessageEnum, cebMsg);
    }

    /**
     * 获取ZJAgentToken
     *
     * @param cebCode
     * @return
     */
    public static ZJAgentToken getZJAgentToken(String cebCode) {
        return TokenUtil.getZjPortToken(cebCode);
    }

    public static CustomsMsgDto buildCustomsMsg(String cebCode, String xml, CebMessageEnum cebMessageEnum, String dxpId) {
        ZJAgentToken token = TokenUtil.getZjPortToken(cebCode);
        List<CebSignInfo> cebSignInfoList = token.getCebSignInfoList();
        CebSignInfo cebSignInfo = cebSignInfoList.stream().filter(c -> Objects.equals(c.getDxpId(), dxpId)).findAny().orElse(null);
        if (Objects.isNull(cebSignInfo)) {
            cebSignInfo = token.getCebSignInfo();
            log.info("buildCustomsMsg dxp:{} 未匹配到申报信息 改用cebCode匹配到的dxp:{}", dxpId, cebSignInfo.getDxpId());
        }
        if (Objects.isNull(cebSignInfo)) {
            throw new RuntimeException("根据配置的DXP:" + dxpId + "未查询到申报信息");
        }
        try {
            String signStr = CebSignUtil.signXml(cebSignInfo, xml);
            String dxpMsg = buildCustomsMsg(signStr.getBytes(), dxpId, cebMessageEnum.getTag());
            log.info("总署CEB报文 {} ，DXP业务报文{}", cebMessageEnum.getDesc(), dxpMsg);
            return new CustomsMsgDto(cebMessageEnum, xml, signStr, dxpMsg);
        } catch (Exception e) {
            String _exceptionMsg = e.getMessage();
            log.error("总署CEB报文 加密异常：exception={}", _exceptionMsg, e);
            String message = String.format("总署CEB报文加密异常:%s", _exceptionMsg);
            throw new RuntimeException(message, e);
        }
    }

    /**
     * 通过ceb报文，转换DXP报文
     *
     * @param cebCode        海关十位编码
     * @param cebMessage     ceb组装信息
     * @param cebMessageEnum ceb枚举
     * @return
     */
    private static CustomsMsgDto buildCustomsMsg(String cebCode, Object cebMessage, CebMessageEnum cebMessageEnum, String dxpId, boolean setGuid) {

        ZJAgentToken token = TokenUtil.getZjPortToken(cebCode);
        List<CebSignInfo> cebSignInfoList = token.getCebSignInfoList();
        CebSignInfo cebSignInfo = cebSignInfoList.stream().filter(c -> Objects.equals(c.getDxpId(), dxpId)).findAny().orElse(null);
        if (Objects.isNull(cebSignInfo)) {
            cebSignInfo = token.getCebSignInfo();
            log.info("buildCustomsMsg dxp:{} 未匹配到申报信息 改用cebCode匹配到的dxp:{}", dxpId, cebSignInfo.getDxpId());
        }
        if (Objects.isNull(cebSignInfo)) {
            throw new RuntimeException("根据配置的DXP:" + dxpId + "未查询到申报信息");
        }
        String xmlFormat;
        String guid = null;
        try {
            xmlFormat = XMLUtil.convertToXml(cebMessage);
            if (xmlFormat == null) {
                throw new RuntimeException("CEB报文转换XML数据为空");
            }
            if (setGuid) {
                guid = getGuid(cebMessage);
            }
            log.info("总署CEB报文 {} ，原始业务报文{}", cebMessageEnum.getDesc(), xmlFormat);
        } catch (Exception e) {
            log.error("总署CEB报文 {} XML报文组装异常, cause={}", cebMessageEnum.getDesc(), e.getMessage(), e);
            throw new RuntimeException("CEB报文转换XML异常", e);
        }
        try {
            String signStr = CebSignUtil.signXml(cebSignInfo, xmlFormat);
            String dxpMsg = buildCustomsMsg(signStr.getBytes(), dxpId, cebMessageEnum.getTag());
            log.info("总署CEB报文 {} ，DXP业务报文{}", cebMessageEnum.getDesc(), dxpMsg);
            return new CustomsMsgDto(cebMessageEnum, xmlFormat, signStr, dxpMsg, guid);
        } catch (Exception e) {
            String _exceptionMsg = e.getMessage();
            log.error("总署CEB报文 加密异常：exception={}", _exceptionMsg, e);
            String message = String.format("总署CEB报文加密异常:%s", _exceptionMsg);
            throw new RuntimeException(message, e);
        }
    }

    private static String getGuid(Object cebMessage) {
        if (cebMessage instanceof CEB311Message) {
            // 订单
            CEB311Message message = (CEB311Message) cebMessage;
            return message.getGuid();
        } else if (cebMessage instanceof CEB621Message) {
            // 清单
            CEB621Message message = (CEB621Message) cebMessage;
            return message.getGuid();
        } else if (cebMessage instanceof CEB511Message) {
            // 运单
            CEB511Message message = (CEB511Message) cebMessage;
            return message.getGuid();
        } else if (cebMessage instanceof CEB625Message) {
            // 退货
            CEB625Message message = (CEB625Message) cebMessage;
            return message.getGuid();
        } else if (cebMessage instanceof CEB623Message) {
            // 撤单
            CEB623Message message = (CEB623Message) cebMessage;
            return message.getGuid();
        }
        return null;
    }

    private static CustomsMsgDto buildPddCustomsMsg(String cebCode, Object cebMessage, CebMessageEnum cebMessageEnum, String dxpId) {

        ZJAgentToken token = TokenUtil.getZjPortToken(cebCode);
        List<CebSignInfo> cebSignInfoList = token.getCebSignInfoList();
        CebSignInfo cebSignInfo = cebSignInfoList.stream().filter(c -> Objects.equals(c.getDxpId(), dxpId)).findAny().orElse(null);
        if (Objects.isNull(cebSignInfo)) {
            cebSignInfo = token.getCebSignInfo();
            log.info("buildCustomsMsg dxp:{} 未匹配到申报信息 改用cebCode匹配到的dxp:{}", dxpId, cebSignInfo.getDxpId());
        }
        if (Objects.isNull(cebSignInfo)) {
            throw new RuntimeException("根据配置的DXP:" + dxpId + "未查询到申报信息");
        }
        String xmlFormat;
        try {
            xmlFormat = XMLUtil.convertToXml(cebMessage);
            if (xmlFormat == null) {
                throw new RuntimeException("CEB报文转换XML数据为空");
            }
            log.info("总署CEB报文 {} ，原始业务报文{}", cebMessageEnum.getDesc(), xmlFormat);
        } catch (Exception e) {
            log.error("总署CEB报文 {} XML报文组装异常, cause={}", cebMessageEnum.getDesc(), e.getMessage(), e);
            throw new RuntimeException("CEB报文转换XML异常", e);
        }
        try {
            String signStr = CebSignUtil.signXml(cebSignInfo, xmlFormat);
            String dxpMsg = buildPddCustomsMsg(signStr.getBytes(), dxpId, cebMessageEnum.getTag());
            log.info("总署CEB报文 {} ，DXP业务报文{}", cebMessageEnum.getDesc(), dxpMsg);
            return new CustomsMsgDto(cebMessageEnum, xmlFormat, signStr, dxpMsg);
        } catch (Exception e) {
            log.error("总署CEB报文 加密异常：exception={}", e.getMessage(), e);
            throw new RuntimeException("总署报文加密异常", e);
        }
    }

    /**
     * 组装DXP报文
     *
     * @param bs      ceb报文字节数据
     * @param dxpId   传输ID
     * @param msgType 类型，如：CEB311Message
     * @return
     * @throws Exception
     */
    public static String buildCustomsMsg(byte[] bs, String dxpId, String msgType) throws Exception {
        DxpMsg dxpMsg = new DxpMsg();
        dxpMsg.setData(Base64.getEncoder().encodeToString(bs));
        dxpMsg.setVer("1.0");
        dxpMsg.setXmlnsDxp("http://www.chinaport.gov.cn/dxp");
        dxpMsg.setXmlnsXsi("http://www.w3.org/2001/XMLSchema-instance");
        dxpMsg.setXmlnsDs("http://www.w3.org/2000/09/xmldsig#");
        TransInfo transInfo = new TransInfo();
        transInfo.setCopMsgId(UUID.randomUUID().toString());
        ReceiverIds receiverIds = new ReceiverIds();
        receiverIds.setReceiverId("DXPEDCCEB0000002");
        transInfo.setReceiverIds(Collections.singletonList(receiverIds));
        transInfo.setCreatTime(new Date());
        transInfo.setMsgType(msgType);
        transInfo.setSenderId(dxpId);
        dxpMsg.setTransInfo(transInfo);
        return XMLUtil.convertToXml(dxpMsg);
    }

    public static String buildPddCustomsMsg(byte[] bs, String dxpId, String msgType) throws Exception {
        PddDxpMsg dxpMsg = new PddDxpMsg();
        dxpMsg.setData(Base64.getEncoder().encodeToString(bs));
        dxpMsg.setVer("1.0");
        dxpMsg.setDxp("http://www.chinaport.gov.cn/dxp");
        TransInfo transInfo = new TransInfo();
        transInfo.setCopMsgId(UUID.randomUUID().toString());
        ReceiverIds receiverIds = new ReceiverIds();
        receiverIds.setReceiverId("DXPEDCCEB0000002");
        transInfo.setReceiverIds(Collections.singletonList(receiverIds));
        transInfo.setCreatTime(new Date());
        transInfo.setMsgType(msgType);
        transInfo.setSenderId(dxpId);
        dxpMsg.setTransInfo(transInfo);
        return XMLUtil.convertToXml(dxpMsg);
    }

    /**
     * 获取回执类型
     */
    public static CallbackType getCebCallbackType(String content) {

        // Module::总署回执
        // Type::清单回执
        if (content.contains("CEB622Message")) {
            return CallbackType.INVENTORY;
        }
        // Type::运单回执
        if (content.contains("CEB512Message")) {
            return CallbackType.SHIPMENT;
        }
        // Type::订单回执
        if (content.contains("CEB312Message")) {
            return CallbackType.ORDER;
        }
        // Type::退货回执
        if (content.contains("CEB626Message")) {
            return CallbackType.REFUND;
        }
        // Type::撤单回执
        if (content.contains("CEB624Message")) {
            return CallbackType.CANCEL;
        }
        // Type::撤单回执
        if (content.contains("CEB816Message")) {
            return CallbackType.TAX;
        }
        if (content.contains("CEB818Message")) {
            return CallbackType.TAXSTATUS;
        }
        return CallbackType.NULL;
    }

    /**
     * 获取清单返回封装结果
     *
     * @param cebMsgCallback
     * @return
     */
    public static InventoryCallback getInventoryCallback(String cebMsgCallback) throws Exception {
        InventoryCallback inventoryCallback = new InventoryCallback();
        String requestXmlString = cebMsgCallback.replace("xmlns=\"http://www.chinaport.gov.cn/ceb\"", "");
        CEB622Message response = XMLUtil.converyToJavaBean(requestXmlString, CEB622Message.class);
        inventoryCallback.setEbpCode(response.getInventoryReturn().getEbpCode());
        inventoryCallback.setSn(response.getInventoryReturn().getCopNo());
        Date returnDate = DateUtils.parseDate(response.getInventoryReturn().getReturnTime(), "yyyyMMddHHmmssSSS");
        inventoryCallback.setReturnTime(returnDate);
        inventoryCallback.setAgentCode(response.getInventoryReturn().getAgentCode());
        inventoryCallback.setInvtNo(response.getInventoryReturn().getInvtNo());
        inventoryCallback.setPreNo(response.getInventoryReturn().getPreNo());
        inventoryCallback.setReturnStatus(response.getInventoryReturn().getReturnStatus());
        inventoryCallback.setReturnInfo(response.getInventoryReturn().getReturnInfo());
        return inventoryCallback;
    }

    /**
     * 获取运单返回封装结果
     *
     * @param cebMsgCallback
     * @return
     */
    public static ShipmentCallback getShipmentCallback(String cebMsgCallback) throws Exception {
        ShipmentCallback shipmentCallback = new ShipmentCallback();
        String requestXmlString = cebMsgCallback.replace("xmlns=\"http://www.chinaport.gov.cn/ceb\"", "");
        CEB512Message response = XMLUtil.converyToJavaBean(requestXmlString, CEB512Message.class);
        shipmentCallback.setLogisticsCode(response.getLogisticsReturn().getLogisticsCode());
        shipmentCallback.setLogisticsNo(response.getLogisticsReturn().getLogisticsNo());
        Date returnDate = DateUtils.parseDate(response.getLogisticsReturn().getReturnTime(), "yyyyMMddHHmmssSSS");
        shipmentCallback.setReturnTime(returnDate);
        shipmentCallback.setReturnStatus(response.getLogisticsReturn().getReturnStatus());
        shipmentCallback.setReturnInfo(response.getLogisticsReturn().getReturnInfo());
        return shipmentCallback;
    }

    /**
     * 获取订单返回封装结果
     *
     * @param cebMsgCallback
     * @return
     */
    public static OrderCallback getOrderCallback(String cebMsgCallback) throws Exception {
        OrderCallback orderCallback = new OrderCallback();
        String requestXmlString = cebMsgCallback.replace("xmlns=\"http://www.chinaport.gov.cn/ceb\"", "");
        CEB312Message response = XMLUtil.converyToJavaBean(requestXmlString, CEB312Message.class);
        orderCallback.setEbpCode(response.getOrderReturn().getEbpCode());
        orderCallback.setEbcCode(response.getOrderReturn().getEbcCode());
        orderCallback.setOrderNo(response.getOrderReturn().getOrderNo());
        Date returnDate = DateUtils.parseDate(response.getOrderReturn().getReturnTime(), "yyyyMMddHHmmssSSS");
        orderCallback.setReturnTime(returnDate);
        orderCallback.setReturnStatus(response.getOrderReturn().getReturnStatus());
        orderCallback.setReturnInfo(response.getOrderReturn().getReturnInfo());
        return orderCallback;
    }

}
