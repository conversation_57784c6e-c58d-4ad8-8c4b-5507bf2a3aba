package com.danding.cds.declare.sdk.executor;

import com.alibaba.fastjson.JSON;
import com.danding.cds.customs.district.api.enums.CustomsDistrictEnum;
import com.danding.cds.declare.sdk.CustomsSupport;
import com.danding.cds.declare.sdk.clear.base.CustomsClient;
import com.danding.cds.declare.sdk.clear.base.CustomsResult;
import com.danding.cds.declare.sdk.enums.CustomsPaymentStatus;
import com.danding.cds.declare.sdk.enums.CustomsType;
import com.danding.cds.declare.sdk.model.cancel.WarpCancelOrderInfo;
import com.danding.cds.declare.sdk.model.inventory.WrapInventoryOrderInfo;
import com.danding.cds.declare.sdk.model.order.WrapOrderDeclareInfo;
import com.danding.cds.declare.sdk.model.payment.CustomsPayDeclareResult;
import com.danding.cds.declare.sdk.model.payment.WrapPaymentInfo;
import com.danding.cds.declare.sdk.model.refund.WarpRefundOrderInfo;
import com.danding.cds.declare.sdk.model.shipment.WrapShipmentInfo;
import com.danding.cds.declare.sdk.payment.base.BaseChannel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.util.Objects;

/**
 * 订单申报相关逻辑
 */
@Slf4j
public class CustomsClearExecutor implements Serializable {

    public CustomsResult paymentDeclare(CustomsSupport support, WrapPaymentInfo info, String env){
        // 根据是否代理支付公司申报来判断采用第三方申报还是直接对接海关申报
        if (true){
            BaseChannel payChannel = support.getChannelRegistry().findChannel(info.getChannel());
            String payToken = info.getTokenJson();
            log.info("[op: PaymentDeclareExecutor-handle] payChannel={}, token={}",
                    JSON.toJSONString(payChannel),payToken);
            // 拆单用子订单号报关 不拆单用订单号报关
            if (CustomsPaymentStatus.WAIT_RE_PUSH.getValue().equals(info.getStatus())){
                info.setAction("edit");
            }else if (CustomsPaymentStatus.WAIT_DECLARE.getValue().equals(info.getStatus())){
                info.setAction("add");
            }else {
                log.error("[op: PaymentDeclareExecutor-handle] WrapPaymentInfo={},该订单非可推送状态，推送失败",JSON.toJSONString(info));
                return CustomsResult.fail("该订单非可推送状态");
            }
            log.info("[op: PaymentDeclareExecutor-handle] param={}", JSON.toJSONString(info));
            CustomsPayDeclareResult result = payChannel.customDeclare(info,payToken);
        }else {
            String clientName = info.getCustoms();
            log.info("申报单号：{}，【支付单申报】方式的client为：{}", info.getOrderNo(), clientName);
            support.getClientRegistry().findClient(info.getCustoms()).paymentDeclare(info);
        }
        return CustomsResult.success();
    }


    public CustomsResult orderDeclare(CustomsSupport support, WrapOrderDeclareInfo bizModel) {
        String clientName = bizModel.getDeclareWay();
        if (StringUtils.isEmpty(clientName)) {
            clientName = bizModel.getCustoms();
        }
        log.info("申报单号：{}，【订单申报】方式的client为：{}", bizModel.getDeclareOrderNo(), clientName);
        CustomsClient customsClient = support.getClientRegistry().findClient(clientName);
        customsClient.orderDeclare(bizModel);
        return CustomsResult.success();
    }

    public CustomsResult inventoryDeclare(CustomsSupport support, WrapInventoryOrderInfo info) {

        String clientName = info.getDeclareWay();
        if (StringUtils.isEmpty(clientName)) {
            clientName = info.getCustomsInventoryDto().getClientCustoms();
        }
        log.info("申报单号：{}，【清单申报】方式的client为：{}", info.getCustomsInventoryDto().getOrderNo(), clientName);
        CustomsClient client = support.getClientRegistry().findClient(clientName);
        client.inventoryDeclare(info);
        return CustomsResult.success();
    }

    public CustomsResult logisticsDeclare(CustomsSupport support, WrapShipmentInfo info){

        String clientName = info.getDeclareWay();
        if (StringUtils.isEmpty(clientName)) {
            clientName = info.getCustoms();
        }
        CustomsClient client = support.getClientRegistry().findClient(clientName);
        log.info("申报单号：{}，【运单申报】方式的client为：{}", info.getDeclareOrderNo(), clientName);
        /**
         * 芥舟海渠道口的兼容:
         * 暂时用zjCustomsClient去报运单
         */
        if (Objects.isNull(client)) {
            if (Objects.equals(clientName, CustomsDistrictEnum.HAIKOU.getCustoms())
                    || Objects.equals(clientName, CustomsDistrictEnum.KUNMING.getCustoms())) {
                if (Objects.equals(info.getDeclareCompanyDTO().getCode(), "31209606ZC")) {
                    log.info("logisticsDeclare 芥舟海口的单子 取zjCustomsClient申报");
                    client = support.getClientRegistry().findClient(CustomsType.ZHE_JIANG.getValue());
                }
            }
        }
        client.shipmentDeclare(info);
        return CustomsResult.success();
    }

    public CustomsResult inventoryCancel(CustomsSupport support, WarpCancelOrderInfo info){

        String clientName = info.getDeclareWay();
        if (StringUtils.isEmpty(clientName)) {
            clientName = info.getCustomsInventoryDto().getClientCustoms();
        }
        log.info("申报单号：{}，【清单取消】方式的client为：{}", info.getCustomsInventoryDto().getOrderNo(), clientName);
        CustomsClient client = support.getClientRegistry().findClient(clientName);
        client.inventoryCancel(info);
        return CustomsResult.success();
    }

    public CustomsResult inventoryRefund(CustomsSupport support, WarpRefundOrderInfo info){
        String clientName = info.getDeclareWay();
        if (StringUtils.isEmpty(clientName)) {
            clientName = info.getCustomsInventoryDto().getClientCustoms();
        }
        log.info("申报单号：{}，【清单退货】方式的client为：{}", info.getCustomsInventoryDto().getOrderNo(), clientName);
        CustomsClient client = support.getClientRegistry().findClient(clientName);
        client.inventoryRefund(info);
        return CustomsResult.success();
    }
}

/*
* 子客户端连接方式
* 是否启用redis
* 采用mq还是http
*
* 难点：
* 1.同步回执拆掉后，会导致调用时的逻辑变复杂-由于海关系统对接复杂度，若想统一模式，则需牺牲一部分场景下的便捷。
* 2.如何接收同异步回执并方便后续调用和处理
* 3.accept方法如何关联业务数据
* */

// 前置获取各类条件以确定采用什么方式，调用什么系统
// 关区
// 加签方式
// String signWay = "swxa"; // UKey & machine:swxa[加密机内又分厂家、加密实现方式]
