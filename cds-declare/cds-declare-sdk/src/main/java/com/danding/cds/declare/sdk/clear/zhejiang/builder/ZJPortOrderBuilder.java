package com.danding.cds.declare.sdk.clear.zhejiang.builder;

import com.danding.cds.declare.ceb.internal.enums.CebDeclareType;
import com.danding.cds.declare.sdk.model.order.DeclareOrderItem;
import com.danding.cds.declare.sdk.model.order.WrapOrderDeclareInfo;
import com.danding.cds.declare.zjport.domain.base.Head;
import com.danding.cds.declare.zjport.domain.base.JKFSign;
import com.danding.cds.declare.zjport.domain.request.Body;
import com.danding.cds.declare.zjport.domain.request.Request;
import com.danding.cds.declare.zjport.domain.request.orderInfo.JKFGoodsPurchaser;
import com.danding.cds.declare.zjport.domain.request.orderInfo.JKFOrderDetail;
import com.danding.cds.declare.zjport.domain.request.orderInfo.JKFOrderImportHead;
import com.danding.cds.declare.zjport.domain.request.orderInfo.OrderInfo;
import com.danding.cds.declare.zjport.internal.enums.HzPortBusinessType;
import org.joda.time.DateTime;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 浙江电子口岸|订单申报信息组装
 */
public class ZJPortOrderBuilder implements Serializable {

    // 申报类型
    private static String DECLARE_TYPE = "1";

    // 进出口标志
    private static String IE_FLAG = "I";

    // 支付类型 其他
    private static String PAY_TYPE = "03";

    // 币制
    private static String CURR_CODE = "142";

    // 证件类型代码
    private static String PAPER_TYPE = "01";

    private static String USER_PROCOTOL = "本人承诺所购买商品系个人合理自用，"
            + "现委托商家代理申报、代缴税款等通关事宜，本人保证遵守《海关法》和国"
            + "家相关法律法规，保证所提供的身份信息和收货信息真实完整，无侵犯他人"
            + "权益的行为，以上委托关系系如实填写，本人愿意接受海关、检验检疫机构"
            + "及其他监管部门的监管，并承担相应法律责任.";

    private WrapOrderDeclareInfo info;
    private String mode;
    public ZJPortOrderBuilder(WrapOrderDeclareInfo info,String mode) {
        this.info = info;
        this.mode = mode;
    }

    public Request build(){
        return buildRequest();
    }

    private Request buildRequest(){
        Request request = new Request();
        request.setHead(buildHead());
        request.setBody(buildBody());
        return request;
    }


    private Head buildHead() {
        Head head = new Head();
        head.setBusinessType(HzPortBusinessType.IMPORTORDER.getType());
        return head;
    }

    private Body buildBody(){
        Body body = new Body();
        body.setOrderInfoList(buildOrderInfoList());
        return body;
    }

    private List<OrderInfo> buildOrderInfoList(){
        List<OrderInfo> orderInfoList = new ArrayList<>();
        orderInfoList.add(buildOrderInfo());
        return orderInfoList;
    }

    private OrderInfo buildOrderInfo(){
        OrderInfo orderInfo = new OrderInfo();
        orderInfo.setJkfSign(buildJkfSign());
        orderInfo.setJkfOrderImportHead(buildImportHead());
        orderInfo.setJkfOrderDetailList(buildDetail());
        orderInfo.setJkfGoodsPurchaser(buildGoodsPurchaser());
        return orderInfo;
    }

    private JKFOrderImportHead buildImportHead(){
        JKFOrderImportHead importHead = new JKFOrderImportHead();
        importHead.setECommerceCode(info.getEbcCompanyDTO().getCode());
        importHead.setECommerceName(info.getEbcCompanyDTO().getName());
        importHead.setIeFlag(IE_FLAG);
        importHead.setPayType(PAY_TYPE);
        importHead.setPayCompanyCode(info.getPayCompany().getCode());
        importHead.setPayCompanyName(info.getPayCompany().getName());
        importHead.setPayNumber(info.getPayTransactionId());
        importHead.setOrderNo(info.getDeclareOrderNo());
        importHead.setOrderTaxAmount(info.getOrderTaxAmount());
        importHead.setDiscount(info.getDiscount());
        importHead.setFeeAmount(info.getFreight());
        importHead.setInsureAmount("0");
        importHead.setCompanyCode(info.getEbpCompanyDTO().getCode());
        importHead.setCompanyName(info.getEbpCompanyDTO().getName());
        importHead.setTradeTime(new DateTime(info.getTradeTime()).toString("yyyy-MM-dd HH:mm:ss"));
        importHead.setCurrCode(CURR_CODE);
        importHead.setConsignee(info.getConsignee());
        importHead.setConsigneeTel(info.getConsigneeTel());
        importHead.setConsigneeAddress(info.getConsigneeAddress());
        importHead.setConsigneeEmail(info.getConsigneeEmail());
        importHead.setSenderCountry("142");
        importHead.setSenderName(info.getSenderName());
        importHead.setPurchaserId(info.getConsigneeTel());
        importHead.setLogisCompanyCode(info.getLogisticsCompany().getCode());
        importHead.setLogisCompanyName(info.getLogisticsCompany().getName());
        importHead.setUserProcotol(USER_PROCOTOL);
        int totalCount = 0;
        BigDecimal goodsTotal = BigDecimal.ZERO;
        for (DeclareOrderItem declareOrderItem : info.getItemList()) {
            goodsTotal = goodsTotal.add(new BigDecimal(declareOrderItem.getGoodsCount()).multiply(new BigDecimal(declareOrderItem.getUnitPrice())));
            totalCount += declareOrderItem.getGoodsCount();
        }
        BigDecimal orderTotal =goodsTotal
                .add(new BigDecimal(info.getOrderTaxAmount()))
                .add(new BigDecimal(info.getFreight()));
        importHead.setOrderTotalAmount(orderTotal.toString());
        importHead.setTotalAmount(goodsTotal.toString());
        importHead.setOrderGoodsAmount(goodsTotal.toString());
        importHead.setTotalCount(totalCount);
        return importHead;
    }

    private List<JKFOrderDetail> buildDetail(){
        List<JKFOrderDetail> detailList = new ArrayList<>();
        int goodsOrder = 0;
        for (DeclareOrderItem item : info.getItemList()) {
            goodsOrder ++;
            JKFOrderDetail detail = new JKFOrderDetail();
            detail.setGoodsOrder(goodsOrder);
            detail.setGoodsName(item.getGoodsName());
            detail.setGoodsModel(item.getGoodsModel());
            detail.setCodeTs(item.getHsCode());
            detail.setUnitPrice(item.getUnitPrice());
            detail.setCurrency(CURR_CODE);
            detail.setGoodsUnit(item.getGoodsUnit());
            detail.setGoodsCount(item.getGoodsCount());
            detail.setOriginCountry(item.getOriginCountry());
            detailList.add(detail);
        }
        return detailList;
    }

    private JKFGoodsPurchaser buildGoodsPurchaser(){
        JKFGoodsPurchaser purchaser = new JKFGoodsPurchaser();
        purchaser.setId(info.getConsigneeTel());
        purchaser.setName(info.getPayerName());
        purchaser.setAddress(info.getConsigneeAddress());
        purchaser.setPaperType(PAPER_TYPE);
        purchaser.setPaperNumber(info.getPayerIdNumber());
        purchaser.setTelNumber(info.getConsigneeTel());
        return purchaser;
    }

    private JKFSign buildJkfSign() {
        JKFSign jkfSign = new JKFSign();
        //<companyCode> 	发送方备案编号	VARCHAR2(20)	必填	发送方备案编号,不可随意填写
        String companyCode = info.getDeclareCompanyDTO().getCode();
        jkfSign.setCompanyCode(companyCode);
        //<businessNo>	业务编码	VARCHAR2(20)	必填	可以是清单预录入号，字段作用是回执给到企业的时候通过这个编号企业能认出对应之前发送的哪个单子
        jkfSign.setBusinessNo(info.getDeclareOrderNo()); // TODO:后续需改成申报单号以便做返回格式的统一
        //<businessType>	业务类型	VARCHAR2(30)	必填	业务类型 PERSONAL_GOODS_DECLAR
        jkfSign.setBusinessType(HzPortBusinessType.IMPORTORDER.getType());
        //<declareType>	申报类型	CHAR(1)	必填	企业报送类型。1-新增 2-变更 3-删除。默认为1。
        jkfSign.setDeclareType(CebDeclareType.CREATE.getType());
        //<cebFlag>			必填	填写或01表示在途在库单证， 02 表示企业采用方案二对接，自行生成加签总署报文， 03表示采用方案一对接，委托平台生成总署报文，回调企业加签服务器加签
        if ("ceb".equals(mode)){
            jkfSign.setCebFlag("02");
        }else {
            jkfSign.setCebFlag("03");
        }
        //<note>	备注	VARCHAR2(256)	可空
        jkfSign.setNote("");
        return jkfSign;
    }
}
