package com.danding.cds.declare.sdk.clear.zhejiang.builder;

import com.danding.cds.declare.ceb.internal.enums.CebDeclareType;
import com.danding.cds.declare.sdk.model.shipment.WrapShipmentInfo;
import com.danding.cds.declare.sdk.utils.DateUtil;
import com.danding.cds.declare.zjport.domain.base.Head;
import com.danding.cds.declare.zjport.domain.base.JKFSign;
import com.danding.cds.declare.zjport.domain.request.Body;
import com.danding.cds.declare.zjport.domain.request.Request;
import com.danding.cds.declare.zjport.domain.request.wayBill.WayBill;
import com.danding.cds.declare.zjport.domain.request.wayBill.WayBillImportDto;
import com.danding.cds.declare.zjport.internal.enums.HzPortBusinessType;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ZJPortShipmentBuilder implements Serializable {
    private WrapShipmentInfo info;

    public ZJPortShipmentBuilder(WrapShipmentInfo info) {
        this.info = info;
    }

    public Request build() {
        return buildRequest();
    }

    private Request buildRequest() {
        Request request = new Request();
        request.setHead(buildHead());
        request.setBody(buildBody());
        return request;
    }


    private Head buildHead() {
        Head head = new Head();
        head.setBusinessType(HzPortBusinessType.IMPORTBILL.getType());
        return head;
    }

    private Body buildBody() {
        Body body = new Body();
        body.setWayBillList(buildWayBillList());
        return body;
    }

    private List<WayBill> buildWayBillList() {
        List<WayBill> wayBillList = new ArrayList<>();
        wayBillList.add(buildWayBill());
        return wayBillList;
    }

    private WayBill buildWayBill() {
        WayBill wayBill = new WayBill();
        wayBill.setJkfSign(buildJkfSign());
        wayBill.setWayBillImportDto(buildWayBillImportDto());
        return wayBill;
    }

    private JKFSign buildJkfSign() {
        JKFSign jkfSign = new JKFSign();
        //<companyCode> 	发送方备案编号	VARCHAR2(20)	必填	发送方备案编号,不可随意填写
        String companyCode = info.getDeclareCompanyDTO().getCode();
        jkfSign.setCompanyCode(companyCode);
        //可以是运单号, 主要作用是回执给到企业的时候通过这个编号企业能认出对应之前发送的哪个单子
        jkfSign.setBusinessNo(info.getSn());
        jkfSign.setBusinessType(HzPortBusinessType.IMPORTBILL.getType());
        jkfSign.setDeclareType(CebDeclareType.CREATE.getType());
        jkfSign.setCebFlag("02");
        jkfSign.setNote("");
        return jkfSign;
    }

    private WayBillImportDto buildWayBillImportDto() {
        WayBillImportDto wayBillImportDto = new WayBillImportDto();
        wayBillImportDto.setWayBill(info.getLogisticsNo());
        wayBillImportDto.setPackNo(Integer.parseInt(info.getPackNo()));
        wayBillImportDto.setGrossWeight(info.getWeight());
        wayBillImportDto.setGoodsName(info.getGoodsInfo());
        wayBillImportDto.setSendArea("浙江金华");
        wayBillImportDto.setConsigneeArea(info.getConsigneeProvince() + info.getConsigneeCity() + info.getConsigneeDistrict() + info.getConsigneeStreet());
        wayBillImportDto.setConsignee(info.getConsignee());
        wayBillImportDto.setConsigneeAddress(info.getConsigneeProvince() + info.getConsigneeCity() + info.getConsigneeDistrict() + info.getConsigneeStreet() + info.getConsigneeAddress());
        wayBillImportDto.setConsigneeTel(info.getConsigneeTel());
        //wayBillImportDto.setCustomsCode("2924");
        wayBillImportDto.setCustomsCode(info.getPortCode());
        wayBillImportDto.setWorth(info.getWorth());
        wayBillImportDto.setImportDateStr(DateUtil.formatDateStr(new Date(), DateUtil.DATE_PATTERN));
        wayBillImportDto.setCurrCode("142");
        wayBillImportDto.setLogisCompanyCode(info.getDeclareCompanyDTO().getCode());
        wayBillImportDto.setLogisCompanyName(info.getDeclareCompanyDTO().getName());
        wayBillImportDto.setOrderNo(info.getDeclareOrderNo());
        return wayBillImportDto;
    }
}
