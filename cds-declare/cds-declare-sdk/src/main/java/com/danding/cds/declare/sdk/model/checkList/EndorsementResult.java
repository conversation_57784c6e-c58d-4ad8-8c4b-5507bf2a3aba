package com.danding.cds.declare.sdk.model.checkList;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class EndorsementResult implements Serializable {
    private static final long serialVersionUID = -588435391473622559L;
    public static final String STATUS_INIT = "INIT";
    public static final String STATUS_EXAMINED = "EXAMINED";
    public static final String STATUS_EXCEPTION = "EXCEPTION";
    public static final String STATUS_FINISH = "FINISH";
    public static final String STATUS_DELETED_APPLY = "DELETED_APPLY";
    public static final String STATUS_DELETED_APPLY_TO_PERSON = "DELETED_APPLY_TO_PERSON";
    public static final String STATUS_DELETED_APPLY_REFUND = "DELETED_APPLY_REFUND";

    /**
     * 业务编号
     */
    private String businessNo;

    /**
     * 预录入编号
     */
    private String preNo;

    /**
     * 事件
     */
    private String action;

    /**
     * 真实核注单号
     */
    private String realOrderNo;
    /**
     * 海关状态
     */
    private String customsStatus;
    /**
     * 错误信息描述
     */
    private List<String> information;

    /**
     * 回执报文
     */
    private String message;

    /**
     * 是否跳过状态更新，只记录日志
     */
    private Boolean skipStatusUpdate = false;


    public EndorsementResult(String businessNo, String preNo, String action, String realOrderNo, String customsStatus, List<String> information, String message) {
        this.businessNo = businessNo;
        this.preNo = preNo;
        this.action = action;
        this.realOrderNo = realOrderNo;
        this.customsStatus = customsStatus;
        this.information = information;
        this.message = message;
        this.skipStatusUpdate = false;
    }

    public EndorsementResult(String businessNo, String preNo, String action, String realOrderNo, String customsStatus, List<String> information, String message, Boolean skipStatusUpdate) {
        this.businessNo = businessNo;
        this.preNo = preNo;
        this.action = action;
        this.realOrderNo = realOrderNo;
        this.customsStatus = customsStatus;
        this.information = information;
        this.message = message;
        this.skipStatusUpdate = skipStatusUpdate != null ? skipStatusUpdate : false;
    }
}
