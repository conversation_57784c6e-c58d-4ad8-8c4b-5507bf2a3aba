package com.danding.cds.declare.sdk.model.inventory;

import com.danding.cds.declare.sdk.model.WrapBeanInfo;
import com.danding.cds.declare.sdk.model.account.AccountBookDto;
import com.danding.cds.declare.sdk.model.company.CompanyInfo;
import com.danding.cds.declare.sdk.model.route.RouteInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 海关申报-清单数据
 */
@Data
public class WrapInventoryOrderInfo extends WrapBeanInfo implements Serializable {

    /**
     * 当前申报步骤 默认1
     */
    private int step = 1;

    private String type = "ADD";

    public WrapInventoryOrderInfo() {
    }

    public WrapInventoryOrderInfo(
            CompanyInfo assureCompanyDTO,
            CustomsInventoryInfo customsInventoryDto,
            List<CustomsInventoryItemInfo> listCustomsInventoryItemInfo,
            CompanyInfo internalAreaCompany,
            CompanyInfo logisticsCompanyDTO,
            CompanyInfo logisticDeclareCompany,
            boolean isDeclareLogisticsInSystem,
            CompanyInfo ebpCompanyDTO,
            CompanyInfo ebcCompanyDTO,
            CompanyInfo declareCompanyDTO,
            CompanyInfo payCompanyDTO,
            AccountBookDto accountBookDto,
            RouteInfo routeInfo) {
        super(ebpCompanyDTO,ebcCompanyDTO,declareCompanyDTO,logisticsCompanyDTO);
        this.assureCompanyDTO = assureCompanyDTO;
        this.customsInventoryDto = customsInventoryDto;
        this.listCustomsInventoryItemInfo = listCustomsInventoryItemInfo;
        this.internalAreaCompany =internalAreaCompany;
        this.logisticsCompanyDTO= logisticsCompanyDTO;
        this.payCompanyDTO  = payCompanyDTO;
        this.isDeclareLogisticsInSystem = isDeclareLogisticsInSystem;
        this.logisticDeclareCompany = logisticDeclareCompany;
        this.accountBookDto = accountBookDto;
        this.routeInfo = routeInfo;
    }

    public WrapInventoryOrderInfo(
                                  CompanyInfo assureCompanyDTO,
                                  CustomsInventoryInfo customsInventoryDto,
                                  List<CustomsInventoryItemInfo> listCustomsInventoryItemInfo,
                                  CompanyInfo internalAreaCompany,
                                  CompanyInfo logisticsCompanyDTO,
                                  CompanyInfo logisticDeclareCompany,
                                  boolean isDeclareLogisticsInSystem,
                                  CompanyInfo ebpCompanyDTO,
                                  CompanyInfo ebcCompanyDTO,
                                  CompanyInfo declareCompanyDTO,
                                  CompanyInfo payCompanyDTO){
        super(ebpCompanyDTO,ebcCompanyDTO,declareCompanyDTO,logisticsCompanyDTO);
        this.assureCompanyDTO = assureCompanyDTO;
        this.customsInventoryDto = customsInventoryDto;
        this.listCustomsInventoryItemInfo = listCustomsInventoryItemInfo;
        this.internalAreaCompany =internalAreaCompany;
        this.logisticsCompanyDTO= logisticsCompanyDTO;
        this.payCompanyDTO  = payCompanyDTO;
        this.isDeclareLogisticsInSystem = isDeclareLogisticsInSystem;
        this.logisticDeclareCompany = logisticDeclareCompany;
    }
    private String businessKey;

    /**
     * 相关清单对象信息
     */
    protected CustomsInventoryInfo customsInventoryDto;

    /**
     * 清单物流明细
     */
    protected List<CustomsInventoryItemInfo> listCustomsInventoryItemInfo;
    /**
     * 区内企业
     */
    protected CompanyInfo internalAreaCompany ;

    /**
     * 物流公司
     */
    protected CompanyInfo logisticsCompanyDTO;
    /**
     * 担保企业
     */
    protected CompanyInfo assureCompanyDTO;
    /**
     * 重庆支付企业
     */
    protected CompanyInfo payCompanyDTO;

    /**
     * 运单申报企业
     */
    protected CompanyInfo logisticDeclareCompany;
    /**
     * 是否申报运单
     */
    protected boolean isDeclareLogisticsInSystem;

    @Override
    public String getDeclareNos() {
        return customsInventoryDto == null ? "" : customsInventoryDto.getOrderNo();
    }
}
