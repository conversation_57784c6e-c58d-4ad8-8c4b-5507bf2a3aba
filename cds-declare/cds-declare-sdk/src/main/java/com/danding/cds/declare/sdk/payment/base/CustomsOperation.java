package com.danding.cds.declare.sdk.payment.base;


import com.danding.cds.declare.sdk.model.payment.CustomsPayDeclareResult;
import com.danding.cds.declare.sdk.model.payment.WrapPaymentInfo;

/**
 * 海关推送
 *
 * DATE: 18/6/1 下午4:46 <br>
 * MAIL: <EMAIL> <br>
 * AUTHOR: miluo
 */
public interface CustomsOperation {

    /**
     * 支付推送海关接口
     *
     * @param customDeclareParams 海关申报参数
     */
    default CustomsPayDeclareResult customDeclare(WrapPaymentInfo customDeclareParams, String tokenJson) throws PayException {
        CustomsPayDeclareResult result=new CustomsPayDeclareResult();
        result.setSuccess(false);
        result.setErrorMsg("方法未实现");
        result.setExtra("unsupported");
        return result;
    }


}
