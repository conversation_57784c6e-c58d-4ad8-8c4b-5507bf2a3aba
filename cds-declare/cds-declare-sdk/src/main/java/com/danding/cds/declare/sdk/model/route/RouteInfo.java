package com.danding.cds.declare.sdk.model.route;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @program: cds-center
 * @description: 路径信息
 * @author: 潘本乐（Belep）
 * @create: 2021-09-29 16:48
 **/
@Data
public class RouteInfo implements Serializable {
    /**
     * 路径标识
     */
    private String code;

    /**
     * 路径名称
     */
    private String name;
    /**
     * 申报方式；HZDC：杭州数据中心；zhejiang：浙江电子口岸；tianjin：天津；chongqing：重庆；auto：自动(根据配置)
     */
    private String declareWay;
    /**
     * 申报高级配置(支持代理申报或更换默认申报实现)
     */
    private List<RouteDeclareConfig> routeDeclareConfigList;

    private String extraJson;

    /**
     *  路由拓展信息
     */
    private RouteExtraDto routeExtraDto;
}
