package com.danding.cds.declare.sdk.pdd;

import org.apache.commons.io.IOUtils;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.xml.sax.InputSource;

import javax.xml.XMLConstants;
import javax.xml.crypto.OctetStreamData;
import javax.xml.crypto.dsig.CanonicalizationMethod;
import javax.xml.crypto.dsig.TransformService;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerConfigurationException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.StringReader;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * Xml处理的工具
 *
 * <AUTHOR>
 * @date 2020/7/22 17:49
 */
public class PddXmlUtils {

    public static String createDataToSignSm2(String digestValue) throws Exception {
        return createDataToSign(digestValue, true);
    }

    /**
     * 创建待签名的xml数据
     *
     * @param digestValue
     * @return
     * @throws Exception
     */
    public static String createDataToSign(String digestValue) throws Exception {

        return createDataToSign(digestValue, false);
    }

    private static String createDataToSign(String digestValue, boolean isSm2) throws Exception {
        DocumentBuilder builder = getDocumentBuilder();
        Document document = builder.newDocument();
        Element signedInfoElm = document.createElement("ds:SignedInfo");
        signedInfoElm.setAttribute("xmlns:ceb", "http://www.chinaport.gov.cn/ceb");
        signedInfoElm.setAttribute("xmlns:ds", "http://www.w3.org/2000/09/xmldsig#");
        signedInfoElm.setAttribute("xmlns:xsi", "http://www.w3.org/2001/XMLSchema-instance");

        Element canonicalizationMethodElm = document.createElement("ds:CanonicalizationMethod");
        canonicalizationMethodElm.setAttribute("Algorithm", "http://www.w3.org/TR/2001/REC-xml-c14n-20010315#WithComments");
        canonicalizationMethodElm.appendChild(document.createTextNode(""));

        Element signatureMethodElm = document.createElement("ds:SignatureMethod");
        if (isSm2) {
            signatureMethodElm.setAttribute("Algorithm", "http://www.chinaport.gov.cn/2022/04/xmldsig#sm2-sm3");
        } else {
            signatureMethodElm.setAttribute("Algorithm", "http://www.w3.org/2000/09/xmldsig#rsa-sha1");
        }
        signatureMethodElm.appendChild(document.createTextNode(""));

        Element referenceElm = document.createElement("ds:Reference");
        referenceElm.setAttribute("URI", "");

        Element transformsElm = document.createElement("ds:Transforms");

        Element transformElm = document.createElement("ds:Transform");
        transformElm.setAttribute("Algorithm", "http://www.w3.org/2000/09/xmldsig#enveloped-signature");
        transformElm.appendChild(document.createTextNode(""));

        Element digestMethodElm = document.createElement("ds:DigestMethod");
        if (isSm2) {
            digestMethodElm.setAttribute("Algorithm", "http://www.chinaport.gov.cn/2022/04/xmldsig#sm3");
        } else {
            digestMethodElm.setAttribute("Algorithm", "http://www.w3.org/2000/09/xmldsig#sha1");
        }
        digestMethodElm.appendChild(document.createTextNode(""));

        Element digestValueElm = document.createElement("ds:DigestValue");
        if (!Objects.isNull(digestValue)) {
            digestValueElm.appendChild(document.createTextNode(digestValue));
        }

        transformsElm.appendChild(transformElm);

        referenceElm.appendChild(transformsElm);
        referenceElm.appendChild(digestMethodElm);
        referenceElm.appendChild(digestValueElm);

        signedInfoElm.appendChild(canonicalizationMethodElm);
        signedInfoElm.appendChild(signatureMethodElm);
        signedInfoElm.appendChild(referenceElm);

        document.appendChild(signedInfoElm);

        Transformer transFormer = getTransformer();
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        DOMSource domSource = new DOMSource(document);
        transFormer.transform(domSource, new StreamResult(bos));
        String xmlStr = bos.toString("UTF-8");


        int index = xmlStr.indexOf("<ds:SignedInfo");
        return c14nT(xmlStr.substring(index));
    }

    public static String creatSignatureNode(String dataWaitToSign, String dataSignature, String cryptoMachineCertificateNo, String x509Certificate) throws Exception {

        DocumentBuilder builder = getDocumentBuilder();
        Document document = builder.newDocument();
        Element signatureElm = document.createElement("ds:Signature");
        signatureElm.setAttribute("xmlns:ds", "http://www.w3.org/2000/09/xmldsig#");

        Element signatureValueElm = document.createElement("ds:SignatureValue");
        if (!Objects.isNull(dataSignature)) {
            dataSignature = dataSignature.replace("\r\n", "");
            signatureValueElm.appendChild(document.createTextNode(dataSignature));
        }

        Element keyInfoElm = document.createElement("ds:KeyInfo");

        Element keyNameElm = document.createElement("ds:KeyName");
        keyNameElm.appendChild(document.createTextNode(cryptoMachineCertificateNo));

        Element x509DataElm = document.createElement("ds:X509Data");

        Element x509CertificateElm = document.createElement("ds:X509Certificate");
        x509CertificateElm.appendChild(document.createTextNode(x509Certificate));

        x509DataElm.appendChild(x509CertificateElm);

        keyInfoElm.appendChild(keyNameElm);
        keyInfoElm.appendChild(x509DataElm);

        DocumentBuilder builderSignInfo = getDocumentBuilder();
        Document docSignInfo = builderSignInfo.parse(new InputSource(new StringReader(dataWaitToSign)));
        Element docSignEle = docSignInfo.getDocumentElement();

        Element signedInfoEle = (Element) document.importNode(docSignEle, true);
        signatureElm.appendChild(signedInfoEle);
        signatureElm.appendChild(signatureValueElm);
        signatureElm.appendChild(keyInfoElm);

        document.appendChild(signatureElm);

        Transformer transFormer = getTransformer();
        DOMSource domSource = new DOMSource(document);
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        transFormer.transform(domSource, new StreamResult(bos));
        String xmlStr = bos.toString("UTF-8");

        int index = xmlStr.indexOf("<ds:Signature");
        return c14nT(xmlStr.substring(index));
    }

    public static String appendSignature(String sourceXml, String signature) throws Exception {

        DocumentBuilder builder = getDocumentBuilder();
        Document doc = builder.parse(new InputSource(new StringReader(sourceXml)));
        Element docEle = doc.getDocumentElement();

        DocumentBuilder builderNode = getDocumentBuilder();
        Document docNode = builderNode.parse(new InputSource(new StringReader(signature)));
        Element docNodeEle = docNode.getDocumentElement();

        Element SignatureEle = (Element) doc.importNode(docNodeEle, true);
        docEle.appendChild(SignatureEle);

        Transformer transFormer = getTransformer();
        DOMSource domSource = new DOMSource(docEle);
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        transFormer.transform(domSource, new StreamResult(bos));
        return bos.toString("UTF-8");
    }


    private static DocumentBuilder getDocumentBuilder() throws ParserConfigurationException {
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        factory.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
        factory.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
        factory.setFeature("http://xml.org/sax/features/external-general-entities", false);
        factory.setFeature("http://apache.org/xml/features/nonvalidating/load-external-dtd", false);
        factory.setXIncludeAware(false);
        factory.setExpandEntityReferences(false);
        return factory.newDocumentBuilder();
    }

    private static Transformer getTransformer() throws TransformerConfigurationException {
        TransformerFactory transFactory = TransformerFactory.newInstance();
        transFactory.setAttribute(XMLConstants.ACCESS_EXTERNAL_DTD, "");
        transFactory.setAttribute(XMLConstants.ACCESS_EXTERNAL_STYLESHEET, "");
        return transFactory.newTransformer();
    }

    /**
     * 标准化Xml的数据
     *
     * @param str
     * @return
     * @throws Exception
     */
    public static String c14nT(String str) throws Exception {
        byte[] bytes = str.getBytes(StandardCharsets.UTF_8);
        TransformService ts = TransformService.getInstance(CanonicalizationMethod.INCLUSIVE_WITH_COMMENTS, "DOM");
        OctetStreamData newData = (OctetStreamData) ts.transform(new OctetStreamData(new ByteArrayInputStream(bytes)), null);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        IOUtils.copy(newData.getOctetStream(), baos);
        return baos.toString(StandardCharsets.UTF_8.name());
    }


}
