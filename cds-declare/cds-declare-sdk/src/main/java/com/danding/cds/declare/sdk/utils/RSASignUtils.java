package com.danding.cds.declare.sdk.utils;


import cn.hutool.core.codec.Base64;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.cert.X509Certificate;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;

import static com.umf.base.util.RSAUtils.genCertificate;

/**
 *
 */
@Slf4j
public final class RSASignUtils {


    public static String createSign(String inStr, int type, String signProject, InputStream p8FileInstream) {
        String returnSign = "sign error ";
        try {
            ByteArrayOutputStream byteOutputStream = new ByteArrayOutputStream();
            for (int i = p8FileInstream.read(); i > -1; i = p8FileInstream.read()) {
                byteOutputStream.write(i);
            }
            PrivateKey pk = getPrivateKey(byteOutputStream.toByteArray());
            byte[] signData = sign(pk, inStr.getBytes("UTF-8"), signProject);
            returnSign = Base64.encode(signData);
        } catch (Exception var19) {
            log.error("加签异常 :{} ", var19.getMessage(), var19);
        }
        return returnSign;
    }

    public static byte[] sign(PrivateKey pk, byte[] data, String signProject) {
        try {
            String sha = "SHA256withRSA";
            Signature e = Signature.getInstance(sha);
            e.initSign(pk);
            e.update(data);
            return e.sign();
        } catch (Exception var6) {
            log.error("签名异常 :{}", var6.getMessage(), var6);
            return "sign error".getBytes(StandardCharsets.UTF_8);
        }
    }


    private static PrivateKey getPrivateKey(byte[] key) {
        PrivateKey priKey = null;
        try {
            PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(key);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            priKey = keyFactory.generatePrivate(pkcs8EncodedKeySpec);

        } catch (Exception var19) {
            log.error("获取私钥异常 :{} ", var19.getMessage(), var19);
        }
        return priKey;

    }


    public static String Sensitivity(String reqStr, String RSAChaeset, InputStream inputStream) throws Exception {
        byte[] certFile = new byte[20480];
        InputStream in_cert = inputStream;

        try {
            in_cert.read(certFile);
        } catch (Exception var19) {
            if (in_cert != null) {
                try {
                    in_cert.close();
                } catch (IOException var18) {
                    log.error("获取私钥异常 :{} ", var19.getMessage(), var19);
                }
            }

            throw new Exception(var19);
        } finally {
            if (in_cert != null) {
                try {
                    in_cert.close();
                } catch (IOException var17) {
                    var17.printStackTrace();
                }
            }

        }
        X509Certificate cert = genCertificate(certFile);
        byte[] keyBytes = cert.getPublicKey().getEncoded();
        X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        Key publicKey = keyFactory.generatePublic(x509KeySpec);
        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
        cipher.init(1, publicKey);
        String values = new String(Base64.encode(cipher.doFinal(reqStr.getBytes(RSAChaeset))));
        return values;
    }
}
