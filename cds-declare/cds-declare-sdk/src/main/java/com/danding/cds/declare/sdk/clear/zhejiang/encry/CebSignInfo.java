package com.danding.cds.declare.sdk.clear.zhejiang.encry;

import lombok.Data;

import java.io.Serializable;
import java.security.cert.X509Certificate;

@Data
public class CebSignInfo implements Serializable {

    /**
     * dxpId 必填
     */
    private String dxpId;

    // --- 加密机调用方式 ---
    /**
     * 1-http 2-swxa 4-Ic卡加签(数据为xml格式，返回xml加签后的所有数据如摘要、签名等) 5-Ic卡加签(数据为任意，仅仅返回签名值)
     */
    private int signWay;

    // --- http方式调用加签应用 ---

    /**
     * 域名，
     */
    private String domain;

    // --- 三未信安本机加签配置 ---

    /**
     * 加密机序号
     */
    private Integer index;

    /**
     * 海关十位编码
     */
    private String cebCode;

    /**
     * 证书编号
     */
    private String certNo;

    /**
     * 证书文件路径
     */
    private String signFile;

    /**
     * 证书文件加载类
     */
    private X509Certificate x509cert;

    /**
     * 其他信息
     */
    private String other;
}
