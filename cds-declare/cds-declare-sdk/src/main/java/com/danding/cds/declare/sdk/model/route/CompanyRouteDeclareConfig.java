package com.danding.cds.declare.sdk.model.route;

import com.danding.cds.common.enums.DeclareEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2025/3/31 16:59
 */
@Data
public class CompanyRouteDeclareConfig implements Serializable {
    private String companyCode;

    private DeclareEnum declareEnum;

    private List<RouteDeclareConfig> configList;
}
