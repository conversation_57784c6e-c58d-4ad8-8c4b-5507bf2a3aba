package com.danding.cds.declare.sdk.model.company;

import lombok.Data;

import java.io.Serializable;

/**
 * @program: cds-center
 * @description: 公司申报配置
 * @author: 潘本乐（Belep）
 * @create: 2021-11-23 10:55
 **/
@Data
public class CompanyDeclareConfigDto implements Serializable {

    /**
     * 基础申报类型code
     */
    private String declareCode;
    /**
     * 申报的实现
     */
    private String declareImpl;
    /**
     * 申报类型：支付单-payment;订单-customsOrder;运单-shipment;清单-inventory;清单取消-inventoryCancel;清单退货-inventoryRefund;
     */
    private String type;
}
