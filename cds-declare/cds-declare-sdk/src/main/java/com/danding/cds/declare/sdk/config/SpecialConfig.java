package com.danding.cds.declare.sdk.config;

import com.alibaba.fastjson.JSON;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.declare.sdk.utils.TokenUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Data
@Configuration
@ConfigurationProperties(prefix = "special.config")
@ConditionalOnProperty(prefix = "special.config", name = "open", havingValue = "true")
public class SpecialConfig {
    private Boolean open;

    private List<CustomsSpecialToken> tokens;

    @PostConstruct
    public void init() {
        log.info("[op:SpecialConfig] load list={}", JSON.toJSONString(tokens));
        // 这里把初始化好的token数据,放到CebMessageUtil里面，方便后续使用
        TokenUtil.initSpecialConfig(tokens);
    }

    public CustomsSpecialToken getToken(String code) {
        if (!CollectionUtils.isEmpty(tokens)) {
            for (CustomsSpecialToken token : tokens) {
                if (code.equals(token.getCustomsCode())) {
                    return token;
                }
            }
        }
        return null;
    }

    public CustomsSpecialToken getToken(String code, String customsBookCode) {
        if (!CollectionUtils.isEmpty(tokens)) {
            for (CustomsSpecialToken token : tokens) {
                if (code.equals(token.getCustomsCode())) {
                    String bookCode = token.getCustomsBookCode();
                    //先比较原始的账册号
                    if (Objects.equals(bookCode, customsBookCode)) {
                        return token;
                    } else {
                        //如果没匹配上，再查询一对多的账册号
                        List<String> customsBookCodes = token.getCustomsBookCodes();
                        if (!CollectionUtils.isEmpty(customsBookCodes) && customsBookCodes.contains(customsBookCode)) {
                            CustomsSpecialToken newToken = ConvertUtil.beanConvert(token, CustomsSpecialToken.class);
                            newToken.setCustomsBookCode(customsBookCode);
                            return newToken;
                        }
                    }
                }
            }
        }
        return null;
    }

    public SpecialConfig() {
        log.info("[op:SpecialConfig] 加载...");
        tokens = new ArrayList<>();
    }
}
