package com.danding.cds.declare.sdk.model.checkList;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
public class Sas121MessageRequest extends MessageRequest implements Serializable {
    private static final long serialVersionUID = 5966037031232029363L;
    /**
     * CLOSE 为作废  默认DECLARE申报
     */
    private String declareType = "DECLARE";
    /**
     * 核放单申报信息
     */
    private CustomsChecklistOrderInfo customsChecklistOrderInfo;
    /**
     * 核注清单信息
     */
    private List<CustomsEndorsementOrderInfo> customsEndorsementOrderInfo;
    /**
     * 核放单表体
     */
    private List<CheckListPassPort> checkListPassPortList;
}
