package com.danding.cds.declare.sdk.clear.chongqing.model;

import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(propOrder = {"SKU", "GOODS_SPEC", "CURRENCY_CODE", "PRICE", "QTY", "GOODS_FEE", "TAX_FEE", "COUNTRY"})
@XmlRootElement(name = "ORDER_DETAIL")
public class CQDeliverItem implements Serializable {

    @XmlElement(name = "SKU")
    private String SKU;

    @XmlElement(name = "GOODS_SPEC")
    private String GOODS_SPEC;

    @XmlElement(name = "CURRENCY_CODE")
    private String CURRENCY_CODE;

    @XmlElement(name = "PRICE")
    private String PRICE;

    @XmlElement(name = "QTY")
    private String QTY;

    @XmlElement(name = "GOODS_FEE")
    private String GOODS_FEE;

    @XmlElement(name = "TAX_FEE")
    private String TAX_FEE;

    @XmlElement(name = "COUNTRY")
    private String COUNTRY;
}
