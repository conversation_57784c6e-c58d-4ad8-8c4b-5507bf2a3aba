package com.danding.cds.declare.sdk.payment.wechat;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.cds.declare.sdk.CustomsReport;
import com.danding.cds.declare.sdk.CustomsSupport;
import com.danding.cds.declare.sdk.enums.PayCustoms;
import com.danding.cds.declare.sdk.enums.PayCustomsChannel;
import com.danding.cds.declare.sdk.model.payment.CustomsPayDeclareResult;
import com.danding.cds.declare.sdk.model.payment.WrapPaymentInfo;
import com.danding.cds.declare.sdk.payment.base.BaseChannel;
import com.danding.cds.declare.sdk.payment.base.PayException;
import com.danding.cds.declare.sdk.payment.wechat.model.WxCustomsDeclareModel;
import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class WechatpayChannel extends BaseChannel<WechatpayToken> {

    protected WechatpayConfig config;

    public WechatpayChannel(CustomsSupport support) {
        super(support);
        config = new WechatpayConfig();
    }

    @Override
    public WechatpayToken makeToken(String tokenJson){
        return JSON.parseObject(tokenJson,WechatpayToken.class);
    }

    @Override
    public PayCustomsChannel getPayChannel(){
        return PayCustomsChannel.WECHAT_PAY;
    }

    @Override
    public CustomsPayDeclareResult customDeclare(WrapPaymentInfo customDeclareParams, String tokenJson)
        throws PayException {
        CustomsReport customsReport = new CustomsReport(CustomsReport.TYPE_SEND,CustomsReport.SYSTEM_PAY_CHANNEL,CustomsReport.PROCESS_CLEAR_DECLARE_PAYMENT);
        log.info("[op:WechatPayChannel-customsDeclare] CustomsDeclareParams={}, tokenJson={}", JSON.toJSONString(customDeclareParams),tokenJson);
        try{
            WechatpayToken token = makeToken(tokenJson);
            WxPayClient client = new WxPayClient();
            WxCustomsDeclareModel model = new WxCustomsDeclareModel();
            if ("add".equals(customDeclareParams.getAction())){
                model.setActionType("ADD");
            }else if ("edit".equals(customDeclareParams.getAction())){
                model.setActionType("MODIFY");
            }
            // 订单信息
            model.setOutTradeNo(customDeclareParams.getOutTradeNo());
            model.setTransactionId(customDeclareParams.getTradePayNo());
            // 商户海关备案信息
            model.setMchCustomsNo(customDeclareParams.getMerchantCustomsCode());
            // 海关
            switch (PayCustoms.getEnum(customDeclareParams.getCustoms())){
                case JINYI:
                case HANGZHOU:
                case DEQING:
                    model.setCustoms("HANGZHOU_ZS");
                    break;
                case GUANGZHOU_NS:
                case GUANGZHOU_NS_GJ:
                    model.setCustoms("GUANGZHOU_NS_GJ");
                    break;
                case GUANGZHOU:
                    model.setCustoms("GUANGZHOU_ZS");
                    break;
                case SHANGHAI:
                    model.setCustoms("SHANGHAI_ZS");
                    break;
                case TIANJIN:
                    model.setCustoms("TIANJIN");
                    break;
                case CHONGQING:
                    model.setCustoms("CHONGQING");
                    break;
                default:
                    throw new PayException("微信申报海关未找到匹配项");
            }
            // 拆单
            if (customDeclareParams.getSplitFlag()){
                model.setSubOrderNo(customDeclareParams.getOutOrderNo());
                if (!CustomsSupport.ENV_ONLINE.equals(CustomsSupport.env)){
                    model.setSubOrderNo(CustomsSupport.env + model.getSubOrderNo());
                }
                model.setFeeType("CNY");
                model.setOrderFee(customDeclareParams.getAmount().multiply(new BigDecimal(100)).setScale(0,BigDecimal.ROUND_DOWN).toString());
                model.setProductFee(customDeclareParams.getAmount().multiply(new BigDecimal(100)).setScale(0,BigDecimal.ROUND_DOWN).toString());
                model.setTransportFee(customDeclareParams.getTransportFee().multiply(new BigDecimal(100)).setScale(0,BigDecimal.ROUND_DOWN).toString());
            }
            // 订购人信息
            model.setCertType("IDCARD");
            model.setCertId(customDeclareParams.getBuyerIdNo());
            model.setName(customDeclareParams.getBuyerName());

            Map<String,String> reqData = JSON.parseObject(JSON.toJSONString(model), HashMap.class);
            log.info("[op:WechatPayChannel-customsDeclare] gateway={}, appId={}, mchId={}, partnerKey={},reqData={}", config.getCustomDeclareOrderGateway(), token.getAppId(), token.getMchId(), token.getPartnerKey(), JSON.toJSONString(reqData));
            customsReport.buildRequest(JSON.toJSONString(reqData));
            log.info("[op:WechatPayChannel-customsDeclare] signMap={}", JSON.toJSONString(client.fillRequestDataWithoutNorceStr(token.getAppId(),token.getMchId(),token.getPartnerKey(),reqData)));
            Map<String,String> resultMap;
            if (CustomsSupport.ENV_ONLINE.equals(CustomsSupport.env)){
                resultMap = client.customsDeclare(config.getCustomDeclareOrderGateway(),token.getAppId(),token.getMchId(),token.getPartnerKey(),reqData);
            }else {
                resultMap = new HashMap<>();
                JSONObject jsonObject = JSON.parseObject("{\"transaction_id\":\"4200000697202009061143117204\",\"verify_department\":\"NETSUNION\",\"modify_time\":\"20200906113930\",\"sign\":\"EF335A198EB60C3D01B860F42A1DEEDD\",\"err_code\":\"0\",\"return_msg\":\"成功\",\"err_code_des\":\"OK\",\"mch_id\":\"1540436701\",\"sub_order_id\":\"420000069720200906114311720400\",\"out_trade_no\":\"20200906113709000000000000021975\",\"sub_order_no\":\"DOCP202009061137080001031481\",\"cert_check_result\":\"SAME\",\"appid\":\"wxfdbf105303bd1ffb\",\"verify_department_trade_id\":\"2020090637100667267994310100804\",\"result_code\":\"SUCCESS\",\"state\":\"UNDECLARED\",\"return_code\":\"SUCCESS\"}");
                for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                    resultMap.put(entry.getKey(),entry.getValue().toString());
                }
            }
            log.info("[op:WechatPayChannel-customsDeclare] resultMap={}", JSON.toJSONString(resultMap));
            customsReport.buildResponse(JSON.toJSONString(resultMap));
            CustomsPayDeclareResult result;
            if("SUCCESS".equals(resultMap.get("return_code"))){
                if ("SUCCESS".equals(resultMap.get("result_code"))){
                    if ("FAIL".equals(resultMap.get("state"))){
                        result = CustomsPayDeclareResult.fail("微信支付单申报状态:申报失败");
                    }else if ("EXCEPT".equals(resultMap.get("state"))){
                        result = CustomsPayDeclareResult.fail("微信支付单申报状态:海关接口异常");
                    }else if(!"SAME".equals(resultMap.get("cert_check_result"))){
                        result = CustomsPayDeclareResult.fail("支付人身份证与申报信息不一致");
                    }else{
                        // 转换成与海关|系统对应的核验机构代码
                        String verDept = resultMap.get("verify_department");
                        if ("UNIONPAY".equals(verDept)){
                            verDept = "1";
                        }else if ("NETSUNION".equals(verDept)){
                            verDept = "2";
                        }else {
                            verDept = "3";
                        }
                        result = CustomsPayDeclareResult.ok(verDept,resultMap.get("verify_department_trade_id"));
                        result.setSubBankNo(resultMap.get("sub_order_id"));
                    }
                }else {
                    result = CustomsPayDeclareResult.fail(resultMap.get("err_code_des"));
                }
                if ("SAME".equals(resultMap.get("cert_check_result"))){
                    result.setIdentityCheck(true);
                } else {
                    result.setIdentityCheck(false);
                }
            } else {
                result = CustomsPayDeclareResult.fail(resultMap.get("return_msg"));
            }
            result.setExtra(JSON.toJSONString(resultMap));
            result.setPostMsg(JSON.toJSONString(reqData));
            result.setCustoms(customDeclareParams.getCustoms());
            result.setOutRequestNo(customDeclareParams.getOutRequestNo());
            customsReport.buildProcessData(result);
            support.accept(customsReport);
            log.info("[op:wechatpayCustomsDeclare] request:{},response={}", JSON.toJSONString(reqData), JSON.toJSONString(result));
            return result;
        }catch (Exception e){
            log.error("query order request fail, parms={}, cause:{}", customDeclareParams, Throwables.getStackTraceAsString(e));
            throw new PayException("query.refund.fail");
        }
    }

    public static void main(String[] args) {
        Map<String,String> resultMap = new HashMap<>();
        JSONObject jsonObject = JSON.parseObject("{\"transaction_id\":\"4200000697202009061143117204\",\"verify_department\":\"NETSUNION\",\"modify_time\":\"20200906113930\",\"sign\":\"EF335A198EB60C3D01B860F42A1DEEDD\",\"err_code\":\"0\",\"return_msg\":\"成功\",\"err_code_des\":\"OK\",\"mch_id\":\"1540436701\",\"sub_order_id\":\"420000069720200906114311720400\",\"out_trade_no\":\"20200906113709000000000000021975\",\"sub_order_no\":\"DOCP202009061137080001031481\",\"cert_check_result\":\"SAME\",\"appid\":\"wxfdbf105303bd1ffb\",\"verify_department_trade_id\":\"2020090637100667267994310100804\",\"result_code\":\"SUCCESS\",\"state\":\"UNDECLARED\",\"return_code\":\"SUCCESS\"}");
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            resultMap.put(entry.getKey(),entry.getValue().toString());
        }
        System.out.println(JSON.toJSONString(resultMap));
    }
}
