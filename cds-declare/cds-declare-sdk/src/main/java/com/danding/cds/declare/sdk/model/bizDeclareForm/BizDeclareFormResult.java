package com.danding.cds.declare.sdk.model.bizDeclareForm;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class BizDeclareFormResult implements Serializable {
    private static final long serialVersionUID = -588435391473622559L;
    public static final String STATUS_UN_CHANGE = "UN_CHANGE";
    public static final String STATUS_RECORDED = "RECORDED";
    public static final String STATUS_FINISHED = "FINISHED";
    public static final String STATUS_RECORD_EXCEPTION = "RECORD_EXCEPTION";
    public static final String STATUS_FINISH_EXCEPTION = "FINISH_EXCEPTION";
    public static final String STATUS_PAUSED = "PAUSED";
    public static final String STATUS_PAUSE_RECOVERY = "PAUSE_RECOVERY";

    public static final String TYPE_COMMON = "COMMON";
    public static final String TYPE_SYNC = "0";
    public static final String TYPE_RECORD = "1";
    public static final String TYPE_EDIT = "2";
    public static final String TYPE_FINISH = "3";

    /**
     * 业务编号
     */
    private String businessNo;

    /**
     * 预录入编号
     */
    private String preNo;

    /**
     * 事件
     */
    private String action;

    /**
     * 海关：业务申报表编号
     */
    private String declareFormNo;
    /**
     * 海关审核状态（申报表回执）
     */
    private String customsReceiptStatus;
    /**
     * 海关状态
     */
    private String customsStatus;

    /**
     * 错误信息描述
     */
    private List<String> information;

    /**
     * 回执报文
     */
    private String message;

    /**
     * 回执类型
     */
    private String messageType;
}
