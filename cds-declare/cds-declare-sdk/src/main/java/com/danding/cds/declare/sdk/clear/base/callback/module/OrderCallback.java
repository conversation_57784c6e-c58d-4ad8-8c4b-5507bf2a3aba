package com.danding.cds.declare.sdk.clear.base.callback.module;

import com.danding.cds.http.saas.annotation.TenantHttpField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class OrderCallback implements Serializable {

    /**
     * 电商平台
     */
    private String ebpCode;

    /**
     * 电商企业
     */
    private String ebcCode;

    /**
     * 电商企业
     */
    @TenantHttpField(alias = "declareOrderNo")
    private String orderNo;

    /**
     * 回执时间
     */
    private Date returnTime;

    /**
     * 回执状态
     */
    private String returnStatus;

    /**
     * 回执信息
     */
    private String returnInfo;
}
