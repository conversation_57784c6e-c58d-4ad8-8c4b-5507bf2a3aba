package com.danding.cds.declare.sdk.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
@Slf4j
public final class DateUtil {

    private static final Object lock = new Object();

    public static final String DEF_PATTERN = "yyyy-MM-dd HH:mm:ss";//默认时间格式

    public static final String DATE_TIMESTAMP_PATTERN = "yyyy-MM-dd HH:mm:ss:SSS";//默认时间格式

    public static final String DATE_PATTERN = "yyyy-MM-dd";//日期格式

    public static final String DATE_DEF_PATTERN = "yyyyMMddHHmmss";//日期格式

    public static final String DATE_PATTERN_UNDEF = "yyyyMMdd";

    public static final long MILLISECOND_OF_DAY = 86400000l;//一天的毫秒数

    private static Map<String, ThreadLocal<SimpleDateFormat>> sdfMap = new HashMap<String, ThreadLocal<SimpleDateFormat>>();

    public static String dateToString(Date date, String pattern) {
        try {
            if (date == null)
                return null;
            return new SimpleDateFormat(pattern).format(date);
        } catch (Exception e) {
            return null;
        }
    }


    /**
     * 根据Pattern获取SimpleDateFormat
     *
     * @param pattern
     * @return
     */
    private static SimpleDateFormat getSdf(final String pattern) {
        ThreadLocal<SimpleDateFormat> simpleDateFormatThreadLocal = sdfMap.get(pattern);
        if (simpleDateFormatThreadLocal == null) {
            synchronized (lock) {
                if (simpleDateFormatThreadLocal == null) {
                    simpleDateFormatThreadLocal = new ThreadLocal<SimpleDateFormat>() {
                        @Override
                        protected SimpleDateFormat initialValue() {
                            SimpleDateFormat sdf = new SimpleDateFormat(pattern);
                            //现在在国内，默认时区写死成北京时区
//                            sdf.setTimeZone(TimeZone.getTimeZone("CST"));
                            return sdf;
                        }
                    };
                    sdfMap.put(pattern, simpleDateFormatThreadLocal);
                }
            }
        }
        return simpleDateFormatThreadLocal.get();
    }

    /**
     * 方法说明：获得指定格式当前系统时间字符串
     *
     * @param pattern
     * @return
     * @throws Exception
     */
    public static String getCDateString(String pattern){
        SimpleDateFormat sf = getSdf(pattern);
        return sf.format(new Date());
    }

    /**
     * 方法说明：获得指定格式当前系统时间字符串
     *
     * @return
     * @throws Exception
     */
    public static String getCurrentDateTimeOfStr() {
        String result = "";
        try {
            result = getCDateString("yyyyMMdd");
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
        }
        return result;
    }

    /**
     * 方法说明：获得指定格式当前系统时间字符串 格式：yyyy-MM-dd HH:mm:ss
     *
     * @return
     * @throws Exception
     */
    public static String getCurrentDateTime() {
        String result = "";
        try {
            result = getCDateString("yyyy-MM-dd HH:mm:ss");
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
        }
        return result;
    }

    public static long getDatetime(String pattern, String dt) throws Exception {
        SimpleDateFormat sf = getSdf(pattern);
        return sf.parse(dt).getTime();
    }

    public static String getDateForLong(long time) {
        Calendar c = Calendar.getInstance();
        c.setTimeInMillis(time);
        SimpleDateFormat sdf = getSdf("yyyy-MM-dd");
        return sdf.format(c.getTime());
    }

    public static String getDateForLong(String pattern, long time) {
        Calendar c = Calendar.getInstance();
        c.setTimeInMillis(time);
        SimpleDateFormat sdf = getSdf(pattern);
        return sdf.format(c.getTime());
    }

    public static int getTwoDay(Date date1, Date date2) {
        Calendar calendar1 = Calendar.getInstance();
        calendar1.setTime(date1);
        Calendar calendar2 = Calendar.getInstance();
        calendar2.setTime(date2);

        int day1 = calendar1.get(Calendar.DAY_OF_YEAR);
        int day2 = calendar2.get(Calendar.DAY_OF_YEAR);

        return day1 - day2;
    }

    /**
     * 按指定格式格式化日期
     *
     * @param d
     * @param parttern
     * @return
     */
    public static String formatDateStr(Date d, String parttern) {
        if(d==null){
            return null;
        }
        SimpleDateFormat df = getSdf(parttern);
        return df.format(d);
    }

    /**
     * 默认格式时间转字符串
     *
     * @param d
     * @return
     */
    public static String defFormatDateStr(Date d) {
        return formatDateStr(d, DEF_PATTERN);
    }

    /**
     * 解析Date字符串为Date
     *
     * @param dataStr
     * @param pattern
     * @return
     * @throws Exception
     */
    public static Date parseDate(String dataStr, String pattern) {
        if (StringUtils.isEmpty(dataStr)) {
            return null;
        }
        if (StringUtils.isEmpty(pattern)) {
            pattern = DateUtil.DEF_PATTERN;
        }
        try {
            SimpleDateFormat df = getSdf(pattern);
            return df.parse(dataStr);
        } catch (ParseException e) {
            return null;
        }
    }

    public static Date buildByHMSE(Date time, int h, int m, int s, int ms) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(time);
        calendar.set(Calendar.HOUR_OF_DAY, h);
        calendar.set(Calendar.MINUTE, m);
        calendar.set(Calendar.SECOND, s);
        calendar.set(Calendar.MILLISECOND, ms);
        return calendar.getTime();
    }

    public static Date getDateBeforeOrAfter(Date date, int pre) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.DAY_OF_YEAR, pre);
        return c.getTime();
    }

    public static Date getBeforOrAfterByHour(Date date, int hour) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.HOUR_OF_DAY, hour);
        return c.getTime();
    }

    public static Date getBeforOrAfterByMinute(Date date, int minute) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.MINUTE, minute);
        return c.getTime();
    }

    /**
     * 获取口岸申报时间段
     * 根据Diamond上配置的 起始时分秒 获取当前时间的 年月日-时分秒，并向后推24小时，算出结束时间的年月日-时分秒
     *
     * @param timeFm
     * @return 返回 起始时间 和 结束时间 数组
     */
    public static Date[] getPortTimeBw(Date nowTime, String timeFm) {
        String[] timeArray = timeFm.split(":");
        Date[] dates = new Date[2];
        //当前时间的截止时间
        Date nowfmTime = DateUtil.buildByHMSE(nowTime, Integer.valueOf(timeArray[0]), Integer.valueOf(timeArray[1]), Integer.valueOf(timeArray[2]), 0);
        if (nowTime.after(nowfmTime)) {
            dates[0] = nowfmTime;
            dates[1] = DateUtil.getDateBeforeOrAfter(dates[0], 1);
        } else {
            dates[1] = nowfmTime;
            dates[0] = DateUtil.getDateBeforeOrAfter(dates[1], -1);
        }
        return dates;
    }

    /**
     * 获取当前时间后一个小时的时间
     *
     * @return
     */
    public static Date getOneHoursAgoOnCurrentDate() {
        Date currentDate = new Date();
        Calendar cal = Calendar.getInstance();
        cal.setTime(currentDate);
        cal.add(Calendar.HOUR_OF_DAY, 1);
        return cal.getTime();
    }


    /**
     * 两个时间的相差的天数
     *
     * @param d1
     * @param d2
     * @return
     */
    public static long getDateBetween(Date d1, Date d2) {
        Calendar c1 = Calendar.getInstance();
        c1.setTime(d1);
        Calendar c2 = Calendar.getInstance();
        c2.setTime(d2);
        if (c1.get(Calendar.YEAR) == c2.get(Calendar.YEAR)) {
            int d1OfY = c1.get(Calendar.DAY_OF_YEAR);
            int d2OfY = c2.get(Calendar.DAY_OF_YEAR);
            return d2OfY - d1OfY;
        } else {
            c1.set(Calendar.HOUR_OF_DAY, 0);
            c1.set(Calendar.MINUTE, 0);
            c1.set(Calendar.SECOND, 0);
            c1.set(Calendar.MILLISECOND, 0);

            c2.set(Calendar.HOUR_OF_DAY, 0);
            c2.set(Calendar.MINUTE, 0);
            c2.set(Calendar.SECOND, 0);
            c2.set(Calendar.MILLISECOND, 0);
            return (c2.getTimeInMillis() - c1.getTimeInMillis()) / MILLISECOND_OF_DAY;
        }
    }


    /**
     * 获取date的前n个或者下n个月第一天 Calendar
     * 日期格式 DATE_PATTERN
     */
    public static Calendar getMonthFisrtDayByDate(Date date, int n) {
        Date countDate = null;
        if (date == null) {
            date = new Date();
        }
        countDate = DateUtil.parseDate(DateUtil.formatDateStr(date, "yyyy-MM"), "yyyy-MM");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(countDate);
        calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) + n);
        return calendar;
    }


    /**
     * 获取date的前n个或者下n个月第一天的随机时分秒 时间
     */
    public static Date getNextMonthFirstDayRandomTime(Date date) {
        Calendar calendar = getMonthFisrtDayByDate(date, 1);
        Random random = new Random();
        int hourRandom = random.nextInt(24);
        int minuteRandom = random.nextInt(60);
        int secondRandom = random.nextInt(60);
        calendar.set(Calendar.HOUR_OF_DAY, hourRandom);
        calendar.set(Calendar.MINUTE, minuteRandom);
        calendar.set(Calendar.SECOND, secondRandom);
        return calendar.getTime();
    }

    public static Date getNextYearFirstDayRandomTime(Date date) {
        if (date == null) {
            date = new Date();
        }
        Calendar calendar = Calendar.getInstance();
        Random random = new Random();
        int hourRandom = random.nextInt(24);
        int minuteRandom = random.nextInt(60);
        int secondRandom = random.nextInt(60);
        calendar.setTime(date);
        calendar.add(Calendar.YEAR, 1);
        calendar.set(Calendar.MONTH, 0);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, hourRandom);
        calendar.set(Calendar.MINUTE, minuteRandom);
        calendar.set(Calendar.SECOND, secondRandom);
        return calendar.getTime();
    }

    /**
     * 获取当天的起始时间
     *
     * @return
     */
    public static Date getCurrentDayStartTime() {
        Calendar calendar = new GregorianCalendar();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取当天的结束时间
     *
     * @return
     */
    public static Date getCurrentDayEndTime() {
        Calendar calendar = new GregorianCalendar();
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        return calendar.getTime();
    }

    public static long getDayMilliseconds(int days) {
        return 1000 * 60 * 60 * 24 * days;
    }

    private static String ymdhms = "yyyyMMddHHmmssssssss";
//    private static SimpleDateFormat yyyyMMddHHmmssssssss = new SimpleDateFormat(ymdhms);

    /**
     * 获得当前时间
     * 格式：yyyyMMddHHmmssssssss
     *
     * @return String
     */
    public static String getCurrentTime() {
        SimpleDateFormat yyyyMMddHHmmssssssss = new SimpleDateFormat(ymdhms);
        return yyyyMMddHHmmssssssss.format(new Date());
    }

    /**
     * 返回�?个格式化的日期字符串
     *
     * @param pattern 日期格式，若为空则默认为yyyyMMdd
     * @return
     */
    public static String getTCurrentDate(String pattern) {
        SimpleDateFormat datePattern = null;
        if (null == pattern || "".equals(pattern)) {
            datePattern = new SimpleDateFormat("yyyyMMdd");
        } else {
            datePattern = new SimpleDateFormat(pattern);
        }
        return datePattern.format(new Date());
    }
    
    public static void main(String[] args) {
        Date date = parseDate("2015-2-2", DATE_PATTERN);
        for (int i = 0; i < 1; i++) {
            System.out.println(getNextYearFirstDayRandomTime(date));
        }

    }

}
