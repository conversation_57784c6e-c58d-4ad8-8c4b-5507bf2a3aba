package com.danding.cds.declare.sdk.clear.zhejiang.builder;

import com.danding.cds.declare.ceb.domain.base.BaseTransfer;
import com.danding.cds.declare.ceb.domain.ceb311.CEB311Message;
import com.danding.cds.declare.ceb.domain.ceb311.Order;
import com.danding.cds.declare.ceb.domain.ceb311.OrderHead;
import com.danding.cds.declare.ceb.domain.ceb311.OrderList;
import com.danding.cds.declare.ceb.internal.enums.CebDeclareType;
import com.danding.cds.declare.ceb.internal.utils.CebDateUtil;
import com.danding.cds.declare.sdk.model.order.DeclareOrderItem;
import com.danding.cds.declare.sdk.model.order.WrapOrderDeclareInfo;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

public class ZJCebOrderBuilder implements Serializable {

    private String dxpId;
    private WrapOrderDeclareInfo info;

    public ZJCebOrderBuilder(WrapOrderDeclareInfo info, String dxpId) {
        this.info = info;
        this.dxpId = dxpId;
    }

    public CEB311Message build() {
        String uuid = UUID.randomUUID().toString().toUpperCase();
        return buildOrderMessage(uuid);
    }

    private CEB311Message buildOrderMessage(String uuid) {
        CEB311Message ceb311Message = new CEB311Message();
        ceb311Message.setOrder(buildOrder(uuid));
        BaseTransfer baseTransfer = buildBaseTransfer();
        ceb311Message.setGuid(uuid);
        ceb311Message.setBaseTransfer(baseTransfer);
        return ceb311Message;
    }

    private Order buildOrder(String uuid) {
        Order order = new Order();
        order.setOrderHead(buildHead(uuid));
        order.setOrderList(buildList());
        return order;
    }

    private OrderHead buildHead(String uuid) {
        OrderHead orderHead = new OrderHead();
        //系统唯一序号、报送类型、报送时间、业务状态
        orderHead.setGuid(uuid);
        orderHead.setAppType(CebDeclareType.CREATE.getType());
        orderHead.setAppTime(CebDateUtil.format(new Date(), CebDateUtil.defaultCebDateStringFormat));
        orderHead.setAppStatus("2");
        orderHead.setOrderType("I");
        orderHead.setOrderNo(info.getDeclareOrderNo());
        orderHead.setEbpCode(info.getEbpCompanyDTO().getCebCode());
        orderHead.setEbpName(info.getEbpCompanyDTO().getCebName());
        orderHead.setEbcCode(info.getEbcCompanyDTO().getCebCode());
        orderHead.setEbcName(info.getEbcCompanyDTO().getCebName());
        BigDecimal goodsTotal = BigDecimal.ZERO;
        for (DeclareOrderItem declareOrderItem : info.getItemList()) {
            goodsTotal = goodsTotal.add(new BigDecimal(declareOrderItem.getGoodsCount()).multiply(new BigDecimal(declareOrderItem.getUnitPrice())));
        }
        orderHead.setGoodsValue(goodsTotal.toString());
        orderHead.setFreight(info.getFreight());
        orderHead.setDiscount(info.getDiscount());
        orderHead.setTaxTotal(info.getOrderTaxAmount());
        BigDecimal payTotal = goodsTotal
                .add(new BigDecimal(info.getOrderTaxAmount()))
                .add(new BigDecimal(info.getFreight())).subtract(new BigDecimal(info.getDiscount()));
        orderHead.setActuralPaid(payTotal.toString());
        orderHead.setCurrency("142");
        // 这里兼容下，因为历史取值成了收件人，新加的字段不好维护历史数据
        String buyerTelNumber = info.getPayerTelNumber();
        if (StringUtils.isEmpty(buyerTelNumber)) {
            buyerTelNumber = info.getConsigneeTel();
        }
        orderHead.setBuyerRegNo(buyerTelNumber);
        orderHead.setBuyerName(info.getPayerName());
        orderHead.setBuyerTelephone(buyerTelNumber);
        orderHead.setBuyerIdType("1");
        orderHead.setBuyerIdNumber(info.getPayerIdNumber());
        orderHead.setPayCode(info.getPayCompany().getCebCode());
        orderHead.setPayName(info.getPayCompany().getCebName());
        orderHead.setPayTransactionId(info.getPayTransactionId());
        orderHead.setConsignee(info.getConsignee());
        orderHead.setConsigneeTelephone(info.getConsigneeTel());
        orderHead.setConsigneeAddress(info.getConsigneeAddress());
        return orderHead;
    }

    private List<OrderList> buildList() {
        List<OrderList> list = new ArrayList<>();
        int gnum = 0;
        for (DeclareOrderItem item : info.getItemList()) {
            OrderList orderList = new OrderList();
            gnum++;
            orderList.setGnum(gnum);
            orderList.setItemNo(item.getGoodsNo());
            orderList.setItemName(item.getGoodsName());
            orderList.setGmodel(item.getGoodsModel());
            orderList.setUnit(item.getGoodsUnit());
            orderList.setQty(item.getGoodsCount() + "");
            orderList.setPrice(item.getUnitPrice());
            orderList.setTotalPrice(new BigDecimal(item.getUnitPrice()).multiply(new BigDecimal(item.getGoodsCount())).toString());
            orderList.setCurrency("142");
            orderList.setCountry(item.getOriginCountry());
            list.add(orderList);
        }
        return list;
    }

    private BaseTransfer buildBaseTransfer() {
        BaseTransfer baseTransfer = new BaseTransfer();
        String declareCompanyCode = info.getDeclareCompanyDTO().getCebCode();
        String declareCompanyName = info.getDeclareCompanyDTO().getCebName();
        baseTransfer.setCopCode(declareCompanyCode);
        baseTransfer.setCopName(declareCompanyName);
        baseTransfer.setDxpMode("DXP");
        baseTransfer.setDxpId(dxpId);
        return baseTransfer;
    }


}
