package com.danding.cds.declare.sdk.model.checkList;

import lombok.Data;

import java.io.Serializable;

/**
 * 核注清单表头参数（ccs系统）
 */
@Data
public class InvtHeadType implements Serializable {
    /**
     * 清单类型
     * 0：普通清单，1：集报清单，3：先入区后报关，4：简单加工，5：保税展示交易，6：区内流转，7：区港联动，8:保税电商，9：一纳成品内销  默认为0：普通清单
     */
    private String invtType;
    /**
     * 进出口标记代码  I：进口,E：出口
     */
    private String impexpMarkCd;
    /**
     * 监管方式
     */
    private String supvModecd;
    /**
     * 运输方式
     * 4-公路运输
     */
    private String trspModecd;
    /**
     * 是否报关标志
     * 1.报关2.非报关
     */
    private String dclcusFlag;
    /**
     * 报关类型代码
     * 1.关联报关2.对应报关；“是否需要报关”字段填写为“是”，企业可选择“关联报关单”/“对应报关单”；“是否需要报关”字段填写为“否”，该项不可填。
     */
    private String dclcusTypeCd;
    /**
     * 报关单类型
     */
    private String decType;
    /**
     * 对应报关单申报单位社会统一信用代码
     */
    private String corrEntryDclEtpsSccd;
    /**
     * 对应报关单申报单位代码  当报关类型DCLCUS_TYPECD字段为2时，该字段必填
     */
    private String corrEntryDclEtpsNo;
    /**
     * 对应报关单申报单位名称  当报关类型DCLCUS_TYPECD字段为2时，该字段必填
     */
    private String corrEntryDclEtpsNm;
    /**
     * 是否生成报关单
     * 1-生成 2-不生成
     */
    private String genDecFlag;
    /**
     * 起运/运抵国(地区）可修改
     */
    private String stshipTrsarvNatcd;
    /**
     * 进出境关别  可修改
     */
    private String impexpPortcd;
    /**
     * 关联备案编号(账册编号)
     */
    private String rltPutrecNo;
    /**
     * 关联清单编号(结转类专用)
     */
    private String rltInvtNo;

    /**
     * 关联报关单申报单位社会统一信用代码
     */
    private String rltEntryDclEtpsSccd;

    /**
     * 关联报关单海关申报单位编码
     */
    private String rltEntryDclEtpsNo;

    /**
     * 关联报关单申报单位名称
     */
    private String rltEntryDclEtpsNm;

    /**
     * 关联报关单境内收发货人社会信用代码
     */
    private String rltEntryBizopEtpsSccd;

    /**
     * 关联报关单境内收发货人编号
     */
    private String rltEntryBizopEtpsNo;

    /**
     * 关联报关单境内收发货人名称
     */
    private String rltEntryBizopEtpsNm;

    /**
     * 关联报关单消费使用单位社会统一信用代码
     */
    private String rltEntryRvsngdEtpsSccd;

    /**
     * 关联报关单消费使用单位编号
     */
    private String rltEntryRvsngdEtpsNo;

    /**
     * 关联报关单消费使用单位名称
     */
    private String rltEntryRvsngdEtpsNm;

    /**
     * 料件成品标记代码
     */
    private String mtpckEndprdMarkcd;


    /**
     * 备案编号(账册编号)
     */
    private String putrecNo;

    /**
     * 清单报关时使用。海关端报关单入库时，反填并反馈企业端
     * 如果企业导入报文选择：报关：1、报关单类型：X或Y、报关类型为：2-对应报关时，该项为必填项
     */
    private String entryNo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 录入员IC卡号
     */
    private String icCardNo;

    /**
     * 申报类型代码 1-备案申请 2-变更申请 3-注销申请
     */
    private String dclTypecd;

    /**
     * 申请表编号
     */
    private String applyNo;
}
