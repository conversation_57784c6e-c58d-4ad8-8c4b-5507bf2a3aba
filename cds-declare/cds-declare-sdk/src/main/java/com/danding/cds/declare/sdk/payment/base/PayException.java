package com.danding.cds.declare.sdk.payment.base;

import lombok.Getter;
import lombok.ToString;

/**
 * 支付异常
 *
 * DATE: 16/8/22 上午11:54 <br>
 * MAIL: <EMAIL> <br>
 * AUTHOR: zhanghecheng
 */
@ToString
public class PayException extends RuntimeException {

    @Getter
    private final int status;

    /**
     * 校验失败信息
     */
    @Getter
    private final String error;

    /**
     * 校验失败的一些参数说明信息
     */
    @Getter
    private final Object[] params;

    public PayException(int status, String error, Object... params){
        this.status = status;
        this.error = error;
        this.params = params;
    }

    public PayException(String error, Object... param) {
        this(400, error, param);
    }

    public PayException(String error) {
        this(error, null);
    }


    public PayException(int status, String error){
        this(status, error, null);
    }

}

