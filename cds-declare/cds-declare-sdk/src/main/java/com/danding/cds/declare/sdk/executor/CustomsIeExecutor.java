package com.danding.cds.declare.sdk.executor;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.common.utils.*;
import com.danding.cds.declare.ceb.internal.utils.XMLUtil;
import com.danding.cds.declare.sdk.CustomsReport;
import com.danding.cds.declare.sdk.CustomsSupport;
import com.danding.cds.declare.sdk.clear.base.CustomsResult;
import com.danding.cds.declare.sdk.config.CustomsSpecialToken;
import com.danding.cds.declare.sdk.ie.*;
import com.danding.cds.declare.sdk.model.bizDeclareForm.AppGoodsInfo;
import com.danding.cds.declare.sdk.model.bizDeclareForm.AppHeadInfo;
import com.danding.cds.declare.sdk.model.bizDeclareForm.AppUcnsInfo;
import com.danding.cds.declare.sdk.model.bizDeclareForm.Sas101MessageRequest;
import com.danding.cds.declare.sdk.model.checkList.Icp101MessageRequest;
import com.danding.cds.declare.sdk.model.checkList.Inv101MessageRequest;
import com.danding.cds.declare.sdk.model.checkList.Sas121MessageRequest;
import com.danding.cds.declare.sdk.model.checklistAuth.Sas122MessageRequest;
import com.danding.cds.declare.zjspecial.domain.sas121.Signature;
import com.danding.cds.declare.zjspecial.internal.mq.ZJSpecialProducer;
import com.github.kevinsawicki.http.HttpRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;


/**
 * 货物出入区相关逻辑
 */
@Slf4j
public class CustomsIeExecutor implements Serializable {
    /**
     * 特殊监管区域小程序
     */
    private static final String SPECIAL_CUSTOMS_APP = "SpecialCustomsApp";

    private CustomsSupport support;

    public CustomsIeExecutor(CustomsSupport support) {
        this.support = support;
    }

    /**
     * - customsCode: 331866K00D
     * customsName: 义乌南波万供应链管理有限公司
     * socialCreditCode: 91330782MA8G4DJ8X4
     * senderId: DXPENT0000468451
     * consumerId: DXPENT0000468451_HZ
     * customsBookCode: T2925W000135
     *
     * @return
     */

    public static CustomsSpecialToken getYwNoOneCustomsSpecialTokenByCustomsBookCode() {
        CustomsSpecialToken specialToken = new CustomsSpecialToken();
        specialToken.setCustomsCode("331866K00D");
        specialToken.setCustomsName("义乌南波万供应链管理有限公司");
        specialToken.setSocialCreditCode("91330782MA8G4DJ8X4");
        specialToken.setIcCard("2100040124879");
        specialToken.setSenderId("DXPENT0000468451");
        specialToken.setConsumerId("DXPENT0000468451_HZ");
        specialToken.setCustomsBookCode("T2925W000135");
        return specialToken;
    }

    private boolean skipHzPush(String hzNo) {
        List<String> skipHzList = ThreadContextUtil.getSkipHzPushNoList();
        if (CollectionUtils.isEmpty(skipHzList)) {
            return false;
        }
        return skipHzList.contains(hzNo);
    }

    public CustomsResult checklistHZ(Sas121MessageRequest request) {
        log.info("checklistHZ：request={}", JSON.toJSONString(request));
        CustomsReport customsReport = new CustomsReport(CustomsReport.TYPE_SEND,
                CustomsReport.SYSTEM_SPECIAL_CLIENT);
        CustomsResult result;
        //针对金华楠岳的特殊处理：c单用004 b单用066
        if ("3307680004".equals(request.getCustomsCompanyCode())) {
            request.setCustomsCompanyCode("3307660066");
        }
        //针对绍兴代塔的特殊处理：c单用330668900A b单用330666900Z
        if ("330668900A".equals(request.getCustomsCompanyCode())) {
            request.setCustomsCompanyCode("330666900Z");
        }
        CustomsSpecialToken specialToken;
        if (Objects.nonNull(request.getCustomsChecklistOrderInfo()) && Objects.equals(request.getCustomsChecklistOrderInfo().getPassPortTypeCd(), "6")) {
            log.info("空车入区 通过区内企业编码匹配");
            specialToken = support.getSpecialConfig().getToken(request.getCustomsCompanyCode());
        } else {
            specialToken = support.getSpecialConfig().getToken(request.getCustomsCompanyCode(), request.getCustomsBookCode());
        }
        /*if ("T2924W000137".equals(request.getCustomsBookCode())) {
            specialToken = getCustomsSpecialTokenByCustomsBookCode();
        }*/
        try {
            Signature signature = new ChecklistBuilder().build(request, specialToken);
            String xml = XMLUtil.convertToXml(signature, "utf-8");
            customsReport.buildRequest(xml);
            this.send(request.getCustomsChecklistOrderInfo().getChecklistOrderNo(), xml, specialToken);
            result = CustomsResult.success();
            ChecklistLogUtils.setChecklistLogDetail(xml);
        } catch (Exception e) {
            log.info("[op:CustomsIeExecutor-checklistHZ] exception, cause={}", e.getMessage(), e);

            result = CustomsResult.fail("系统异常");
        }
        support.accept(customsReport);
        return result;
    }

    public CustomsResult checklistAuthHZ(Sas122MessageRequest request) {
        log.info("checklistAuthHZ：request={}", JSON.toJSONString(request));
        CustomsResult result;
        CustomsReport customsReport = new CustomsReport(CustomsReport.TYPE_SEND,
                CustomsReport.SYSTEM_SPECIAL_CLIENT);
        CustomsSpecialToken specialToken = support.getSpecialConfig().getToken(request.getCustomsCompanyCode());
        try {
            com.danding.cds.declare.zjspecial.domain.Signature signature = new ChecklistAuthBuilder().build(request, specialToken);
            String xml = XMLUtil.convertToXml(signature, "utf-8");
            // 使用xml多态序列化时会指定类型，这里手动把类型去掉
            xml = StringUtils.replace(xml, " xsi:type=\"Sas122Package\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"", "");
            customsReport.buildRequest(xml);
            log.info("checklistAuthHZ：xml={}", xml);
            this.send(request.getChecklistAuthSn(), xml, specialToken);
            result = CustomsResult.success();
            ChecklistAuthLogUtils.setChecklistAuthLogDetail(xml);
        } catch (Exception e) {
            log.error("[op:CustomsIeExecutor-checklistAuthHZ] exception, cause={}", e.getMessage(), e);
            result = CustomsResult.fail("系统异常");
        }
        support.accept(customsReport);
        return result;
    }

    public CustomsResult bizDeclareFormHZ(Sas101MessageRequest request) {
        log.info("bizDeclareFormHZ：request={}", JSON.toJSONString(request));
        CustomsResult result;
        CustomsReport customsReport = new CustomsReport(CustomsReport.TYPE_SEND,
                CustomsReport.SYSTEM_SPECIAL_CLIENT);
        CustomsSpecialToken specialToken = support.getSpecialConfig().getToken(request.getCustomsCompanyCode());
        try {
            com.danding.cds.declare.zjspecial.domain.Signature signature = new BizDeclareFormBuilder().build(request, specialToken);
            String xml = XMLUtil.convertToXml(signature, "utf-8");
            // 使用xml多态序列化时会指定类型，这里手动把类型去掉
            xml = StringUtils.replace(xml, " xsi:type=\"Sas101Package\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"", "");
            customsReport.buildRequest(xml);
            log.info("bizDeclareFormHZ：xml={}", xml);
            this.send(request.getBizDeclareFormSn(), xml, specialToken);
            result = CustomsResult.success();
            BizDeclareFormLogUtils.setBizDeclareFormLogDetail(xml);
        } catch (Exception e) {
            log.error("[op:CustomsIeExecutor-bizDeclareFormHZ] exception, cause={}", e.getMessage(), e);
            result = CustomsResult.fail("系统异常");
        }
        support.accept(customsReport);
        return result;
    }

    public static void main(String[] args) {
        Sas101MessageRequest request = new Sas101MessageRequest();
        request.setBizDeclareFormSn("");
        AppHeadInfo appHeadInfo = new AppHeadInfo();
        appHeadInfo.setSeqNo("");
        appHeadInfo.setSasDclNo("");
        appHeadInfo.setMasterCusCd("2924");
        appHeadInfo.setDclTypeCd("1");
        appHeadInfo.setBusinessTypeCd("G");
        appHeadInfo.setDirectionTypeCd("I");
        appHeadInfo.setAreaInOriActNo("T2924W000100");
        appHeadInfo.setAreaOutOriActNo("");
        appHeadInfo.setAreaInEtpsNo("330766K009");
        appHeadInfo.setAreaInEtpsNm("金华代塔供应链管理有限公司");
        appHeadInfo.setAreaInEtpsSccd("91330703MA2E738J6M");
        appHeadInfo.setAreaOutEtpsNo("");
        appHeadInfo.setAreaOutEtpsNm("");
        appHeadInfo.setAreaOutEtpsSccd("");
        appHeadInfo.setDpstLevyBlNo("");
        appHeadInfo.setValidTime(new Date());
        appHeadInfo.setDclEr("测试");
        appHeadInfo.setExhibitionPlace("");
        appHeadInfo.setDclEtpsNo("330766K009");
        appHeadInfo.setDclEtpsNm("金华代塔供应链管理有限公司");
        appHeadInfo.setDclEtpsSccd("91330703MA2E738J6M");
        appHeadInfo.setInputCode("330766K009");
        appHeadInfo.setInputSccd("91330703MA2E738J6M");
        appHeadInfo.setInputName("金华代塔供应链管理有限公司");
        appHeadInfo.setEtpsPreentNo("JG2025011000004");
        appHeadInfo.setMtpckEndprdTypeCd("I");
        appHeadInfo.setRmk("123123");
        appHeadInfo.setCol1("");
        appHeadInfo.setCol2("");
        appHeadInfo.setCol3(new Date());
        appHeadInfo.setCol4("");
        appHeadInfo.setFreeDomestic("");

        request.setAppHeadInfo(appHeadInfo);
        AppGoodsInfo appGoodsInfo = new AppGoodsInfo();
        appGoodsInfo.setSeqNo("");
        appGoodsInfo.setSasDclNo("");
        appGoodsInfo.setSasDclSeqNo(new BigDecimal("1"));
        appGoodsInfo.setOriActGdsSeqNo(new BigDecimal("4405"));
        appGoodsInfo.setMtpckEndprdTypeCd("I");
        appGoodsInfo.setGdeCd("2106909090");
        appGoodsInfo.setGdsNm("sotya女性HUPAVIR冲剂7包/盒");
        appGoodsInfo.setGdsSpcfModelDesc("7包/盒");
        appGoodsInfo.setDclQty(new BigDecimal("1"));
        appGoodsInfo.setDclUnitCd("140");
        appGoodsInfo.setDclUprcAmt(new BigDecimal("28"));
        appGoodsInfo.setDclTotalAmt(new BigDecimal("28"));
        appGoodsInfo.setDclCurrCd("142");
        appGoodsInfo.setLicenceNo("123456");
        appGoodsInfo.setLicenceValidTime(new Date());
        appGoodsInfo.setGdsMarkCd("0");
        appGoodsInfo.setGdsRmk("123123");
        appGoodsInfo.setModfMarkCd("3");
        appGoodsInfo.setRmk("123123");
        appGoodsInfo.setGdsMtNo("812362016420");
        appGoodsInfo.setLawfUnitCd("035");
        appGoodsInfo.setSecdLawfUnitCd("");
        appGoodsInfo.setCol1("0");
        appGoodsInfo.setCol2("312");
        appGoodsInfo.setCol3(new BigDecimal("0"));
        appGoodsInfo.setCol4("");
        appGoodsInfo.setEndprdGdsTypeCd("");

        AppGoodsInfo appGoodsInfo2 = new AppGoodsInfo();
        appGoodsInfo2.setSeqNo("");
        appGoodsInfo2.setSasDclNo("");
        appGoodsInfo2.setSasDclSeqNo(new BigDecimal("2"));
        appGoodsInfo2.setOriActGdsSeqNo(null);
        appGoodsInfo2.setMtpckEndprdTypeCd("I");
        appGoodsInfo2.setGdeCd("2106909090");
        appGoodsInfo2.setGdsNm("sotya女性HUPAVIR冲剂7包/盒");
        appGoodsInfo2.setGdsSpcfModelDesc("7包/盒");
        appGoodsInfo2.setDclQty(new BigDecimal("1"));
        appGoodsInfo2.setDclUnitCd("140");
        appGoodsInfo2.setDclUprcAmt(new BigDecimal("28"));
        appGoodsInfo2.setDclTotalAmt(new BigDecimal("28"));
        appGoodsInfo2.setDclCurrCd("142");
        appGoodsInfo2.setLicenceNo("123456");
        appGoodsInfo2.setLicenceValidTime(new Date());
        appGoodsInfo2.setGdsMarkCd("0");
        appGoodsInfo2.setGdsRmk("123123");
        appGoodsInfo2.setModfMarkCd("3");
        appGoodsInfo2.setRmk("123123");
        appGoodsInfo2.setGdsMtNo("812362016420");
        appGoodsInfo2.setLawfUnitCd("035");
        appGoodsInfo2.setSecdLawfUnitCd("");
        appGoodsInfo2.setCol1("0");
        appGoodsInfo2.setCol2("312");
        appGoodsInfo2.setCol3(new BigDecimal("0"));
        appGoodsInfo2.setCol4("");
        appGoodsInfo2.setEndprdGdsTypeCd("");
        List<AppGoodsInfo> appGoodsInfos = new ArrayList<>();
        appGoodsInfos.add(appGoodsInfo);
        appGoodsInfos.add(appGoodsInfo2);
        AppUcnsInfo appUcnsInfo = new AppUcnsInfo();
        appUcnsInfo.setSeqNo("");
        appUcnsInfo.setSasDclNo("");
        appUcnsInfo.setEndprdSeqNo(new BigDecimal("1"));
        appUcnsInfo.setMtpckSeqNo(new BigDecimal("2"));
        appUcnsInfo.setNetUseupQty(new BigDecimal("2"));
        appUcnsInfo.setLossRate(new BigDecimal("0"));
        appUcnsInfo.setModfMarkCd("3");
        appUcnsInfo.setCol1("");
        appUcnsInfo.setCol2(new Date());
        appUcnsInfo.setCol3(new BigDecimal("0"));
        appUcnsInfo.setCol4("");
        List<AppUcnsInfo> appUcnsInfos = new ArrayList<>();
        appUcnsInfos.add(appUcnsInfo);
        request.setAppGoodsInfos(appGoodsInfos);
        request.setAppUcnsInfos(appUcnsInfos);
        request.setMessage("");
        request.setCustomsCompanyCode("");
        request.setCustomsBookCode("");
        request.setCustomsAreaCode("");

        CustomsSpecialToken specialToken = new CustomsSpecialToken();
        specialToken.setSenderId("DXPENT0000477639");
        specialToken.setIcCard("2100040042104");
        specialToken.setCustomsCode("330766K009");
        com.danding.cds.declare.zjspecial.domain.Signature signature = new BizDeclareFormBuilder().build(request, specialToken);
        try {
            String xml = XMLUtil.convertToXml(signature, "utf-8");
            xml = StringUtils.replace(xml, " xsi:type=\"Sas101Package\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"", "");
            System.out.println(xml);
            log.info("金二推送报文走杭州数据中心,fileName={}", "JG2025011000004");
            ZJSpecialProducer producer = new ZJSpecialProducer(
                    "**************", "5672",
                    "EPORT_HZ", "password",
                    specialToken.getSenderId(),
                    specialToken.getIcCard(), "DXPEDCSAS0000001");
            producer.sendMessage("JG2025011000004", xml, false);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public void send(String fileName, String dataOrign, CustomsSpecialToken specialToken) throws Exception {
        log.info("金二推送报文：fileName={},dataOrign={},specialToken={}", fileName, dataOrign, specialToken);
        if (CustomsSupport.ENV_ONLINE.equals(CustomsSupport.env)) {
            String connectionType = specialToken.getConnectionType();
            // 如果推送类型是特殊监管区域
            if (Objects.equals(SPECIAL_CUSTOMS_APP, connectionType)) {
                String specialCustomsAppUrl = specialToken.getSpecialCustomsAppUrl();
                log.info("金二特殊监管区域，远程调用SpecialCustomsApp，fileName= {} ,url={}", fileName, specialCustomsAppUrl);
                if (StringUtils.isEmpty(specialCustomsAppUrl)) {
                    return;
                }
                String sendFileName = fileName + "_" + DateUtil.format(new Date(), "yyyyMMddHHmmss") + ".xml";
                Map<String, String> values = new HashMap<>();
                values.put("fileName", sendFileName);
                values.put("content", dataOrign);
                HttpRequest httpRequest = HttpRequestUtil.post(specialCustomsAppUrl, JSON.toJSONString(values));
                String body = httpRequest.body();
                if (httpRequest.ok() && Objects.equals(body, "SUCCESS")) {
                    log.info("金二特殊监管区域，远程调用SpecialCustomsApp成功，fileName= {} ,");
                } else {
                    log.error("金二特殊监管区域，远程调用SpecialCustomsApp失败，fileName= {} ,url={},返回信息：{}", fileName, specialCustomsAppUrl, body);
                    throw new RuntimeException("特殊监管区域，远程调用SpecialCustomsApp失败");
                }
            } else {
                log.info("金二推送报文走杭州数据中心,fileName={}", fileName);
                ZJSpecialProducer producer = new ZJSpecialProducer(
                        "**************", "5672",
                        "EPORT_HZ", "password",
                        specialToken.getSenderId(),
                        specialToken.getIcCard(), "DXPEDCSAS0000001");
                producer.sendMessage(fileName, dataOrign, skipHzPush(fileName));
            }
        }
    }


    public CustomsResult checklistTwoStep(Icp101MessageRequest twoStepRequest) {
        log.info("checklistTwoStep：request={}", JSON.toJSONString(twoStepRequest));
        CustomsReport customsReport = new CustomsReport(CustomsReport.TYPE_SEND,
                CustomsReport.SYSTEM_SPECIAL_CLIENT);
        CustomsResult result;
        CustomsSpecialToken specialToken = support.getSpecialConfig().getToken(twoStepRequest.getCustomsCompanyCode());
        /*if ("T2924W000137".equals(twoStepRequest.getCustomsBookCode())) {
            specialToken = getCustomsSpecialTokenByCustomsBookCode();
        }*/
        try {
            com.danding.cds.declare.zjspecial.domain.icp101.Signature signature = new CheckListTwoStepBuilder().build(twoStepRequest, specialToken);
            String xml = XMLUtil.convertToXml(signature, "utf-8");
            customsReport.buildRequest(xml);
            this.send(twoStepRequest.getCustomsChecklistOrderInfo().getChecklistOrderNo(), xml, specialToken);
            result = CustomsResult.success();
            ChecklistLogUtils.setChecklistLogDetail(xml);
        } catch (Exception e) {
            log.info("[op:CustomsIeExecutor-checklistHZ] exception, cause={}", e.getMessage(), e);

            result = CustomsResult.fail("系统异常");
        }
        support.accept(customsReport);
        return result;
    }

    public static CustomsSpecialToken getCustomsSpecialTokenByCustomsBookCode() {
        CustomsSpecialToken specialToken = new CustomsSpecialToken();
        specialToken.setCustomsCode("330766K00W");
        specialToken.setCustomsName("金华凡尔纳供应链管理有限公司");
        specialToken.setSocialCreditCode("91330703MA2EEG4GX8");
        specialToken.setIcCard("2100040053044");
        specialToken.setSenderId("DXPENT0000027028");
        specialToken.setConsumerId("DXPENT0000027028_HZ");
        specialToken.setCustomsBookCode("T2924W000137");
        return specialToken;
    }

    private CustomsSpecialToken getJHNYSpecialToken() {
        CustomsSpecialToken specialToken = new CustomsSpecialToken();
        specialToken.setCustomsCode("3307660066");
        specialToken.setCustomsName("金华楠岳供应链管理有限公司");
        specialToken.setSocialCreditCode("91330703MA7H14EC9A");
        specialToken.setIcCard("JJ6G900219687");
        specialToken.setSenderId("DXPENT0000485641");
        specialToken.setConsumerId("DXPENT0000485641_HZ");
        specialToken.setCustomsBookCode("T2925W000135");
        return specialToken;
    }

    public static CustomsSpecialToken getFenToken_T2924W000093() {
        CustomsSpecialToken specialToken = new CustomsSpecialToken();
        specialToken.setCustomsCode("330766K00W");
        specialToken.setCustomsName("金华凡尔纳供应链管理有限公司");
        specialToken.setSocialCreditCode("91330703MA2EEG4GX8");
        specialToken.setIcCard("2100040053044");
        specialToken.setSenderId("DXPENT0000027028");
        specialToken.setConsumerId("DXPENT0000027028_HZ");
        specialToken.setCustomsBookCode("T2924W000093");
        return specialToken;
    }

    public static CustomsSpecialToken getYWJIAYIN_T2925W000338() {
        CustomsSpecialToken specialToken = new CustomsSpecialToken();
        specialToken.setCustomsCode("331866003L");
        specialToken.setCustomsName("义乌市佳应供应链管理有限公司");
        specialToken.setSocialCreditCode("91330782MAC08W9M75");
        specialToken.setIcCard("JJ6G900216379");
        specialToken.setSenderId("DXPENT0000485639");
        specialToken.setConsumerId("DXPENT0000485639_HZ");
        specialToken.setCustomsBookCode("T2925W000338");
        return specialToken;
    }

    public static CustomsSpecialToken getYWSHUOYUN_T2925W000349() {
        CustomsSpecialToken specialToken = new CustomsSpecialToken();
        specialToken.setCustomsCode("331866003H");
        specialToken.setCustomsName("义乌朔云供应链管理有限公司");
        specialToken.setSocialCreditCode("91330782MAC00DH96Q");
        specialToken.setIcCard("2100040125183");
        specialToken.setSenderId("DXPENT0000485642");
        specialToken.setConsumerId("DXPENT0000485642_HZ");
        specialToken.setCustomsBookCode("T2925W000349");
        return specialToken;
    }

    public static CustomsSpecialToken getShaoXingZongBao(String customsBookCode) {
        CustomsSpecialToken specialToken = new CustomsSpecialToken();
        specialToken.setCustomsCode("330666900Z");
        specialToken.setCustomsName("绍兴代塔供应链管理有限公司");
        specialToken.setSocialCreditCode("91330602MACHFAXC1R");
        specialToken.setIcCard("2100040136328");
        specialToken.setSenderId("DXPENT0000532889");
        specialToken.setConsumerId("DXPENT0000532889_HZ");
        specialToken.setCustomsBookCode(customsBookCode);
        return specialToken;
    }

    public CustomsResult endorsementHZ(Inv101MessageRequest request) {
        log.info("endorsementHZ：request={}", JSON.toJSONString(request));
        CustomsReport customsReport = new CustomsReport(CustomsReport.TYPE_SEND,
                CustomsReport.SYSTEM_SPECIAL_CLIENT);
        CustomsResult result;
        //针对金华楠岳的特殊处理：c单用004 b单用066
        if ("3307680004".equals(request.getCustomsCompanyCode())) {
            request.setCustomsCompanyCode("3307660066");
        }
        //针对绍兴代塔的特殊处理：c单用330668900A b单用330666900Z
        if ("330668900A".equals(request.getCustomsCompanyCode())) {
            request.setCustomsCompanyCode("330666900Z");
        }
        CustomsSpecialToken specialToken = support.getSpecialConfig().getToken(request.getCustomsCompanyCode(), request.getCustomsBookCode());
        // todo fixme 这个位置纯碎是为了兼容一个企业两本账册情况，不优雅，要改，要改，要改
        if ("T2924W000137".equals(request.getCustomsBookCode())) {
            specialToken = getCustomsSpecialTokenByCustomsBookCode();
        } else if (Objects.equals("T2925W000135", request.getCustomsBookCode())) {
            specialToken = getYwNoOneCustomsSpecialTokenByCustomsBookCode();
        } else if (Objects.equals("T2924W000093", request.getCustomsBookCode())) {
            specialToken = getFenToken_T2924W000093();
        } else if (Objects.equals("T2964W000112", request.getCustomsBookCode())) {
            specialToken = getShaoXingZongBao("T2964W000112");
        } else if (Objects.equals("T2964W000113", request.getCustomsBookCode())) {
            specialToken = getShaoXingZongBao("T2964W000113");
        } else if (Objects.equals("T2925W000338", request.getCustomsBookCode())) {
            specialToken = CustomsIeExecutor.getYWJIAYIN_T2925W000338();
        } else if (Objects.equals("T2925W000349", request.getCustomsBookCode())) {
            specialToken = CustomsIeExecutor.getYWSHUOYUN_T2925W000349();
        }
        try {
            com.danding.cds.declare.zjspecial.domain.Signature signature;
            if ("SECONDE_OUT".equals(request.getBussinessType())) {//二线出区
                signature = new EndorsementBuilder().build(request, specialToken);
            } else {
                signature = new EndorsementBuilderByInvtOrder().build(request, specialToken);
            }
            String xml = XMLUtil.convertToXml(signature, "utf-8");
            xml = StringUtils.replace(xml, " xsi:type=\"Inv101Package\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"", "");
            log.info("endorsementHZ：xml={}", xml);
            customsReport.buildRequest(xml);
            this.send(request.getEndorsementOrderNo(), xml, specialToken);
            result = CustomsResult.success();
            //将请求报文存入threadLocal中
            EndorsementLogUtils.setEndorsementLogDetail(xml);
        } catch (Exception e) {
            log.error("核注申报处理异常 :{}", e.getMessage(), e);
            result = CustomsResult.fail("系统异常");
        }
        support.accept(customsReport);
        return result;
    }

}
