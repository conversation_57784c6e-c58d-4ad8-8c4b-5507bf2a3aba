package com.danding.cds.declare.sdk.bean.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @program: cds-center
 * @description: s
 * @author: 潘本乐（Belep）
 * @create: 2021-09-29 14:31
 **/
@Data
public class ShipmentSendInfoReqVo implements Serializable {
    /**
     * province : 云南省
     * city : 昆明市
     * area : 经开区
     * address_detail : 综合保税区
     * send_name :  E宠商城
     * send_phone : 4008889200
     * send_eamil : null
     * send_compay : null
     * send_zip_code :
     * send_city_code :
     */

    @JsonProperty("province")
    private String province;
    @JsonProperty("city")
    private String city;
    @JsonProperty("area")
    private String area;
    @JsonProperty("address_detail")
    private String addressDetail;
    @JsonProperty("send_name")
    private String sendName;
    @JsonProperty("send_phone")
    private String sendPhone;
    @JsonProperty("send_eamil")
    private Object sendEamil;
    @JsonProperty("send_compay")
    private Object sendCompay;
    @JsonProperty("send_zip_code")
    private String sendZipCode;
    @JsonProperty("send_city_code")
    private String sendCityCode;
}
