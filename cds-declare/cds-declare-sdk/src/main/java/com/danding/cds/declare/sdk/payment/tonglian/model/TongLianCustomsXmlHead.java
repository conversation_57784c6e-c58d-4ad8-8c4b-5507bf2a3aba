package com.danding.cds.declare.sdk.payment.tonglian.model;

import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(propOrder = {"version", "visitorId", "mchtId", "orderNo", "transDatetime", "charset", "signType", "signMsg"})
@XmlRootElement(name = "HEAD")
public class TongLianCustomsXmlHead implements Serializable {

    /**
     * 版本号 v5.6
     */
    @XmlElement(name = "VERSION")
    private String version;
    /**
     * 接入方ID 接入方编号，商户送‘MCT’
     */
    @XmlElement(name = "VISITOR_ID")
    private String visitorId;
    /**
     * 报关用的商户号
     */
    @XmlElement(name = "MCHT_ID")
    private String mchtId;
    /**
     * 报关流水号
     */
    @XmlElement(name = "ORDER_NO")
    private String orderNo;
    /**
     * 发送时间 yyyyMMddHH24mmss
     */
    @XmlElement(name = "TRANS_DATETIME")
    private String transDatetime;
    /**
     * 字符集 默认值1，UTF-8
     */
    @XmlElement(name = "CHARSET")
    private String charset;
    /**
     * 签名方式 默认值1,MD5签名
     */
    @XmlElement(name = "SIGN_TYPE")
    private String signType;
    /**
     * 签名密文
     */
    @XmlElement(name = "SIGN_MSG")
    private String signMsg;
}
