package com.danding.cds.declare.sdk.clear.base.callback.module;

import com.danding.cds.declare.sdk.model.tax.Tax;
import lombok.Data;

import java.util.Date;

/**
 * @Author: Raymond
 * @Date: 2020/7/24 13:58
 * @Description:
 */
@Data
public class TaxStatus extends Tax {
//    /**
//     * 清单号
//     */
//    private String invtNo;

    /**
     * 税单号
     */
    private String taxNo;

    /**
     * 回执时间
     */
    private Date returnTime;

    /**
     * 税单状态
     */
    private String status;

    /**
     * 缴款书编号
     */
    protected String entDutyNo;

    /**
     * 担保企业代码
     */
    protected String assureCode;

}
