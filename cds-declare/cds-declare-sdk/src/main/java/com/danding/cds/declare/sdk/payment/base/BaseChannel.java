package com.danding.cds.declare.sdk.payment.base;


import com.danding.cds.declare.sdk.CustomsSupport;
import com.danding.cds.declare.sdk.enums.PayCustomsChannel;

import java.io.Serializable;

/**
 * 基础渠道类
 */
public abstract class BaseChannel<T extends BaseToken> implements CustomsOperation, Serializable {
    public BaseChannel(CustomsSupport support) {
        this.support = support;
    }

    protected CustomsSupport support;
    /**
     * 设置支付渠道
     * @return
     */
    public abstract PayCustomsChannel getPayChannel();

    /**
     * 有需要token的渠道方式，需要提供从tokenJson转化为具体Token对象的方法
     * @param tokenJson
     * @return
     */
    public abstract T makeToken(String tokenJson);

}
