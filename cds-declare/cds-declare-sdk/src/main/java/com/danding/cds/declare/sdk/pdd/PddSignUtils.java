package com.danding.cds.declare.sdk.pdd;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.IOUtils;

import javax.xml.crypto.OctetStreamData;
import javax.xml.crypto.dsig.CanonicalizationMethod;
import javax.xml.crypto.dsig.TransformException;
import javax.xml.crypto.dsig.TransformService;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;


/**
 * SignUtils
 *
 * <AUTHOR>
 * @date 2020/7/22 17:42
 */
@Slf4j
public class PddSignUtils {

    public static String getDigestValue(byte[] bytes) throws NoSuchAlgorithmException, TransformException, IOException {
        TransformService ts = TransformService.getInstance(CanonicalizationMethod.INCLUSIVE_WITH_COMMENTS, "DOM");
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream();) {
            OctetStreamData data = new OctetStreamData(new ByteArrayInputStream(bytes));
            OctetStreamData newData = (OctetStreamData) ts.transform(data, null);
            IOUtils.copy(newData.getOctetStream(), baos);
            MessageDigest md = MessageDigest.getInstance("SHA1");
            md.update(baos.toByteArray());
            byte[] out = md.digest();
            return Base64.encodeBase64String(out);
        } catch (Exception e) {
            log.error("SIGN EXCEPTION={}", e.getMessage(), e);
            throw new RuntimeException("读取证书失败：", e);
        }
    }

}
