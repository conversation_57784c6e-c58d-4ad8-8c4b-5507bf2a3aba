package com.danding.cds.declare.sdk.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.Provider;
import java.security.SecureRandom;
import java.security.Security;

/**
 * <p></p>
 * User: <a href="mailto:<EMAIL>">严明明</a>
 * Date: 15/1/15
 * Time: 下午2:42
 */
public class AesUtil {
    private SecretKeySpec keySpec;
    private IvParameterSpec iv;
    private boolean useCFB = false;
    private boolean useECB = false;
    private static Provider sunJECProvider = Security.getProvider("SunJCE");
    private static final Logger logger = LoggerFactory.getLogger("AesUtil");
    public AesUtil(byte[] aesKey, byte[] iv) {
        if(aesKey != null && aesKey.length >= 16 && (iv == null || iv.length >= 16)) {
            if(iv == null) {
                iv = Md5Util.compute(aesKey);
            }
            this.keySpec = new SecretKeySpec(aesKey, "AES");
            this.iv = new IvParameterSpec(iv);
        } else {
            throw new RuntimeException("错误的初始密钥");
        }
    }

    public AesUtil(byte[] aesKey, boolean cfb) {
        if(aesKey != null && aesKey.length >= 16) {
            this.useCFB = cfb;
            this.keySpec = new SecretKeySpec(aesKey, "AES");
            this.iv = new IvParameterSpec(Md5Util.compute(aesKey));
        } else {
            throw new RuntimeException("错误的初始密钥");
        }
    }

    public AesUtil(byte[] aesKey, boolean cfb, boolean ecb) {
        if(aesKey != null && aesKey.length >= 16) {
            this.useCFB = cfb;
            this.useECB = ecb;
            this.keySpec = new SecretKeySpec(aesKey, "AES");
            this.iv = new IvParameterSpec(Md5Util.compute(aesKey));
        } else {
            throw new RuntimeException("错误的初始密钥");
        }
    }

    public byte[] encrypt(byte[] data) {
        Cipher cipher = null;

        try {
            if(this.useCFB) {
                cipher = Cipher.getInstance("AES/CFB/NoPadding", sunJECProvider);
                cipher.init(1, this.keySpec, this.iv);
            }
            if(this.useECB){
                cipher = Cipher.getInstance("AES/ECB/PKCS5Padding", sunJECProvider);
                cipher.init(Cipher.DECRYPT_MODE, keySpec);
            }else {
                cipher = Cipher.getInstance("AES/CBC/PKCS5Padding", sunJECProvider);
                cipher.init(1, this.keySpec, this.iv);
            }

            return cipher.doFinal(data);
        } catch (Exception var4) {
            logger.info("----AESUtil解密异常：e={}",var4);
            throw new RuntimeException(var4);
        }
    }

//    public byte[] decrypt(byte[] secret) {
//        Cipher cipher = null;
//        try {
//            if(this.useCFB) {
//                cipher = Cipher.getInstance("AES/CFB/NoPadding", sunJECProvider);
//                cipher.init(2, this.keySpec, this.iv);
//            }else if(this.useECB){
//                cipher = Cipher.getInstance("AES/ECB/PKCS5Padding", sunJECProvider);
//                cipher.init(2, this.keySpec, this.iv);
//            } else {
//                cipher = Cipher.getInstance("AES/CBC/PKCS5Padding", sunJECProvider);
//                cipher.init(2, this.keySpec, this.iv);
//            }
//
//            return cipher.doFinal(secret);
//        } catch (Exception var4) {
//            throw new RuntimeException(var4);
//        }
//    }

    public static byte[] randomKey(int size) {
        try {
            KeyGenerator e = KeyGenerator.getInstance("AES");
            e.init(size, new SecureRandom());
            return e.generateKey().getEncoded();
        } catch (Exception var2) {
            throw new RuntimeException(var2);
        }
    }

    public boolean isUseCFB() {
        return useCFB;
    }

    public void setUseCFB(boolean useCFB) {
        this.useCFB = useCFB;
    }

    public boolean isUseECB() {
        return useECB;
    }

    public void setUseECB(boolean useECB) {
        this.useECB = useECB;
    }
}
