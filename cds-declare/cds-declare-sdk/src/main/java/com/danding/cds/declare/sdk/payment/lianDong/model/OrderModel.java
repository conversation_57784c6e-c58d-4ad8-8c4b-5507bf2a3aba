package com.danding.cds.declare.sdk.payment.lianDong.model;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 * @date 2021/11/25
 */
@Data
public class OrderModel implements Serializable {

    private long id;

    /**
     * 订单编码
     */
    private String sn;

    /**
     * 应用id
     */
    private String appId;

    /**
     * 模块
     */
    private int subMod;

    /**
     * 订单总价
     */
    private BigDecimal totalPrice;

    /**
     * 用户id
     */
    private long userId;

    /**
     * 外部店铺id
     */
    private long outShopId;

    /**
     * 创建时间
     */
    private long createTime;


    /**
     * 支付时间
     */
    private long payTime;

    /**
     * 删除标记
     */
    private boolean delFlag;

    /**
     * 外部订单编号
     */
    private String outSn;




}
