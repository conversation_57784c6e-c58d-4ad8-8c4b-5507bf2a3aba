package com.danding.cds.declare.sdk.clear.zhejiang.builder;

import com.danding.cds.customs.district.api.enums.CustomsDistrictEnum;
import com.danding.cds.declare.ceb.domain.base.BaseTransfer;
import com.danding.cds.declare.ceb.domain.ceb625.CEB625Message;
import com.danding.cds.declare.ceb.domain.ceb625.InvtRefund;
import com.danding.cds.declare.ceb.domain.ceb625.InvtRefundHead;
import com.danding.cds.declare.ceb.domain.ceb625.InvtRefundList;
import com.danding.cds.declare.ceb.internal.enums.CebDeclareType;
import com.danding.cds.declare.ceb.internal.utils.CebDateUtil;
import com.danding.cds.declare.sdk.constant.CustomsDistrictCons;
import com.danding.cds.declare.sdk.enums.BusinessTypeEnums;
import com.danding.cds.declare.sdk.model.inventory.CustomsInventoryItemInfo;
import com.danding.cds.declare.sdk.model.refund.WarpRefundOrderInfo;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

public class ZJCebInventoryRefundBuilder implements Serializable {

    private WarpRefundOrderInfo info;

    private String dxpId;

    public ZJCebInventoryRefundBuilder(WarpRefundOrderInfo info, String dxpId) {
        this.info = info;
        this.dxpId = dxpId;
    }

    public CEB625Message build() {
        return buildOrderMessage();
    }

    private CEB625Message buildOrderMessage() {
        CEB625Message ceb625Message = new CEB625Message();
        InvtRefund invtRefund = buildInvtRefund();
        List<InvtRefund> invtRefunds = Lists.newArrayList(invtRefund);
        BaseTransfer baseTransfer = buildBaseTransfer();
        ceb625Message.setGuid(invtRefund.invtRefundHead.guid);
        ceb625Message.invtRefund = invtRefunds;
        ceb625Message.baseTransfer = baseTransfer;
        return ceb625Message;

    }

    private BaseTransfer buildBaseTransfer() {
        BaseTransfer baseTransfer = new BaseTransfer();
        String declareCompanyCode = this.info.getDeclareCompanyDTO().getCebCode();
        String declareCompanyName = this.info.getDeclareCompanyDTO().getCebName();
        baseTransfer.setCopCode(declareCompanyCode);
        baseTransfer.setCopName(declareCompanyName);
        baseTransfer.setDxpMode("DXP");
        baseTransfer.setDxpId(dxpId);
        return baseTransfer;
    }

    private InvtRefund buildInvtRefund() {
        InvtRefund invtRefund = new InvtRefund();
        InvtRefundHead invtRefundHead = buildIvRefundHead();
        List<InvtRefundList> invtRefundLists = buildInvtRefundList();
        invtRefund.invtRefundHead = invtRefundHead;
        invtRefund.invtRefundList = invtRefundLists;
        return invtRefund;
    }

    private List<InvtRefundList> buildInvtRefundList() {
        List<InvtRefundList> invtRefundLists = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(this.info.getListCustomsInventoryItemInfo())) {

            for (CustomsInventoryItemInfo itemDO : this.info.getListCustomsInventoryItemInfo()) {
                InvtRefundList invtRefundList = new InvtRefundList();
                String codeTs = itemDO.getHsCode();
                if (codeTs == null) codeTs = "";
                String unit = itemDO.getUnit();
                if (unit == null) unit = "";
                invtRefundList.setGcode(codeTs.trim());
                invtRefundList.setGnum(String.valueOf(itemDO.getGnum()));
                invtRefundList.setNote("");
                invtRefundList.setQty(String.valueOf(itemDO.getCount()));
                invtRefundList.setGname(itemDO.getItemName());
                invtRefundList.setUnit(unit.trim());
                invtRefundLists.add(invtRefundList);
            }
        }
        return invtRefundLists;
    }

    private InvtRefundHead buildIvRefundHead() {
        InvtRefundHead invtRefundHead = new InvtRefundHead();
        invtRefundHead.guid = UUID.randomUUID().toString().toUpperCase();
        invtRefundHead.setAppType(CebDeclareType.CREATE.getType());
        invtRefundHead.setAppTime(CebDateUtil.format(new Date(), CebDateUtil.defaultCebDateStringFormat));
        invtRefundHead.setAppStatus("2");
        invtRefundHead.setOrderNo(this.info.getCustomsInventoryDto().getOrderNo());

        invtRefundHead.setEbpCode(this.info.getEbpCompanyDTO().getCebCode());
        //<companyName> 电商平台名称  VARCHAR2(200)   是   电商平台在跨境电商综合服务平台的备案名称
        invtRefundHead.setEbpName(this.info.getEbpCompanyDTO().getCebName());
        //<eCommerceCode>   电商企业代码  VARCHAR2(20)    是   电商企业在跨境平台备案编码
        invtRefundHead.setEbcCode(this.info.getEbcCompanyDTO().getCebCode());
        //<eCommerceName>   电商企业名称  NVARCHAR2(200)  是   电商企业在跨境平台备案的企业名称
        invtRefundHead.setEbcName(this.info.getEbcCompanyDTO().getCebName());
        /**
         * 关区代码
         */
        invtRefundHead.setCustomsCode("2924");
        if ("L2923B21A004".equals(info.getCustomsInventoryDto().getBookNo())) {
            invtRefundHead.setCustomsCode("2923");
        } else if (CustomsDistrictCons.YW_2925_CUSTOMS_DISTRICT.contains(info.getCustomsInventoryDto().getBookNo())) {
            invtRefundHead.setCustomsCode("2925");
        }
        String customsAreaCode = info.getCustomsAreaCode();
        if (StringUtils.isNotBlank(customsAreaCode)) {
            invtRefundHead.setCustomsCode(customsAreaCode);
        }
        //根据退货单里面的关区代码取关区代码
        if (!StringUtils.isEmpty(this.info.getRefundOrderInfoDto().getCustomsCode())) {
            CustomsDistrictEnum customsDistrictEnum = CustomsDistrictEnum.getEnum(this.info.getRefundOrderInfoDto().getCustomsCode());
            if (Objects.nonNull(customsDistrictEnum) && !StringUtils.isEmpty(customsDistrictEnum.getPortCode())) {
                invtRefundHead.setCustomsCode(customsDistrictEnum.getPortCode());
            }
        }
        //invtRefundHead.setCustomsCode(this.info.getCustomsInventoryDto().getCustoms());
        //物流运单编号、物流企业代码、物流企业名称
        invtRefundHead.setLogisticsNo(this.info.getCustomsInventoryDto().getLogisticsNo());
        //<logisCompanyName>	物流企业名称	VARCHAR2(200)	是	物流企业在跨境平台备案的企业名称
        invtRefundHead.setLogisticsName(this.info.getLogisticsCompanyDTO().getCebName());
        //<logisCompanyCode>	物流企业代码	VARCHAR2(20)	是	物流企业在跨境平台备案编码
        invtRefundHead.setLogisticsCode(this.info.getLogisticsCompanyDTO().getCebCode());
        //订购人信息
        /**
         * 证件类型
         */
        invtRefundHead.setBuyerIdType(this.info.getCustomsInventoryDto().getBuyerIdType());
        /**
         * 订购人证件号码
         */
        invtRefundHead.setBuyerIdNumber(this.info.getCustomsInventoryDto().getBuyerIdNumber());
        /**
         * 购买人电话
         */
        invtRefundHead.setBuyerTelephone(this.info.getCustomsInventoryDto().getBuyerTelNumber());
        /**
         * 购买人姓名
         */
        invtRefundHead.setBuyerName(this.info.getCustomsInventoryDto().getBuyerName());
        /**
         * 海关业务单号
         */
        invtRefundHead.setInvtNo(this.info.getCustomsInventoryDto().getInventoryNo());
        invtRefundHead.setPreNo(this.info.getCustomsInventoryDto().getPreNo());
        //如果type为芥舟则用清单sn赋值CopNo
        if (Objects.equals(info.getBusinessType(), BusinessTypeEnums.JIE_ZHOU.getCode()) && !StringUtils.isEmpty(this.info.getCustomsInventoryDto().getSn())) {
            invtRefundHead.setCopNo(this.info.getCustomsInventoryDto().getSn());
        } else {
            invtRefundHead.setCopNo(String.valueOf(this.info.getRefundOrderInfoDto().getId()));
        }
        invtRefundHead.setReason(this.info.getRefundOrderInfoDto().getReason());
        invtRefundHead.setAgentCode(this.info.getDeclareCompanyDTO().getCebCode());
        invtRefundHead.setAgentName(this.info.getDeclareCompanyDTO().getCebName());
        return invtRefundHead;
    }
}
