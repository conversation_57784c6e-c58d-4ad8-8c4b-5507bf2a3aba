package com.danding.cds.declare.sdk.payment.lianDong.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.umf.api.payments.Amount;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 商品信息
 * @date 2021/11/26
 */
@Data
public class Item implements Serializable {

    /**
     * 商户系统id.
     */
    @JSONField(name = "mer_item_id")
    private String mer_item_id;

    /**
     * 商户系统id.
     */
    @JSONField(name = "type")
    private String type;

    /**
     * 商品名称
     */
    @JSONField(name = "name")
    private String name;

    /**
     * 商品描述
     */
    @JSONField(name = "description")
    private String description;

    /**
     * 金额. 商品的价格.
     */
    @JSONField(name = "amount")
    private Amount amount;

    /**
     *数量. 商品的数量.
     */
    @JSONField(name = "quantity")
    private String quantity;
}
