package com.danding.cds.declare.sdk.model.callback;

import lombok.Data;

import java.io.Serializable;

@Data
public class BookItemCallback implements Serializable {
    /**
     * 仓库账册号
     */
    private String bwsNo;
    /**
     * 变更次数
     */
    private String chgTmsCnt;
    /**
     * 商品序号
     */
    private String gdsSeqno;
    /**
     * 最近入仓(核增）日期
     */
    private String inDate;
    /**
     * 商品料号
     */
    private String gdsMtno;
    /**
     * 商品编码
     */
    private String gdecd;
    /**
     * 商品名称
     */
    private String gdsNm;
    /**
     * 商品规格型号
     */
    private String gdsSpcfModelDesc;
    /**
     * 国别代码
     */
    private String natcd;
    /**
     * 申报计量单位代码
     */
    private String dclUnitcd;
    /**
     * 法定计量单位代码
     */
    private String lawfUnitcd;
    /**
     * 第二法定计量单位代码
     */
    private String secdLawfUnitcd;
    /**
     * 申报单价金额
     */
    private String dclUprcAmt;
    /**
     * 申报币制代码
     */
    private String dclCurrcd;
    /**
     * 平均美元单价
     */
    private String avgPrice;
    /**
     * 库存美元总价
     */
    private String totalAmt;
    /**
     * 入仓数量
     */
    private String inQty;
    /**
     * 入仓法定数量
     */
    private String inLawfQty;
    /**
     * 第二入仓法定数量
     */
    private String inSecdLawfQty;
    /**
     * 实增数量
     */
    private String actlIncQty;
    /**
     * 实减数量
     */
    private String actlRedcQty;
    /**
     * 预增数量
     */
    private String prevdIncQty;
    /**
     * 预减数量
     */
    private String prevdRedcQty;
    /**
     * 最近出仓(区）日期
     */
    private String outDate;
    /**
     * 存储(监管）期限
     */
    private String limitDate;
    /**
     * 设备入区方式代码 记账式系统自动返填,1:一线入区、2：二线入区、3:结转入区
     */
    private String inType;
    /**
     * 记账清单编号
     */
    private String invtNo;
    /**
     * 记账清单商品序号
     */
    private String invtGNo;
    /**
     * 海关执行标记代码
     */
    private String cusmExeMarkcd;
    /**
     * 备注
     */
    private String rmk;
    /**
     * 修改标记 0-未修改 1-修改 2-删除 3-增加
     */
    private String modfMarkcd;

    /**
     * 商品来源标识: 1-境外重点料件，2-境外普通料件，3-国内采购料件，4-专账成品转入料件，默认为空
     */
    private String col1;
}
