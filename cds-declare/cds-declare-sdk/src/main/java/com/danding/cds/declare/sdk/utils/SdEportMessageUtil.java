package com.danding.cds.declare.sdk.utils;

import com.alibaba.fastjson.JSON;
import com.danding.cds.common.config.EnvironmentConfig;
import com.danding.cds.declare.ceb.internal.enums.CebMessageEnum;
import com.danding.cds.declare.ceb.internal.utils.XMLUtil;
import com.danding.cds.declare.sdk.bean.dto.CustomsMsgDto;
import com.danding.cds.declare.sdk.clear.zhejiang.ZJAgentToken;
import com.danding.cds.declare.sdk.clear.zhejiang.encry.SdEportKeyInfo;
import com.danding.cds.declare.sdk.model.WrapBeanInfo;
import com.danding.cds.declare.sdport.utils.SdEportWebServiceUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @program: cds-center
 * @description: 山东电子口岸消息工具
 * @author: 潘本乐（Belep）
 * @create: 2022-06-08 20:41
 **/
@Slf4j
public class SdEportMessageUtil {

    /**
     * 申报调用
     *
     * @param info 申报信息
     */
    public static void invokeDeclare(WrapBeanInfo info) {

        String cebCode = info.getDeclareCompanyCebCode();
        String interfaceUrl = SdEportWebServiceUtil.TEST_MESSAGE_INTERFACE_URL;
        String cebSignMsg = null;
        String cebTag = null;
        if (EnvironmentConfig.isOnline()) {
            interfaceUrl = SdEportWebServiceUtil.PRO_MESSAGE_SEND_URL;
            // 获取清单CEB加签报文
            CustomsMsgDto customsMsgDto = CebMessageUtil.buildCustomsMessage(info);
            cebSignMsg = customsMsgDto.getCebSignMsg();
            cebTag = customsMsgDto.getCebMsgEnum().getTag();
        } else {
            Tuple<CebMessageEnum, Object> cebMsgTuple = CebMessageUtil.buildCebMsg(info, "DXPENT0000470256");
            try {
                cebSignMsg = XMLUtil.convertToXml(cebMsgTuple.getS());
                cebTag = cebMsgTuple.getF().getTag();
            } catch (Exception e) {
                log.error("申报单号: {} , 测试环境异常：{}，地址：{}", e.getMessage(), e);
                throw new RuntimeException("CEBMessage转换异常:" + e.getMessage(), e);
            }
        }
        log.info("申报单号: {} , 发送山东电子口岸地址：{}", info.getDeclareNos(), interfaceUrl);
        log.info("申报单号: {} , 发送山东电子口岸信息：{}", info.getDeclareNos(), cebSignMsg);
        ZJAgentToken token = TokenUtil.getZjPortToken(cebCode);
        SdEportKeyInfo sdEportKeyInfo = token.getSdEportKeyInfo();
        log.info("申报单号: {} , SdEportKeyInfo：{}", info.getDeclareNos(), JSON.toJSON(sdEportKeyInfo));
        String fileName = SdEportWebServiceUtil.sendSingleOfUploadFiles(interfaceUrl, cebTag, cebSignMsg, sdEportKeyInfo.getLoginName(), sdEportKeyInfo.getLoginPassword(), sdEportKeyInfo.getPfxPath(), sdEportKeyInfo.getPfxPassword());
        log.info("申报单号: {} , 发送山东电子口岸成功，文件名称为: {}", info.getDeclareNos(), fileName);
    }
}
