package src;

import com.danding.cds.common.utils.JSONUtils;
import com.danding.cds.declare.ceb.domain.ceb816.CEB816Message;
import com.danding.cds.declare.ceb.internal.utils.XMLUtil;
import com.thoughtworks.xstream.io.xml.Dom4JReader;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.junit.Test;

import javax.xml.bind.annotation.DomHandler;
import javax.xml.parsers.DocumentBuilder;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Create 2021/9/2  15:17
 * @Describe
 **/
public class xmlTest {
    private String xml = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>" +
            "<CEB816Message xmlns=\"http://www.chinaport.gov.cn/ceb\" version=\"1.0\" guid=\"b1ed7956-b2b3-4b03-8199-72ff15741017\">" +
            "    <Tax>" +
            "        <TaxHeadRd>" +
            "            <guid>b1ed7956-b2b3-4b03-8199-72ff15741017</guid>" +
            "            <returnTime>20210902104147403</returnTime>" +
            "        </TaxHeadRd>" +
            "        <TaxListRd>" +
            "            <gnum>1</gnum>" +
            "        </TaxListRd>" +
            "    </Tax>" +
            "</CEB816Message>";

    @Test
    public void test() {
        ceshiyixai("Tax", "TaxHeadRd", "guid");
    }

    private void ceshiyixai(String... strings) {
        Document document = null;
        try {
            document = DocumentHelper.parseText(xml);
        } catch (DocumentException e) {
            e.printStackTrace();
        }
        Element rootElement = document.getRootElement();
        int length = strings.length;
        Element ele = rootElement;
        for (int i = 0; i < length; i++) {
            ele = ele.element(strings[i]);
        }
        System.out.println(ele.getText());
    }


    private void getElement(List<Element> elements, String key) {
        for (Element e : elements) {
            if (e.elements().size() != 0) {
                System.out.println(e.getName() + ":");
                getElement(e.elements(), key);
            } else {
                if (e.getName().equals(key)) {

                }
                System.out.println(e.getName() + ":" + e.getText());
            }
        }
    }

    @Test
    public void test1() throws Exception {
        String xml = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n" +
                "<CEB816Message xmlns=\"http://www.chinaport.gov.cn/ceb\" version=\"1.0\" guid=\"b1ed7956-b2b3-4b03-8199-72ff15741017\">\n" +
                "    <Tax>\n" +
                "        <TaxHeadRd>\n" +
                "            <guid>b1ed7956-b2b3-4b03-8199-72ff15741017</guid>\n" +
                "            <returnTime>20210902104147403</returnTime>\n" +
                "            <invtNo>29242021I762714338</invtNo>\n" +
                "            <taxNo>29242021I762714338_0</taxNo>\n" +
                "            <customsTax>0.0</customsTax>\n" +
                "            <valueAddedTax>31.44</valueAddedTax>\n" +
                "            <consumptionTax>0.0</consumptionTax>\n" +
                "            <status>1</status>\n" +
                "            <entDutyNo></entDutyNo>\n" +
                "            <note></note>\n" +
                "            <assureCode>330766K00W</assureCode>\n" +
                "            <ebcCode>330766K00W</ebcCode>\n" +
                "            <logisticsCode>31209606ZC</logisticsCode>\n" +
                "            <agentCode>330766K00W</agentCode>\n" +
                "            <customsCode>2924</customsCode>\n" +
                "            <orderNo>XP1821090121501703439925007999</orderNo>\n" +
                "            <logisticsNo>777055901422411</logisticsNo>\n" +
                "        </TaxHeadRd>\n" +
                "        <TaxListRd>\n" +
                "            <gnum>1</gnum>\n" +
                "            <gcode>2309109000</gcode>\n" +
                "            <taxPrice>499.0</taxPrice>\n" +
                "            <customsTax>0.0</customsTax>\n" +
                "            <valueAddedTax>31.44</valueAddedTax>\n" +
                "            <consumptionTax>0.0</consumptionTax>\n" +
                "        </TaxListRd>\n" +
                "    </Tax>\n" +
                "</CEB816Message>";
        xml = xml.replace("xmlns=\"http://www.chinaport.gov.cn/ceb\"", "");
        CEB816Message ceb816Message = XMLUtil.converyToJavaBean(xml, CEB816Message.class);
    }
}
