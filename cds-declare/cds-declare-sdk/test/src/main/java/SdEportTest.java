import com.alibaba.fastjson.JSON;
import com.danding.cds.declare.ceb.internal.utils.XMLUtil;
import com.danding.cds.declare.sdk.clear.base.callback.CallbackType;
import com.danding.cds.declare.sdk.utils.CebMessageUtil;
import com.danding.cds.declare.sdport.bean.SdEportCalllbackDto;
import com.danding.cds.declare.sdport.client.EnteLoginInfo;
import com.danding.cds.declare.sdport.client.FileInfo;
import com.danding.cds.declare.sdport.utils.SdEportWebServiceUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.util.CollectionUtils;

import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * @program: cds-center
 * @description: 山东电子口岸
 * @author: 潘本乐（Belep）
 * @create: 2022-06-10 14:09
 **/
@Slf4j
public class SdEportTest {

    String dxpId = "DXPENT0000470256";
    String cebMessage = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><ceb:CEB311Message xmlns:ceb=\"http://www.chinaport.gov.cn/ceb\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" guid=\"5EBC20B7-2789-47E6-864E-B6A8BC9FC860\" version=\"1.0\"><ceb:Order><ceb:OrderHead><ceb:guid>5EBC20B7-2789-47E6-864E-B6A8BC9FC860</ceb:guid><ceb:appType>1</ceb:appType><ceb:appTime>20220610171520</ceb:appTime><ceb:appStatus>2</ceb:appStatus><ceb:orderType>I</ceb:orderType><ceb:orderNo>22060981589262</ceb:orderNo><ceb:ebpCode>4411976FWK</ceb:ebpCode><ceb:ebpName>广州唯品会电子商务有限公司</ceb:ebpName><ceb:ebcCode>3726680002</ceb:ebcCode><ceb:ebcName>威海鲸邮天下互联科技有限公司</ceb:ebcName><ceb:goodsValue>58.65</ceb:goodsValue><ceb:freight>0.0100</ceb:freight><ceb:discount>0.0000</ceb:discount><ceb:taxTotal>5.3400</ceb:taxTotal><ceb:acturalPaid>64.0000</ceb:acturalPaid><ceb:currency>142</ceb:currency><ceb:buyerRegNo>13606545076</ceb:buyerRegNo><ceb:buyerName>郭靖</ceb:buyerName><ceb:buyerTelephone>13606545076</ceb:buyerTelephone><ceb:buyerIdType>1</ceb:buyerIdType><ceb:buyerIdNumber>530111198206222142</ceb:buyerIdNumber><ceb:payCode>3301961Q9Q</ceb:payCode><ceb:payName>浙江唯品会支付服务有限公司</ceb:payName><ceb:payTransactionId>20220609000171489131</ceb:payTransactionId><ceb:consignee>郭靖</ceb:consignee><ceb:consigneeTelephone>13606545076</ceb:consigneeTelephone><ceb:consigneeAddress>云南省_玉溪市_易门县_云南省玉溪市易门县龙泉街道</ceb:consigneeAddress></ceb:OrderHead><ceb:OrderList><ceb:gnum>1</ceb:gnum><ceb:itemName>RedSeal红印儿童蜂胶牙膏75G/支</ceb:itemName><ceb:gmodel>75G/支</ceb:gmodel><ceb:unit>012</ceb:unit><ceb:qty>3</ceb:qty><ceb:price>19.55</ceb:price><ceb:totalPrice>58.65</ceb:totalPrice><ceb:currency>142</ceb:currency><ceb:country>609</ceb:country></ceb:OrderList></ceb:Order><ceb:BaseTransfer><ceb:copCode>3726680002</ceb:copCode><ceb:copName>威海鲸邮天下互联科技有限公司</ceb:copName><ceb:dxpMode>DXP</ceb:dxpMode><ceb:dxpId>DXPENT0000470256</ceb:dxpId></ceb:BaseTransfer><ds:Signature xmlns:ds=\"http://www.w3.org/2000/09/xmldsig#\"><ds:SignedInfo><ds:CanonicalizationMethod Algorithm=\"http://www.w3.org/TR/2001/REC-xml-c14n-20010315#WithComments\"/><ds:SignatureMethod Algorithm=\"http://www.w3.org/2000/09/xmldsig#rsa-sha1\"/><ds:Reference URI=\"\"><ds:Transforms><ds:Transform Algorithm=\"http://www.w3.org/2000/09/xmldsig#enveloped-signature\"/></ds:Transforms><ds:DigestMethod Algorithm=\"http://www.w3.org/2000/09/xmldsig#sha1\"/><ds:DigestValue>jGAKFHYQOGTSUbQkdlm8rQsNNQM=</ds:DigestValue></ds:Reference></ds:SignedInfo><ds:SignatureValue>Jc3TU88kfMcy2xeeG9amo8mOw/eCC7Mm1TrRmn5EJpYzAvqKA6613yeJ12T1h9RePc3VshYY6Evt\n" +
            "wQBod04XURkldG/4dNAjQ1I4HZU44dWAIfL179mkD3g/Ke0pNW7tNh1/LNxzxt5iQD+pTfF21z+L\n" +
            "k0t8+eiPAYGmEv7h9H4=</ds:SignatureValue><ds:KeyInfo><ds:KeyName>72339069014666613</ds:KeyName><ds:X509Data><ds:X509Certificate>MIIFCzCCBHSgAwIBAgIIAQEAAAAAbXUwDQYJKoZIhvcNAQEFBQAwfDELMAkGA1UEBhMCY24xFTAT\n" +
            "BgNVBAoeDE4tVv11NVtQU+NcuDEVMBMGA1UECx4Mi8FOZnuhdAZOLV/DMQ0wCwYDVQQIHgRTF06s\n" +
            "MSEwHwYDVQQDHhhOLVb9dTVbUE4aUqGLwU5me6F0Bk4tX8MxDTALBgNVBAceBFMXTqwwHhcNMjIw\n" +
            "NTEwMDAwMDAwWhcNNDIwNTEwMDAwMDAwWjCBzjELMAkGA1UEBhMCQ04xDzANBgNVBAgMBua1meax\n" +
            "nzEPMA0GA1UEBwwG5p2t5beeMTMwMQYDVQQKDCrlqIHmtbfpsrjpgq7lpKnkuIvkupLogZTnp5Hm\n" +
            "ioDmnInpmZDlhazlj7gxMzAxBgNVBAsMKuWogea1t+myuOmCruWkqeS4i+S6kuiBlOenkeaKgOac\n" +
            "iemZkOWFrOWPuDEzMDEGA1UEAwwq5aiB5rW36bK46YKu5aSp5LiL5LqS6IGU56eR5oqA5pyJ6ZmQ\n" +
            "5YWs5Y+4MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDhIiTy2ENyHBBaIZhyuymy1WTWnTJ5\n" +
            "aK0EQu5eJfNcli60PyPSrKuf+vbJ/PsWw1Y1TxDiAp3sqChWJv/Uce2/3DDlwTlIbNm/LPmkMdX5\n" +
            "Js00HBF6nYbcZa2vGsL41jmMDKaQAK5XW+7hwBSiv7Ih4xPWzGjd21rz/6LZ0VKHtQIDAQABgRkA\n" +
            "1tC5+rXn19PStc7x1qTK6bncwO3W0NDEo4ICJjCCAiIwDgYDVR0PAQH/BAQDAgP4MAkGA1UdEwQC\n" +
            "MAAwHwYDVR0jBBgwFoAU+XWjeEULQmjCBFOr68NOPlR4dGAwHQYDVR0OBBYEFMTrkGsJsnA4R6w2\n" +
            "+nRsR96UMCuaMGQGA1UdIARdMFswWQYAMFUwUwYIKwYBBQUHAgEWR2h0dHBzOi8vd3d3LmNoaW5h\n" +
            "cG9ydC5nb3YuY24vdGNtc2ZpbGUvdS9jbXMvd3d3LzIwMjIwNC8xMjEzMjk0OHh0NnAucGRmMH8G\n" +
            "A1UdHwR4MHYwdKByoHCGbmxkYXA6Ly9sZGFwLmNoaW5hcG9ydC5nb3YuY246Mzg5L2NuPWNybDAx\n" +
            "MDEwMCxvdT1jcmwwMSxvdT1jcmwsYz1jbj9jZXJ0aWZpY2F0ZVJldm9jYXRpb25MaXN0P2Jhc2U/\n" +
            "Y249Y3JsMDEwMTAwMD4GCCsGAQUFBwEBBDIwMDAuBggrBgEFBQcwAYYiaHR0cDovL29jc3AuY2hp\n" +
            "bmFwb3J0Lmdvdi5jbjo4ODAwLzAZBgorBgEEAalDZAUGBAsMCTAwMDAxMDMxODAZBgorBgEEAalD\n" +
            "ZAUJBAsMCTAwMDAxMDMxODASBgorBgEEAalDZAIBBAQMAjE5MBIGCisGAQQBqUNkAgQEBAwCQ0Ew\n" +
            "HgYIYIZIAYb4QwkEEgwQMDAwMDAwMDAwMDAxMDQxNzAgBggqgRzQFAQBBAQUExI5MTM3MTAwME1B\n" +
            "M1JOS0ZDMEMwDQYJKoZIhvcNAQEFBQADgYEANX6AALGYaB+ekhjzWP0frxd59W2owQuizYvxuyhv\n" +
            "D1EFZVPcwgwxxQ9Itvn5N1/sjY5c65Ky2uMzmG4YWeA1JN9sRnAq3erGJ33BAy5kYSQYmTm1a9Rd\n" +
            "KaTeFIUUFYYzaYyBza44I+mk/SUVhXW7jIdKGR9FmOzceHTbzn6hmCg=</ds:X509Certificate></ds:X509Data></ds:KeyInfo></ds:Signature></ceb:CEB311Message>";

    String name = "bigchaintest";
    String password = "Gyst8888";
    String pfxPath = "classpath:pfx/test/MA3RNKFC0.pfx";
    String pfxPassword = "123456";

    @Test
    public void sendDxpMessage() throws Exception {

        String dxpMessage = CebMessageUtil.buildCustomsMsg(cebMessage.getBytes(StandardCharsets.UTF_8), dxpId, "CEB311Message");
        log.info("发送信息：{}", dxpMessage);
        // 申报上传文件
        SdEportWebServiceUtil.sendSingleOfUploadFiles(SdEportWebServiceUtil.TEST_MESSAGE_INTERFACE_URL, dxpMessage, "CEB311Message", name, password, pfxPath, pfxPassword);
    }

    @Test
    public void sendCebMessage() throws Exception {

        log.info("发送信息：{}", cebMessage);
        // 申报上传文件
        String fileName = SdEportWebServiceUtil.sendSingleOfUploadFiles(SdEportWebServiceUtil.TEST_MESSAGE_INTERFACE_URL, "CEB311Message", cebMessage, name, password, pfxPath, pfxPassword);
        log.info("发送成功，文件名称:{}", fileName);
    }

    @Test
    public void sendAndCallbackCebMessage() throws Exception {
        sendCebMessage();
        download();
    }


    @Test
    public void download() throws Exception {
        // 申报回执下载
        EnteLoginInfo enteLoginInfo = SdEportWebServiceUtil.getEnteLoginInfo(name, password, pfxPath, pfxPassword);
        // 获取所有的待下载文件
        List<FileInfo> fileInfoList = SdEportWebServiceUtil.waitDownloadList(SdEportWebServiceUtil.TEST_MESSAGE_INTERFACE_URL, enteLoginInfo);
        if (CollectionUtils.isEmpty(fileInfoList)) {
            log.info("文件下载数据为空");
        }
        for (FileInfo fileInfo : fileInfoList) {
            // 下载单个文件
            List<String> eportCallbackList = SdEportWebServiceUtil.downloadOneFile(SdEportWebServiceUtil.TEST_MESSAGE_INTERFACE_URL, enteLoginInfo, fileInfo.getFileTempID());

            if (CollectionUtils.isEmpty(eportCallbackList)) {
                continue;
            }
            for (String eportCallback : eportCallbackList) {
                log.info("山东电子口岸原始回执:{}", eportCallback);
                // 判断下是不是海关总署回执，
                CallbackType callbackType = CebMessageUtil.getCebCallbackType(eportCallback);
                if (!CallbackType.NULL.equals(callbackType)) {
                    log.info("监听到的海关回执包含C单总署回执报文 ,执行下回执解析处理");
                } else {
                    SdEportCalllbackDto sdEportCalllbackDto = XMLUtil.converyToJavaBean(eportCallback, SdEportCalllbackDto.class);
                    if (sdEportCalllbackDto.verifyFailed()) {
                        log.error("山东电子口岸回执，申报校验失败：{}", JSON.toJSONString(sdEportCalllbackDto));
                    }
                }
            }

            // 通知下山东海关
            SdEportWebServiceUtil.notifySdeportDownloadSuccess(SdEportWebServiceUtil.TEST_MESSAGE_INTERFACE_URL, enteLoginInfo, fileInfo.getFileTempID());
            System.out.println(eportCallbackList);
        }
    }


    @Test
    public void xmlToObj() throws Exception {

        String xml = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                "<ResponseMessage>\n" +
                "        <XML_TYPE>C</XML_TYPE>\n" +
                "        <XML_NAME>SDEPORT_RECEIPT_C_311_20220610145155020_5EBC20B7-2.xml</XML_NAME>\n" +
                "        <CLASS_NAME>订单报文</CLASS_NAME>\n" +
                "        <APP_CODE>311</APP_CODE>\n" +
                "        <FILE_DATE_TIME>2022-06-10 14:51:55</FILE_DATE_TIME>\n" +
                "        <FILE_ORIGINAL_NAME>2022061014:51:40_de6435b796ae456fa84df66e08e92697.xml</FILE_ORIGINAL_NAME>\n" +
                "        <GUID>5EBC20B7-2789-47E6-864E-B6A8BC9FC860</GUID>\n" +
                "        <ACK>0</ACK>\n" +
                "        <MSG_INFO/>\n" +
                "        <ERROR_INFO/>\n" +
                "</ResponseMessage>";

        SdEportCalllbackDto sdEportReceiveInfoDto = XMLUtil.converyToJavaBean(xml, SdEportCalllbackDto.class);
        System.out.println(sdEportReceiveInfoDto);
    }
}
