/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.danding.cds.entity.DTO;

import com.danding.cds.entity.Attachment;
import com.danding.cds.entity.Browse;
import com.danding.cds.entity.MailCC;
import com.danding.cds.entity.enums.MailStatus;
import com.danding.component.common.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

/**
 * 邮件实体类
 *
 * <AUTHOR>
 * @since 2022-08-25
 */
@Data
@ApiModel(value = "mailPoolDTO对象", description = "邮件实体表")
public class MailPoolDTO extends Page implements Serializable {

	private Long id;

	private String ids;

	/**
	 * 外部邮件id
	 */
	@ApiModelProperty(value = "外部邮件id")
	private String messageId;

	/**
	 * 邮件主题
	 */
	@ApiModelProperty(value = "邮件主题")
	private String subject;

	/**
	 * 发件人名称
	 */
	@ApiModelProperty(value = "发件人名称")
	private String addresser;

	/**
	 * 发件人名称
	 */
	@ApiModelProperty(value = "发件人名称")
	private String addresserName;

	/**
	 * 发件人邮箱账号
	 */
	@ApiModelProperty(value = "发件人邮箱账号")
	private String addresserAccount;

	/**
	 * 抄送
	 */
	@ApiModelProperty(value = "抄送")
	private String ccJson;

	/**
	 * 密抄
	 */
	@ApiModelProperty(value = "密抄")
	private String bccJson;

	/**
	 * 收件人
	 */
	@ApiModelProperty(value = "收件人Json串")
	private String toMailJson;

	/**
	 * 附件
	 */
	@ApiModelProperty(value = "附件")
	private String attachmentJson;

	/**
	 * 邮件内容
	 */
	@ApiModelProperty(value = "邮件内容")
	private String content;

	/**
	 * 处理状态：1待处理，2处理中，3无需处理，4已挂起，5已完成
	 */
	@ApiModelProperty(value = "处理状态：1待处理，2处理中，3无需处理，4已挂起，5已完成")
	private Integer status;

	/**
	 * 处理人
	 */
	@ApiModelProperty(value = "处理人")
	private Long userId;

	/**
	 * 处理人名称
	 */
	@ApiModelProperty(value = "处理人名称")
	private String userName;

	/**
	 * 业务类型
	 */
	@ApiModelProperty(value = "业务类型")
	private String code;

	/**
	 * 业务类型
	 */
	@ApiModelProperty(value = "业务类型")
	private List<String> codes;

	/**
	 * 业务类型
	 */
	@ApiModelProperty(value = "业务类型名称")
	private String codeName;

	/**
	 * 关联单号
	 */
	@ApiModelProperty(value = "关联单号")
	private String associatedNumber;

	/**
	 * 邮件来源
	 */
	@ApiModelProperty(value = "邮件来源")
	private String source;


	/**
	 * 邮件来源
	 */
	@ApiModelProperty(value = "邮件来源")
	private List<String> sources;

	/**
	 * 接收时间
	 */
	@ApiModelProperty(value = "接收时间")
	private Long receiptTime;


	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

	/**
	 * 1认领，2分配，3转交，4处理，5重新处理
	 */
	private Integer type;

	/**
	 * 认领状态 1待认领，2已认领
	 */
	private Integer claimStatus;

	private Long startTime;

	private Long endTime;

	/**
	 * 浏览列表
	 */
	private List<Browse> browseList;

	private List<Integer>statusList;

	/**
	 * 业务类型code
	 */
	private List<String> codeList;


}
