/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.danding.cds.entity.VO;

import com.danding.cds.entity.Attachment;
import com.danding.cds.entity.Browse;
import com.danding.cds.entity.MailCC;
import com.danding.cds.entity.enums.MailStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 邮件实体类
 *
 * <AUTHOR>
 * @since 2022-08-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "mailPoolVO对象", description = "邮件实体表")
public class MailPoolVO  implements Serializable {
	private static final long serialVersionUID = 1L;


	private Long id;

	/**
	 * 外部邮件id
	 */
	@ApiModelProperty(value = "外部邮件id")
	private String messageId;

	/**
	 * 邮件主题
	 */
	@ApiModelProperty(value = "邮件主题")
	private String subject;

	/**
	 * 发件人名称
	 */
	@ApiModelProperty(value = "发件人名称")
	private String addresserName;

	/**
	 * 发件人邮箱账号
	 */
	@ApiModelProperty(value = "发件人邮箱账号")
	private String addresserAccount;

	/**
	 * 邮件内容
	 */
	@ApiModelProperty(value = "邮件内容")
	private String content;

	/**
	 * 附件
	 */
	@ApiModelProperty(value = "附件")
	private String attachmentJson;

	/**
	 * 协议
	 */
	@ApiModelProperty(value = "浏览列表")
	private String browseJson;

	/**
	 * 处理状态：1待处理，2处理中，3无需处理，4已挂起，5已完成
	 */
	@ApiModelProperty(value = "处理状态：1待处理，2处理中，3无需处理，4已挂起，5已完成")
	private Integer status;

	/**
	 * 处理状态：1待处理，2处理中，3无需处理，4已挂起，5已完成
	 */
	private String statusDesc;

	/**
	 * 认领时间
	 */
	@ApiModelProperty(value = "认领时间")
	private Long claimTime;

	/**
	 * 处理人
	 */
	@ApiModelProperty(value = "处理人")
	private Long userId;

	/**
	 * 处理人名称
	 */
	@ApiModelProperty(value = "处理人名称")
	private String userName;

	/**
	 * 业务类型
	 */
	@ApiModelProperty(value = "业务类型")
	private String code;

	/**
	 * 业务类型
	 */
	@ApiModelProperty(value = "业务类型名称")
	private String codeName;

	/**
	 * 关联单号
	 */
	@ApiModelProperty(value = "关联单号")
	private String associatedNumber;

	/**
	 * 邮件来源
	 */
	@ApiModelProperty(value = "邮件来源")
	private String source;

	/**
	 * 接收时间
	 */
	@ApiModelProperty(value = "接收时间")
	private Long receiptTime;

	/**
	 * 抄送
	 */
	@ApiModelProperty(value = "抄送")
	private String ccJson;

	/**
	 * 收件人
	 */
	@ApiModelProperty(value = "收件人Json串")
	private String toMailJson;

	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private Long createBy;


	/**
	 * 修改人
	 */
	@ApiModelProperty(value = "修改人")
	private Long updateBy;

	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Long createTime;

	/**
	 * 修改时间
	 */
	@ApiModelProperty(value = "修改时间")
	private Long updateTime;

	/**
	 * 删除标识
	 */
	@ApiModelProperty(value = "删除标识")
	private Integer deleted;


	/**
	 * 浏览列表
	 */
	private List<Browse> browseList;

	/**
	 * 附件列表
	 */
	private List<Attachment> attachmentList;


	/**
	 * 抄送列表
	 */
	private List<MailCC> mailCCList;

	/**
	 * 密抄列表
	 */
	private List<MailCC> mailBccList;

	/**
	 * 收件人列表
	 */
	private List<MailCC> toMailList;




	public String getStatusDesc() {
		if (this.getStatus() != null) {
			MailStatus mailStatus=MailStatus.getByCode(this.getStatus());
			if(mailStatus!=null){
				return mailStatus.getDesc();
			}
		}
		return "";
	}

}
