package com.danding.cds.entity.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;
import com.google.common.base.Objects;
import io.swagger.models.auth.In;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * sku 相同
 *
 * <AUTHOR>
 * @date 2020/7/21 20:17
 */
@Getter
public enum MailStatus implements IEnum<Integer> {

	/**
	 * 待处理
	 */
	PENDING(1, "待处理"),

	/**
	 * 处理中
	 */
	INHAND(2, "处理中"),

	/**
	 * 无需处理
	 */
	NODISPOSE(3, "无需处理"),

	/**
	 * 4已挂起
	 */
	SUSPEND(4, "已挂起"),
	/**
	 * 已完成
	 */
	FINISH(5, "已完成");

	/**
	 * 标记数据库存的值是code
	 */
	private final int code;

	/**
	 * 可以对外使用的描述 标记响应json值
	 */
	private final String desc;

	MailStatus(int code, String desc) {
		this.code = code;
		this.desc = desc;
	}

	public static MailStatus getByCode(Integer code) {
		for (MailStatus from : MailStatus.values()) {
			if (Objects.equal(from.code, code)) {
				return from;
			}
		}
		return null;
	}


	public static MailStatus fromInt(Integer code) {
		for (MailStatus from : MailStatus.values()) {
			if (Objects.equal(from.code, code)) {
				return from;
			}
		}
		throw new IllegalArgumentException("unknown BondedType.code: " + code);
	}

	/**
	 * 获取待处理状态列表
	 *
	 * @return
	 */
	public static List<Integer> getPendingStatusList() {
		return Arrays.asList(MailStatus.PENDING.getCode(), MailStatus.INHAND.getCode(), MailStatus.SUSPEND.getCode());
	}


	@Override
	public Integer getValue() {
		return this.code;
	}
}
