/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.danding.cds.entity.DTO;

import com.danding.component.common.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 邮件操作日志实体类
 *
 * <AUTHOR>
 * @since 2022-08-25
 */
@Data
@ApiModel(value = "mailOperationLogDTO对象", description = "邮件操作日志表")
public class MailOperationLogDTO  extends Page implements Serializable {


	/**
	 * 邮件id
	 */
	@ApiModelProperty(value = "邮件id")
	private Long mailId;

	/**
	 * 外部邮件id
	 */
	@ApiModelProperty(value = "外部邮件id")
	private String messageId;

	/**
	 * 邮件主题
	 */
	@ApiModelProperty(value = "邮件主题")
	private String subject;

	/**
	 * 操作名称
	 */
	@ApiModelProperty(value = "操作名称")
	private String name;

	/**
	 * 操作类型
	 */
	@ApiModelProperty(value = "操作类型")
	private Integer type;

	/**
	 * 操作状态
	 */
	@ApiModelProperty(value = "操作状态")
	private Integer status;

	/**
	 * 操作内容
	 */
	@ApiModelProperty(value = "邮件内容")
	private String content;

	/**
	 * 处理人
	 */
	@ApiModelProperty(value = "处理人")
	private Long userId;

	/**
	 * 处理人名称
	 */
	@ApiModelProperty(value = "处理人名称")
	private String userName;

	/**
	 * 创建人id
	 */
	@ApiModelProperty(value = "创建人id")
	private Long createBy;

	/**
	 * 操作人名称
	 */
	@ApiModelProperty(value = "操作人名称")
	private String createName;

	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Long createTime;


	/**
	 * 删除标识
	 */
	@ApiModelProperty(value = "删除标识")
	private Integer deleted;

	private Long id;

}
