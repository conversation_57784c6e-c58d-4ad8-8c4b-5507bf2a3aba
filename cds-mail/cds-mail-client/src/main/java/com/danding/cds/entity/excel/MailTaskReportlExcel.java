package com.danding.cds.entity.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class MailTaskReportlExcel implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 用户名
	 */
	@ExcelProperty(value = "用户名", index = 0)
	private String userName;

	/**
	 * 接收任务
	 */
	@ExcelProperty(value = "接收任务", index = 1)
	private Integer receiveTask;

	/**
	 * 未完成
	 */
	@ExcelProperty(value = "未完成", index = 2)
	private Integer unfinish;

	/**
	 * 完成
	 */
	@ExcelProperty(value = "完成", index = 3)
	private Integer finish;

	/**
	 * 无需处理
	 */
	@ExcelProperty(value = "无需处理", index = 4)
	private Integer noDispose;

	/**
	 * 转交
	 */
	@ExcelProperty(value = "转交", index = 5)
	private Integer careOf;


}
