package com.danding.cds.facade;

import com.danding.cds.entity.DTO.MailPoolDTO;
import com.danding.soul.client.common.result.RpcResult;

public interface IMailPoolFacade {

    public RpcResult list(MailPoolDTO mailPoolDTO);

    public RpcResult detail(MailPoolDTO mailPoolDTO);

    public RpcResult updateMail(MailPoolDTO mailPoolDTO);

    public RpcResult mailCountByUserId(MailPoolDTO mailPoolDTO);

    public RpcResult getMail();

    public RpcResult getMailStatus();

    public RpcResult claimList(MailPoolDTO mailPoolDTO);

    public RpcResult<Boolean> mailCountExport(MailPoolDTO mailPoolDTO);

    public void contact(String code, String var1, String var2);

    public RpcResult addBrowse(MailPoolDTO mailPoolDTO);

    public RpcResult checkCode(MailPoolDTO mailPoolDTO);

    public RpcResult getMailTabNum(MailPoolDTO mailPoolDTO);

    public RpcResult updateMailBatch(MailPoolDTO mailPoolDTO);

    public RpcResult claimBatch(MailPoolDTO mailPoolDTO);
}
