package com.danding.cds.entity.VO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
@Data
@ApiModel(value = "MailTaskReportVO对象", description = "邮件事件统计实体")
public class MailTaskReportVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 处理人
     */
    @ApiModelProperty(value = "处理人")
    private Long userId;

    /**
     * 处理人名称
     */
    @ApiModelProperty(value = "处理人名称")
    private String userName;

    /**
     * 接收任务
     */
    private Integer receiveTask;

    /**
     * 未完成
     */
    private Integer unfinish;

    /**
     * 完成
     */
    private Integer finish;

    /**
     * 无需处理
     */
    private Integer noDispose;

    /**
     * 转交
     */
    private Integer careOf;
}
