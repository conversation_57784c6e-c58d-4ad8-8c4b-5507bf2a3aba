/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.danding.cds.entity.VO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 邮件过滤配置实体类
 *
 * <AUTHOR>
 * @since 2022-11-14
 */
@Data
@ApiModel(value = "MailFitlerVO对象", description = "邮件过滤配置实体表")
public class MailFilterVO implements Serializable {
	private Long id;

	/**
	 * 邮箱名称
	 */
	@ApiModelProperty(value = "邮箱名称")
	private String name;

	/**
	 * 账号
	 */
	@ApiModelProperty(value = "账号")
	private String account;

	/**
	 * 状态
	 */
	@ApiModelProperty(value = "状态：1开启，2关闭")
	private Integer status;

	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Long createTime;

	/**
	 * 删除标识
	 */
	@ApiModelProperty(value = "删除标识")
	private Integer deleted;

}
