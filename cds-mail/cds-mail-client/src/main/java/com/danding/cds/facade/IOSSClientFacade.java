package com.danding.cds.facade;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @date 2023/5/31 14:18
 */
public interface IOSSClientFacade {

	/**
	 * 根据文件名获取key
	 * @param fileName
	 * @param extName
	 * @return
	 */
	String genKeyByFileName(String fileName, String extName);

	/**
	 * 上传文件流
	 */
	String upload(String env, String key, InputStream in,Boolean attach);

	/**
	 * 上传文件流
	 */
	String upload(String key, InputStream in,Boolean attach);

	/**
	 * 上传oss
	 *
	 * @param baos
	 * @param fileName
	 * @return
	 */
	String uploadObject(String fileName, ByteArrayOutputStream baos,Boolean attach);

	/**
	 * 获取下载地址
	 *
	 * @param fileName
	 * @return
	 */
	String getDownLoadUrl(String fileName);
}
