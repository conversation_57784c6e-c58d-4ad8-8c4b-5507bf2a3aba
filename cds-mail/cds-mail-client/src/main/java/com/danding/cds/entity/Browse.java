package com.danding.cds.entity;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 邮件浏览实体类
 *
 * <AUTHOR>
 * @since 2022-08-25
 */
@Data
@ApiModel(value = "browse对象", description = "邮件浏览表")
public class Browse implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long userId;

    private String userName;

    private Long createTime;
}
