/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.danding.cds.entity.VO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 邮件配置实体类
 *
 * <AUTHOR>
 * @since 2022-08-25
 */
@Data
@ApiModel(value = "mailConfigVO对象", description = "邮件配置实体表")
public class MailConfigVO implements Serializable {
	private Long id;

	/**
	 * 邮箱名称
	 */
	@ApiModelProperty(value = "邮箱名称")
	private String name;

	/**
	 * 账号
	 */
	@ApiModelProperty(value = "账号")
	private String account;

	/**
	 * 密码
	 */
	@ApiModelProperty(value = "密码")
	private String password;

	/**
	 * 邮件服务器
	 */
	@ApiModelProperty(value = "邮件服务器")
	private String host;

	/**
	 * 协议
	 */
	@ApiModelProperty(value = "协议")
	private String protocol;

	/**
	 * 状态
	 */
	@ApiModelProperty(value = "状态：1开启，2关闭")
	private Integer status;

	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private Long createBy;

	/**
	 * 修改人
	 */
	@ApiModelProperty(value = "修改人")
	private Long updateBy;

	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Long createTime;

	/**
	 * 修改时间
	 */
	@ApiModelProperty(value = "修改时间")
	private Long updateTime;

	/**
	 * 删除标识
	 */
	@ApiModelProperty(value = "删除标识")
	private Integer deleted;

}
