<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.danding</groupId>
    <artifactId>cds-center</artifactId>
    <packaging>pom</packaging>
    <version>1.0-SNAPSHOT</version>
    <modules>
        <module>cds-item</module>
        <module>cds-order</module>
        <module>cds-company</module>
        <module>cds-common</module>
        <module>cds-web</module>
        <module>cds-electricport</module>
        <module>cds-declare</module>
        <module>cds-download</module>
        <module>cds-taxes</module>
        <module>cds-toolkits</module>
        <module>cds-outer</module>
        <module>cds-mail</module>
    </modules>
    <properties>
        <jdk.version>1.8</jdk.version>
        <encoding>UTF-8</encoding>
        <springboot.version>2.2.5.RELEASE</springboot.version>
        <fastjson.version>1.2.67</fastjson.version>
        <guava.version>20.0</guava.version>
        <hutool.version>5.3.3</hutool.version>
        <apache.common.version>3.3.2</apache.common.version>
        <joda.version>2.10.1</joda.version>
        <lombok.version>1.16.10</lombok.version>
        <http.request.version>6.0</http.request.version>
        <dt.component.xxljob.version>2.0.0-RELEASE</dt.component.xxljob.version>
        <dt.component.saas.version>2.0.0-RELEASE</dt.component.saas.version>
        <dt.component.rocketmq.version>2.0.0-RELEASE</dt.component.rocketmq.version>
        <dt.component.canal.version>1.0.0-SNAPSHOT</dt.component.canal.version>
        <mybatis.tenant.version>1.2.2-RELEASE</mybatis.tenant.version>
        <danding.sharding.core.route.version>4.1.1.RC-004</danding.sharding.core.route.version>
        <springfox.version>2.6.1</springfox.version>
        <swagger.ui.version>1.9.6</swagger.ui.version>
        <local.version>1.0-SNAPSHOT</local.version>
        <declare.sdk.version>1.1-SNAPSHOT</declare.sdk.version>
        <erp.rpc.client.version>3.1.21-RELEASE</erp.rpc.client.version>
        <spring-boot-maven-plugin.version>2.7.5</spring-boot-maven-plugin.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <!-- 一方库 -->
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>cds-common</artifactId>
                <version>${local.version}</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>cds-company-api</artifactId>
                <version>${local.version}</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>cds-item-api</artifactId>
                <version>${local.version}</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>cds-order-api</artifactId>
                <version>${local.version}</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>cds-order-c-api</artifactId>
                <version>${local.version}</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>cds-out-rpc</artifactId>
                <version>${local.version}</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>cds-download-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>cds-taxes-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>cds-district-declare-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>cds-es-search-component</artifactId>
                <version>${local.version}</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>cds-declare-sdk</artifactId>
                <version>${declare.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>cds-web</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- 二方库 -->
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>danding-encryption-standalone</artifactId>
                <version>1.2.2-RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>common-utils</artifactId>
                <version>2.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>common-es</artifactId>
                <version>3.1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>business-common</artifactId>
                <version>1.2.3.1-RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>api-common</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>apolloclient-component</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>dubbo-seata-component</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>mybatis-component</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>web-component</artifactId>
                <version>1.1.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>dt-component-rocketmq-business</artifactId>
                <version>${dt.component.rocketmq.version}</version>
            </dependency>
            <!--            <dependency>-->
            <!--                <groupId>com.danding</groupId>-->
            <!--                <artifactId>mq-component</artifactId>-->
            <!--                <version>1.0-SNAPSHOT</version>-->
            <!--            </dependency>-->
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>boost-component</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>cache-component</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>dubbo-component</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>ares-config-rpc-client</artifactId>
                <version>${erp.rpc.client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>ares-rpc-configuration-client</artifactId>
                <version>${erp.rpc.client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>ares-rpc-trade-client</artifactId>
                <version>${erp.rpc.client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>ares-goods-rpc-client</artifactId>
                <version>${erp.rpc.client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>ares-order-rpc-client</artifactId>
                <version>${erp.rpc.client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.danding.component</groupId>
                <artifactId>mybatis-tenant-component</artifactId>
                <version>${mybatis.tenant.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.baomidou</groupId>
                        <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.shardingsphere</groupId>
                        <artifactId>sharding-core-route</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.danding.component</groupId>
                <artifactId>danding-sharding-core-route</artifactId>
                <version>${danding.sharding.core.route.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.shardingsphere</groupId>
                <artifactId>sharding-core-common</artifactId>
                <version>9999-exist</version>
            </dependency>
            <dependency>
                <groupId>com.danding.component</groupId>
                <artifactId>sharding-core-common</artifactId>
                <version>${danding.sharding.core.route.version}</version>
            </dependency>

            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>oms-order-rpc-client</artifactId>
                <version>1.2.4-RELEASE</version>
            </dependency>
            <!-- 三方库 -->
            <!-- 日志收集 -->
            <dependency>
                <groupId>net.logstash.logback</groupId>
                <artifactId>logstash-logback-encoder</artifactId>
                <version>5.2</version>
            </dependency>
            <!-- Email -->
            <dependency>
                <groupId>javax.mail</groupId>
                <artifactId>mail</artifactId>
                <version>1.4.7</version>
            </dependency>
            <!-- SpringBoot版本管理 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${springboot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- 时间处理 -->
            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>${joda.version}</version>
            </dependency>
            <!-- 工具类 -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${apache.common.version}</version>
            </dependency>
            <!-- json -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <!-- lombok -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <!-- Http请求 -->
            <dependency>
                <groupId>com.github.kevinsawicki</groupId>
                <artifactId>http-request</artifactId>
                <version>${http.request.version}</version>
            </dependency>
            <!-- 调度中心 -->
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>dt-component-xxljob</artifactId>
                <version>${dt.component.xxljob.version}</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>saas-manage-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>dt-component-saas</artifactId>
                <version>${dt.component.saas.version}</version>
            </dependency>
            <!--            <dependency>-->
            <!--                <groupId>com.xuxueli</groupId>-->
            <!--                <artifactId>xxl-job-core</artifactId>-->
            <!--                <version>${xxl.version}</version>-->
            <!--            </dependency>-->
            <!-- swagger -->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>${springfox.version}</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger-ui</artifactId>
                <version>${springfox.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>swagger-bootstrap-ui</artifactId>
                <version>${swagger.ui.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.afterturn</groupId>
                <artifactId>easypoi-spring-boot-starter</artifactId>
                <version>4.1.0</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>2.2.6</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.poi</groupId>
                        <artifactId>poi</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.poi</groupId>
                        <artifactId>poi-ooxml</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.poi</groupId>
                        <artifactId>poi-ooxml-schemas</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dt</groupId>
                <artifactId>dt-component-canal</artifactId>
                <version>${dt.component.canal.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
                <version>2.2.5.RELEASE</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <!-- 中央仓库配置 -->
    <repositories>
        <repository>
            <id>danding</id>
            <name>danding</name>
            <url>http://mvn.yang800.cn/repository/maven-public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>
    <!-- 打包上传配置 -->
    <distributionManagement>
        <repository>
            <id>danding</id>
            <name>danding release resp</name>
            <url>http://mvn.yang800.cn/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>danding</id>
            <name>danding snapshot resp</name>
            <url>http://mvn.yang800.cn/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
    <!-- jdk配置 -->
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>${jdk.version}</source>
                    <target>${jdk.version}</target>
                    <encoding>${encoding}</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
