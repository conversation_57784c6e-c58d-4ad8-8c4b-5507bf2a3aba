from registry.cn-hangzhou.aliyuncs.com/danding/corp:openjdk-8-agent-1.2
VOLUME /home/<USER>
ADD target/ccs-company-service.jar ccs-company-service.jar
EXPOSE 8080
RUN curl -Os https://arthas.aliyun.com/arthas-boot.jar && \
ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
echo "Asia/Shanghai" > /etc/timezone
ENV JAVA_OPTS="$JAVA_OPTS -Dfile.encoding=UTF8  -Duser.timezone=GMT+08"
ENTRYPOINT exec java $JAVA_OPTS -Xms1g -Xmx3g -Djava.security.edg=file:/dev/./urandom -jar /ccs-company-service.jar
