<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>cds-company</artifactId>
        <groupId>com.danding</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>cds-company-service</artifactId>

    <dependencies>
        <!-- 一方包 -->
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-es-component</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-out-rpc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-snow-flake</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-company-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-company-out-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-declare-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-order-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-order-c-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-mq-component</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <!-- 二方包 -->
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>common-utils</artifactId>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.danding</groupId>-->
        <!--            <artifactId>dubbo-seata-component</artifactId>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>boost-component</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cache-component</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>mybatis-component</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.shardingsphere</groupId>
                    <artifactId>sharding-core-route</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.danding.component</groupId>
            <artifactId>mybatis-tenant-component</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding.component</groupId>
            <artifactId>danding-sharding-core-route</artifactId>
        </dependency>

        <dependency>
            <groupId>com.danding.component</groupId>
            <artifactId>sharding-core-common</artifactId>
        </dependency>
        <!-- 三方包 -->
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
            <version>5.3.1</version>
        </dependency>
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding.component</groupId>
            <artifactId>mybatis-tenant-component</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <!-- 京东sdk -->
        <dependency>
            <groupId>com.jd</groupId>
            <artifactId>open-api-sdk</artifactId>
            <version>2.2.0</version>
        </dependency>
        <dependency>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-mapper-asl</artifactId>
            <version>1.9.2</version>
        </dependency>
        <dependency>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-core-asl</artifactId>
            <version>1.9.2</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>ccs-company-service</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot-maven-plugin.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>
                                repackage
                            </goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>