<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.cds.common.generate.dao.PayMerchantAccountChannelMapper">
  <resultMap id="BaseResultMap" type="com.danding.cds.common.generate.DO.PayMerchantAccountChannel">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="merchant_id" jdbcType="VARCHAR" property="merchantId" />
    <result column="merchant_sn" jdbcType="VARCHAR" property="merchantSn" />
    <result column="channel" jdbcType="VARCHAR" property="channel" />
    <result column="recp_account" jdbcType="VARCHAR" property="recpAccount" />
    <result column="recp_code" jdbcType="VARCHAR" property="recpCode" />
    <result column="recp_name" jdbcType="VARCHAR" property="recpName" />
    <result column="token_json" jdbcType="LONGVARCHAR" property="tokenJson" />
  </resultMap>
</mapper>