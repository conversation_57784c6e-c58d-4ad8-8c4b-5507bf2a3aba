<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.cds.common.generate.dao.PayMerchantAccountMapper">
  <resultMap id="BaseResultMap" type="com.danding.cds.common.generate.DO.PayMerchantAccount">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="sn" jdbcType="VARCHAR" property="sn" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="note" jdbcType="VARCHAR" property="note" />
  </resultMap>
</mapper>