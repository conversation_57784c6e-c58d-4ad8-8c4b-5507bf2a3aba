<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.cds.route.impl.mapper.RouteMapper">
  <resultMap id="BaseResultMap" type="com.danding.cds.route.impl.entity.RouteDO">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="action_json" jdbcType="VARCHAR" property="actionJson" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="extra_json" jdbcType="LONGVARCHAR" property="extraJson" />
    <result column="enable" jdbcType="TINYINT" property="enable" />
  </resultMap>
</mapper>