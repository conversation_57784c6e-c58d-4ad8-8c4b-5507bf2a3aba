<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.cds.message.impl.mapper.MessageMapper">
  <resultMap id="BaseResultMap" type="com.danding.cds.message.impl.entity.MessageDO">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="notify_url" jdbcType="VARCHAR" property="notifyUrl" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="tag_json" jdbcType="VARCHAR" property="tagJson" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="business_code" jdbcType="VARCHAR" property="businessCode" />
    <result column="active_data" jdbcType="VARCHAR" property="activeData" />
    <result column="request_data" jdbcType="VARCHAR" property="requestData" />
  </resultMap>
</mapper>