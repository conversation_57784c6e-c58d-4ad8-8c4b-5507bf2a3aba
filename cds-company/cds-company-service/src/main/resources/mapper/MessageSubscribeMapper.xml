<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.cds.message.impl.mapper.MessageSubscribeMapper">
  <resultMap id="BaseResultMap" type="com.danding.cds.message.impl.entity.MessageSubscribeDO">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="subscribe_tag" jdbcType="VARCHAR" property="subscribeTag" />
    <result column="message_type_json" jdbcType="VARCHAR" property="messageTypeJson" />
    <result column="notify_url" jdbcType="VARCHAR" property="notifyUrl" />
    <result column="detail" jdbcType="VARCHAR" property="detail" />
    <result column="alert_emails" jdbcType="VARCHAR" property="alertEmails" />
    <result column="max_count" jdbcType="TINYINT" property="maxCount" />
    <result column="retry_strategy" jdbcType="VARCHAR" property="retryStrategy" />
    <result column="enable" jdbcType="BIT" property="enable" />
  </resultMap>
</mapper>