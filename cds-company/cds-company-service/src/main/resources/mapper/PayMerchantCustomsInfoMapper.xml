<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.cds.common.generate.dao.PayMerchantCustomsInfoMapper">
  <resultMap id="BaseResultMap" type="com.danding.cds.common.generate.DO.PayMerchantCustomsInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="merchant_id" jdbcType="VARCHAR" property="merchantId" />
    <result column="merchant_sn" jdbcType="VARCHAR" property="merchantSn" />
    <result column="customs" jdbcType="VARCHAR" property="customs" />
    <result column="merchant_customs_code" jdbcType="VARCHAR" property="merchantCustomsCode" />
    <result column="merchant_customs_name" jdbcType="VARCHAR" property="merchantCustomsName" />
  </resultMap>
</mapper>