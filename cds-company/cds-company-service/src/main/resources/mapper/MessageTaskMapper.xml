<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.cds.message.impl.mapper.MessageTaskMapper">
  <resultMap id="BaseResultMap" type="com.danding.cds.message.impl.entity.MessageTaskDO">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="subscribe_id" jdbcType="BIGINT" property="subscribeId" />
    <result column="message_id" jdbcType="BIGINT" property="messageId" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="business_code" jdbcType="VARCHAR" property="businessCode" />
    <result column="notify_url" jdbcType="VARCHAR" property="notifyUrl" />
    <result column="send_record_json" jdbcType="VARCHAR" property="sendRecordJson" />
    <result column="count" jdbcType="TINYINT" property="count" />
    <result column="last_notify_time" jdbcType="TIMESTAMP" property="lastNotifyTime" />
    <result column="next_notify_time" jdbcType="TIMESTAMP" property="nextNotifyTime" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="request_data" jdbcType="VARCHAR" property="requestData" />
  </resultMap>
</mapper>