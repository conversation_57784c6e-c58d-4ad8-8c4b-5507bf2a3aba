<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.cds.common.generate.dao.CdsCustomsHsMapper">
  <resultMap id="BaseResultMap" type="com.danding.cds.common.generate.DO.CdsCustomsHs">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="hs_code" jdbcType="VARCHAR" property="hsCode" />
    <result column="hs_name" jdbcType="VARCHAR" property="hsName" />
    <result column="vat" jdbcType="DECIMAL" property="vat" />
    <result column="tariff" jdbcType="DECIMAL" property="tariff" />
    <result column="export_tax_rate" jdbcType="DECIMAL" property="exportTaxRate" />
    <result column="export_drawback_tax_rate" jdbcType="DECIMAL" property="exportDrawbackTaxRate" />
    <result column="export_tentative_tax_rate" jdbcType="DECIMAL" property="exportTentativeTaxRate" />
    <result column="import_discount_tax_rate" jdbcType="DECIMAL" property="importDiscountTaxRate" />
    <result column="import_tentative_tax_rate" jdbcType="DECIMAL" property="importTentativeTaxRate" />
    <result column="import_general_tax_rate" jdbcType="DECIMAL" property="importGeneralTaxRate" />
    <result column="consumption_flag" jdbcType="TINYINT" property="consumptionFlag" />
    <result column="consumption_tax" jdbcType="DECIMAL" property="consumptionTax" />
    <result column="consumption_num_tax" jdbcType="DECIMAL" property="consumptionNumTax" />
    <result column="price_per_unit" jdbcType="DECIMAL" property="pricePerUnit" />
    <result column="first_legal_unit" jdbcType="VARCHAR" property="firstLegalUnit" />
    <result column="second_legal_unit" jdbcType="VARCHAR" property="secondLegalUnit" />
    <result column="uom_name" jdbcType="VARCHAR" property="uomName" />
  </resultMap>
</mapper>