<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.cds.customs.hs.impl.mapper.CustomsHsMapper">
    <resultMap id="BaseResultMap" type="com.danding.cds.customs.hs.impl.entity.CustomsHsDO">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="hs_code" jdbcType="VARCHAR" property="hsCode" />
        <result column="hs_name" jdbcType="VARCHAR" property="hsName" />
        <result column="vat" jdbcType="DECIMAL" property="vat" />
        <result column="tariff" jdbcType="DECIMAL" property="tariff" />
        <result column="consumption_flag" jdbcType="TINYINT" property="consumptionFlag" />
        <result column="consumption_tax" jdbcType="DECIMAL" property="consumptionTax" />
        <result column="consumption_num_tax" jdbcType="DECIMAL" property="consumptionNumTax" />
        <result column="price_per_unit" jdbcType="DECIMAL" property="pricePerUnit" />
        <result column="per_unit_name" jdbcType="VARCHAR" property="perUnitName" />
        <result column="enable" jdbcType="TINYINT" property="enable" />
    </resultMap>
</mapper>