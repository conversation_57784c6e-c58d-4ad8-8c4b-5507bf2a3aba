<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.cds.path.impl.mapper.PathDOMapper">
    <resultMap id="BaseResultMap" type="com.danding.cds.path.impl.entity.PathDO">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="code" jdbcType="VARCHAR" property="code" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="route_id" jdbcType="BIGINT" property="routeId" />
        <result column="first_identify" jdbcType="VARCHAR" property="firstIdentify" />
        <result column="second_identify" jdbcType="VARCHAR" property="secondIdentify" />
        <result column="third_identify" jdbcType="VARCHAR" property="thirdIdentify" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="enable" jdbcType="TINYINT" property="enable" />
        <result column="extra_json" jdbcType="VARCHAR" property="extraJson" />
    </resultMap>
</mapper>