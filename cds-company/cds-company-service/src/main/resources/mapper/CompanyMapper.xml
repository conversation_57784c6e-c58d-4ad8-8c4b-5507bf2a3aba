<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.cds.company.impl.mapper.CompanyMapper">
    <resultMap type="com.danding.cds.company.impl.entity.CompanyDO" id="CdsCompanyMap">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="code" jdbcType="VARCHAR" property="code" />
        <result column="qualify_json" jdbcType="VARCHAR" property="qualifyJson" />
        <result column="district_json" jdbcType="VARCHAR" property="districtJson" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="extra_json" jdbcType="LONGVARCHAR" property="extraJson" />
        <result column="enable" jdbcType="TINYINT" property="enable" />
    </resultMap>
</mapper>