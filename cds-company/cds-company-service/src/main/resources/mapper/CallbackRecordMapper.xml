<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.cds.callback.impl.mapper.CallbackRecordMapper">
  <resultMap id="BaseResultMap" type="com.danding.cds.callback.impl.entity.CallbackRecordDO">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="notify_url" jdbcType="VARCHAR" property="notifyUrl" />
    <result column="business_code" jdbcType="VARCHAR" property="businessCode" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="count" jdbcType="TINYINT" property="count" />
    <result column="active_data" jdbcType="VARCHAR" property="activeData" />
    <result column="request_data" jdbcType="VARCHAR" property="requestData" />
    <result column="response_data" jdbcType="VARCHAR" property="responseData" />
    <result column="last_notify_time" jdbcType="TIMESTAMP" property="lastNotifyTime" />
  </resultMap>
</mapper>