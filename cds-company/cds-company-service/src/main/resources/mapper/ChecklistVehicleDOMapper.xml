<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.cds.vehicle.impl.mapper.ChecklistVehicleMapper">
  <resultMap id="BaseResultMap" type="com.danding.cds.vehicle.impl.entity.ChecklistVehicleDO">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="vehicle_license_plate" jdbcType="VARCHAR" property="vehicleLicensePlate" />
    <result column="vehicle_weight" jdbcType="DECIMAL" property="vehicleWeight" />
    <result column="vehicle_frame_no" jdbcType="VARCHAR" property="vehicleFrameNo" />
    <result column="vehicle_frame_weight" jdbcType="DECIMAL" property="vehicleFrameWeight" />
  </resultMap>
</mapper>