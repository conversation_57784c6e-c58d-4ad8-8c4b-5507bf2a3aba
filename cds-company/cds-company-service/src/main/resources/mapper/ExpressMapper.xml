<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.cds.express.impl.mapper.ExpressMapper">
  <resultMap id="BaseResultMap" type="com.danding.cds.express.impl.entity.ExpressDO">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="express_company_id" jdbcType="BIGINT" property="expressCompanyId" />
    <result column="enable" jdbcType="TINYINT" property="enable" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>
</mapper>