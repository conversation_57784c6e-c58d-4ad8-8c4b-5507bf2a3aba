<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.cds.payChannel.impl.mapper.PayChannelMapper">
  <resultMap id="BaseResultMap" type="com.danding.cds.payChannel.impl.entity.PayChannelDO">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="pay_company_id" jdbcType="BIGINT" property="payCompanyId" />
    <result column="enable" jdbcType="TINYINT" property="enable" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>
</mapper>