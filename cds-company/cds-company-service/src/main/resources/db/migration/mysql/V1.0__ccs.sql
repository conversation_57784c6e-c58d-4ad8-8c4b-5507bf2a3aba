# Dump of table undo_log
# ------------------------------------------------------------

CREATE TABLE `undo_log` (
  `id` bigint auto_increment COMMENT 'ID',
  `branch_id` bigint not null COMMENT '分支事务id',
  `xid` varchar(100) not null COMMENT '全局事务id',
  `context`  varchar(128) not null COMMENT '撤消日志上下文，例如序列化',
  `rollback_info`  longblob not null COMMENT '回滚信息',
  `log_status`  int not null comment '0:正常状态，1:防御状态',
  `log_created`  datetime not null COMMENT '创建时间',
  `log_modified`  datetime not null COMMENT '修改时间',
  PRIMARY KEY (`id`),
  CONSTRAINT ux_undo_log UNIQUE (xid, branch_id)
) ENGINE=InnoDB DEFAULT charset=utf8mb4 COMMENT '分布式事务模式撤消表';


# Dump of table ccs_customs_uom
# ------------------------------------------------------------

CREATE TABLE `ccs_customs_uom` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `code` varchar(8) NOT NULL DEFAULT '' COMMENT '编号',
  `name` varchar(32) NOT NULL DEFAULT '' COMMENT '名称',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY idx_code(`code`),
  INDEX idx_name(`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='海关单位表';


# Dump of table ccs_customs_currency
# ------------------------------------------------------------

CREATE TABLE `ccs_customs_currency` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `code` varchar(8) NOT NULL DEFAULT '' COMMENT '编号',
  `name` varchar(32) NOT NULL DEFAULT '' COMMENT '名称',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY idx_code(`code`),
  INDEX idx_name(`name`)
) ENGINE = INNODB DEFAULT CHARSET = utf8mb4 COMMENT = '海关币制';


# Dump of table ccs_customs_country
# ------------------------------------------------------------

CREATE TABLE `ccs_customs_country` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `code` varchar(8) NOT NULL DEFAULT '' COMMENT '编号',
  `name` varchar(32) NOT NULL DEFAULT '' COMMENT '名称',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY idx_code(`code`),
  INDEX idx_name(`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='海关国家表';


# Dump of table ccs_ciq_country
# ------------------------------------------------------------

CREATE TABLE `ccs_ciq_country` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `code` varchar(8) NOT NULL DEFAULT '' COMMENT '编号',
  `name` varchar(32) NOT NULL DEFAULT '' COMMENT '名称',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY idx_code(`code`),
  INDEX idx_name(`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='国检国家表';


# Dump of table ccs_ciq_uom
# ------------------------------------------------------------

CREATE TABLE `ccs_ciq_uom` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `code` varchar(8) NOT NULL DEFAULT '' COMMENT '编号',
  `name` varchar(32) NOT NULL DEFAULT '' COMMENT '名称',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY idx_code(`code`),
  INDEX idx_name(`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='国检单位表';


# Dump of table ccs_ciq_usage
# ------------------------------------------------------------

CREATE TABLE `ccs_ciq_usage` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `code` varchar(8) NOT NULL DEFAULT '' COMMENT '编号',
  `name` varchar(32) NOT NULL DEFAULT '' COMMENT '名称',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY idx_code(`code`),
  INDEX idx_name(`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='国检用途表';


# Dump of table ccs_ciq_packing
# ------------------------------------------------------------

CREATE TABLE `ccs_ciq_packing` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `code` varchar(8) NOT NULL DEFAULT '' COMMENT '编号',
  `name` varchar(32) NOT NULL DEFAULT '' COMMENT '名称',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY idx_code(`code`),
  INDEX idx_name(`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='国检包装表';


# Dump of table ccs_ciq_transport
# ------------------------------------------------------------

CREATE TABLE `ccs_ciq_transport` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `code` varchar(8) NOT NULL DEFAULT '' COMMENT '编号',
  `name` varchar(32) NOT NULL DEFAULT '' COMMENT '名称',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY idx_code(`code`),
  INDEX idx_name(`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='国检运输表';


# Dump of table ccs_customs_hs
# ------------------------------------------------------------

CREATE TABLE `ccs_customs_hs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `hs_code` varchar(16) NOT NULL COMMENT 'HS编码',
  `hs_name` varchar(255) NOT NULL COMMENT '产品类别',
  `vat` decimal(10,4) NOT NULL DEFAULT '0' COMMENT '增值税',
  `tariff` decimal(10,4) NOT NULL DEFAULT '0' COMMENT '关税',
  `consumption_flag` tinyint(3) NOT NULL DEFAULT '0' COMMENT '消费税从价从量标志(消费税计征标准(0=不征；5=从量；10=从价))',
  `consumption_tax` decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '消费税率从价税率',
  `consumption_num_tax` decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '消费税从量税率',
  `price_per_unit` decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '每单位价格界限,>=改值时去从量税率',
  `uom_name` varchar(32) NOT NULL DEFAULT '' COMMENT '计量单位名称',
  `enable` tinyint(3) NOT NULL DEFAULT '1' COMMENT '状态:0.停用;1.启用(默认)',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_hs_code` (`hs_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='海关HS编码';


# Dump of table ccs_company
# ------------------------------------------------------------

CREATE TABLE `ccs_company` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(32) NOT NULL COMMENT '企业名称',
  `code` varchar(10) NOT NULL COMMENT '海关十位备案编码',
  `qualify_json` varchar(1024) NOT NULL DEFAULT '[]' COMMENT '企业资质列表',
  `district_json` varchar(1024) NOT NULL DEFAULT '[]' COMMENT '地方关区信息',
  `extra_json` text NOT NULL COMMENT 'json储存的其他属性键值对',
  `enable` tinyint(3) NOT NULL default 1 comment '状态:0.停用;1.启用(默认)',
  `remark` varchar(512) NOT NULL DEFAULT '' COMMENT '备注',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='企业表';


# Dump of table ccs_pay_channel
# ------------------------------------------------------------

CREATE TABLE `ccs_pay_channel` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `code` varchar(32) NOT NULL DEFAULT '' COMMENT '支付渠道标识',
  `name` varchar(32) NOT NULL DEFAULT '' COMMENT '支付渠道名称',
  `pay_company_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '支付企业ID',
  `enable` tinyint(3) not null default '1' comment '状态:0.停用;1.启用(默认)',
  `remark` varchar(512) not null DEFAULT '' COMMENT '备注',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付渠道表';


# Dump of table ccs_express
# ------------------------------------------------------------

CREATE TABLE `ccs_express` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `code` varchar(32) NOT NULL DEFAULT '' COMMENT '快递标识',
  `name` varchar(32) NOT NULL DEFAULT '' COMMENT '快递名称',
  `express_company_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '物流企业ID',
  `enable` tinyint(3) not null default '1' comment '状态:0.停用;1.启用(默认)',
  `remark` varchar(512) not null DEFAULT '' COMMENT '备注',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='快递表';


# Dump of table ccs_route
# ------------------------------------------------------------

CREATE TABLE `ccs_route` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `code` varchar(32) NOT NULL DEFAULT '' COMMENT '路径标识',
  `name` varchar(32) NOT NULL DEFAULT '' COMMENT '路径名称',
  `action_json` varchar(128) NOT NULL DEFAULT '[]' COMMENT '申报项',
  `extra_json` text NOT NULL COMMENT 'json储存的其他属性键值对',
  `enable` tinyint(3) not null default '1' comment '状态:0.停用;1.启用(默认)',
  `remark` varchar(512) not null DEFAULT '' COMMENT '备注',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='路径表';


# Dump of table ccs_callback_record
# ------------------------------------------------------------

CREATE TABLE `ccs_callback_record` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `notify_url` varchar(256) NOT NULL DEFAULT '' COMMENT '回传地址',
  `business_code` varchar(64) NOT NULL DEFAULT '' COMMENT '业务编号，用于查询关联的回传记录',
  `type` varchar(32) NOT NULL DEFAULT '' COMMENT '通知类型',
  `status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '通知状态,1:待回传，10:回传成功，-10:回传失败',
  `count` tinyint(4) NOT NULL DEFAULT '0' COMMENT '通知次数',
  `active_data` varchar(4096) NOT NULL DEFAULT '' COMMENT '触发回传的初始参数',
  `request_data` varchar(4096) NOT NULL DEFAULT '' COMMENT '请求数据',
  `response_data` varchar(4096) NOT NULL DEFAULT '' COMMENT '响应数据',
  `last_notify_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后回传时间',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='上游回传记录';

INSERT INTO ccs_customs_uom(code,name)VALUES('001','台');
INSERT INTO ccs_customs_uom(code,name)VALUES('002','座');
INSERT INTO ccs_customs_uom(code,name)VALUES('003','辆');
INSERT INTO ccs_customs_uom(code,name)VALUES('004','艘');
INSERT INTO ccs_customs_uom(code,name)VALUES('005','架');
INSERT INTO ccs_customs_uom(code,name)VALUES('006','套');
INSERT INTO ccs_customs_uom(code,name)VALUES('007','个');
INSERT INTO ccs_customs_uom(code,name)VALUES('008','只');
INSERT INTO ccs_customs_uom(code,name)VALUES('009','头');
INSERT INTO ccs_customs_uom(code,name)VALUES('010','张');
INSERT INTO ccs_customs_uom(code,name)VALUES('011','件');
INSERT INTO ccs_customs_uom(code,name)VALUES('012','支');
INSERT INTO ccs_customs_uom(code,name)VALUES('013','枝');
INSERT INTO ccs_customs_uom(code,name)VALUES('014','根');
INSERT INTO ccs_customs_uom(code,name)VALUES('015','条');
INSERT INTO ccs_customs_uom(code,name)VALUES('016','把');
INSERT INTO ccs_customs_uom(code,name)VALUES('017','块');
INSERT INTO ccs_customs_uom(code,name)VALUES('018','卷');
INSERT INTO ccs_customs_uom(code,name)VALUES('019','副');
INSERT INTO ccs_customs_uom(code,name)VALUES('020','片');
INSERT INTO ccs_customs_uom(code,name)VALUES('021','组');
INSERT INTO ccs_customs_uom(code,name)VALUES('022','份');
INSERT INTO ccs_customs_uom(code,name)VALUES('023','幅');
INSERT INTO ccs_customs_uom(code,name)VALUES('025','双');
INSERT INTO ccs_customs_uom(code,name)VALUES('026','对');
INSERT INTO ccs_customs_uom(code,name)VALUES('027','棵');
INSERT INTO ccs_customs_uom(code,name)VALUES('028','株');
INSERT INTO ccs_customs_uom(code,name)VALUES('029','井');
INSERT INTO ccs_customs_uom(code,name)VALUES('030','米');
INSERT INTO ccs_customs_uom(code,name)VALUES('031','盘');
INSERT INTO ccs_customs_uom(code,name)VALUES('032','平方米');
INSERT INTO ccs_customs_uom(code,name)VALUES('033','立方米');
INSERT INTO ccs_customs_uom(code,name)VALUES('034','筒');
INSERT INTO ccs_customs_uom(code,name)VALUES('035','千克');
INSERT INTO ccs_customs_uom(code,name)VALUES('036','克');
INSERT INTO ccs_customs_uom(code,name)VALUES('037','盆');
INSERT INTO ccs_customs_uom(code,name)VALUES('038','万个');
INSERT INTO ccs_customs_uom(code,name)VALUES('039','具');
INSERT INTO ccs_customs_uom(code,name)VALUES('040','百副');
INSERT INTO ccs_customs_uom(code,name)VALUES('041','百支');
INSERT INTO ccs_customs_uom(code,name)VALUES('042','百把');
INSERT INTO ccs_customs_uom(code,name)VALUES('043','百个');
INSERT INTO ccs_customs_uom(code,name)VALUES('044','百片');
INSERT INTO ccs_customs_uom(code,name)VALUES('045','刀');
INSERT INTO ccs_customs_uom(code,name)VALUES('046','疋');
INSERT INTO ccs_customs_uom(code,name)VALUES('047','公担');
INSERT INTO ccs_customs_uom(code,name)VALUES('048','扇');
INSERT INTO ccs_customs_uom(code,name)VALUES('049','百枝');
INSERT INTO ccs_customs_uom(code,name)VALUES('050','千只');
INSERT INTO ccs_customs_uom(code,name)VALUES('051','千块');
INSERT INTO ccs_customs_uom(code,name)VALUES('052','千盒');
INSERT INTO ccs_customs_uom(code,name)VALUES('053','千枝');
INSERT INTO ccs_customs_uom(code,name)VALUES('054','千个');
INSERT INTO ccs_customs_uom(code,name)VALUES('055','亿支');
INSERT INTO ccs_customs_uom(code,name)VALUES('056','亿个');
INSERT INTO ccs_customs_uom(code,name)VALUES('057','万套');
INSERT INTO ccs_customs_uom(code,name)VALUES('058','千张');
INSERT INTO ccs_customs_uom(code,name)VALUES('059','万张');
INSERT INTO ccs_customs_uom(code,name)VALUES('060','千伏安');
INSERT INTO ccs_customs_uom(code,name)VALUES('061','千瓦');
INSERT INTO ccs_customs_uom(code,name)VALUES('062','千瓦时');
INSERT INTO ccs_customs_uom(code,name)VALUES('063','千升');
INSERT INTO ccs_customs_uom(code,name)VALUES('067','英尺');
INSERT INTO ccs_customs_uom(code,name)VALUES('070','吨');
INSERT INTO ccs_customs_uom(code,name)VALUES('071','长吨');
INSERT INTO ccs_customs_uom(code,name)VALUES('072','短吨');
INSERT INTO ccs_customs_uom(code,name)VALUES('073','司马担');
INSERT INTO ccs_customs_uom(code,name)VALUES('074','司马斤');
INSERT INTO ccs_customs_uom(code,name)VALUES('075','斤');
INSERT INTO ccs_customs_uom(code,name)VALUES('076','磅');
INSERT INTO ccs_customs_uom(code,name)VALUES('077','担');
INSERT INTO ccs_customs_uom(code,name)VALUES('078','英担');
INSERT INTO ccs_customs_uom(code,name)VALUES('079','短担');
INSERT INTO ccs_customs_uom(code,name)VALUES('080','两');
INSERT INTO ccs_customs_uom(code,name)VALUES('081','市担');
INSERT INTO ccs_customs_uom(code,name)VALUES('083','盎司');
INSERT INTO ccs_customs_uom(code,name)VALUES('084','克拉');
INSERT INTO ccs_customs_uom(code,name)VALUES('085','市尺');
INSERT INTO ccs_customs_uom(code,name)VALUES('086','码');
INSERT INTO ccs_customs_uom(code,name)VALUES('088','英寸');
INSERT INTO ccs_customs_uom(code,name)VALUES('089','寸');
INSERT INTO ccs_customs_uom(code,name)VALUES('095','升');
INSERT INTO ccs_customs_uom(code,name)VALUES('096','毫升');
INSERT INTO ccs_customs_uom(code,name)VALUES('097','英加仑');
INSERT INTO ccs_customs_uom(code,name)VALUES('098','美加仑');
INSERT INTO ccs_customs_uom(code,name)VALUES('099','立方英尺');
INSERT INTO ccs_customs_uom(code,name)VALUES('101','立方尺');
INSERT INTO ccs_customs_uom(code,name)VALUES('110','平方码');
INSERT INTO ccs_customs_uom(code,name)VALUES('111','平方英尺');
INSERT INTO ccs_customs_uom(code,name)VALUES('112','平方尺');
INSERT INTO ccs_customs_uom(code,name)VALUES('115','英制马力');
INSERT INTO ccs_customs_uom(code,name)VALUES('116','公制马力');
INSERT INTO ccs_customs_uom(code,name)VALUES('118','令');
INSERT INTO ccs_customs_uom(code,name)VALUES('120','箱');
INSERT INTO ccs_customs_uom(code,name)VALUES('121','批');
INSERT INTO ccs_customs_uom(code,name)VALUES('122','罐');
INSERT INTO ccs_customs_uom(code,name)VALUES('123','桶');
INSERT INTO ccs_customs_uom(code,name)VALUES('124','扎');
INSERT INTO ccs_customs_uom(code,name)VALUES('125','包');
INSERT INTO ccs_customs_uom(code,name)VALUES('126','箩');
INSERT INTO ccs_customs_uom(code,name)VALUES('127','打');
INSERT INTO ccs_customs_uom(code,name)VALUES('128','筐');
INSERT INTO ccs_customs_uom(code,name)VALUES('129','罗');
INSERT INTO ccs_customs_uom(code,name)VALUES('130','匹');
INSERT INTO ccs_customs_uom(code,name)VALUES('131','册');
INSERT INTO ccs_customs_uom(code,name)VALUES('132','本');
INSERT INTO ccs_customs_uom(code,name)VALUES('133','发');
INSERT INTO ccs_customs_uom(code,name)VALUES('134','枚');
INSERT INTO ccs_customs_uom(code,name)VALUES('135','捆');
INSERT INTO ccs_customs_uom(code,name)VALUES('136','袋');
INSERT INTO ccs_customs_uom(code,name)VALUES('139','粒');
INSERT INTO ccs_customs_uom(code,name)VALUES('140','盒');
INSERT INTO ccs_customs_uom(code,name)VALUES('141','合');
INSERT INTO ccs_customs_uom(code,name)VALUES('142','瓶');
INSERT INTO ccs_customs_uom(code,name)VALUES('143','千支');
INSERT INTO ccs_customs_uom(code,name)VALUES('144','万双');
INSERT INTO ccs_customs_uom(code,name)VALUES('145','万粒');
INSERT INTO ccs_customs_uom(code,name)VALUES('146','千粒');
INSERT INTO ccs_customs_uom(code,name)VALUES('147','千米');
INSERT INTO ccs_customs_uom(code,name)VALUES('148','千英尺');
INSERT INTO ccs_customs_uom(code,name)VALUES('163','部');

INSERT INTO ccs_customs_country(code,name)VALUES('100','亚洲');
INSERT INTO ccs_customs_country(code,name)VALUES('101','阿富汗');
INSERT INTO ccs_customs_country(code,name)VALUES('102','巴林');
INSERT INTO ccs_customs_country(code,name)VALUES('103','孟加拉国');
INSERT INTO ccs_customs_country(code,name)VALUES('104','不丹');
INSERT INTO ccs_customs_country(code,name)VALUES('105','文莱');
INSERT INTO ccs_customs_country(code,name)VALUES('106','缅甸');
INSERT INTO ccs_customs_country(code,name)VALUES('107','柬埔寨');
INSERT INTO ccs_customs_country(code,name)VALUES('108','塞浦路斯');
INSERT INTO ccs_customs_country(code,name)VALUES('109','朝鲜');
INSERT INTO ccs_customs_country(code,name)VALUES('110','中国香港');
INSERT INTO ccs_customs_country(code,name)VALUES('111','印度');
INSERT INTO ccs_customs_country(code,name)VALUES('112','印度尼西亚');
INSERT INTO ccs_customs_country(code,name)VALUES('113','伊朗');
INSERT INTO ccs_customs_country(code,name)VALUES('114','伊拉克');
INSERT INTO ccs_customs_country(code,name)VALUES('115','以色列');
INSERT INTO ccs_customs_country(code,name)VALUES('116','日本');
INSERT INTO ccs_customs_country(code,name)VALUES('117','约旦');
INSERT INTO ccs_customs_country(code,name)VALUES('118','科威特');
INSERT INTO ccs_customs_country(code,name)VALUES('119','老挝');
INSERT INTO ccs_customs_country(code,name)VALUES('120','黎巴嫩');
INSERT INTO ccs_customs_country(code,name)VALUES('121','中国澳门');
INSERT INTO ccs_customs_country(code,name)VALUES('122','马来西亚');
INSERT INTO ccs_customs_country(code,name)VALUES('123','马尔代夫');
INSERT INTO ccs_customs_country(code,name)VALUES('124','蒙古');
INSERT INTO ccs_customs_country(code,name)VALUES('125','尼泊尔');
INSERT INTO ccs_customs_country(code,name)VALUES('126','阿曼');
INSERT INTO ccs_customs_country(code,name)VALUES('127','巴基斯坦');
INSERT INTO ccs_customs_country(code,name)VALUES('128','巴勒斯坦');
INSERT INTO ccs_customs_country(code,name)VALUES('129','菲律宾');
INSERT INTO ccs_customs_country(code,name)VALUES('130','卡塔尔');
INSERT INTO ccs_customs_country(code,name)VALUES('131','沙特阿拉伯');
INSERT INTO ccs_customs_country(code,name)VALUES('132','新加坡');
INSERT INTO ccs_customs_country(code,name)VALUES('133','韩国');
INSERT INTO ccs_customs_country(code,name)VALUES('134','斯里兰卡');
INSERT INTO ccs_customs_country(code,name)VALUES('135','叙利亚');
INSERT INTO ccs_customs_country(code,name)VALUES('136','泰国');
INSERT INTO ccs_customs_country(code,name)VALUES('137','土耳其');
INSERT INTO ccs_customs_country(code,name)VALUES('138','阿联酋');
INSERT INTO ccs_customs_country(code,name)VALUES('139','也门共和国');
INSERT INTO ccs_customs_country(code,name)VALUES('141','越南');
INSERT INTO ccs_customs_country(code,name)VALUES('142','中国');
INSERT INTO ccs_customs_country(code,name)VALUES('143','台澎金马关税区');
INSERT INTO ccs_customs_country(code,name)VALUES('144','东帝汶');
INSERT INTO ccs_customs_country(code,name)VALUES('145','哈萨克斯坦');
INSERT INTO ccs_customs_country(code,name)VALUES('146','吉尔吉斯斯坦');
INSERT INTO ccs_customs_country(code,name)VALUES('147','塔吉克斯坦');
INSERT INTO ccs_customs_country(code,name)VALUES('148','土库曼斯坦');
INSERT INTO ccs_customs_country(code,name)VALUES('149','乌兹别克斯坦');
INSERT INTO ccs_customs_country(code,name)VALUES('199','亚洲其他国家(地区)');
INSERT INTO ccs_customs_country(code,name)VALUES('200','非洲');
INSERT INTO ccs_customs_country(code,name)VALUES('201','阿尔及利亚');
INSERT INTO ccs_customs_country(code,name)VALUES('202','安哥拉');
INSERT INTO ccs_customs_country(code,name)VALUES('203','贝宁');
INSERT INTO ccs_customs_country(code,name)VALUES('204','博茨瓦那');
INSERT INTO ccs_customs_country(code,name)VALUES('205','布隆迪');
INSERT INTO ccs_customs_country(code,name)VALUES('206','喀麦隆');
INSERT INTO ccs_customs_country(code,name)VALUES('207','加那利群岛');
INSERT INTO ccs_customs_country(code,name)VALUES('208','佛得角');
INSERT INTO ccs_customs_country(code,name)VALUES('209','中非共和国');
INSERT INTO ccs_customs_country(code,name)VALUES('210','塞卜泰');
INSERT INTO ccs_customs_country(code,name)VALUES('211','乍得');
INSERT INTO ccs_customs_country(code,name)VALUES('212','科摩罗');
INSERT INTO ccs_customs_country(code,name)VALUES('213','刚果');
INSERT INTO ccs_customs_country(code,name)VALUES('214','吉布提');
INSERT INTO ccs_customs_country(code,name)VALUES('215','埃及');
INSERT INTO ccs_customs_country(code,name)VALUES('216','赤道几内亚');
INSERT INTO ccs_customs_country(code,name)VALUES('217','埃塞俄比亚');
INSERT INTO ccs_customs_country(code,name)VALUES('218','加蓬');
INSERT INTO ccs_customs_country(code,name)VALUES('219','冈比亚');
INSERT INTO ccs_customs_country(code,name)VALUES('220','加纳');
INSERT INTO ccs_customs_country(code,name)VALUES('221','几内亚');
INSERT INTO ccs_customs_country(code,name)VALUES('222','几内亚(比绍)');
INSERT INTO ccs_customs_country(code,name)VALUES('223','科特迪瓦');
INSERT INTO ccs_customs_country(code,name)VALUES('224','肯尼亚');
INSERT INTO ccs_customs_country(code,name)VALUES('225','利比里亚');
INSERT INTO ccs_customs_country(code,name)VALUES('226','利比亚');
INSERT INTO ccs_customs_country(code,name)VALUES('227','马达加斯加');
INSERT INTO ccs_customs_country(code,name)VALUES('228','马拉维');
INSERT INTO ccs_customs_country(code,name)VALUES('229','马里');
INSERT INTO ccs_customs_country(code,name)VALUES('230','毛里塔尼亚');
INSERT INTO ccs_customs_country(code,name)VALUES('231','毛里求斯');
INSERT INTO ccs_customs_country(code,name)VALUES('232','摩洛哥');
INSERT INTO ccs_customs_country(code,name)VALUES('233','莫桑比克');
INSERT INTO ccs_customs_country(code,name)VALUES('234','纳米比亚');
INSERT INTO ccs_customs_country(code,name)VALUES('235','尼日尔');
INSERT INTO ccs_customs_country(code,name)VALUES('236','尼日利亚');
INSERT INTO ccs_customs_country(code,name)VALUES('237','留尼汪');
INSERT INTO ccs_customs_country(code,name)VALUES('238','卢旺达');
INSERT INTO ccs_customs_country(code,name)VALUES('239','圣多美和普林西比');
INSERT INTO ccs_customs_country(code,name)VALUES('240','塞内加尔');
INSERT INTO ccs_customs_country(code,name)VALUES('241','塞舌尔');
INSERT INTO ccs_customs_country(code,name)VALUES('242','塞拉利昂');
INSERT INTO ccs_customs_country(code,name)VALUES('243','索马里');
INSERT INTO ccs_customs_country(code,name)VALUES('244','南非');
INSERT INTO ccs_customs_country(code,name)VALUES('245','西撒哈拉');
INSERT INTO ccs_customs_country(code,name)VALUES('246','苏丹');
INSERT INTO ccs_customs_country(code,name)VALUES('247','坦桑尼亚');
INSERT INTO ccs_customs_country(code,name)VALUES('248','多哥');
INSERT INTO ccs_customs_country(code,name)VALUES('249','突尼斯');
INSERT INTO ccs_customs_country(code,name)VALUES('250','乌干达');
INSERT INTO ccs_customs_country(code,name)VALUES('251','布基纳法索');
INSERT INTO ccs_customs_country(code,name)VALUES('252','民主刚果');
INSERT INTO ccs_customs_country(code,name)VALUES('253','赞比亚');
INSERT INTO ccs_customs_country(code,name)VALUES('254','津巴布韦');
INSERT INTO ccs_customs_country(code,name)VALUES('255','莱索托');
INSERT INTO ccs_customs_country(code,name)VALUES('256','梅利利亚');
INSERT INTO ccs_customs_country(code,name)VALUES('257','斯威士兰');
INSERT INTO ccs_customs_country(code,name)VALUES('258','厄立特里亚');
INSERT INTO ccs_customs_country(code,name)VALUES('259','马约特岛');
INSERT INTO ccs_customs_country(code,name)VALUES('299','非洲其他国家(地区)');
INSERT INTO ccs_customs_country(code,name)VALUES('300','欧洲');
INSERT INTO ccs_customs_country(code,name)VALUES('301','比利时');
INSERT INTO ccs_customs_country(code,name)VALUES('302','丹麦');
INSERT INTO ccs_customs_country(code,name)VALUES('303','英国');
INSERT INTO ccs_customs_country(code,name)VALUES('304','德国');
INSERT INTO ccs_customs_country(code,name)VALUES('305','法国');
INSERT INTO ccs_customs_country(code,name)VALUES('306','爱尔兰');
INSERT INTO ccs_customs_country(code,name)VALUES('307','意大利');
INSERT INTO ccs_customs_country(code,name)VALUES('308','卢森堡');
INSERT INTO ccs_customs_country(code,name)VALUES('309','荷兰');
INSERT INTO ccs_customs_country(code,name)VALUES('310','希腊');
INSERT INTO ccs_customs_country(code,name)VALUES('311','葡萄牙');
INSERT INTO ccs_customs_country(code,name)VALUES('312','西班牙');
INSERT INTO ccs_customs_country(code,name)VALUES('313','阿尔巴尼亚');
INSERT INTO ccs_customs_country(code,name)VALUES('314','安道尔');
INSERT INTO ccs_customs_country(code,name)VALUES('315','奥地利');
INSERT INTO ccs_customs_country(code,name)VALUES('316','保加利亚');
INSERT INTO ccs_customs_country(code,name)VALUES('318','芬兰');
INSERT INTO ccs_customs_country(code,name)VALUES('320','直布罗陀');
INSERT INTO ccs_customs_country(code,name)VALUES('321','匈牙利');
INSERT INTO ccs_customs_country(code,name)VALUES('322','冰岛');
INSERT INTO ccs_customs_country(code,name)VALUES('323','列支敦士登');
INSERT INTO ccs_customs_country(code,name)VALUES('324','马耳他');
INSERT INTO ccs_customs_country(code,name)VALUES('325','摩纳哥');
INSERT INTO ccs_customs_country(code,name)VALUES('326','挪威');
INSERT INTO ccs_customs_country(code,name)VALUES('327','波兰');
INSERT INTO ccs_customs_country(code,name)VALUES('328','罗马尼亚');
INSERT INTO ccs_customs_country(code,name)VALUES('329','圣马力诺');
INSERT INTO ccs_customs_country(code,name)VALUES('330','瑞典');
INSERT INTO ccs_customs_country(code,name)VALUES('331','瑞士');
INSERT INTO ccs_customs_country(code,name)VALUES('334','爱沙尼亚');
INSERT INTO ccs_customs_country(code,name)VALUES('335','拉脱维亚');
INSERT INTO ccs_customs_country(code,name)VALUES('336','立陶宛');
INSERT INTO ccs_customs_country(code,name)VALUES('337','格鲁吉亚');
INSERT INTO ccs_customs_country(code,name)VALUES('338','亚美尼亚');
INSERT INTO ccs_customs_country(code,name)VALUES('339','阿塞拜疆');
INSERT INTO ccs_customs_country(code,name)VALUES('340','白俄罗斯');
INSERT INTO ccs_customs_country(code,name)VALUES('343','摩尔多瓦');
INSERT INTO ccs_customs_country(code,name)VALUES('344','俄罗斯联邦');
INSERT INTO ccs_customs_country(code,name)VALUES('347','乌克兰');
INSERT INTO ccs_customs_country(code,name)VALUES('349','塞尔维亚和黑山');
INSERT INTO ccs_customs_country(code,name)VALUES('350','斯洛文尼亚');
INSERT INTO ccs_customs_country(code,name)VALUES('351','克罗地亚');
INSERT INTO ccs_customs_country(code,name)VALUES('352','捷克共和国');
INSERT INTO ccs_customs_country(code,name)VALUES('353','斯洛伐克');
INSERT INTO ccs_customs_country(code,name)VALUES('354','马其顿');
INSERT INTO ccs_customs_country(code,name)VALUES('355','波斯尼亚-黑塞哥维那共');
INSERT INTO ccs_customs_country(code,name)VALUES('356','梵蒂冈城国');
INSERT INTO ccs_customs_country(code,name)VALUES('399','欧洲其他国家(地区)');
INSERT INTO ccs_customs_country(code,name)VALUES('400','拉丁美洲');
INSERT INTO ccs_customs_country(code,name)VALUES('401','安提瓜和巴布达');
INSERT INTO ccs_customs_country(code,name)VALUES('402','阿根廷');
INSERT INTO ccs_customs_country(code,name)VALUES('403','阿鲁巴岛');
INSERT INTO ccs_customs_country(code,name)VALUES('404','巴哈马');
INSERT INTO ccs_customs_country(code,name)VALUES('405','巴巴多斯');
INSERT INTO ccs_customs_country(code,name)VALUES('406','伯利兹');
INSERT INTO ccs_customs_country(code,name)VALUES('408','玻利维亚');
INSERT INTO ccs_customs_country(code,name)VALUES('409','博内尔');
INSERT INTO ccs_customs_country(code,name)VALUES('410','巴西');
INSERT INTO ccs_customs_country(code,name)VALUES('411','开曼群岛');
INSERT INTO ccs_customs_country(code,name)VALUES('412','智利');
INSERT INTO ccs_customs_country(code,name)VALUES('413','哥伦比亚');
INSERT INTO ccs_customs_country(code,name)VALUES('414','多米尼亚共和国');
INSERT INTO ccs_customs_country(code,name)VALUES('415','哥斯达黎加');
INSERT INTO ccs_customs_country(code,name)VALUES('416','古巴');
INSERT INTO ccs_customs_country(code,name)VALUES('417','库腊索岛');
INSERT INTO ccs_customs_country(code,name)VALUES('418','多米尼加共和国');
INSERT INTO ccs_customs_country(code,name)VALUES('419','厄瓜多尔');
INSERT INTO ccs_customs_country(code,name)VALUES('420','法属圭亚那');
INSERT INTO ccs_customs_country(code,name)VALUES('421','格林纳达');
INSERT INTO ccs_customs_country(code,name)VALUES('422','瓜德罗普');
INSERT INTO ccs_customs_country(code,name)VALUES('423','危地马拉');
INSERT INTO ccs_customs_country(code,name)VALUES('424','圭亚那');
INSERT INTO ccs_customs_country(code,name)VALUES('425','海地');
INSERT INTO ccs_customs_country(code,name)VALUES('426','洪都拉斯');
INSERT INTO ccs_customs_country(code,name)VALUES('427','牙买加');
INSERT INTO ccs_customs_country(code,name)VALUES('428','马提尼克');
INSERT INTO ccs_customs_country(code,name)VALUES('429','墨西哥');
INSERT INTO ccs_customs_country(code,name)VALUES('430','蒙特塞拉特');
INSERT INTO ccs_customs_country(code,name)VALUES('431','尼加拉瓜');
INSERT INTO ccs_customs_country(code,name)VALUES('432','巴拿马');
INSERT INTO ccs_customs_country(code,name)VALUES('433','巴拉圭');
INSERT INTO ccs_customs_country(code,name)VALUES('434','秘鲁');
INSERT INTO ccs_customs_country(code,name)VALUES('435','波多黎各');
INSERT INTO ccs_customs_country(code,name)VALUES('436','萨巴');
INSERT INTO ccs_customs_country(code,name)VALUES('437','圣卢西亚');
INSERT INTO ccs_customs_country(code,name)VALUES('438','圣马丁岛');
INSERT INTO ccs_customs_country(code,name)VALUES('439','圣文森特和格林纳丁斯');
INSERT INTO ccs_customs_country(code,name)VALUES('440','萨尔瓦多');
INSERT INTO ccs_customs_country(code,name)VALUES('441','苏里南');
INSERT INTO ccs_customs_country(code,name)VALUES('442','特立尼达和多巴哥');
INSERT INTO ccs_customs_country(code,name)VALUES('443','特克斯和凯科斯群岛');
INSERT INTO ccs_customs_country(code,name)VALUES('444','乌拉圭');
INSERT INTO ccs_customs_country(code,name)VALUES('445','委内瑞拉');
INSERT INTO ccs_customs_country(code,name)VALUES('446','英属维尔京群岛');
INSERT INTO ccs_customs_country(code,name)VALUES('447','圣其茨-尼维斯');
INSERT INTO ccs_customs_country(code,name)VALUES('448','圣皮埃尔和密克隆');
INSERT INTO ccs_customs_country(code,name)VALUES('449','荷属安地列斯群岛');
INSERT INTO ccs_customs_country(code,name)VALUES('499','拉丁美洲其他国家(地区)');
INSERT INTO ccs_customs_country(code,name)VALUES('500','北美洲');
INSERT INTO ccs_customs_country(code,name)VALUES('501','加拿大');
INSERT INTO ccs_customs_country(code,name)VALUES('502','美国');
INSERT INTO ccs_customs_country(code,name)VALUES('503','格陵兰');
INSERT INTO ccs_customs_country(code,name)VALUES('504','百慕大');
INSERT INTO ccs_customs_country(code,name)VALUES('599','北美洲其他国家(地区)');
INSERT INTO ccs_customs_country(code,name)VALUES('600','大洋洲');
INSERT INTO ccs_customs_country(code,name)VALUES('601','澳大利亚');
INSERT INTO ccs_customs_country(code,name)VALUES('602','库克群岛');
INSERT INTO ccs_customs_country(code,name)VALUES('603','斐济');
INSERT INTO ccs_customs_country(code,name)VALUES('604','盖比群岛');
INSERT INTO ccs_customs_country(code,name)VALUES('605','马克萨斯群岛');
INSERT INTO ccs_customs_country(code,name)VALUES('606','瑙鲁');
INSERT INTO ccs_customs_country(code,name)VALUES('607','新喀里多尼亚');
INSERT INTO ccs_customs_country(code,name)VALUES('608','瓦努阿图');
INSERT INTO ccs_customs_country(code,name)VALUES('609','新西兰');
INSERT INTO ccs_customs_country(code,name)VALUES('610','诺福克岛');
INSERT INTO ccs_customs_country(code,name)VALUES('611','巴布亚新几内亚');
INSERT INTO ccs_customs_country(code,name)VALUES('612','社会群岛');
INSERT INTO ccs_customs_country(code,name)VALUES('613','所罗门群岛');
INSERT INTO ccs_customs_country(code,name)VALUES('614','汤加');
INSERT INTO ccs_customs_country(code,name)VALUES('615','土阿莫土群岛');
INSERT INTO ccs_customs_country(code,name)VALUES('616','土布艾群岛');
INSERT INTO ccs_customs_country(code,name)VALUES('617','萨摩亚');
INSERT INTO ccs_customs_country(code,name)VALUES('618','基里巴斯');
INSERT INTO ccs_customs_country(code,name)VALUES('619','图瓦卢');
INSERT INTO ccs_customs_country(code,name)VALUES('620','密克罗尼西亚联邦');
INSERT INTO ccs_customs_country(code,name)VALUES('621','马绍尔群岛');
INSERT INTO ccs_customs_country(code,name)VALUES('622','帕劳共和国');
INSERT INTO ccs_customs_country(code,name)VALUES('623','法属波利尼西亚');
INSERT INTO ccs_customs_country(code,name)VALUES('625','瓦利斯和浮图纳');
INSERT INTO ccs_customs_country(code,name)VALUES('699','大洋洲其他国家(地区)');
INSERT INTO ccs_customs_country(code,name)VALUES('701','国(地)别不详的');
INSERT INTO ccs_customs_country(code,name)VALUES('702','联合国及机构和国际组织')