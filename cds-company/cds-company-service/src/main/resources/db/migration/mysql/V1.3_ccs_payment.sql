CREATE TABLE ccs_pay_merchant_account(
       `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
       `sn` varchar(20) NOT NULL unique COMMENT '商户编码',
       `name` varchar(40)  NOT NULL COMMENT '商户名称',
       `company_name` varchar(20) NOT NULL COMMENT '收款企业',
       `note` varchar(256) DEFAULT '' COMMENT '备注',
       `create_by` bigint(20) NOT NULL DEFAULT 0 COMMENT '创建人',
       `update_by` bigint(20) NOT NULL DEFAULT 0 COMMENT '更新人',
       `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
       `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
       `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '1逻辑删除',
       PRIMARY KEY(`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='商户信息';




CREATE TABLE ccs_pay_merchant_customs_info(
--  系统字段
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `merchant_id` bigint(20) NOT NULL COMMENT '商户id',
    `merchant_sn` varchar(20) NOT NULL COMMENT '商户编码',
    `customs` varchar(40) NOT NULL COMMENT '申报海关编号',
--  主要字段
    `merchant_customs_code` varchar(20) NOT NULL COMMENT '商户海关备案编号',
    `merchant_customs_name` varchar(20) NOT NULL COMMENT '商户海关备案编号',
--  辅助字段
    `create_by` bigint(20) NOT NULL DEFAULT 0 COMMENT '创建人',
    `update_by` bigint(20) NOT NULL DEFAULT 0 COMMENT '更新人',
    `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
    `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
    `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '1逻辑删除',
    PRIMARY KEY(`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='商户海关信息表';


CREATE TABLE ccs_pay_merchant_account_channel(
   `Id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
   `merchant_id` varchar(20) NOT NULL COMMENT '商户id',
   `merchant_sn` varchar(20) NOT NULL COMMENT '商户编码',
   `channel` varchar(20) NOT NULL COMMENT '渠道',
   `recp_account` varchar(64) NOT NULL COMMENT '收款账号',
   `recp_code` varchar(64) NOT NULL COMMENT '收款企业编号',
   `recp_name` varchar(64) NOT NULL COMMENT '收款企业名称',
   `token_json` text NOT NULL COMMENT '验签令牌Json字符串，根据渠道由不同类转化而来',
   `create_by` bigint(20) NOT NULL DEFAULT 0 COMMENT '创建人',
   `update_by` bigint(20) NOT NULL DEFAULT 0 COMMENT '更新人',
   `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
   `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
   `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '1逻辑删除',
   PRIMARY KEY(`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='商户与渠道相关信息';