# Dump of table ccs_message_subscribe
# ------------------------------------------------------------

CREATE TABLE `ccs_message_subscribe` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `subscribe_tag` varchar(32) NOT NULL DEFAULT '' COMMENT '订阅标签',
  `message_type_json` varchar(1024) NOT NULL DEFAULT '' COMMENT '接收的消息类型列表',
  `notify_url` varchar(256) NOT NULL DEFAULT '' COMMENT '回传地址',
  `detail` varchar(64) NOT NULL DEFAULT '' COMMENT '订阅说明',
  `alert_emails` varchar(64) NOT NULL DEFAULT '' COMMENT '报警邮箱',
  `max_count` tinyint(4) NOT NULL DEFAULT '3' COMMENT '最大通知次数',
  `retry_strategy` varchar(32) NOT NULL DEFAULT '' COMMENT '重试策略',
  `enable` tinyint(1) NOT NULL default 1 comment '状态:0.停用;1.启用(默认)',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='消息订阅关系表';


# Dump of table ccs_message
# ------------------------------------------------------------

CREATE TABLE `ccs_message` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `notify_url` varchar(256) NULL DEFAULT '' COMMENT '精准回传地址',
  `type` varchar(32) NOT NULL DEFAULT '' COMMENT '消息类型',
  `tag_json` varchar(32) NOT NULL DEFAULT '' COMMENT '多个消息标签',
  `status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '消息状态',
  `business_code` varchar(64) NOT NULL DEFAULT '' COMMENT '业务编号',
  `active_data` varchar(4096) DEFAULT '' COMMENT '触发回传的初始参数',
  `request_data` varchar(4096) DEFAULT '' COMMENT '请求数据',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='消息记录表';


# Dump of table ccs_message_task
# ------------------------------------------------------------

CREATE TABLE `ccs_message_task` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `subscribe_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '订阅ID',
  `message_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '消息ID',
  `type` varchar(32) NOT NULL DEFAULT '' COMMENT '消息类型|冗余',
  `business_code` varchar(64) NOT NULL DEFAULT '' COMMENT '业务编号|冗余',
  `notify_url` varchar(256) NOT NULL DEFAULT '' COMMENT '执行回传地址',
  `request_data` varchar(4096) DEFAULT '' COMMENT '请求数据',
  `send_record_json` varchar(4096) NULL DEFAULT '' COMMENT '发送记录',
  `count` tinyint(4) NOT NULL DEFAULT '0' COMMENT '通知次数',
  `last_notify_time` timestamp NULL DEFAULT NULL COMMENT '最后回传时间',
  `next_notify_time` timestamp NULL DEFAULT NULL COMMENT '下次回传时间',
  `status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '执行状态',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='消息执行表';


