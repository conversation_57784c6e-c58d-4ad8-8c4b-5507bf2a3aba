CREATE TABLE `ccs_message_history` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `notify_url` varchar(256) DEFAULT '' COMMENT '精准回传地址',
  `type` varchar(32) NOT NULL DEFAULT '' COMMENT '消息类型',
  `tag_json` varchar(32) NOT NULL DEFAULT '' COMMENT '多个消息标签',
  `status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '消息状态',
  `business_code` varchar(64) NOT NULL DEFAULT '' COMMENT '业务编号',
  `active_data` varchar(4096) DEFAULT '' COMMENT '触发回传的初始参数',
  `request_data` varchar(4096) DEFAULT '' COMMENT '请求数据',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='消息记录历史表';

CREATE TABLE `ccs_message_task_history` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `subscribe_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '订阅ID',
  `message_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '消息ID',
  `type` varchar(32) NOT NULL DEFAULT '' COMMENT '消息类型|冗余',
  `business_code` varchar(64) NOT NULL DEFAULT '' COMMENT '业务编号|冗余',
  `notify_url` varchar(256) NOT NULL DEFAULT '' COMMENT '执行回传地址',
  `request_data` varchar(4096) DEFAULT '' COMMENT '请求数据',
  `send_record_json` varchar(4096) DEFAULT '' COMMENT '发送记录',
  `count` tinyint(4) NOT NULL DEFAULT '0' COMMENT '通知次数',
  `last_notify_time` timestamp NULL DEFAULT NULL COMMENT '最后回传时间',
  `next_notify_time` timestamp NULL DEFAULT NULL COMMENT '下次回传时间',
  `status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '执行状态',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='消息执行历史表';

ALTER TABLE ccs_message_task add index idx_message(`message_id`);
ALTER TABLE ccs_message_task_history add index idx_message(`message_id`);


ALTER TABLE ccs_message_task add index idx_business(`business_code`);
ALTER TABLE ccs_message_task_history add index idx_business(`business_code`);
ALTER TABLE ccs_message add index idx_business(`business_code`);
ALTER TABLE ccs_message_history add index idx_business(`business_code`);