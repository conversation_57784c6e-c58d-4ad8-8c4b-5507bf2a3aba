
ALTER TABLE ccs_customs_hs ADD COLUMN export_tax_rate decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '出口税率' after tariff;
ALTER TABLE ccs_customs_hs ADD COLUMN export_drawback_tax_rate decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '出口退税税率' after export_tax_rate;
ALTER TABLE ccs_customs_hs ADD COLUMN export_tentative_tax_rate decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '出口暂定税率' after export_drawback_tax_rate;
ALTER TABLE ccs_customs_hs ADD COLUMN import_discount_tax_rate decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '进口优惠税率' after export_tentative_tax_rate;
ALTER TABLE ccs_customs_hs ADD COLUMN import_tentative_tax_rate decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '进口暂定税率' after import_discount_tax_rate;
ALTER TABLE ccs_customs_hs ADD COLUMN import_general_tax_rate decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '进口普通税率' after import_tentative_tax_rate;


ALTER TABLE ccs_customs_hs ADD COLUMN first_legal_unit varchar(10) NOT NULL COMMENT '法定第一单位' after price_per_unit;
ALTER TABLE ccs_customs_hs ADD COLUMN second_legal_unit varchar(10) NOT NULL DEFAULT '' COMMENT '法定第二单位' after first_legal_unit;