CREATE TABLE `ccs_path` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `code` varchar(32) NOT NULL DEFAULT '' COMMENT '路由标识',
  `name` varchar(32) NOT NULL DEFAULT '' COMMENT '路由名称',
  `route_id` bigint(20) NOT NULL COMMENT '路径id',
  `first_identify` varchar(100) NOT NULL DEFAULT '' COMMENT '标识1',
  `second_identify` varchar(100) NOT NULL DEFAULT '' COMMENT '标识2',
  `third_identify` varchar(100) NOT NULL DEFAULT '' COMMENT '标识3',
  `extra_json` varchar(500) NOT NULL DEFAULT '' COMMENT 'json储存的其他属性键值对',
  `remark` varchar(512) NOT NULL DEFAULT '' COMMENT '备注',
  `enable` tinyint(3) NOT NULL DEFAULT '1' COMMENT '状态:0.停用;1.启用(默认)',
  `create_by` bigint(20) NOT NULL DEFAULT '0',
  `update_by` bigint(20) NOT NULL DEFAULT '0',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;