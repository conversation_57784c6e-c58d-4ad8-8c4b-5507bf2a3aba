CREATE TABLE `ccs_checklist_vehicle` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `vehicle_license_plate` varchar(50) NOT NULL DEFAULT '' COMMENT '车辆车牌',
  `vehicle_weight` decimal(20,0) NOT NULL COMMENT '车重',
  `vehicle_frame_no` varchar(10) NOT NULL COMMENT '车架号',
  `vehicle_frame_weight` decimal(20,0) NOT NULL COMMENT '车架重',
  `create_by` bigint(20) NOT NULL DEFAULT '0',
  `update_by` bigint(20) NOT NULL DEFAULT '0',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;