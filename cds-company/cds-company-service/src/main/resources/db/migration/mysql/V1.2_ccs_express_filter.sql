CREATE TABLE `ccs_express_filter` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `ref_express_id` int(10) DEFAULT NULL COMMENT '相关快递ID',
  `ref_express_code` varchar(20) DEFAULT NULL COMMENT '相关快递代码',
  `prov` varchar(20) NOT NULL COMMENT '省',
  `city` varchar(20) NOT NULL COMMENT '市',
  `area` varchar(20) DEFAULT NULL COMMENT '区',
  `replace_express_id` int(11) DEFAULT NULL COMMENT '替换的快递ID',
  `replace_express_code` varchar(20) DEFAULT NULL COMMENT '替换的快递代码',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;
