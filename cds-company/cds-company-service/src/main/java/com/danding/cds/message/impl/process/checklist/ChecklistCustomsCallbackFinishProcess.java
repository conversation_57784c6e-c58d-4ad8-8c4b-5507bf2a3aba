package com.danding.cds.message.impl.process.checklist;

import com.alibaba.fastjson.JSON;
import com.danding.cds.message.api.dto.MessageDTO;
import com.danding.cds.message.api.enums.MessageType;
import com.danding.cds.message.impl.process.MessageProcess;
import com.danding.cds.message.impl.process.MessageRegistry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
public class ChecklistCustomsCallbackFinishProcess extends MessageProcess {

    public ChecklistCustomsCallbackFinishProcess(MessageRegistry registry) {
        super(registry);
    }

    @Override
    public Map<String, Object> buildSendInfo(MessageDTO messageDTO) {
        String activeData = messageDTO.getActiveData();
        Map<String, Object> result = new HashMap<>();
        result.put("content", activeData);
        log.info("ChecklistCustomsCallbackProcess req - {}", JSON.toJSONString(result));
        return result;
    }

    @Override
    public MessageType getType() {
        return MessageType.CHECKLIST_CALLBACK_RETRY_FINISH;
    }
}
