package com.danding.cds.payChannel.impl.service;

import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSON;
import com.danding.cds.bean.dto.BaseDataSyncTypeEnums;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.JSONUtils;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.common.utils.ValidatorUtils;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.company.impl.entity.CompanyDO;
import com.danding.cds.company.impl.mapper.CompanyMapper;
import com.danding.cds.payChannel.api.dto.PayChannelDTO;
import com.danding.cds.payChannel.api.dto.PayChannelSearch;
import com.danding.cds.payChannel.api.dto.PayChannelSubmit;
import com.danding.cds.payChannel.api.service.PayChannelService;
import com.danding.cds.payChannel.impl.entity.PayChannelDO;
import com.danding.cds.payChannel.impl.mapper.PayChannelMapper;
import com.danding.cds.service.BaseDataSyncService;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Caching;
import tk.mybatis.mapper.entity.Example;

import javax.validation.Validator;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Auther: Dante-Gxj
 * @Date: 2020/5/6 09:29
 * @Description:
 */
@Slf4j
@DubboService
public class PayChannelServiceImpl implements PayChannelService {

    @Autowired
    private PayChannelMapper payChannelMapper;

    @Autowired
    private Validator validator;
    @Autowired
    private CompanyMapper companyMapper;
    @DubboReference
    private CompanyService companyService;
    @Autowired
    private BaseDataSyncService baseDataSyncService;

    @Override
    @PageSelect
    public ListVO<PayChannelDTO> paging(PayChannelSearch search) {
        Example example = new Example(PayChannelDO.class);
        example.orderBy("createTime").desc();
        Example.Criteria criteria = example.createCriteria();
        if (!LongUtil.isNone(search.getPayChannelId())) {
            criteria.andEqualTo("id", search.getPayChannelId());
        }
        if (!LongUtil.isNone(search.getPayCompanyId())) {
            criteria.andEqualTo("payCompanyId",search.getPayCompanyId());
        }
        Long createFrom = LongUtil.getFrom(search.getCreateFrom(),search.getCreateTo());
        Long createTo = LongUtil.getEnd(search.getCreateFrom(),search.getCreateTo());
        if (!LongUtil.isNone(createFrom)) {
            criteria.andGreaterThanOrEqualTo("createTime", new DateTime(createFrom).toString("yyyy-MM-dd HH:mm:ss"));
        }
        if (!LongUtil.isNone(createTo)) {
            criteria.andLessThanOrEqualTo("createTime", new DateTime(createTo).toString("yyyy-MM-dd HH:mm:ss"));
        }
        if (search.getEnable() != null){
            criteria.andEqualTo("enable",search.getEnable());
        }
        List<PayChannelDO> list= payChannelMapper.selectByExample(example);
        ListVO<PayChannelDTO> result = new ListVO<>();
        result.setDataList(JSON.parseArray(JSONUtils.toJSONString(list),PayChannelDTO.class));
        List<PayChannelDTO> dataList = list.stream().map((PayChannelDO item) -> {
            PayChannelDTO optionDTO = new PayChannelDTO();
            BeanUtils.copyProperties(item, optionDTO);
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            optionDTO.setCreateTime(sf.format(item.getCreateTime()));
            return optionDTO;
        }).collect(Collectors.toList());
        result.setDataList(dataList);
        // 分页
        PageInfo<PayChannelDTO> pageInfo = new PageInfo(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    @Override
    @Caching(evict={
            @CacheEvict(value = "PayChannelWithEnable",allEntries = true)
            , @CacheEvict(value = "PayChannelWithCode",key = "#submit.getCode()")
            , @CacheEvict(value = "PayChannelWithId",key = "#submit.getId()")
    })
    public Long upset(PayChannelSubmit submit) throws ArgsErrorException {
        // Step::入参校验
        String inputError = ValidatorUtils.doValidator(validator,submit);
        if (null != inputError){
            throw new ArgsErrorException(inputError);
        }
        Long payCompanyId  = submit.getPayCompanyId();
        if((submit.getEnable() != null && submit.getEnable() == 1)) {
            if (payCompanyId != null) {
                CompanyDO condition = new CompanyDO();
                condition.setId(payCompanyId);
                CompanyDO _companyDO = companyMapper.selectOne(condition);
                if (_companyDO == null) {
                    throw new ArgsErrorException("支付企业参数错误");
                } else if (new Integer(0).equals(_companyDO.getEnable())) {
                    throw new ArgsErrorException("支付企业禁用状态不能选择");
                }
            } else {
                throw new ArgsErrorException("支付企业参数错误");
            }
        }
        // Step::业务校验
        PayChannelDTO old = this.findByCode(submit.getCode());
        if (old != null && (submit.getId() == null || submit.getId() ==0 || !submit.getId().equals(old.getId()))){
            throw new ArgsErrorException("重复的编号");
        }

        if ((submit.getId() == null || submit.getId() == 0) || (submit.getEnable() != null && submit.getEnable() == 1)){
            CompanyDTO companyDTO = companyService.findUnifiedCrossInfoById(submit.getPayCompanyId());
            if (companyDTO.getEnable() != 1){
                throw new ArgsErrorException(companyDTO.getName() + "企业未启用，请启用后再试");
            }
        }
        // Step::持久化
        PayChannelDO entity = new PayChannelDO();
        BeanUtils.copyProperties(submit, entity);
        if (LongUtil.isNone(submit.getId())) {
            if (submit.getEnable() != null) {
                entity.setEnable(1);
            }
            entity.setId(null);
            UserUtils.setCreateAndUpdateBy(entity);
            payChannelMapper.insertSelective(entity);

            baseDataSyncService.syncDataMQSend(BaseDataSyncTypeEnums.PAY_CHANNEL, BaseDataSyncTypeEnums.INSERT, entity);
        }else {
            old = this.findById(submit.getId());
            if (old == null){
                throw new ArgsErrorException("ID不正确");
            }
            // 禁用操作
            if ((submit.getEnable() != null && submit.getEnable() == 0) || old.getEnable() == 0) {
                if (!Objects.equals(UserUtils.getUserId(), 0)) {
                    UserUtils.setUpdateBy(entity);
                }
                entity.setUpdateTime(new Date());
                payChannelMapper.updateByPrimaryKeySelective(entity);

                PayChannelDO syncData = payChannelMapper.selectByPrimaryKey(entity.getId());
                baseDataSyncService.syncDataMQSend(BaseDataSyncTypeEnums.PAY_CHANNEL, BaseDataSyncTypeEnums.UPDATE, syncData);
            }else {
                throw new ArgsErrorException("支付渠道未禁用不可编辑");
            }
        }
        submit.setId(entity.getId());
        return entity.getId();
    }

    @Override
//    @Cacheable(value = "PayChannelWithCode" ,key = "#code",unless = "#result==null")
    public PayChannelDTO findByCode(String code) {
        PayChannelDO condition = new PayChannelDO();
        condition.setCode(code);
        PayChannelDO model = payChannelMapper.selectOne(condition);
        if (model == null){
            return null;
        }
        return this.buildDTO(model);
    }

    @Override
//    @Cacheable(value = "PayChannelWithId" ,key = "#id",unless = "#result==null")
    public PayChannelDTO findById(Long id) {
        return this.buildDTO(payChannelMapper.selectByPrimaryKey(id));
    }

    @Override
//    @Cacheable(value = "PayChannelWithEnable" ,key = "'2'",unless = "#result==null")
    public List<PayChannelDTO> listEnable() {
        PayChannelSearch search = new PayChannelSearch();
        search.setPageIgnore(1);
        search.setEnable(1);
        ListVO<PayChannelDTO> paging = this.paging(search);
        return paging.getDataList();
    }

    public List<PayChannelDTO> listAll() {
        PayChannelSearch search = new PayChannelSearch();
        search.setPageIgnore(1);
        ListVO<PayChannelDTO> paging = this.paging(search);
        return paging.getDataList();
    }

    private PayChannelDTO buildDTO(PayChannelDO routeDO){
        PayChannelDTO result = new PayChannelDTO();
        BeanUtils.copyProperties(routeDO,result);
        return result;
    }
}
