package com.danding.cds.company.impl.mapper;

import com.danding.cds.company.impl.entity.CompanyDO;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import tk.mybatis.mapper.additional.insert.InsertListMapper;
import tk.mybatis.mapper.common.Mapper;

/**
 * 企业表(CompanyDO)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-04-26 14:45:03
 */
public interface CompanyMapper extends Mapper<CompanyDO>, AggregationPlusMapper<CompanyDO>, BatchUpdateMapper<CompanyDO>, InsertListMapper<CompanyDO> {

}