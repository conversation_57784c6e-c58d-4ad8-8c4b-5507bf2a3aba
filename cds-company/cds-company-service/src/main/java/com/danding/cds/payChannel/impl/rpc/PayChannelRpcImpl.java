package com.danding.cds.payChannel.impl.rpc;

import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.out.api.PayChannelRpc;
import com.danding.cds.out.bean.RpcResult;
import com.danding.cds.out.bean.vo.res.PayChannelResVO;
import com.danding.cds.payChannel.api.dto.PayChannelDTO;
import com.danding.cds.payChannel.api.service.PayChannelService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.Objects;

/**
 * 支付企业外部调用RpcImpl
 */
@Slf4j
@DubboService
public class PayChannelRpcImpl implements PayChannelRpc {

    @DubboReference
    private PayChannelService payChannelService;

    @DubboReference
    private CompanyService companyService;

    /**
     * 获取支付企业名称
     *
     * @param code 支付渠道标识
     * @return 支付企业名称
     */
    @Override
    public RpcResult<String> getPayCompanyNameByCode(String code) {
        log.info("PayChannelRpcImpl-getPayCompanyNameByCode code={}", code);
        PayChannelDTO payChannelDTO = payChannelService.findByCode(code);
        if (Objects.nonNull(payChannelDTO) && Objects.nonNull(payChannelDTO.getPayCompanyId())) {
            CompanyDTO companyDTO = companyService.findUnifiedCrossInfoById(payChannelDTO.getPayCompanyId());
            if (Objects.nonNull(companyDTO)) {
                log.info("PayChannelRpcImpl-getPayCompanyNameByCode code={}, company={}", code, companyDTO.getName());
                return RpcResult.success("success", companyDTO.getName());
            }
        }
        log.error("PayChannelRpcImpl-getPayCompanyNameByCode error code={}", code);
        return RpcResult.error("支付编码不存在");
    }

    @Override
    public RpcResult<PayChannelResVO> findByCode(String code) {
        log.info("PayChannelRpcImpl-findByCode code={}", code);
        if (StringUtils.isBlank(code)) {
            return RpcResult.error("支付编码不能为空");
        }
        PayChannelDTO payChannelDTO = payChannelService.findByCode(code);
        if (Objects.isNull(payChannelDTO)) {
            return RpcResult.error("支付编码不存在");
        }
        CompanyDTO companyDTO = companyService.findUnifiedCrossInfoById(payChannelDTO.getPayCompanyId());
        if (Objects.isNull(companyDTO)) {
            return RpcResult.error("支付企业不存在");
        }
        PayChannelResVO resVO = new PayChannelResVO();
        resVO.setCode(payChannelDTO.getCode());
        resVO.setName(payChannelDTO.getName());
        resVO.setPayCompanyName(companyDTO.getName());
        return RpcResult.success(resVO);
    }
}
