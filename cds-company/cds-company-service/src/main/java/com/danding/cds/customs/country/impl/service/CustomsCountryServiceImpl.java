package com.danding.cds.customs.country.impl.service;

import com.alibaba.fastjson.JSON;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.customs.country.api.dto.CustomsCountryDTO;
import com.danding.cds.customs.country.api.service.CustomsCountryService;
import com.danding.cds.customs.country.impl.entity.CustomsCountryDO;
import com.danding.cds.customs.country.impl.mapper.CustomsCountryMapper;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * @Auther: shenfeng
 * @Date: 2020-04-28 16:43:57
 * @Description:
 */
@DubboService
public class CustomsCountryServiceImpl implements CustomsCountryService {

    @Autowired
    private CustomsCountryMapper customsCountryMapper;

    @Override
    public CustomsCountryDTO findById(Long id) {
        if(LongUtil.isNone(id)){
            return null;
        }else{
            CustomsCountryDO customsCountryDO = customsCountryMapper.selectByPrimaryKey(id);
            return this.buildDTO(customsCountryDO);
        }
    }

    @Override
//    @Cacheable(value = "customsCountryByCode", key = "#code", unless = "#result==null")
    public CustomsCountryDTO findByCode(String code) {
        CustomsCountryDO condition = new CustomsCountryDO();
        condition.setCode(code);
        return this.buildDTO(customsCountryMapper.selectOne(condition));
    }

    @Override
//    @Cacheable(value = "customsCountryByName", key = "#name", unless = "#result==null")
    public CustomsCountryDTO findByName(String name) {
        CustomsCountryDO condition = new CustomsCountryDO();
        condition.setName(name);
        return this.buildDTO(customsCountryMapper.selectOne(condition));
    }

    @Override
    public List<CustomsCountryDTO> findByName(List<String> nameList) {
        Example example = new Example(CustomsCountryDO.class);
        example.createCriteria().andEqualTo("deleted", false)
                .andIn("name", nameList);
        List<CustomsCountryDO> list = customsCountryMapper.selectByExample(example);
        return ConvertUtil.listConvert(list, CustomsCountryDTO.class);
    }

    @Override
    public List<CustomsCountryDTO> listAll() {
        CustomsCountryDO condition = new CustomsCountryDO();
        condition.setDeleted(false);
        List<CustomsCountryDO> doList = customsCountryMapper.select(condition);
        List<CustomsCountryDTO> dtoList = JSON.parseArray(JSON.toJSONString(doList), CustomsCountryDTO.class);
        return dtoList;
    }

    private CustomsCountryDTO buildDTO(CustomsCountryDO customsCountryDO){
        if(customsCountryDO == null){
            return null;
        }else {
            CustomsCountryDTO customsCountryDTO = new CustomsCountryDTO();
            BeanUtils.copyProperties(customsCountryDO,customsCountryDTO);
            return customsCountryDTO;
        }
    }
}
