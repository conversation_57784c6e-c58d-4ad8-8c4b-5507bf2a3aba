package com.danding.cds.route.impl.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.bean.dto.BaseDataSyncTypeEnums;
import com.danding.cds.bean.dto.DeclareConfig;
import com.danding.cds.bean.dto.DeclareWayDTO;
import com.danding.cds.common.enums.DeclareEnum;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.common.utils.ValidatorUtils;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.company.api.vo.CompanyResVo;
import com.danding.cds.company.impl.entity.CompanyDO;
import com.danding.cds.company.impl.mapper.CompanyMapper;
import com.danding.cds.customs.dictionary.api.service.CustomsDictionaryService;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.item.api.vo.CustomsBookResVo;
import com.danding.cds.route.api.dto.*;
import com.danding.cds.route.api.enums.RouteActionEnum;
import com.danding.cds.route.api.service.RouteService;
import com.danding.cds.route.api.vo.RouteConfigResVo;
import com.danding.cds.route.api.vo.RouteInfoResVo;
import com.danding.cds.route.api.vo.RouteResVo;
import com.danding.cds.route.api.vo.RouteUpdateConfigReqVO;
import com.danding.cds.route.impl.entity.RouteDO;
import com.danding.cds.route.impl.mapper.RouteMapper;
import com.danding.cds.service.BaseDataSyncService;
import com.danding.cds.service.base.DeclareWayBaseService;
import com.danding.cds.util.DtoBuildUtil;
import com.danding.cds.v2.bean.dto.EntityWarehouseDTO;
import com.danding.cds.v2.service.EntityWarehouseService;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import javax.validation.Validator;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Auther: Dante-Gxj
 * @Date: 2020/4/30 11:38
 * @Description:
 */
@Slf4j
@DubboService
public class RouteServiceImpl implements RouteService {

    @Autowired
    private RouteMapper routeMapper;

    @Autowired
    private Validator validator;
    @Autowired
    private CompanyMapper companyMapper;
    @Autowired
    private DeclareWayBaseService declareWayBaseService;

    @DubboReference
    private CustomsBookService customsBookService;

    @Autowired
    private BaseDataSyncService baseDataSyncService;
    @Autowired
    private CompanyService companyService;

    @DubboReference
    private CustomsDictionaryService customsDictionaryService;

    @DubboReference
    private EntityWarehouseService entityWarehouseService;

    @Override
/*    @Caching(evict={
//            @CacheEvict(value = "RouteWithEnable",allEntries = true)
//            , @CacheEvict(value = "RouteWithCode",key = "#submit.getCode()")
//            , @CacheEvict(value = "RouteWithId",key = "#submit.getId()")
    })*/
    public Long upset(RouteSubmit submit) throws ArgsErrorException {
        log.info("路径提交数据 :{}", JSON.toJSONString(submit));
        // Step::业务校验
        RouteDTO old = this.findByCode(submit.getCode());
        if (old != null && (submit.getId() == null || submit.getId() == 0 || !submit.getId().equals(old.getId()))) {
            throw new ArgsErrorException("路径标识重复");
        }
        Integer userId = UserUtils.getUserId();
        // Step::持久化
        RouteDO routeDO = new RouteDO();
        // 这里新建和更新都默认加下"auto"
        routeDO.setDeclareWay("auto");
        BeanUtils.copyProperties(submit, routeDO);
        routeDO.setActionJson(JSON.toJSONString(submit.getActionList()));
        RouteExtra extra = new RouteExtra();
        BeanUtils.copyProperties(submit, extra);
        routeDO.setExtraJson(JSON.toJSONString(extra));
        if (submit.getActionList().contains(RouteActionEnum.DECLARE_INVENTORY.getCode())) {
            if (LongUtil.isNone(submit.getEbpId())) {
                throw new ArgsErrorException("清单申报电商平台不能为空");
            }
            if (LongUtil.isNone(submit.getEbcId())) {
                throw new ArgsErrorException("清单申报电商企业不能为空");
            }
            if (LongUtil.isNone(submit.getAssureCompanyId())) {
                throw new ArgsErrorException("清单申报担保企业不能为空");
            }
            if (LongUtil.isNone(submit.getListDeclareCompanyId())) {
                throw new ArgsErrorException("清单报文传输企业不能为空");
            }
//            if (StringUtils.isEmpty(submit.getListDeclareDxpId())) {
//                throw new ArgsErrorException("清单传输节点不能为空");
//            }
            if (LongUtil.isNone(submit.getCustomsBookId())) {
                throw new ArgsErrorException("清单申报账册不能为空");
            }
        }
        if (submit.getActionList().contains(RouteActionEnum.DECLARE_LOGISTICS.getCode())) {
            if (LongUtil.isNone(submit.getLogisticsDeclareCompanyId())) {
                throw new ArgsErrorException("运单报文传输企业不能为空");
            }
//            if (StringUtils.isEmpty(submit.getLogisticsDeclareDxpId())) {
//                throw new ArgsErrorException("运单传输节点不能为空");
//            }
        }
        if (submit.getActionList().contains(RouteActionEnum.DECLARE_ORDER.getCode())) {
            if (LongUtil.isNone(submit.getOrderDeclareCompanyId())) {
                throw new ArgsErrorException("订单报文传输企业不能为空");
            }
//            if (StringUtils.isEmpty(submit.getOrderDeclareDxpId())) {
//                throw new ArgsErrorException("订单传输节点不能为空");
//            }
        }
        if (submit.getActionList().contains(RouteActionEnum.DECLARE_PAYMENT.getCode())) {
            if (LongUtil.isNone(submit.getEbpId())) {
                throw new ArgsErrorException("支付单申报电商平台不能为空");
            }
        }
        if (Objects.nonNull(submit.getAssureCompanyId()) && Objects.nonNull(submit.getCustomsBookId())) {
            CompanyResVo companyResVo = companyService.findByIdV2(submit.getAssureCompanyId());
            if (Objects.isNull(companyResVo)) {
                throw new ArgsErrorException("未查询到担保企业信息");
            }
            String guaranteeCustoms = companyResVo.getGuaranteeCustoms();
            if (StringUtils.isEmpty(guaranteeCustoms)) {
                throw new ArgsErrorException("未查询到担保企业保函关信息");
            }
            CustomsBookResVo customsBookResVo = customsBookService.findByIdV2(submit.getCustomsBookId());
            List<String> guaranteeCustomsList = JSON.parseArray(guaranteeCustoms, String.class);
            if (!guaranteeCustomsList.contains(customsBookResVo.getCustomsAreaCode())) {
                throw new ArgsErrorException("担保企业保函关区不包含账册关区");
            }
        }
        if ((submit.getId() == null || submit.getId() == 0) || (submit.getEnable() != null && submit.getEnable() == 1)) {
            checkCompany(extra.getAssureCompanyId(), "担保企业");
            checkCompany(extra.getListDeclareCompanyId(), "清关企业");
            checkCompany(extra.getOrderDeclareCompanyId(), "报文传输企业");
            checkCompany(extra.getEbpId(), "电商平台");
            checkCompany(extra.getEbcId(), "电商企业");
            checkCompany(extra.getLogisticsDeclareCompanyId(), "运单报文传输企业");
            if (!LongUtil.isNone(extra.getCustomsBookId())) {
                CustomsBookResVo customsBookResVo = customsBookService.findByIdV2(extra.getCustomsBookId());
                if (customsBookResVo.getEnable() != 1) {
                    throw new ArgsErrorException(customsBookResVo.getBookNo() + "账册未启用，请启用后再试");
                }
            }
        }
        if (submit.getId() == null || submit.getId() == 0) {
            // Step::入参校验
            String inputError = ValidatorUtils.doValidator(validator, submit);
            if (null != inputError) {
                throw new ArgsErrorException(inputError);
            }
            // Step::业务校验
            routeDO.setId(null);
            routeDO.setEnable(1);
            routeDO.setCreateBy(userId);
            routeDO.setUpdateBy(userId);
            routeMapper.insertSelective(routeDO);

            RouteDTO routeDTO = DtoBuildUtil.buildRouteDTO(routeDO);
            baseDataSyncService.syncDataMQSend(BaseDataSyncTypeEnums.ROUTE, BaseDataSyncTypeEnums.INSERT, routeDTO);
        } else {
            routeDO.setUpdateBy(userId);
            routeDO.setUpdateTime(new Date());
            old = this.findById(submit.getId());
            if (old == null) {
                throw new ArgsErrorException("ID不正确");
            }
            // 禁用操作
            if ((submit.getEnable() != null && submit.getEnable() == 0) || old.getEnable() == 0) {
                if (!Objects.equals(UserUtils.getUserId(), 0)) {
                    UserUtils.setUpdateBy(routeDO);
                }
                routeDO.setUpdateTime(new Date());
                routeMapper.updateByPrimaryKeySelective(routeDO);

                RouteDO syncData = routeMapper.selectByPrimaryKey(routeDO.getId());
                RouteDTO routeDTO = DtoBuildUtil.buildRouteDTO(syncData);
                baseDataSyncService.syncDataMQSend(BaseDataSyncTypeEnums.ROUTE, BaseDataSyncTypeEnums.UPDATE, routeDTO);
            } else {
                throw new ArgsErrorException("路径未禁用，不可编辑");
            }
            submit.setCode(old.getCode());
        }
        submit.setId(routeDO.getId());
        return routeDO.getId();
    }

    @Override
    public Long upsetV2(RouteSubmitV2 submit) throws ArgsErrorException {
        Long routeId = this.upset(submit);
        List<DeclareConfig> proxyDeclareConfigList = submit.getProxyDeclareConfigList();
        this.replaceDeclareConfig(routeId, proxyDeclareConfigList);
        return routeId;
    }

    /**
     * 将代理类型的declareConfig替换老的
     *
     * @param routeId
     * @param proxyDeclareConfigList
     */
    private void replaceDeclareConfig(Long routeId, List<DeclareConfig> proxyDeclareConfigList) {
        RouteDTO routeDTO = this.findById(routeId);
        if (Objects.isNull(routeDTO)) {
            throw new ArgsErrorException("更新失败");
        }
        if (CollectionUtils.isEmpty(proxyDeclareConfigList)) {
            throw new ArgsErrorException("当前申报方式不能为空");
        }
        List<DeclareConfig> newDeclareConfig = this.generateNewDeclareConfig(proxyDeclareConfigList, routeDTO);
        RouteDO routeDO = new RouteDO();
        routeDO.setId(routeId);
        // 暂时写死auto 全切换完之后可以去掉这行
        routeDO.setDeclareWay("auto");
        routeDO.setDeclareConfig(JSON.toJSONString(newDeclareConfig));
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(routeDO);
        }
        routeDO.setUpdateTime(new Date());
        routeMapper.updateByPrimaryKeySelective(routeDO);
    }

    /**
     * 生成新的申报方式
     * 将老的declareConfig转为map 新的put进去替换
     *
     * @param proxyDeclareConfigList
     * @param routeDTO
     * @return
     */
    private List<DeclareConfig> generateNewDeclareConfig(List<DeclareConfig> proxyDeclareConfigList, RouteDTO routeDTO) {
        String declareConfig = routeDTO.getDeclareConfig();
        List<DeclareConfig> declareConfigList;
        Map<String, DeclareConfig> typeMap = new HashMap<>(2 << 3);
        if (!StringUtils.isEmpty(declareConfig)) {
            declareConfigList = JSON.parseArray(declareConfig, DeclareConfig.class);
            if (!CollectionUtils.isEmpty(declareConfigList)) {
                typeMap = declareConfigList.stream().collect(Collectors.toMap(DeclareConfig::getType, Function.identity(), (v1, v2) -> v1));
            }
        }
        if (CollectionUtils.isEmpty(proxyDeclareConfigList)) {
            throw new ArgsErrorException("申报配置不能为空");
        }
        for (DeclareConfig p : proxyDeclareConfigList) {
            typeMap.put(p.getType(), p);
        }
        log.info("generateNewDeclareConfig routeDTO={}", JSON.toJSONString(routeDTO));
        log.info("generateNewDeclareConfig typeMap={}", JSON.toJSONString(typeMap));
        this.checkWholeDeclareConfig(routeDTO, typeMap);
        List<DeclareConfig> newDeclareConfig = typeMap.values().stream().collect(Collectors.toList());
        return newDeclareConfig;
    }

    /**
     * 校验每个配置项是否有对应的申报方式
     *
     * @param routeDTO
     * @param typeMap
     */
    private void checkWholeDeclareConfig(RouteDTO routeDTO, Map<String, DeclareConfig> typeMap) {
        //不好做自动化校验 直接写死四单申报的校验
        List<String> actionList = routeDTO.getActionList();
        if (actionList.contains(RouteActionEnum.DECLARE_ORDER.getCode())) {
            log.info("checkWholeDeclareConfig customsOrder companyId={}", routeDTO.getOrderDeclareCompanyId());
            this.checkSubDeclareConfig(typeMap, RouteActionEnum.DECLARE_ORDER, DeclareEnum.CUSTOMS_ORDER, routeDTO.getOrderDeclareCompanyId());
        }
        if (actionList.contains(RouteActionEnum.DECLARE_LOGISTICS.getCode())) {
            log.info("checkWholeDeclareConfig shipment companyId={}", routeDTO.getLogisticsDeclareCompanyId());
            this.checkSubDeclareConfig(typeMap, RouteActionEnum.DECLARE_LOGISTICS, DeclareEnum.SHIPMENT, routeDTO.getLogisticsDeclareCompanyId());
        }
        if (actionList.contains(RouteActionEnum.DECLARE_INVENTORY.getCode())) {
            // TODO: 2021/12/28 是否需要校验撤单与退货
            log.info("checkWholeDeclareConfig inventory companyId={}", routeDTO.getListDeclareCompanyId());
            this.checkSubDeclareConfig(typeMap, RouteActionEnum.DECLARE_INVENTORY, DeclareEnum.INVENTORY, routeDTO.getListDeclareCompanyId());
            this.checkSubDeclareConfig(typeMap, RouteActionEnum.DECLARE_INVENTORY, DeclareEnum.INVENTORY_CANCEL, routeDTO.getListDeclareCompanyId());
            this.checkSubDeclareConfig(typeMap, RouteActionEnum.DECLARE_INVENTORY, DeclareEnum.INVENTORY_REFUND, routeDTO.getListDeclareCompanyId());
        }
    }

    /**
     * @param typeMap               已有的申报方式的typeMap
     * @param routeAction           action的枚举类
     * @param declareType           declareConfig的type的枚举
     * @param orderDeclareCompanyId 对应报文传输企业的id
     */
    private void checkSubDeclareConfig(Map<String, DeclareConfig> typeMap, RouteActionEnum routeAction, DeclareEnum declareType, Long orderDeclareCompanyId) {
        if (typeMap.containsKey(declareType.getType())) {
            DeclareConfig declareConfig = typeMap.get(declareType.getType());
            // 路径需要配置默认申报方式
            if (Objects.isNull(declareConfig) || Objects.isNull(declareConfig.getDeclareCode()) || Objects.equals(declareConfig.getDeclareCode(), "")) {
                throw new ArgsErrorException(routeAction.getDesc() + "当前申报方式未配置");
            }
        } else {
            CompanyResVo orderDeclareCompany = companyService.findByIdV2(orderDeclareCompanyId);
            if (Objects.isNull(orderDeclareCompany)) {
                throw new ArgsErrorException(routeAction.getDesc() + "报文传输企业未找到");
            }
            String declareConfig = orderDeclareCompany.getDeclareConfig();
            List<DeclareConfig> companyDeclareConfigs = JSON.parseArray(declareConfig, DeclareConfig.class);
            if (CollectionUtils.isEmpty(companyDeclareConfigs)) {
                throw new ArgsErrorException(routeAction.getDesc() + "报文传输企业未配置申报方式");
            }
            Map<String, DeclareConfig> companyTypeMap = companyDeclareConfigs.stream().collect(Collectors.toMap(DeclareConfig::getType, Function.identity(), (v1, v2) -> v1));
            if (companyTypeMap.containsKey(declareType.getType())) {
                DeclareConfig companyDeclareConfig = companyTypeMap.get(declareType.getType());
                if (Objects.isNull(companyDeclareConfig) || Objects.isNull(companyDeclareConfig.getDeclareCode()) || Objects.equals(companyDeclareConfig.getDeclareCode(), "")) {
                    throw new ArgsErrorException(routeAction.getDesc() + "申报方式未配置");
                }
            }
        }
    }


    private void checkCompany(Long companyId, String errorMsg) {
        if (companyId != null) {
            CompanyDO condition = new CompanyDO();
            condition.setId(companyId);
            CompanyDO _companyDO = companyMapper.selectOne(condition);
            if (_companyDO == null) {
                throw new ArgsErrorException(errorMsg + "参数错误");
            } else if (new Integer(0).equals(_companyDO.getEnable())) {
                throw new ArgsErrorException(_companyDO.getName() + "企业未启用，请启用后再试");
            }
        }
    }

    @Override
    public List<RouteDTO> listAll() {
        List<RouteDO> routeDOS = routeMapper.selectAll();
        return routeDOS.stream().map(this::buildDTO).collect(Collectors.toList());
    }

    @Override
    public ListVO<RouteDTO> paging(RouteSearch search) {
        //区内企业查找账册id

        List<Long> bookIds = new ArrayList<>();
        if (Objects.nonNull(search.getAreaCompanyId())) {
            List<CustomsBookResVo> bookDTOS = customsBookService.findByAreaCompanyId(search.getAreaCompanyId());
            if (!CollectionUtils.isEmpty(bookDTOS)) {
                bookIds = bookDTOS.stream().map(CustomsBookResVo::getId).collect(Collectors.toList());
                search.setBookIdList(bookIds);
            }
        }
        if (StringUtil.isNotEmpty(search.getEntityWarehouseCode())) {
            List<String> entityWarehouseCodeList = Arrays.asList(search.getEntityWarehouseCode().split(","));
            List<EntityWarehouseDTO> entityWarehouseDTOS = entityWarehouseService.findDTOByErpCode(entityWarehouseCodeList);
            if (CollUtil.isNotEmpty(entityWarehouseDTOS)) {
                List<Long> bookIdList = entityWarehouseDTOS.stream().map(EntityWarehouseDTO::getCustomsBookId).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(bookIds)) {
                    bookIds = new ArrayList<>(CollUtil.intersection(bookIdList, bookIds));
                } else {
                    bookIds = bookIdList;
                }
                search.setBookIdList(bookIds);
            }
        }
        if (CollectionUtils.isEmpty(bookIds) &&
                (Objects.nonNull(search.getAreaCompanyId()) || Objects.nonNull(search.getEntityWarehouseCode()))) {
            //解决： 当【区内企业】未查到账册时，搜索条件不处理【区内企业】查询
            bookIds.add(-1L);
            search.setBookIdList(bookIds);
        }
        if (StringUtil.isNotEmpty(search.getRouteTag())) {
            List<String> routeTagList = Arrays.asList(search.getRouteTag().split(","));
            int routeTags = routeTagList.stream().mapToInt(Integer::valueOf).sum();
            search.setRouteTags(routeTags);
        }
        PageHelper.startPage(search.getCurrentPage(), search.getPageSize());
        List<RouteDO> list = routeMapper.routeInfoSelect(search);
        ListVO<RouteDTO> result = new ListVO<>();
        List<RouteDTO> dtoList = list.stream().map(this::buildDTO).collect(Collectors.toList());
        result.setDataList(dtoList);
        // 分页
        PageInfo<RouteDTO> pageInfo = new PageInfo(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    @Override
    public void batchEnableSwitch(List<Long> idList, Integer status) {
        if (CollectionUtils.isEmpty(idList) || Objects.isNull(status)) {
            log.warn("参数不能为空");
            return;
        }
        Example example = new Example(RouteDO.class);
        example.createCriteria().andIn("id", idList).andEqualTo("deleted", 0);
        RouteDO routeDO = new RouteDO();
        routeDO.setEnable(status);
        routeMapper.updateByExampleSelective(routeDO, example);

        List<RouteDO> syncDataList = routeMapper.selectByExample(example);
        for (RouteDO syncData : syncDataList) {
            RouteDTO routeDTO = DtoBuildUtil.buildRouteDTO(syncData);
            baseDataSyncService.syncDataMQSend(BaseDataSyncTypeEnums.ROUTE, BaseDataSyncTypeEnums.UPDATE, routeDTO);
        }
    }

    @Override
//    @Cacheable(value = "RouteWithCode" ,key = "#code",unless = "#result==null")
    public RouteDTO findByCode(String code) {
        RouteDO condition = new RouteDO();
        condition.setCode(code);
        RouteDO routeDO = routeMapper.selectOne(condition);
        if (routeDO == null) {
            return null;
        }
        return this.buildDTO(routeDO);
    }

    @Override
    public RouteResVo findByCodeV2(String code) {
        RouteDO condition = new RouteDO();
        condition.setCode(code);
        RouteDO routeDO = routeMapper.selectOne(condition);
        if (routeDO == null) {
            return null;
        }
        RouteResVo routeResVo = new RouteResVo();
        BeanUtils.copyProperties(routeDO, routeResVo);
        routeResVo.setActionJsonList(JSON.parseArray(routeDO.getActionJson(), String.class));
        return routeResVo;
    }

    @Override
    public RouteInfoResVo findRouteByCode(String code) {
        RouteDO condition = new RouteDO();
        condition.setCode(code);
        RouteDO routeDO = routeMapper.selectOne(condition);
        if (routeDO == null) {
            return null;
        }

        RouteInfoResVo routeResVo = new RouteInfoResVo();
        BeanUtils.copyProperties(routeDO, routeResVo);
        routeResVo.setActionJsonList(JSON.parseArray(routeDO.getActionJson(), String.class));
        // 设置申报配置
        String declareConfig = routeDO.getDeclareConfig();
        if (StringUtils.isEmpty(declareConfig)) {
            return routeResVo;
        }
        List<RouteConfigDto> routeConfigDtoList = JSON.parseArray(declareConfig, RouteConfigDto.class);
        if (CollectionUtils.isEmpty(routeConfigDtoList)) {
            return routeResVo;
        }
        Set<String> wayCodeSet = routeConfigDtoList.stream().flatMap(z -> {
            List<String> codeList = new ArrayList<>();
            codeList.add(z.getDeclareCode());
            codeList.add(z.getProxyCode());
            return codeList.stream();
        }).collect(Collectors.toSet());
        // 获取申报方式详细
        List<DeclareWayDTO> byCodes = declareWayBaseService.findByCodes(wayCodeSet);
        Map<String, DeclareWayDTO> declareWayDtoMap = byCodes.stream()
                .collect(Collectors.toMap(DeclareWayDTO::getCode, Function.identity(), (v1, v2) -> v1));
        String extraJson = routeResVo.getExtraJson();
        RouteExtra routeExtra = JSON.parseObject(extraJson, RouteExtra.class);
        List<RouteConfigResVo> declareConfigList = routeConfigDtoList.stream()
                .map(k -> {
                    if (StringUtils.isEmpty(k.getDeclareCode()) && StringUtils.isEmpty(k.getProxyCode())) {
                        return null;
                    }
                    String dxpId = null;
                    DeclareEnum declareEnum = DeclareEnum.getEnum(k.getType());
                    switch (declareEnum) {
                        case SHIPMENT:
                            dxpId = routeExtra.getLogisticsDeclareDxpId();
                            break;
                        case CUSTOMS_ORDER:
                            dxpId = routeExtra.getOrderDeclareDxpId();
                            break;
                        case INVENTORY:
                        case INVENTORY_CANCEL:
                        case INVENTORY_REFUND:
                            dxpId = routeExtra.getListDeclareDxpId();
                            break;
                        default:
                            break;
                    }
                    RouteConfigResVo configResVo = new RouteConfigResVo();
                    if (!StringUtils.isEmpty(dxpId)) {
                        configResVo.setDxpId(dxpId);
                    }
                    String proxyCode = k.getProxyCode();
                    configResVo.setProxyCode(proxyCode);
                    DeclareWayDTO proxyWay = declareWayDtoMap.get(proxyCode);
                    if (proxyWay != null) {
                        configResVo.setProxyImpl(proxyWay.getDeclareImpl());
                    }

                    String declareCode = k.getDeclareCode();
                    configResVo.setDeclareCode(declareCode);
                    DeclareWayDTO declareWay = declareWayDtoMap.get(declareCode);
                    if (declareWay != null) {
                        configResVo.setDeclareImpl(declareWay.getDeclareImpl());
                    }

                    configResVo.setType(k.getType());
                    return configResVo;
                }).filter(Objects::nonNull).collect(Collectors.toList());

        routeResVo.setDeclareConfigList(declareConfigList);
        return routeResVo;
    }

    @Override
    // @Cacheable(value = "RouteWithId" ,key = "#id",unless = "#result==null")
    public RouteDTO findById(Long id) {
        RouteDO routeDO = routeMapper.selectByPrimaryKey(id);
        return this.buildDTO(routeDO);
    }

    @Override
    public List<RouteDTO> findById(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return new ArrayList<>();
        }
        List<RouteDO> routeDOS = this.findRouteDOById(idList);
        return routeDOS.stream().map(r ->
                this.buildDTO(r)
        ).collect(Collectors.toList());
    }

    private List<RouteDO> findRouteDOById(List<Long> idList) {
        Example example = new Example(RouteDO.class);
        example.createCriteria().andIn("id", idList).andEqualTo("deleted", false);
        List<RouteDO> routeDOS = routeMapper.selectByExample(example);
        return routeDOS;
    }

    @Override
//    @Cacheable(value = "RouteWithEnable" ,unless = "#result==null")
    public List<RouteDTO> listEnable() {
        Example example = new Example(RouteDO.class);
        example.createCriteria().andEqualTo("deleted", false).andEqualTo("enable", 1);
        List<RouteDO> routeDOS = routeMapper.selectByExample(example);
        return routeDOS.stream().map(this::buildDTO).collect(Collectors.toList());
    }

//    @Override
//    public List<RouteDTO> listAll() {
//        RouteSearch search = new RouteSearch();
//        ListVO<RouteDTO> paging = this.paging(search);
//        return paging.getDataList();
//    }

    @Override
    public List<DeclareConfig> listDeclareConfig(Long id) {
        List<DeclareConfig> list = new ArrayList<>();
        if (id == null) {
            return list;
        }
        list = this.findDeclareConfigListByRouteId(id);
        return list;
    }

    /**
     * 批量修改担保企业
     *
     * @param idList
     * @param assureCompanyId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateAssureCompany(List<Long> idList, Long assureCompanyId) {
        log.info("batchUpdateAssureCompany assureCompanyId={} idList={}", assureCompanyId, JSON.toJSONString(idList));
        List<RouteDO> routeDOList = this.findRouteDOById(idList);
        CompanyDTO companyDTO = companyService.findUnifiedCrossInfoById(assureCompanyId);
        String guaranteeCustoms = companyDTO.getGuaranteeCustoms();
        if (StringUtils.isEmpty(guaranteeCustoms)) {
            throw new ArgsInvalidException("企业未配置保函关区");
        }
        List<String> guaranteeCustomsList = JSON.parseArray(guaranteeCustoms, String.class);
        Map<Long, CustomsBookResVo> customsBookMap = new HashMap<>(2 << 3);
        routeDOList.forEach(r -> {
            RouteExtra routeExtra = JSON.parseObject(r.getExtraJson(), RouteExtra.class);
            this.updateAssureCompanyCheck(guaranteeCustomsList, customsBookMap, r, routeExtra);
            //修改担保企业配置
            routeExtra.setAssureCompanyId(assureCompanyId);
            r.setExtraJson(JSON.toJSONString(routeExtra));
        });
        routeMapper.batchUpdateByPrimaryKey(routeDOList);
    }

    /**
     * 批量修改担保企业的校验
     *
     * @param guaranteeCustomsList
     * @param customsBookMap
     * @param r
     * @param routeExtra
     */
    private void updateAssureCompanyCheck(List<String> guaranteeCustomsList, Map<Long, CustomsBookResVo> customsBookMap, RouteDO r, RouteExtra routeExtra) {
        if (Objects.equals(r.getEnable(), 1)) {
            throw new ArgsInvalidException("路径:" + r.getName() + "启用中，无法修改担保企业");
        }
        String actionJson = r.getActionJson();
        if (!actionJson.contains(RouteActionEnum.DECLARE_ORDER.getCode()) &&
                !actionJson.contains(RouteActionEnum.DECLARE_INVENTORY.getCode())) {
            throw new ArgsInvalidException("路径:" + r.getName() + "未选择订单/清单申报项，无法修改担保企业");
        }
        // extra里面的校验
        Long customsBookId = routeExtra.getCustomsBookId();
        CustomsBookResVo customsBookResVo;
        if (customsBookMap.containsKey(customsBookId)) {
            customsBookResVo = customsBookMap.get(customsBookId);
        } else {
            customsBookResVo = customsBookService.findByIdV2(customsBookId);
            if (Objects.nonNull(customsBookResVo)) {
                customsBookMap.put(customsBookId, customsBookResVo);
            } else {
                throw new ArgsInvalidException("未查询到账册id:" + customsBookId + "信息");
            }
        }
        String customsAreaCode = customsBookResVo.getCustomsAreaCode();
        if (!guaranteeCustomsList.contains(customsAreaCode)) {
            throw new ArgsInvalidException("路径:" + r.getName() + "担保企业的保函关区不包含账册所在关区");
        }
    }

    /**
     * 批量修改清单/订单报文传输企业
     *
     * @param routeUpdateConfigReqVO
     */
    @Override
    public void batchUpdateMessageTransCompany(RouteUpdateConfigReqVO routeUpdateConfigReqVO) {
        log.info("batchUpdateMessageTransCompany reqVo={}", JSON.toJSONString(routeUpdateConfigReqVO));
        //批量修改的校验
        this.checkBatchUpdateTransCompanyParams(routeUpdateConfigReqVO);

        String type = routeUpdateConfigReqVO.getType();
        List<Long> idList = routeUpdateConfigReqVO.getIdList();
        List<DeclareConfig> newDeclareConfigList = routeUpdateConfigReqVO.getProxyDeclareConfigList();
        Long companyId = routeUpdateConfigReqVO.getCompanyId();
        String declareQueue = routeUpdateConfigReqVO.getDeclareQueue();
        List<RouteDTO> routeDTOList = this.findById(idList);
        List<RouteDO> routeDOList = new ArrayList<>();
        if (Objects.equals(type, RouteActionEnum.DECLARE_INVENTORY.getCode())) {
            routeDTOList.forEach(r -> {
                if (Objects.equals(r.getEnable(), 1)) {
                    throw new ArgsInvalidException("路径:" + r.getName() + "启用中，无法修改清单报文传输企业");
                }
                //其实这行不会触发 先留着吧
                if (!r.getActionList().contains(RouteActionEnum.DECLARE_INVENTORY.getCode())) {
                    throw new ArgsInvalidException("路径:" + r.getName() + "未选择清单申报项，无法修改清单报文传输企业");
                }
                RouteDO routeDO = new RouteDO();
                routeDO.setId(r.getId());
                //这个地方只能骚操作一下 dto里居然没有extra
                RouteExtra routeExtra = ConvertUtil.beanConvert(r, RouteExtra.class);
                routeExtra.setListDeclareCompanyId(companyId);
                routeExtra.setListDeclareDxpId(routeUpdateConfigReqVO.getDxpId());
                routeDO.setExtraJson(JSON.toJSONString(routeExtra));
                List<DeclareConfig> finalDeclareConfig = this.replaceNewConfig(newDeclareConfigList, r.getDeclareConfig());
                finalDeclareConfig.forEach(f -> {
                    if (Objects.equals(f.getType(), DeclareEnum.INVENTORY.getType())) {
                        f.setDeclareQueue(declareQueue);
                    }
                });
                routeDO.setDeclareConfig(JSON.toJSONString(finalDeclareConfig));
                routeDOList.add(routeDO);
            });
        } else if (Objects.equals(type, RouteActionEnum.DECLARE_ORDER.getCode())) {
            routeDTOList.forEach(r -> {
                if (Objects.equals(r.getEnable(), 1)) {
                    throw new ArgsInvalidException("路径:" + r.getName() + "启用中，无法修改订单报文传输企业");
                }
                if (!r.getActionList().contains(RouteActionEnum.DECLARE_ORDER.getCode())) {
                    throw new ArgsInvalidException("路径:" + r.getName() + "未选择订单申报项，无法修改订单报文传输企业");
                }
                RouteDO routeDO = new RouteDO();
                routeDO.setId(r.getId());
                //这个地方只能骚操作一下 dto里居然没有extra
                RouteExtra routeExtra = ConvertUtil.beanConvert(r, RouteExtra.class);
                routeExtra.setOrderDeclareCompanyId(companyId);
                routeExtra.setOrderDeclareDxpId(routeUpdateConfigReqVO.getDxpId());
                routeDO.setExtraJson(JSON.toJSONString(routeExtra));
                List<DeclareConfig> finalDeclareConfig = this.replaceNewConfig(newDeclareConfigList, r.getDeclareConfig());
                finalDeclareConfig.forEach(f -> {
                    if (Objects.equals(f.getType(), DeclareEnum.CUSTOMS_ORDER.getType())) {
                        f.setDeclareQueue(declareQueue);
                    }
                });
                routeDO.setDeclareConfig(JSON.toJSONString(finalDeclareConfig));
                routeDOList.add(routeDO);
            });
        }
        routeDOList.forEach(routeDO -> routeMapper.updateByPrimaryKeySelective(routeDO));
    }

    @Override
    public void batchUpdateRouteTag(List<Long> idList, String routeTag) {
        if (CollUtil.isEmpty(idList)) {
            throw new ArgsInvalidException("路径id不能为空");
        }
        if (StringUtil.isEmpty(routeTag)) {
            routeTag = "0";
        }
        RouteDO routeDO = new RouteDO();
        routeDO.setRouteTag(Integer.valueOf(routeTag));
        Example example = new Example(RouteDO.class);
        example.createCriteria().andEqualTo("deleted", false)
                .andIn("id", idList);
        routeMapper.updateByExampleSelective(routeDO, example);
    }

    /**
     * 替换新的申报配置
     *
     * @param newDeclareConfigList
     * @param oldDeclareConfig
     * @return
     */
    private List<DeclareConfig> replaceNewConfig(List<DeclareConfig> newDeclareConfigList, String oldDeclareConfig) {
        List<DeclareConfig> declareConfigList = JSON.parseArray(oldDeclareConfig, DeclareConfig.class);
        Map<String, DeclareConfig> typeMap = new HashMap<>(2 << 3);
        if (!CollectionUtils.isEmpty(declareConfigList)) {
            typeMap = declareConfigList.stream().collect(Collectors.toMap(DeclareConfig::getType, Function.identity(), (v1, v2) -> v1));
        }
        for (DeclareConfig p : newDeclareConfigList) {
            typeMap.put(p.getType(), p);
        }
        List<DeclareConfig> finalDeclareConfig = typeMap.values().stream().collect(Collectors.toList());
        return finalDeclareConfig;
    }

    /**
     * 校验批量修改传输企业参数
     *
     * @param routeUpdateConfigReqVO
     */
    private void checkBatchUpdateTransCompanyParams(RouteUpdateConfigReqVO routeUpdateConfigReqVO) {
        if (Objects.isNull(routeUpdateConfigReqVO)) {
            throw new ArgsInvalidException("参数为空");
        }
        if (Objects.isNull(routeUpdateConfigReqVO.getType())) {
            throw new ArgsInvalidException("未指定修改类型");
        }
        if (Objects.isNull(routeUpdateConfigReqVO.getCompanyId())) {
            throw new ArgsInvalidException("报文传输企业不允许为空");
        }
        if (Objects.isNull(routeUpdateConfigReqVO.getIdList())) {
            throw new ArgsInvalidException("修改路径不允许为空");
        }
//        if (Objects.isNull(routeUpdateConfigReqVO.getDxpId())) {
//            throw new ArgsInvalidException("传输节点不允许为空");
//        }
//        if (Objects.isNull(routeUpdateConfigReqVO.getDeclareQueue())) {
//            throw new ArgsInvalidException("隔离队列不允许为空");
//        }
        if (CollectionUtils.isEmpty(routeUpdateConfigReqVO.getProxyDeclareConfigList())) {
            throw new ArgsInvalidException("申报配置不允许为空");
        }
        CompanyDTO companyDTO = companyService.findById(routeUpdateConfigReqVO.getCompanyId());
        if (Objects.isNull(companyDTO)) {
            throw new ArgsInvalidException("未查询到报文传输企业");
        }
    }

    private RouteDTO buildDTO(RouteDO routeDO) {
        if (Objects.isNull(routeDO)) {
            return null;
        }
        return DtoBuildUtil.buildRouteDTO(routeDO);
    }

    @Override
    public List<RouteDTO> findByBookId(Long bookId) {
        Example example = new Example(RouteDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andCondition(" extra_json  REGEXP ',\\\"customsBookId\\\":" + bookId + ",'");
        criteria.andEqualTo("enable", true);
        List<RouteDO> list = routeMapper.selectByExample(example);
        if (list == null) list = new ArrayList<>();
        return list.stream().map(this::buildDTO).collect(Collectors.toList());
    }

    private List<DeclareConfig> findDeclareConfigListByRouteId(Long id) {
        List<DeclareConfig> list = new ArrayList<>();
        RouteDTO routeDTO = this.findById(id);
        if (Objects.nonNull(routeDTO)) {
            String declareConfig = routeDTO.getDeclareConfig();
            list = JSON.parseArray(declareConfig, DeclareConfig.class);
        }
        return list;
    }
}
