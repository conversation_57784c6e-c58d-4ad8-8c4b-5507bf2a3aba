package com.danding.cds.mapper;

import com.danding.cds.bean.dao.CallbackLogDO;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import tk.mybatis.mapper.additional.insert.InsertListMapper;
import tk.mybatis.mapper.common.Mapper;

/**
 * <AUTHOR>
 * @Create 2021/8/31  17:16
 * @Describe
 **/
public interface CustomsCallbackLogMapper extends Mapper<CallbackLogDO>, InsertListMapper<CallbackLogDO>, BatchUpdateMapper<CallbackLogDO>, AggregationPlusMapper<CallbackLogDO> {
}
