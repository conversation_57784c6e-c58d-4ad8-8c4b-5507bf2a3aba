package com.danding.cds.payChannel.impl.entity;

import com.danding.cds.common.model.BaseDO;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;

@Table(name = "ccs_pay_channel")
@Getter
@Setter
public class PayChannelDO extends BaseDO {
    /**
     * 支付渠道标识
     */
    private String code;

    /**
     * 支付渠道名称
     */
    private String name;

    /**
     * 支付企业ID
     */
    @Column(name = "pay_company_id")
    private Long payCompanyId;

    /**
     * 状态:0.停用;1.启用(默认)
     */
    private Integer enable;

    /**
     * 备注
     */
    private String remark;
}