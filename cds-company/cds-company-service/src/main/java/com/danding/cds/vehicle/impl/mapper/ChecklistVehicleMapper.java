package com.danding.cds.vehicle.impl.mapper;


import com.danding.cds.vehicle.impl.entity.ChecklistVehicleDO;
import com.danding.logistics.mybatis.common.Mapper;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import tk.mybatis.mapper.additional.insert.InsertListMapper;

public interface ChecklistVehicleMapper extends Mapper<ChecklistVehicleDO>, InsertListMapper<ChecklistVehicleDO>, BatchUpdateMapper<ChecklistVehicleDO>, AggregationPlusMapper<ChecklistVehicleDO> {
}