package com.danding.cds.message.impl.process.deliver;

import com.alibaba.fastjson.JSON;
import com.danding.cds.callback.api.dto.DeliverActiveInfo;
import com.danding.cds.message.api.dto.MessageDTO;
import com.danding.cds.message.api.enums.MessageType;
import com.danding.cds.message.api.process.OrderDeliverMessage;
import com.danding.cds.message.impl.process.MessageProcess;
import com.danding.cds.message.impl.process.MessageRegistry;
import com.danding.common.utils.CopyUtil;
import org.springframework.stereotype.Component;

import java.util.Map;


@Component
public class OrderDeliverProcess extends MessageProcess {
    public OrderDeliverProcess(MessageRegistry registry) {
        super(registry);
    }

    @Override
    public MessageType getType() {
        return MessageType.ORDER_DELIVER;
    }

    @Override
    public Map<String, Object> buildSendInfo(MessageDTO messageDTO) {
        DeliverActiveInfo activeInfo = JSON.parseObject(messageDTO.getActiveData(),DeliverActiveInfo.class);
        OrderDeliverMessage deliverMessage = CopyUtil.copy(activeInfo,OrderDeliverMessage.class);
        return objToMap(deliverMessage);
    }
}
