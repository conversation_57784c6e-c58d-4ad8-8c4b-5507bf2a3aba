package com.danding.cds.message.impl.mq;


import com.danding.cds.message.api.dto.MessageDTO;
import com.danding.cds.message.api.service.MessageService;
import com.danding.cds.mq.handler.MessageHandlerAfterInit;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RocketMQMessageListener(
        consumerGroup = "ccs-message-generate-consumer",
        topic = "ccs-message-generate-topic"
)
public class MessageGenerateConsumer extends MessageHandlerAfterInit {

    @DubboReference
    private MessageService messageService;

    @Override
    public void handle(Object message) throws RuntimeException {
        String key = message.toString();
        Long messageId = Long.valueOf(key);
        MessageDTO messageDTO = messageService.findById(messageId);
        messageService.generateTask(messageDTO);
    }
}
