package com.danding.cds.message.impl.process.customs;

import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.rpc.OrderCRpc;
import com.danding.cds.callback.api.dto.OrderActiveInfo;
import com.danding.cds.common.config.OrderCCallConfig;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.message.api.dto.MessageDTO;
import com.danding.cds.message.api.enums.MessageType;
import com.danding.cds.message.api.process.OrderCustomsOrderMessage;
import com.danding.cds.message.impl.process.MessageProcess;
import com.danding.cds.message.impl.process.MessageRegistry;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.api.service.OrderService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.joda.time.DateTime;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

@Component
public class OrderCustomsOrderPorcess extends MessageProcess {

    @DubboReference
    private OrderService orderService;
    @DubboReference
    private OrderCRpc orderCRpc;
    @Resource
    private OrderCCallConfig orderCCallConfig;


    @DubboReference
    private CompanyService companyService;

    public OrderCustomsOrderPorcess(MessageRegistry registry) {
        super(registry);
    }

    @Override
    public MessageType getType() {
        return MessageType.ORDER_CUSTOMS_ORDER;
    }

    @Override
    public Map<String, Object> buildSendInfo(MessageDTO messageDTO) {
        OrderCustomsOrderMessage message = new OrderCustomsOrderMessage();
        OrderActiveInfo activeInfo = JSON.parseObject(messageDTO.getActiveData(),OrderActiveInfo.class);
        OrderDTO orderDTO = orderCCallConfig.isOrderCCall(this.getClass()) ? orderCRpc.findByIdFull(activeInfo.getId()) : orderService.findByIdFull(activeInfo.getId());
        CompanyDTO ebp = companyService.findUnifiedCrossInfoById(orderDTO.getEbpId());
        message.setDeclareOrderNo(orderDTO.getDeclareOrderNo());
        message.setSystemGlobalSn(orderDTO.getSystemGlobalSn());
        message.setOutOrderNo(orderDTO.getOutOrderNo());
        message.setEbpCode(ebp.getCode());
        message.setCustomsStatus(activeInfo.getCustomsStatus());
        message.setCustomsDetail(activeInfo.getCustomsDetail());
        message.setTime(new DateTime(activeInfo.getCustomsTime()).getMillis());
        return objToMap(message);
    }
}
