package com.danding.cds.payChannel.impl.mapper;

import com.danding.cds.payChannel.impl.entity.PayChannelDO;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import tk.mybatis.mapper.additional.insert.InsertListMapper;
import tk.mybatis.mapper.common.Mapper;

public interface PayChannelMapper extends Mapper<PayChannelDO>, BatchUpdateMapper<PayChannelDO>, InsertListMapper<PayChannelDO>, AggregationPlusMapper<PayChannelDO> {

}