package com.danding.cds.sequence.impl.manager;

import com.danding.cds.sequence.impl.entity.SequenceDO;
import com.danding.cds.sequence.impl.mapper.SequenceMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.Calendar;
import java.util.Date;

@Transactional
@Service
public class SequenceManager {
	
	@Autowired
	private SequenceMapper sequenceDao;

	@Transactional(propagation = Propagation.REQUIRES_NEW)
    public Long nextSequence(String code, boolean reset) {
		Example example=new Example(SequenceDO.class);
		example.createCriteria().andEqualTo("code",code);
		SequenceDO seq= sequenceDao.selectOneByExample(example);
		if (seq != null) {
			Long ret = seq.getValue();
			ret++;
			if (reset) {
				if (seq.getTime() == null)
					ret = 1l;
				else {
					Calendar c = Calendar.getInstance();
					int today = c.get(Calendar.DATE);
					c.setTime(seq.getTime());
					if (c.get(Calendar.DATE) != today)
						ret = 1l;
				}
			}
			seq.setValue(ret);
			seq.setTime(new Date(System.currentTimeMillis()));
			Example _example=new Example(SequenceDO.class);
			Example.Criteria criteria = _example.createCriteria();
			criteria.andEqualTo("code",code);
			if(sequenceDao.updateByExample(seq,_example)>0)
				return ret;
			else
				return null;
		}else
		{
			SequenceDO sequenceDO = new SequenceDO();
			sequenceDO.setCode(code);
			sequenceDO.setName("未定义-"+code);
			sequenceDO.setValue(1L);
			sequenceDO.setTime(new Date());
			sequenceDao.insertSelective(sequenceDO);
			return 1L;
		}
	}

	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public Long stepSequence(String code, Long num) {

		Example example = new Example(SequenceDO.class);
		example.createCriteria().andEqualTo("code", code);
		SequenceDO seq = sequenceDao.selectOneByExample(example);
		Long source = num;
		if (seq != null) {
			Long ret = seq.getValue();
			// 赋值原来的值返回，数据库的值加上步长
			source = ret;
			ret = ret + num;
			seq.setValue(ret);
			seq.setTime(new Date(System.currentTimeMillis()));
			Example _example = new Example(SequenceDO.class);
			Example.Criteria criteria = _example.createCriteria();
			criteria.andEqualTo("code", code);
			if (sequenceDao.updateByExample(seq, _example) > 0) {
				return ++source;
			} else {
				return null;
			}
		} else {
			SequenceDO sequenceDO = new SequenceDO();
			sequenceDO.setCode(code);
			sequenceDO.setName("未定义-" + code);
			sequenceDO.setValue(source);
			sequenceDO.setTime(new Date());
			sequenceDao.insertSelective(sequenceDO);
			return 1L;
		}
	}
}
