package com.danding.cds.message.impl.process.order;

import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.rpc.CustomsInventoryRpc;
import com.danding.cds.c.api.rpc.CustomsOrderCRpc;
import com.danding.cds.c.api.rpc.OrderCRpc;
import com.danding.cds.callback.api.dto.OrderEventActive;
import com.danding.cds.common.config.OrderCCallConfig;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.customs.inventory.api.service.CustomsInventoryService;
import com.danding.cds.customs.order.api.dto.CustomsOrderDTO;
import com.danding.cds.customs.order.api.service.CustomsOrderService;
import com.danding.cds.message.api.dto.MessageDTO;
import com.danding.cds.message.api.enums.MessageType;
import com.danding.cds.message.api.process.OrderResultMessage;
import com.danding.cds.message.impl.process.MessageProcess;
import com.danding.cds.message.impl.process.MessageRegistry;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.api.service.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

@Component
@Slf4j
@RefreshScope
public class OrderResultProcess extends MessageProcess {

    @DubboReference
    private CustomsInventoryService customsInventoryService;
    @DubboReference
    private CustomsInventoryRpc customsInventoryRpc;

    @DubboReference
    private CustomsOrderService customsOrderService;
    @DubboReference
    private CustomsOrderCRpc customsOrderCRpc;

    @DubboReference
    private CompanyService companyService;

    @DubboReference
    private OrderService orderService;
    @DubboReference
    private OrderCRpc orderCRpc;
    @Resource
    private OrderCCallConfig orderCCallConfig;



    public OrderResultProcess(MessageRegistry registry) {
        super(registry);
    }

    @Override
    public MessageType getType() {
        return MessageType.ORDER_RESULT;
    }

    //根据电商平台调整申报单创建时间 （以秒为单位）
    @Value("${ebpId.offset.createTime:}")
    private String ebpIdOffsetCreateTimeJSON;

    @Override
    public Map<String, Object> buildSendInfo(MessageDTO messageDTO) {
        OrderResultMessage message = new OrderResultMessage();
        OrderEventActive activeInfo = JSON.parseObject(messageDTO.getActiveData(), OrderEventActive.class);
        OrderDTO orderDTO = orderCCallConfig.isOrderCCall(this.getClass()) ? orderCRpc.findByIdFull(activeInfo.getId()) : orderService.findByIdFull(activeInfo.getId());
        CompanyDTO ebp = companyService.findUnifiedCrossInfoById(orderDTO.getEbpId());
        message.setEbcCode(ebp.getCode());
        message.setDeclareOrderNo(orderDTO.getDeclareOrderNo());
        message.setOutOrderNo(orderDTO.getOutOrderNo());
        message.setSystemGlobalSn(orderDTO.getSystemGlobalSn());
        message.setStatus(activeInfo.getStatus());
        message.setDeclareOrderNo(activeInfo.getDetail());
        message.setTime(orderDTO.getCreateTime().getTime());
        if ("start".equals(activeInfo.getStatus())) {
            Map<Long, Long> ebpIdSecondMap = JSON.parseObject(ebpIdOffsetCreateTimeJSON, Map.class);
            log.info("ebpIdOffsetCreateTimeJSON={}", ebpIdOffsetCreateTimeJSON);
            message.setEbpCode(ebp.getCode());
            message.setEbpName(ebp.getName());
            if (Objects.nonNull(ebpIdSecondMap)) {
                Long offsetSeconds = ebpIdSecondMap.get(orderDTO.getEbpId());
                if (!LongUtil.isNone(offsetSeconds)) {
                    message.setTime(message.getTime() + (offsetSeconds * 1000));
                }
            }
            CustomsInventoryDTO inventoryDTO = orderCCallConfig.isOrderCCall(this.getClass()) ? customsInventoryRpc.findByOrder(orderDTO.getId(), orderDTO.getCustomsInventorySn()) : customsInventoryService.findByOrder(orderDTO.getId(), orderDTO.getCustomsInventorySn());
            if (inventoryDTO != null) {
                CompanyDTO ebc = companyService.findUnifiedCrossInfoById(inventoryDTO.getEbcId());
                message.setEbcCode(ebc.getCode());
                message.setEbcName(ebc.getName());
                CompanyDTO assure = companyService.findUnifiedCrossInfoById(inventoryDTO.getAssureCompanyId());
                message.setAssureCode(assure.getCode());
                message.setAssureName(assure.getName());
                CompanyDTO logistics = companyService.findUnifiedCrossInfoById(inventoryDTO.getLogisticsCompanyId());
                message.setLogisticsCode(logistics.getCode());
                message.setLogisticsCode(logistics.getName());
            }
            CustomsOrderDTO customsOrderDTO = orderCCallConfig.isOrderCCall(this.getClass()) ? customsOrderCRpc.findByOrder(orderDTO.getId(), orderDTO.getCustomsOrderSn()) : customsOrderService.findByOrder(orderDTO.getId(), orderDTO.getCustomsOrderSn());
            if (customsOrderDTO != null) {
                CompanyDTO payCompany = companyService.findUnifiedCrossInfoById(customsOrderDTO.getPayCompanyId());
                message.setPayCompanyCode(payCompany.getCode());
                message.setPayCompanyName(payCompany.getName());
            }
        }
        return objToMap(message);
    }
}
