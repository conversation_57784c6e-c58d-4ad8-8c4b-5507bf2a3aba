package com.danding.cds.customs.manager.impl.entity;

import com.danding.cds.common.model.BaseDO;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * @Author: yousx
 * @Date: 2023/11/23
 * @Description:
 */
@Getter
@Setter
@Table(name = "ccs_customs_district")
public class CustomsDistrictDO extends BaseDO implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 编码
     */
    @Column(name = "code")
    private String code;

    /**
     * 名称
     */
    @Column(name = "name")
    private String name;

    /**
     * 所属海关
     */
    @Column(name = "customs")
    private String customs;

    /**
     * 海关编码
     */
    @Column(name = "port_code")
    private String portCode;

    /**
     * 租户id
     */
    @Column(name = "tenantry_id")
    private Long tenantryId;

    /**
     * 状态：1:开启 0:关闭 默认1
     */
    @Column(name = "status")
    private Integer status;

}
