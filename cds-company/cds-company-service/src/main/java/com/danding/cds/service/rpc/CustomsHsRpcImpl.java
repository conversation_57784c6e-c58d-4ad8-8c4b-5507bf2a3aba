package com.danding.cds.service.rpc;

import com.danding.cds.common.enums.HsConsumptionFlagEnums;
import com.danding.cds.common.enums.HsFloatTypeEnums;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.customs.hs.api.dto.CustomsHsDTO;
import com.danding.cds.customs.hs.api.service.CustomsHsService;
import com.danding.cds.out.api.CustomsHsRpc;
import com.danding.cds.out.bean.vo.res.CustomsHsResVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description:
 * @date 2025/2/10 13:56
 */
@Slf4j
@DubboService
public class CustomsHsRpcImpl implements CustomsHsRpc {
    @DubboReference
    private CustomsHsService customsHsService;

    @Override
    public CustomsHsResVo findByCode(String code) {
        CustomsHsDTO customsHsDTO = customsHsService.findByCode(code);
        CustomsHsResVo resVo = ConvertUtil.beanConvert(customsHsDTO, CustomsHsResVo.class);
        // 消费税计征标准(0=不征；5=从量；10=从价)
        if (Objects.nonNull(resVo) && Objects.nonNull(customsHsDTO.getFloatType())) {
            resVo.setConsumptionFlag(0);
            //原从量-5，都改为<从价定率>、<浮动类型：化妆品浮动或面膜浮动>
            if (Objects.equals(customsHsDTO.getConsumptionFlag(), HsConsumptionFlagEnums.PRICE.getCode())) {
                if (Objects.equals(customsHsDTO.getFloatType(), HsFloatTypeEnums.HZP_FLOAT.getCode()) ||
                        Objects.equals(customsHsDTO.getFloatType(), HsFloatTypeEnums.MM_FLOAT.getCode())) {
                    resVo.setConsumptionFlag(5);
                } else if (Objects.equals(customsHsDTO.getFloatType(), HsFloatTypeEnums.NO_FLOAT.getCode())) {
                    //原从价-10，都改为<从价定率>、<浮动类型：不浮动>
                    resVo.setConsumptionFlag(10);
                }
            }
        }
        return resVo;
    }
}
