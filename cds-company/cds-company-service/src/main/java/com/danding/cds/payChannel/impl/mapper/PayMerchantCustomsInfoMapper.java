package com.danding.cds.payChannel.impl.mapper;


import com.danding.cds.payChannel.impl.entity.PayMerchantCustomsInfoDO;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import tk.mybatis.mapper.additional.insert.InsertListMapper;
import tk.mybatis.mapper.common.Mapper;

public interface PayMerchantCustomsInfoMapper extends Mapper<PayMerchantCustomsInfoDO>, InsertListMapper<PayMerchantCustomsInfoDO>, BatchUpdateMapper<PayMerchantCustomsInfoDO>, AggregationPlusMapper<PayMerchantCustomsInfoDO> {
}