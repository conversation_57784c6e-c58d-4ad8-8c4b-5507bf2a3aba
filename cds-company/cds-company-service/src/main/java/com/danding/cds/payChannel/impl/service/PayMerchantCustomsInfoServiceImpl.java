package com.danding.cds.payChannel.impl.service;

import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.common.utils.ValidatorUtils;
import com.danding.cds.customs.district.api.enums.CustomsDistrictEnum;
import com.danding.cds.declare.sdk.enums.PayCustoms;
import com.danding.cds.payChannel.api.dto.PayMerchantAccountDTO;
import com.danding.cds.payChannel.api.dto.PayMerchantCustomsInfoDTO;
import com.danding.cds.payChannel.api.dto.PayMerchantCustomsInfoSearch;
import com.danding.cds.payChannel.api.dto.PayMerchantCustomsInfoSubmit;
import com.danding.cds.payChannel.api.service.PayMerchantAccountService;
import com.danding.cds.payChannel.api.service.PayMerchantCustomsInfoService;
import com.danding.cds.payChannel.impl.entity.PayMerchantCustomsInfoDO;
import com.danding.cds.payChannel.impl.mapper.PayMerchantCustomsInfoMapper;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.common.response.SelectItemVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import javax.validation.Validator;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: Raymond
 * @Date: 2020/8/21 13:51
 * @Description:
 */
@Slf4j
@DubboService
public class PayMerchantCustomsInfoServiceImpl implements PayMerchantCustomsInfoService {
    @Autowired
    private PayMerchantCustomsInfoMapper payMerchantCustomsInfoMapper;
    @Autowired
    private PayMerchantAccountService payMerchantAccountService;

    @Autowired
    private Validator validator;

    @Override
    @PageSelect
    public ListVO<PayMerchantCustomsInfoDTO> paging(PayMerchantCustomsInfoSearch search) {
        Example example = new Example(PayMerchantCustomsInfoDO.class);
        example.orderBy("createTime").desc();
        Example.Criteria criteria = example.createCriteria();
        if (!StringUtils.isEmpty(search.getMerchantSn())){
            criteria.andLike("merchantSn", "%" + search.getMerchantSn() + "%");
        }

        if (!StringUtils.isEmpty(search.getCustoms())){
            criteria.andEqualTo("customs", search.getCustoms());
        }

        if (!LongUtil.isNone(search.getMerchantId())){
            criteria.andEqualTo("merchantId", search.getMerchantId());
        }

        if (!StringUtils.isEmpty(search.getMerchantCustomsCode())){
            criteria.andEqualTo("merchantCustomsCode", search.getMerchantCustomsCode());
        }

        List<PayMerchantCustomsInfoDO> list = payMerchantCustomsInfoMapper.selectByExample(example);
        for (PayMerchantCustomsInfoDO item : list) {
            item.setCustoms(PayCustoms.getEnum(item.getCustoms()).getDesc());
        }
        ListVO<PayMerchantCustomsInfoDTO> result = new ListVO<>();

        List<PayMerchantCustomsInfoDTO> dataList = list.stream().map((PayMerchantCustomsInfoDO item) -> {
            PayMerchantCustomsInfoDTO optionDTO = new PayMerchantCustomsInfoDTO();
            BeanUtils.copyProperties(item, optionDTO);
            return optionDTO;
        }).collect(Collectors.toList());
        result.setDataList(dataList);
        // 分页
        PageInfo<PayMerchantCustomsInfoDTO> pageInfo = new PageInfo(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    @Override
    public List<PayMerchantCustomsInfoDTO> queryListPayMerchantCustomsInfoExport(PayMerchantCustomsInfoSearch search) {
        Example example = new Example(PayMerchantCustomsInfoDO.class);
        example.orderBy("createTime").desc();
        Example.Criteria criteria = example.createCriteria();
        if (!StringUtils.isEmpty(search.getMerchantSn())){
            criteria.andLike("merchantSn", "%" + search.getMerchantSn() + "%");
        }


        if (!StringUtils.isEmpty(search.getCustoms())){
            criteria.andEqualTo("customs", search.getCustoms());
        }

        if (!LongUtil.isNone(search.getMerchantId())){
            criteria.andEqualTo("merchantId", search.getMerchantId());
        }

        List<PayMerchantCustomsInfoDO> list = payMerchantCustomsInfoMapper.selectByExample(example);

        List<PayMerchantCustomsInfoDTO> oretList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            for (PayMerchantCustomsInfoDO info : list) {
                PayMerchantCustomsInfoDTO infoDto = new PayMerchantCustomsInfoDTO();
                BeanUtils.copyProperties(info, infoDto);
                infoDto.setCustoms(PayCustoms.getEnum(info.getCustoms()).getDesc());
                oretList.add(infoDto);
            }
        }
        return oretList;
    }

    @Override
    public Long upset(PayMerchantCustomsInfoSubmit submit) throws ArgsErrorException {
        // Step::入参校验
        String inputError = ValidatorUtils.doValidator(validator,submit);
        if (null != inputError){
            throw new ArgsErrorException(inputError);
        }

        // Step::构造持久化对象
        PayMerchantCustomsInfoDO payMerchantCustomsInfoDO = new PayMerchantCustomsInfoDO();
        // 基础信息
        BeanUtils.copyProperties(submit, payMerchantCustomsInfoDO);
        if (submit.getId() == null || submit.getId() == 0){
            payMerchantCustomsInfoDO.setId(null);
            PayMerchantAccountDTO payMerchantAccountDTO = payMerchantAccountService.findById(payMerchantCustomsInfoDO.getMerchantId());
            payMerchantCustomsInfoDO.setMerchantSn(payMerchantAccountDTO.getSn());
            PayMerchantCustomsInfoDTO payMerchantCustomsInfoDTO = findByCodeAndCustoms(payMerchantCustomsInfoDO.getMerchantSn(),  submit.getCustoms());
            if (payMerchantCustomsInfoDTO != null) {
                throw new ArgsErrorException("该商户报关资质不能重复添加");
            }
            UserUtils.setCreateAndUpdateBy(payMerchantCustomsInfoDO);
            payMerchantCustomsInfoMapper.insertSelective(payMerchantCustomsInfoDO);
        }else {
            PayMerchantCustomsInfoDTO old = this.findById(submit.getId());
            if (old == null) {
                throw new ArgsErrorException("ID不正确");
            }

            if (!old.getCustoms().equalsIgnoreCase(PayCustoms.getEnum(submit.getCustoms()).getDesc())) {
                PayMerchantCustomsInfoDTO payMerchantCustomsInfoDTO = findByCodeAndCustoms(old.getMerchantSn(), submit.getCustoms());
                if (payMerchantCustomsInfoDTO != null) {
                    throw new ArgsErrorException("该商户报关资质不能重复添加");
                }
            }
            if (!Objects.equals(UserUtils.getUserId(), 0)) {
                UserUtils.setUpdateBy(payMerchantCustomsInfoDO);
            }
            payMerchantCustomsInfoDO.setUpdateTime(new Date());
            payMerchantCustomsInfoMapper.updateByPrimaryKeySelective(payMerchantCustomsInfoDO);
        }
        submit.setId(payMerchantCustomsInfoDO.getId());
        return payMerchantCustomsInfoDO.getId();
    }

    @Override
    public PayMerchantCustomsInfoDTO findById(long id) {
        if (id == 0) {
            return null;
        } else {
            PayMerchantCustomsInfoDO payMerchantCustomsInfoDO = payMerchantCustomsInfoMapper.selectByPrimaryKey(id);
            if (payMerchantCustomsInfoDO == null) {
                return null;
            } else {
                PayMerchantCustomsInfoDTO payMerchantCustomsInfoDTO = new PayMerchantCustomsInfoDTO();
                BeanUtils.copyProperties(payMerchantCustomsInfoDO, payMerchantCustomsInfoDTO);
                payMerchantCustomsInfoDTO.setCustoms(PayCustoms.getEnum(payMerchantCustomsInfoDO.getCustoms()).getDesc());
                return payMerchantCustomsInfoDTO;
            }
        }
    }

    @Override
    public PayMerchantCustomsInfoDTO findByCodeAndCustoms(String merchantSn, String customs) {
        Example example = new Example(PayMerchantCustomsInfoDO.class);
        example.orderBy("createTime").desc();
        Example.Criteria criteria = example.createCriteria();


        if (!StringUtils.isEmpty(customs)){
            criteria.andEqualTo("customs", customs);
        }

        if (!StringUtils.isEmpty(merchantSn)){
            criteria.andEqualTo("merchantSn", merchantSn);
        }

        PayMerchantCustomsInfoDO oneInfo = payMerchantCustomsInfoMapper.selectOneByExample(example);
        if (oneInfo == null) {
            return null;
        }
        PayMerchantCustomsInfoDTO payMerchantCustomsInfoDTO = new PayMerchantCustomsInfoDTO();
        BeanUtils.copyProperties(oneInfo, payMerchantCustomsInfoDTO);
        return payMerchantCustomsInfoDTO;
    }

    @Override
    public PayMerchantCustomsInfoDTO findByMerchantAndCustoms(Long id, String customs) {
        Example example = new Example(PayMerchantCustomsInfoDO.class);
        example.orderBy("createTime").desc();
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("customs", customs);
        criteria.andEqualTo("merchantId", id);
        PayMerchantCustomsInfoDO oneInfo = payMerchantCustomsInfoMapper.selectOneByExample(example);
        if (oneInfo == null) {
            return null;
        }
        PayMerchantCustomsInfoDTO payMerchantCustomsInfoDTO = new PayMerchantCustomsInfoDTO();
        BeanUtils.copyProperties(oneInfo, payMerchantCustomsInfoDTO);
        return payMerchantCustomsInfoDTO;
    }

    @Override
    public List<SelectItemVO> listPayCustoms() {
        List<SelectItemVO> result = new ArrayList<>();
        for (CustomsDistrictEnum value : CustomsDistrictEnum.values()) {
            SelectItemVO optionDTO = new SelectItemVO();
            if (!StringUtils.isEmpty(value.getCode())) {
                optionDTO.setValue(value.getCode());
                optionDTO.setName(value.getDesc());
                result.add(optionDTO);
            }
        }
        return result;
    }
}
