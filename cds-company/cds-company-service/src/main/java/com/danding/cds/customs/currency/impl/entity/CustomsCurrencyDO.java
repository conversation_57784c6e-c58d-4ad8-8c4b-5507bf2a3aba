package com.danding.cds.customs.currency.impl.entity;

import com.danding.cds.common.model.BaseDO;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Table;
import java.io.Serializable;

@Table(name = "ccs_customs_currency")
@Getter
@Setter
public class CustomsCurrencyDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 5399378321673242684L;
    /**
     * 币制编码
     */
    private String code;

    /**
     * 币制名称
     */
    private String name;

}
