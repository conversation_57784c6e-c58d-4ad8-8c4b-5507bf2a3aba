package com.danding.cds.message.impl.mq;

import com.danding.cds.mq.sender.MessageSenderService;
import com.danding.logistics.mq.common.handler.MessageSender;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class MessageGenerateProducer {

    @Autowired
    private MessageSender messageSender;
    @Autowired
    private MessageSenderService messageSenderService;

    public Boolean send(Long id) {
        String bizResult = id + "";
        messageSender.sendMsg(bizResult, "ccs-message-generate-topic:default");
        return true;
    }

    public Boolean send(Long id, Integer delayLevel) {
        if (Objects.nonNull(delayLevel)) {
            String bizResult = id + "";
            messageSenderService.asyncSendMsgDelay(bizResult, "ccs-message-generate-topic:default", delayLevel);
            return true;
        } else {
            this.send(id);
        }
        return true;
    }


}
