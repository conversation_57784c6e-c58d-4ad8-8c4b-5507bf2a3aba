package com.danding.cds.customs.dictionary.impl.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.common.model.excel.ImportResultResVo;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.common.utils.JSONUtils;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.customs.country.api.dto.DataDictionaryReport;
import com.danding.cds.customs.country.api.dto.ImportExcelDataDictionaryDTO;
import com.danding.cds.customs.dictionary.api.dto.CustomsDictionaryDTO;
import com.danding.cds.customs.dictionary.api.enums.DataDictionaryMainTypeEnums;
import com.danding.cds.customs.dictionary.api.enums.DataDictionaryTypeEnums;
import com.danding.cds.customs.dictionary.api.service.CustomsDictionaryService;
import com.danding.cds.customs.dictionary.api.vo.CustomsDictionaryOperateParam;
import com.danding.cds.customs.dictionary.api.vo.CustomsDictionaryReqVO;
import com.danding.cds.customs.dictionary.api.vo.CustomsDictionaryResVO;
import com.danding.cds.customs.dictionary.api.vo.DataDictionaryEnableSwitchVO;
import com.danding.cds.customs.dictionary.impl.entity.CustomsDictionaryDO;
import com.danding.cds.customs.dictionary.impl.mapper.CustomsDictionaryMapper;
import com.danding.cds.sequence.api.service.SequenceService;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.api.common.response.PageResult;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.danding.soul.client.common.exception.BusinessException;
import com.github.pagehelper.PageInfo;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

@DubboService
@Slf4j
public class CustomsDictionaryServiceImpl implements CustomsDictionaryService {

    @Autowired
    private CustomsDictionaryMapper customsDictionaryMapper;

    @DubboReference
    private SequenceService sequenceService;

    @Override
    @PageSelect
    public ListVO<CustomsDictionaryResVO> paging(CustomsDictionaryReqVO search) {
        Example example = buildExample(search);
        List<CustomsDictionaryDO> list = customsDictionaryMapper.selectByExample(example);
        List<CustomsDictionaryDTO> listDTO = list.stream().map(this::buildDTO).collect(Collectors.toList());
        ListVO<CustomsDictionaryResVO> result = new ListVO<>();
        result.setDataList(listDTO.stream().map(dto -> {
            CustomsDictionaryResVO resVO = new CustomsDictionaryResVO();
            BeanUtils.copyProperties(dto, resVO);
            resVO.setMainTypeDesc(DataDictionaryMainTypeEnums.getEnumByValue(dto.getMainType()).getDesc());
            resVO.setId(dto.getId().toString());
            return resVO;
        }).collect(Collectors.toList()));
        // 分页
        PageInfo<CustomsDictionaryDO> pageInfo = new PageInfo<>(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(search.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    @Override
    public void insert(CustomsDictionaryOperateParam param) {
        String errorMsg = checkValid(param);
        if (StringUtils.isNotEmpty(errorMsg)) {
            log.error("字典插入失败: " + errorMsg);
            throw new BusinessException(errorMsg);
        }
        CustomsDictionaryDO customsDictionaryDO = new CustomsDictionaryDO();
        BeanUtils.copyProperties(param, customsDictionaryDO);
        customsDictionaryMapper.insertSelective(customsDictionaryDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insert(List<CustomsDictionaryOperateParam> paramList) {
        paramList.forEach(param -> {
            String errorMsg = checkValid(param);
            if (StringUtils.isNotEmpty(errorMsg)) {
                log.error("字典插入失败: " + errorMsg);
                throw new BusinessException(errorMsg);
            }
            CustomsDictionaryDO customsDictionaryDO = new CustomsDictionaryDO();
            BeanUtils.copyProperties(param, customsDictionaryDO);
            customsDictionaryMapper.insertSelective(customsDictionaryDO);
        });
    }

    private String checkValid(CustomsDictionaryOperateParam param) {
        if (Objects.isNull(param)) {
            return ("参数为空");
        }
        if (StringUtils.isEmpty(param.getType())) {
            return ("类型参数为空");
        }
        if (StringUtils.isEmpty(param.getCode())) {
            return ("标识(Code)参数为空");
        }
        DataDictionaryTypeEnums typeEnum = DataDictionaryTypeEnums.getEnumByValue(param.getType());
        //to—B清关超时不检查
        if (Objects.equals(typeEnum, DataDictionaryTypeEnums.TO_B_OVERDUE)) {
            return null;
        } else {
            Example example = new Example(CustomsDictionaryDO.class);
            example.createCriteria().andEqualTo("deleted", false)
                    .andEqualTo("type", param.getType())
                    .andEqualTo("code", param.getCode());
            List<CustomsDictionaryDO> customsDictionaryDOS = customsDictionaryMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(customsDictionaryDOS)) {
                return null;
            }
            CustomsDictionaryDO customsDictionaryDO = customsDictionaryDOS.get(0);
            if (customsDictionaryDOS.size() > 1
                    || (Objects.nonNull(param.getId()) && !Objects.equals(customsDictionaryDO.getId(), Long.valueOf(param.getId()))) //更新 code 重复且 id 不一致
                    || (Objects.isNull(param.getId()) && CollectionUtil.isNotEmpty(customsDictionaryDOS)) //新增
            ) {
                String typeDesc = Objects.equals(typeEnum, DataDictionaryTypeEnums.NULL) ? param.getType() : typeEnum.getDesc();
                return ("标识(Code)为[" + param.getCode() + "] 在 [" + typeDesc + "]下已存在");
            }

        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(CustomsDictionaryOperateParam param) {
        String errorMsg = checkValid(param);
        if (StringUtil.isNotEmpty(errorMsg)) {
            log.error("字典编辑失败: " + errorMsg);
            throw new BusinessException(errorMsg);
        }
        if (StringUtils.isEmpty(param.getId())) {
            log.error("字典编辑失败: " + errorMsg);
            throw new BusinessException("id不能为空");
        }
        CustomsDictionaryDO target = customsDictionaryMapper.selectByPrimaryKey(Long.valueOf(param.getId()));
        if (!target.getEnableModify()) {
            log.error("字典编辑失败: " + errorMsg);
            throw new BusinessException("该数据不可编辑");
        }
        if (Objects.equals(1, target.getEnable()) && Objects.equals(target.getType(), DataDictionaryTypeEnums.TYPE.getValue())) {
            log.error("字典编辑失败: " + errorMsg);
            throw new BusinessException("该数据类型已启用,不可编辑");
        }
        if ((Objects.equals(target.getType(), "type")) && !Objects.equals(param.getCode(), target.getCode())) {
            //类型数据 变更code后，类型下的type 需要同步
            Example example = new Example(CustomsDictionaryDO.class);
            example.createCriteria().orEqualTo("type", target.getCode());
            CustomsDictionaryDO customsDictionaryDO = new CustomsDictionaryDO();
            customsDictionaryDO.setType(param.getCode());
            UserUtils.setUpdateBy(customsDictionaryDO);
            customsDictionaryDO.setUpdateTime(new Date());
            customsDictionaryMapper.updateByExampleSelective(customsDictionaryDO, example);
        }
        if (Objects.nonNull(param.getMainType())) {
            target.setMainType(param.getMainType());
        }
        target.setCode(param.getCode());
        target.setName(param.getName());
        UserUtils.setUpdateBy(target);
        target.setUpdateTime(new Date());
        customsDictionaryMapper.updateByPrimaryKeySelective(target);
    }

    @Override
    public void delete(Long id) {
        //逻辑删除
        if (LongUtil.isNone(id)) {
            throw new ArgsErrorException("参数为空");
        }
        CustomsDictionaryDO target = customsDictionaryMapper.selectByPrimaryKey(id);
        if (Objects.isNull(target)) {
            throw new RuntimeException("数据不存在");
        }
        if (Objects.equals(target.getEnableModify(), false)) {
            throw new RuntimeException("该数据不可编辑");
        }
        Example example = new Example(CustomsDictionaryDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("id", id);
        if (Objects.equals(target.getType(), "type")) {
            criteria.orEqualTo("type", target.getCode());
        }
        CustomsDictionaryDO customsDictionaryDO = new CustomsDictionaryDO();
        customsDictionaryDO.setDeleted(true);
        UserUtils.setUpdateBy(customsDictionaryDO);
        customsDictionaryDO.setUpdateTime(new Date());
        customsDictionaryMapper.updateByExampleSelective(customsDictionaryDO, example);
    }

    @Override
    public CustomsDictionaryDTO findById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        CustomsDictionaryDO customsDictionaryDO = customsDictionaryMapper.selectByPrimaryKey(id);
        return ConvertUtil.beanConvert(customsDictionaryDO, CustomsDictionaryDTO.class);
    }

    @Override
    public CustomsDictionaryDTO findByCodeAndType(String code, String type) {
        if (StringUtils.isEmpty(code) || StringUtils.isEmpty(type)) {
            log.warn("参数为空");
            return null;
        }
        Example example = new Example(CustomsDictionaryDO.class);
        example.createCriteria().andEqualTo("deleted", 0).andEqualTo("type", type).andEqualTo("code", code);
        CustomsDictionaryDO customsDictionaryDO = customsDictionaryMapper.selectOneByExample(example);
        return buildDTO(customsDictionaryDO);
    }

    @Override
    public CustomsDictionaryDTO findByCodeAndType(String code, List<String> typeList) {
        if (StringUtils.isEmpty(code) || CollUtil.isEmpty(typeList)) {
            log.warn("参数为空");
            return null;
        }
        Example example = new Example(CustomsDictionaryDO.class);
        example.createCriteria().andEqualTo("deleted", 0).andIn("type", typeList).andEqualTo("code", code);
        CustomsDictionaryDO customsDictionaryDO = customsDictionaryMapper.selectOneByExample(example);
        return buildDTO(customsDictionaryDO);
    }

    @Override
    public List<CustomsDictionaryDTO> findByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return new ArrayList<>();
        }
        Example example = new Example(CustomsDictionaryDO.class);
        example.createCriteria().andEqualTo("deleted", 0).andEqualTo("type", type).andEqualTo("enable", 1);
        List<CustomsDictionaryDO> customsDictionaryDOS = customsDictionaryMapper.selectByExample(example);
        return customsDictionaryDOS.stream().map(this::buildDTO).collect(Collectors.toList());
    }

    @Override
    public Map<String, String> getMapByType(String type) {
        List<CustomsDictionaryDTO> customsDictionaryDTOList = this.findByType(type);
        return customsDictionaryDTOList.stream().collect(Collectors.toMap(CustomsDictionaryDTO::getCode, CustomsDictionaryDTO::getName));
    }

    @Override
    public List<CustomsDictionaryDTO> findByTypeList(List<String> typeList) {
        if (CollUtil.isEmpty(typeList)) {
            return new ArrayList<>();
        }
        Example example = new Example(CustomsDictionaryDO.class);
        example.createCriteria().andEqualTo("deleted", 0).andIn("type", typeList);
        List<CustomsDictionaryDO> customsDictionaryDOS = customsDictionaryMapper.selectByExample(example);
        return customsDictionaryDOS.stream().map(this::buildDTO).collect(Collectors.toList());
    }

    @Override
    public Integer getCountByType(String type) {
        if (StringUtils.isEmpty(type)) {
            log.warn("字典表 getCountByType, 类型为空");
            return null;
        }
        Example example = new Example(CustomsDictionaryDO.class);
        example.createCriteria().andEqualTo("deleted", 0).andEqualTo("type", type);
        return customsDictionaryMapper.selectCountByExample(example);
    }

    @Override
    public List<SelectOptionVO<String>> listByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return new ArrayList<>();
        }
        List<CustomsDictionaryDTO> dtoList = this.findByType(type);
        if (CollectionUtils.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        return dtoList.stream()
                .map((CustomsDictionaryDTO item) -> {
                    SelectOptionVO<String> optionDTO = new SelectOptionVO<>();
                    optionDTO.setId(item.getCode());
                    optionDTO.setName(item.getName());
                    return optionDTO;
                }).collect(Collectors.toList());
    }

    @Override
    public List<SelectOptionVO<String>> listByTypeBatch(List<String> typeList) {
        if (CollUtil.isEmpty(typeList)) {
            return new ArrayList<>();
        }
        List<CustomsDictionaryDTO> dtoList = this.findByTypeList(typeList);
        if (CollectionUtils.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        return dtoList.stream()
                .map((CustomsDictionaryDTO item) -> {
                    SelectOptionVO<String> optionDTO = new SelectOptionVO<>();
                    optionDTO.setId(item.getCode());
                    optionDTO.setName(item.getName());
                    return optionDTO;
                }).collect(Collectors.toList());
    }

    public void updateByCodeAndType(CustomsDictionaryOperateParam data) {
        if (Objects.isNull(data)) {
            log.warn("DataDictionary updateByCodeAndType, data is null");
            return;
        }
        Example example = new Example(CustomsDictionaryDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("deleted", false)
                .andEqualTo("code", data.getCode())
                .andEqualTo("type", data.getType())
                .andEqualTo("enableModify", true);
        CustomsDictionaryDO customsDictionaryDO = new CustomsDictionaryDO();
        customsDictionaryDO.setName(data.getName());
        UserUtils.setUpdateBy(customsDictionaryDO);
        customsDictionaryDO.setUpdateTime(new Date());
        customsDictionaryMapper.updateByExampleSelective(customsDictionaryDO, example);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateBatchByType(List<CustomsDictionaryOperateParam> dataList, String type) {

        if (CollectionUtils.isEmpty(dataList)) {
            log.warn("dictionaryService - updateByType, dataList is null");
            return;
        }

        List<CustomsDictionaryDTO> dictionaryDTOList = this.findByType(type);
        if ((dictionaryDTOList.size() + dataList.size()) > 30) {
            throw new RuntimeException("业务类型个数超过上限，最多存在30种类型！");
        }

        //Map<编码, 可编辑>
        Map<String, Boolean> enableMap = new HashMap<>();
        Map<String, String> oldNameCodeMap = new HashMap<>();
        dictionaryDTOList.forEach(dto -> {
            enableMap.put(dto.getCode(), dto.getEnableModify());
            oldNameCodeMap.put(dto.getName(), dto.getCode());
        });

        if (CollectionUtils.isEmpty(dictionaryDTOList)) {
            log.info("类型不存在");
        }

        //查重
        List<String> duplicateNameList = new ArrayList<>();
        for (CustomsDictionaryOperateParam data : dataList) {
            if (Objects.nonNull(oldNameCodeMap.get(data.getName())) &&
                    !Objects.equals(data.getCode(), oldNameCodeMap.get(data.getName()))) {
                //若该名称 存在老数据中 且 新数据的code 和 老数据的code 不相同 则认为是重复数据
                duplicateNameList.add(data.getName());
            }
        }

        if (!CollectionUtils.isEmpty(duplicateNameList)) {
            throw new RuntimeException("保存失败，存在相同名称=" + JSONUtils.toJSONString(duplicateNameList));
        }

        List<String> insertNameList = new ArrayList<>();

        //更新数据名称， 记录插入数据
        for (CustomsDictionaryOperateParam data : dataList) {
            if (Objects.isNull(data.getCode())) {
                //code 为空 则为新数据
                insertNameList.add(data.getName());
            } else {
                if (enableMap.get(data.getCode())) {
                    this.updateByCodeAndType(data);
                } else {
                    throw new BusinessException("该数据" + data.getName() + "无法编辑");
                }
            }
        }

        //step4 : 生成新的数据
        List<CustomsDictionaryDO> customsDictionaryDOList = insertNameList.stream()
                .distinct()
                .map(name -> {
                    CustomsDictionaryDO customsDictionaryDO = new CustomsDictionaryDO();
                    UserUtils.setCreateAndUpdateBy(customsDictionaryDO);
                    customsDictionaryDO.setType(type);
                    customsDictionaryDO.setName(name);
                    customsDictionaryDO.setEnableModify(true);
                    customsDictionaryDO.setCode(sequenceService.generateDataDictionaryCode());
                    return customsDictionaryDO;
                }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(customsDictionaryDOList)) {
            return;
        }
        customsDictionaryMapper.insertList(customsDictionaryDOList);

    }

    @Override
    public void deleteByCodeType(CustomsDictionaryOperateParam param) {
        if (Objects.isNull(param.getType()) || Objects.isNull(param.getCode())) {
            log.warn("deleteByCodeType , type or code is null");
            throw new BusinessException("类型和编码不能为空");
        }
        Example example = new Example(CustomsDictionaryDO.class);
        example.createCriteria().andEqualTo("deleted", false)
                .andEqualTo("type", param.getType())
                .andEqualTo("code", param.getCode());
        List<CustomsDictionaryDO> customsDictionaryDOS = customsDictionaryMapper.selectByExample(example);
        CustomsDictionaryDO target;
        if (CollectionUtils.isEmpty(customsDictionaryDOS)) {
            log.warn("deleteByCodeType, 类型=[" + param.getType() + "]，编码={" + param.getCode() + "}，未匹配到数据");
            throw new BusinessException("数据字典内未匹配到该数据");
        }
        target = customsDictionaryDOS.get(0);
        if (!target.getEnableModify()) {
            log.warn("deleteByCodeType, data cannot modify");
            throw new BusinessException("[" + target.getName() + "]该数据不可修改");
        }
        target.setDeleted(true);
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            target.setUpdateBy(UserUtils.getUserId());
        }
        target.setUpdateTime(new Date());
        customsDictionaryMapper.updateByPrimaryKeySelective(target);
    }

    private Example buildExample(CustomsDictionaryReqVO search) {
        Example example = new Example(CustomsDictionaryDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("deleted", false);
        // 编码名称
        if (!StringUtils.isEmpty(search.getName())) {
            List<String> names = Arrays.asList(search.getName().split(","));
            criteria.andIn("name", names);
        }
        //数据值
        if (!StringUtils.isEmpty(search.getCode())) {
            List<String> codes = Arrays.asList(search.getCode().split(","));
            criteria.andIn("code", codes);
        }
        if (!StringUtils.isEmpty(search.getType())) {
            criteria.andEqualTo("type", search.getType());
        }
        //数据类型
        if (Objects.nonNull(search.getMainType())) {
            criteria.andEqualTo("mainType", search.getMainType());
        }
        //启用状态
        if (Objects.nonNull(search.getEnable())) {
            criteria.andEqualTo("enable", search.getEnable());
        }
        //创建时间
        if (!LongUtil.isNone(search.getCreateTimeFrom())) {
            criteria.andGreaterThanOrEqualTo("createTime", new DateTime(search.getCreateTimeFrom()).toString("yyyy-MM-dd HH:mm:ss"));
        }
        if (!LongUtil.isNone(search.getCreateTimeTo())) {
            criteria.andLessThanOrEqualTo("createTime", new DateTime(search.getCreateTimeTo()).toString("yyyy-MM-dd HH:mm:ss"));
        }
        example.setOrderByClause("create_time DESC");
        return example;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public DataDictionaryReport importExcel(List<ImportExcelDataDictionaryDTO> list, String type, boolean isSave) throws ArgsErrorException {
        List<ImportExcelDataDictionaryDTO> successRecordList = new ArrayList<>();
        List<ImportExcelDataDictionaryDTO> failRecordList = new ArrayList<>();

        List<CustomsDictionaryDTO> dtoList = this.findByType(type);
        List<String> oldCodeList = dtoList.stream().map(CustomsDictionaryDTO::getCode).collect(Collectors.toList());
        List<String> newCodeList = new ArrayList<>();
        //数据合法性校验
        int rowIdx = 1;
        for (ImportExcelDataDictionaryDTO importDTO : list) {
            rowIdx++;
            String code = importDTO.getCode();
            importDTO.setIdx(rowIdx);
            if (oldCodeList.contains(code)) {
                importDTO.setMessage("第[" + importDTO.getIdx() + "]行编码在字典中已存在");
                failRecordList.add(importDTO);
                continue;
            }
            if (newCodeList.contains(code)) {
                importDTO.setMessage("第[" + importDTO.getIdx() + "]行编码在导入模板中重复");
                failRecordList.add(importDTO);
                continue;
            }
            if (code.contains(" ")) {
                importDTO.setMessage("第[" + importDTO.getIdx() + "]行存在空格");
                failRecordList.add(importDTO);
                continue;
            }
            newCodeList.add(code);
            successRecordList.add(importDTO);
        }
        //插入新数据
        if (isSave && CollectionUtils.isEmpty(failRecordList)) {
            for (ImportExcelDataDictionaryDTO dictionaryDTO : successRecordList) {
                CustomsDictionaryDO dictionaryDO = new CustomsDictionaryDO();
                BeanUtils.copyProperties(dictionaryDTO, dictionaryDO);
                dictionaryDO.setType(type);
                UserUtils.setCreateAndUpdateBy(dictionaryDO);
                customsDictionaryMapper.insertSelective(dictionaryDO);
            }
        }
        DataDictionaryReport dataDictionaryReport = new DataDictionaryReport();
        dataDictionaryReport.setFailCount(failRecordList.size());
        dataDictionaryReport.setFailRecordList(failRecordList);
        dataDictionaryReport.setSuccessCount(successRecordList.size());
        dataDictionaryReport.setSuccessRecordList(successRecordList);
        dataDictionaryReport.setTotalCount(successRecordList.size() + failRecordList.size());
        return dataDictionaryReport;
    }

    @Override
    public ImportResultResVo importExcel(CustomsDictionaryDTO dictionaryDTO, String type) {
        ImportResultResVo resVo = new ImportResultResVo();
        resVo.setFlag(true);
        List<CustomsDictionaryDTO> dtoList = this.findByType(type);
        List<String> oldCodeList = dtoList.stream().map(CustomsDictionaryDTO::getCode).collect(Collectors.toList());
        String code = dictionaryDTO.getCode();
        //校验
        if (oldCodeList.contains(code)) {
            log.error("编码[" + dictionaryDTO.getCode() + "]在字典类型[" + DataDictionaryTypeEnums.getEnumByValue(type).getDesc() + "]下已存在");
            resVo.setReason("编码=[" + dictionaryDTO.getCode() + "]在字典类型[" + DataDictionaryTypeEnums.getEnumByValue(type).getDesc() + "]下已存在");
            resVo.setFlag(false);
            return resVo;
        }
        if (code.contains(" ")) {
            log.error("编码=[" + dictionaryDTO.getCode() + "]存在空格");
            resVo.setReason("编码=[" + dictionaryDTO.getCode() + "]存在空格");
            resVo.setFlag(false);
            return resVo;
        }
        //插入新数据
        CustomsDictionaryDO dictionaryDO = new CustomsDictionaryDO();
        BeanUtils.copyProperties(dictionaryDTO, dictionaryDO);
        dictionaryDO.setType(type);
        UserUtils.setCreateAndUpdateBy(dictionaryDO);
        customsDictionaryMapper.insertSelective(dictionaryDO);
        return resVo;
    }

    @Override
    public void enableSwitch(DataDictionaryEnableSwitchVO switchVO) {
        if (Objects.isNull(switchVO)) {
            log.error("数据字典切换启用禁用失败 - 参数为空");
            return;
        }
        CustomsDictionaryDTO oldDTO = this.findById(switchVO.getId());
        CustomsDictionaryDO customsDictionaryDO = new CustomsDictionaryDO();
        customsDictionaryDO.setEnable(switchVO.getEnable());
        Example example = new Example(CustomsDictionaryDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("id", switchVO.getId());
        if (Objects.nonNull(oldDTO) && Objects.equals(oldDTO.getType(), DataDictionaryTypeEnums.TYPE.getValue())) {
            //类型下数据同步更新启用禁用
            criteria.orEqualTo("type", oldDTO.getCode());
        }
        customsDictionaryMapper.updateByExampleSelective(customsDictionaryDO, example);
    }

    @Override
    public List<CustomsDictionaryDTO> findByTypeAndName(String type, String name) {
        if (StringUtil.isEmpty(name) || StringUtil.isEmpty(type)) {
            return new ArrayList<>();
        }
        Example example = new Example(CustomsDictionaryDO.class);
        example.createCriteria().andEqualTo("type", type)
                .andEqualTo("name", name)
                .andEqualTo("enable", 1)
                .andEqualTo("deleted", 0);
        List<CustomsDictionaryDO> customsDictionaryDOS = customsDictionaryMapper.selectByExample(example);
        return ConvertUtil.listConvert(customsDictionaryDOS, CustomsDictionaryDTO.class);
    }

    private CustomsDictionaryDTO buildDTO(CustomsDictionaryDO customsDictionaryDO) {
        if (Objects.isNull(customsDictionaryDO)) {
            return null;
        }
        CustomsDictionaryDTO customsDictionaryDTO = new CustomsDictionaryDTO();
        BeanUtils.copyProperties(customsDictionaryDO, customsDictionaryDTO);
        return customsDictionaryDTO;
    }
}
