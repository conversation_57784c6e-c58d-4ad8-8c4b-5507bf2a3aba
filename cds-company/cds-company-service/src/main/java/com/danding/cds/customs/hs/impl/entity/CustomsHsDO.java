package com.danding.cds.customs.hs.impl.entity;

import com.danding.cds.common.model.BaseDO;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 海关HS表(CustomsHsDO)实体类
 *
 * <AUTHOR>
 * @since 2020-04-29 09:56
 */
@Data
@Table(name = "ccs_customs_hs")
public class CustomsHsDO extends BaseDO implements Serializable {
    private static final long serialVersionUID = -8994004934963341902L;
    /**
     * HS编码
     */
    @Column(name = "hs_code")
    private String hsCode;

    /**
     * 产品类别
     */
    @Column(name = "hs_name")
    private String hsName;

    /**
     * 增值税
     */
    private BigDecimal vat;

    /**
     * 关税
     */
    private BigDecimal tariff;

    /**
     * 出口税率
     */
    @Column(name = "export_tax_rate")
    private BigDecimal exportTaxRate;

    /**
     * 出口退税税率
     */
    @Column(name = "export_drawback_tax_rate")
    private BigDecimal exportDrawbackTaxRate;

    /**
     * 出口暂定税率
     */
    @Column(name = "export_tentative_tax_rate")
    private BigDecimal exportTentativeTaxRate;

    /**
     * 进口优惠税率
     */
    @Column(name = "import_discount_tax_rate")
    private BigDecimal importDiscountTaxRate;

    /**
     * 进口暂定税率
     */
    @Column(name = "import_tentative_tax_rate")
    private BigDecimal importTentativeTaxRate;

    /**
     * 进口普通税率
     */
    @Column(name = "import_general_tax_rate")
    private BigDecimal importGeneralTaxRate;

    /**
     * 消费税从价从量标志(消费税计征标准("N"=不征；0=从价；5=从量))
     */
    @Column(name = "consumption_flag")
    private Integer consumptionFlag;

    /**
     * 消费税率从价税率
     */
    @Column(name = "consumption_tax")
    private BigDecimal consumptionTax;

    @Deprecated
    /**
     * 消费税从量税率
     */
    @Column(name = "consumption_num_tax")
    private BigDecimal consumptionNumTax;

    /**
     * 从价规格单价
     */
    @Column(name = "price_per_unit")
    private BigDecimal pricePerUnit;

    /**
     * 从量单价
     */
    @Column(name = "specific_price_per_unit")
    private BigDecimal specificPricePerUnit;

    /**
     * 法定第一单位
     */
    @Column(name = "first_legal_unit")
    private String firstLegalUnit;

    /**
     * 法定第二单位
     */
    @Column(name = "second_legal_unit")
    private String secondLegalUnit;

    /**
     * 从价计量单位名称
     */
    @Column(name = "uom_name")
    private String uomName;

    /**
     * 从量计量单位名称
     */
    @Column(name = "specific_uom_name")
    private String specificUomName;

    /**
     * 浮动类型
     */
    @Column(name = "float_type")
    private Integer floatType;

    /**
     * 申报要素
     */
    @Column(name = "declare_element_json")
    private String declareElementJson;

    /**
     * 状态:0.停用;1.启用(默认)
     */
    private Integer enable;
}
