package com.danding.cds.message.impl.process.toERP.Utils;

import org.apache.commons.lang3.StringUtils;

public class MyStringUtils {

    public static String replaceBetween(String source, String start, String end, String replace) {
        if (StringUtils.isEmpty(source)) {
            return null;
        }
        int startIndex = source.indexOf(start);
        if (startIndex != -1) {
            int endIndex = source.indexOf(end, startIndex + 1);
            if (endIndex != -1) {
                String beforeStart = source.substring(0, startIndex + start.length());
                String afterEnd = source.substring(endIndex);
                return beforeStart + replace + afterEnd;
            } else {
                return source;
            }
        } else {
            return source;
        }
    }
}
