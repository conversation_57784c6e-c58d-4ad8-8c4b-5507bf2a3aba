package com.danding.cds.callback.impl.mapper;

import com.danding.cds.callback.impl.entity.CallbackRecordDO;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import tk.mybatis.mapper.additional.insert.InsertListMapper;
import tk.mybatis.mapper.common.Mapper;
@Deprecated
public interface CallbackRecordMapper extends Mapper<CallbackRecordDO>, InsertListMapper<CallbackRecordDO>, BatchUpdateMapper<CallbackRecordDO>, AggregationPlusMapper<CallbackRecordDO> {
}