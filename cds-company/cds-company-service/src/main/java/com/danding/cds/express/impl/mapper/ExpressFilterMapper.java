package com.danding.cds.express.impl.mapper;

import com.danding.cds.express.impl.entity.ExpressFilterDO;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import tk.mybatis.mapper.additional.insert.InsertListMapper;
import tk.mybatis.mapper.common.Mapper;

public interface ExpressFilterMapper extends Mapper<ExpressFilterDO>, InsertListMapper<ExpressFilterDO>, BatchUpdateMapper<ExpressFilterDO>, AggregationPlusMapper<ExpressFilterDO> {
}