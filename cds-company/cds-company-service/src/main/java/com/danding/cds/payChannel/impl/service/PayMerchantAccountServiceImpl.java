package com.danding.cds.payChannel.impl.service;

import com.danding.cds.bean.dto.BaseDataSyncTypeEnums;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.ValidatorUtils;
import com.danding.cds.payChannel.api.dto.PayMerchantAccountDTO;
import com.danding.cds.payChannel.api.dto.PayMerchantAccountSearch;
import com.danding.cds.payChannel.api.dto.PayMerchantAccountSubmit;
import com.danding.cds.payChannel.api.service.PayMerchantAccountService;
import com.danding.cds.payChannel.impl.entity.PayMerchantAccountDO;
import com.danding.cds.payChannel.impl.mapper.PayMerchantAccountMapper;
import com.danding.cds.sequence.api.service.SequenceService;
import com.danding.cds.service.BaseDataSyncService;
import com.danding.common.utils.CopyUtil;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import javax.validation.Validator;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: Raymond
 * @Date: 2020/8/21 11:09
 * @Description:
 */
@Slf4j
@DubboService
public class PayMerchantAccountServiceImpl implements PayMerchantAccountService {
    @Autowired
    private PayMerchantAccountMapper payMerchantAccountMapper;
    @DubboReference
    private SequenceService sequenceService;

    @Autowired
    private Validator validator;

    @Autowired
    private BaseDataSyncService baseDataSyncService;

    @Override
    @PageSelect
    public ListVO<PayMerchantAccountDTO> paging(PayMerchantAccountSearch search) {
        Example example = new Example(PayMerchantAccountDO.class);
        example.orderBy("createTime").desc();
        Example.Criteria criteria = example.createCriteria();
        if (!StringUtils.isEmpty(search.getName())) {
            criteria.andLike("name", "%" + search.getName() + "%");
        }

        if (!StringUtils.isEmpty(search.getCompanyName())){
            criteria.andLike("companyName", "%" + search.getCompanyName() + "%");
        }

        if (!StringUtils.isEmpty(search.getSn())){
            criteria.andLike("sn", "%" + search.getSn() + "%");
        }

        List<PayMerchantAccountDO> list = payMerchantAccountMapper.selectByExample(example);
        ListVO<PayMerchantAccountDTO> result = new ListVO<>();
        List<PayMerchantAccountDTO> dataList = list.stream().map((PayMerchantAccountDO item) -> {
            PayMerchantAccountDTO optionDTO = new PayMerchantAccountDTO();
            BeanUtils.copyProperties(item, optionDTO);
            return optionDTO;
        }).collect(Collectors.toList());
        result.setDataList(dataList);
        // 分页
        PageInfo<PayMerchantAccountDTO> pageInfo = new PageInfo(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    @Override
    public List<PayMerchantAccountDTO> queryListPayMerchantAccountExport(PayMerchantAccountSearch search) {
        Example example = new Example(PayMerchantAccountDO.class);
        example.orderBy("createTime").desc();
        Example.Criteria criteria = example.createCriteria();
        if (!StringUtils.isEmpty(search.getName())){
            criteria.andLike("name", "%" + search.getName() + "%");
        }

        if (!StringUtils.isEmpty(search.getCompanyName())){
            criteria.andLike("companyName", "%" + search.getCompanyName() + "%");
        }

        if (!StringUtils.isEmpty(search.getSn())){
            criteria.andLike("sn", "%" + search.getSn() + "%");
        }

        List<PayMerchantAccountDO> list = payMerchantAccountMapper.selectByExample(example);
        ListVO<PayMerchantAccountDTO> result = new ListVO<>();

        List<PayMerchantAccountDTO> oretList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            for (PayMerchantAccountDO info : list) {
                PayMerchantAccountDTO infoDto = new PayMerchantAccountDTO();
                BeanUtils.copyProperties(info, infoDto);
                oretList.add(infoDto);
            }
        }
        return oretList;
    }

    @Override
    public Long upset(PayMerchantAccountSubmit submit) throws ArgsErrorException {
        // Step::入参校验
        String inputError = ValidatorUtils.doValidator(validator,submit);
        if (null != inputError){
            throw new ArgsErrorException(inputError);
        }

        // Step::构造持久化对象
        PayMerchantAccountDO payMerchantAccountDO = new PayMerchantAccountDO();
        BeanUtils.copyProperties(submit, payMerchantAccountDO); // 基础信息
        payMerchantAccountDO.setCompanyName("该字段废弃");
        if (submit.getId() == null || submit.getId() == 0) {
            payMerchantAccountDO.setId(null);
            payMerchantAccountDO.setSn(sequenceService.generateMerchantAccountSn());
            UserUtils.setCreateAndUpdateBy(payMerchantAccountDO);
            payMerchantAccountMapper.insertSelective(payMerchantAccountDO);

            baseDataSyncService.syncDataMQSend(BaseDataSyncTypeEnums.PAY_MERCHANT_ACCOUNT, BaseDataSyncTypeEnums.INSERT, payMerchantAccountDO);
        } else {
            PayMerchantAccountDTO old = this.findById(submit.getId());
            if (old == null) {
                throw new ArgsErrorException("ID不正确");
            }
            if (!Objects.equals(UserUtils.getUserId(), 0)) {
                UserUtils.setUpdateBy(payMerchantAccountDO);
            }
            payMerchantAccountDO.setUpdateTime(new Date());
            payMerchantAccountMapper.updateByPrimaryKeySelective(payMerchantAccountDO);

            PayMerchantAccountDO syncData = payMerchantAccountMapper.selectByPrimaryKey(payMerchantAccountDO.getId());
            baseDataSyncService.syncDataMQSend(BaseDataSyncTypeEnums.PAY_MERCHANT_ACCOUNT, BaseDataSyncTypeEnums.UPDATE, syncData);
        }
        submit.setId(payMerchantAccountDO.getId());
        return payMerchantAccountDO.getId();
    }

    @Override
    public PayMerchantAccountDTO findById(long id) {
        if (id == 0) {
            return null;
        } else {
            PayMerchantAccountDO payMerchantAccountDO = payMerchantAccountMapper.selectByPrimaryKey(id);
            return CopyUtil.copy(payMerchantAccountDO,PayMerchantAccountDTO.class);
        }
    }

    @Override
    public PayMerchantAccountDTO findBySn(String sn) {
        PayMerchantAccountDO condition = new PayMerchantAccountDO();
        condition.setSn(sn);
        return CopyUtil.copy(payMerchantAccountMapper.selectOne(condition),PayMerchantAccountDTO.class);
    }
}
