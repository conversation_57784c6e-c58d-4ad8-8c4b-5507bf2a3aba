package com.danding.cds.path.impl.service;

import cn.hutool.core.date.DateTime;
import com.danding.cds.bean.dto.BaseDataSyncTypeEnums;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.common.utils.ValidatorUtils;
import com.danding.cds.path.api.dto.PathDTO;
import com.danding.cds.path.api.dto.PathSearch;
import com.danding.cds.path.api.dto.PathSubmit;
import com.danding.cds.path.api.service.PathService;
import com.danding.cds.path.impl.entity.PathDO;
import com.danding.cds.path.impl.mapper.PathMapper;
import com.danding.cds.route.api.dto.RouteDTO;
import com.danding.cds.route.api.service.RouteService;
import com.danding.cds.service.BaseDataSyncService;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import javax.validation.Validator;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@DubboService
@Slf4j
public class PathServiceImpl implements PathService {

    @Autowired
    private PathMapper pathMapper;

    @Autowired
    private Validator validator;

    @DubboReference
    private RouteService routeService;

    @Autowired
    private BaseDataSyncService baseDataSyncService;

    @Override
    public Long upset(PathSubmit submit) throws ArgsErrorException {
        PathDTO old = this.findByCode(submit.getCode());
        if (old != null && (submit.getId() == null || submit.getId() == 0 || !submit.getId().equals(old.getId()))) {
            throw new ArgsErrorException("申报路由标识重复");
        }
        RouteDTO routeDTO = routeService.findById(submit.getRouteId());
        if (Objects.isNull(routeDTO)) {
            if (Objects.equals(submit.getEnable(), 1)) {
                throw new ArgsErrorException("关联路径不存在，请配置后开启");
            }
        } else {
            if (routeDTO.getEnable() == 0) {
                throw new ArgsErrorException("所选路径已禁用，请稍后再试");
            }
        }
        PathDO pathDO = new PathDO();
        BeanUtils.copyProperties(submit, pathDO);
        PathDTO pathDTO = this.findByIdentify(submit.getFirstIdentify(), submit.getSecondIdentify(), submit.getThirdIdentify());
        if (submit.getId() == null || submit.getId() == 0) {
            // Step::入参校验
            String inputError = ValidatorUtils.doValidator(validator, submit);
            if (null != inputError) {
                throw new ArgsErrorException(inputError);
            }
            if (pathDTO != null) {
                throw new ArgsErrorException("该标识组合对应的路由已存在");
            }
            // Step::业务校验
            pathDO.setId(null);
            pathDO.setEnable(1);
            UserUtils.setCreateAndUpdateBy(pathDO);
            pathMapper.insertSelective(pathDO);

            baseDataSyncService.syncDataMQSend(BaseDataSyncTypeEnums.PATH, BaseDataSyncTypeEnums.INSERT, pathDO);
        } else {
            old = this.findById(submit.getId());
            if (old == null) {
                throw new ArgsErrorException("ID不正确");
            }
            if (pathDTO != null && !pathDTO.getId().equals(old.getId())) {
                throw new ArgsErrorException("该标识组合对应的路由已存在");
            }
            // 禁用操作
            if ((submit.getEnable() != null && submit.getEnable() == 0) || old.getEnable() == 0) {
                if (!Objects.equals(UserUtils.getUserId(), 0)) {
                    UserUtils.setUpdateBy(pathDO);
                }
                pathDO.setUpdateTime(new Date());
                pathMapper.updateByPrimaryKeySelective(pathDO);

                PathDO syncData = pathMapper.selectByPrimaryKey(pathDO.getId());
                baseDataSyncService.syncDataMQSend(BaseDataSyncTypeEnums.PATH, BaseDataSyncTypeEnums.UPDATE, syncData);
            } else {
                throw new ArgsErrorException("申报路由未禁用，不可编辑");
            }
            submit.setCode(old.getCode());
        }
        submit.setId(pathDO.getId());
        return pathDO.getId();
    }


    @Override
    @PageSelect
    public ListVO<PathDTO> paging(PathSearch search) {
        Example example = new Example(PathDO.class);
        example.orderBy("createTime").desc();
        Example.Criteria criteria = example.createCriteria();
        if (!LongUtil.isNone(search.getCodeId()) && !LongUtil.isNone(search.getNameId()) && search.getCodeId() != search.getNameId()) {
            ListVO<PathDTO> result = new ListVO<>();
            result.setPage(new PageResult());
            result.setDataList(new ArrayList<>());
            return result;
        }
        if (!LongUtil.isNone(search.getCodeId())) {
            criteria.andEqualTo("id", search.getCodeId());
        }
        if (!LongUtil.isNone(search.getNameId())) {
            criteria.andEqualTo("id", search.getNameId());
        }
        Long createFrom = LongUtil.getFrom(search.getCreateFrom(), search.getCreateTo());
        Long createTo = LongUtil.getEnd(search.getCreateFrom(), search.getCreateTo());
        if (!LongUtil.isNone(createFrom)) {
            criteria.andGreaterThanOrEqualTo("createTime", new DateTime(createFrom).toString("yyyy-MM-dd HH:mm:ss"));
        }
        if (!LongUtil.isNone(createTo)) {
            criteria.andLessThanOrEqualTo("createTime", new DateTime(createTo).toString("yyyy-MM-dd HH:mm:ss"));
        }
        if (search.getEnable() != null) {
            criteria.andEqualTo("enable", search.getEnable());
        }
        if (!StringUtils.isEmpty(search.getFirstIdentify())) {
            criteria.andEqualTo("firstIdentify", search.getFirstIdentify());
        }
        if (!StringUtils.isEmpty(search.getSecondIdentify())) {
            criteria.andEqualTo("secondIdentify", search.getSecondIdentify());
        }
        if (!StringUtils.isEmpty(search.getThirdIdentify())) {
            criteria.andEqualTo("thirdIdentify", search.getThirdIdentify());
        }

        List<PathDO> list = pathMapper.selectByExample(example);
        ListVO<PathDTO> result = new ListVO<>();
        List<PathDTO> dtoList = list.stream().map(this::buildDTO).collect(Collectors.toList());
        result.setDataList(dtoList);
        // 分页
        PageInfo<PathDTO> pageInfo = new PageInfo(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    @Override
    public PathDTO findByCode(String code) {
        PathDO condition = new PathDO();
        condition.setCode(code);
        PathDO pathDO = pathMapper.selectOne(condition);
        if (pathDO == null) {
            return null;
        }
        return this.buildDTO(pathDO);
    }

    @Override
    public PathDTO findById(Long id) {
        return this.buildDTO(pathMapper.selectByPrimaryKey(id));
    }

    @Override
    public PathDTO findByIdentify(String identify1, String identify2, String identify3) throws ArgsErrorException {
        if (!StringUtils.isEmpty(identify3)) {
            if (StringUtils.isEmpty(identify1) || StringUtils.isEmpty(identify2)) {
                throw new ArgsErrorException("标识3不为空时，标识1或标识2不能为空");
            }
        }
        if (!StringUtils.isEmpty(identify2)) {
            if (StringUtils.isEmpty(identify1)) {
                throw new ArgsErrorException("标识2不为空时，标识1不能为空");
            }
        }
        if (StringUtils.isEmpty(identify1)) {
            throw new ArgsErrorException("标识1不能为空");
        }
        PathDO condition = new PathDO();
        if (identify2 == null) {
            identify2 = "";
        }
        if (identify3 == null) {
            identify3 = "";
        }
        condition.setFirstIdentify(identify1);
        condition.setSecondIdentify(identify2);
        condition.setThirdIdentify(identify3);

        PathDO pathDO = pathMapper.selectOne(condition);
        if (pathDO == null) {
            return null;
        }
        return this.buildDTO(pathDO);
    }

    @Override
    public List<PathDTO> listEnable() {
        PathSearch search = new PathSearch();
        search.setPageIgnore(1);
        search.setEnable(1);
        ListVO<PathDTO> paging = this.paging(search);
        return paging.getDataList();
    }

    @Override
    public List<PathDTO> listAll() {
        PathSearch search = new PathSearch();
        search.setPageIgnore(1);
        ListVO<PathDTO> paging = this.paging(search);
        return paging.getDataList();
    }

    private PathDTO buildDTO(PathDO pathDO) {
        PathDTO result = new PathDTO();
        BeanUtils.copyProperties(pathDO, result);
        return result;
    }
}
