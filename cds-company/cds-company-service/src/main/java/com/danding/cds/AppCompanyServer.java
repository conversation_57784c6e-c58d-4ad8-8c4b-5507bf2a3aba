package com.danding.cds;

import com.danding.component.mybatis.common.interceptor.plus.MybatisPlusTenantLineInterceptorConfiguration;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Import;
import tk.mybatis.spring.annotation.MapperScan;

/**
 * @Auther: Dante-Gxj
 * @Date: 2020/4/26 13:57
 * @Description:
 */
@EnableDubbo
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class}
        , scanBasePackages = {
        "com.danding.cds"
        , "com.danding.logistics" // 这个包扫描是为了添加分页查询APO支持
})
//@EnableNacosConfig(globalProperties = @NacosProperties(serverAddr = "${spring.cloud.nacos.config.server-addr}",enableRemoteSyncConfig = "true"))
@MapperScan(basePackages = {"com.danding.cds.**.impl.mapper", "com.danding.cds.**.mapper"})
@EnableCaching // 启用缓存注解
@Import({MybatisPlusTenantLineInterceptorConfiguration.class})
public class AppCompanyServer {
    public static void main(String[] args) {
        if (!StringUtils.isEmpty(System.getProperty("env")) && "DEV".equals(System.getProperty("env").toUpperCase())) {
            if (StringUtils.isEmpty(System.getProperty("local"))) {
                System.setProperty("local", "true");
            }
        }
        System.setProperty("es.set.netty.runtime.available.processors", "false");
        System.setProperty("rocketmq.client.logLevel", "ERROR");
        SpringApplication.run(AppCompanyServer.class, args);
    }

}
