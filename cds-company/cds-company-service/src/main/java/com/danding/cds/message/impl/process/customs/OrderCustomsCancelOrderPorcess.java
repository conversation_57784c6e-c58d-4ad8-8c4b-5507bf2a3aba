package com.danding.cds.message.impl.process.customs;

import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.rpc.OrderCRpc;
import com.danding.cds.callback.api.dto.OrderActiveInfo;
import com.danding.cds.common.config.OrderCCallConfig;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.message.api.dto.MessageDTO;
import com.danding.cds.message.api.enums.MessageType;
import com.danding.cds.message.api.process.OrderCustomsCancelOrderMessage;
import com.danding.cds.message.impl.process.MessageProcess;
import com.danding.cds.message.impl.process.MessageRegistry;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.api.service.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.joda.time.DateTime;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

@Component
@Slf4j
public class OrderCustomsCancelOrderPorcess extends MessageProcess {

    @DubboReference
    private OrderService orderService;
    @DubboReference
    private OrderCRpc orderCRpc;
    @Resource
    private OrderCCallConfig orderCCallConfig;

    @DubboReference
    private CompanyService companyService;

    public OrderCustomsCancelOrderPorcess(MessageRegistry registry) {
        super(registry);
    }

    @Override
    public MessageType getType() {
        return MessageType.ORDER_CUSTOMS_CANCEL;
    }

    @Override
    public Map<String, Object> buildSendInfo(MessageDTO messageDTO) {
        OrderCustomsCancelOrderMessage message = new OrderCustomsCancelOrderMessage();
        OrderActiveInfo activeInfo = JSON.parseObject(messageDTO.getActiveData(), OrderActiveInfo.class);
        if (activeInfo == null) {
            log.error("海关撤单回执消息任务生成异常，业务编码: {} ,空数据", messageDTO.getBusinessCode());
            return null;
        }
        log.warn("[op:buildSendInfo] 撤单回执打印 - {}", activeInfo);
        Long orderId = activeInfo.getId();
        if (orderId == null) {
            return null;
        }
        OrderDTO orderDTO = orderCCallConfig.isOrderCCall(this.getClass()) ? orderCRpc.findByIdFull(orderId) : orderService.findByIdFull(orderId);
        if (orderDTO == null) {
            log.error("未找到有效数据");
            return null;
        }
        CompanyDTO ebp = companyService.findUnifiedCrossInfoById(orderDTO.getEbpId());
        message.setDeclareOrderNo(orderDTO.getDeclareOrderNo());
        message.setSystemGlobalSn(orderDTO.getSystemGlobalSn());
        message.setOutOrderNo(orderDTO.getOutOrderNo());
        message.setEbpCode(ebp.getCode());
//        message.setCustomsStatus(InventoryCalloffStatus.getEnum(activeInfo.getCustomsStatus()).getCode().toString());
        message.setStatus(Integer.valueOf(activeInfo.getCustomsStatus()));
        message.setDetail(activeInfo.getCustomsDetail());
        message.setTime(new DateTime(activeInfo.getCustomsTime()).getMillis());
        return objToMap(message);
    }
}
