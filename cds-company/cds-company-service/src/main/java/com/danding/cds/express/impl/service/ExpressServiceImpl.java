package com.danding.cds.express.impl.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import com.danding.cds.bean.dto.BaseDataSyncTypeEnums;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.common.utils.ValidatorUtils;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.express.api.dto.ExpressConfigParam;
import com.danding.cds.express.api.dto.ExpressDTO;
import com.danding.cds.express.api.dto.ExpressSearch;
import com.danding.cds.express.api.dto.ExpressSubmit;
import com.danding.cds.express.api.service.ExpressService;
import com.danding.cds.express.impl.entity.ExpressDO;
import com.danding.cds.express.impl.mapper.ExpressMapper;
import com.danding.cds.service.BaseDataSyncService;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.github.pagehelper.PageInfo;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import tk.mybatis.mapper.entity.Example;

import javax.validation.Validator;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Auther: Dante-Gxj
 * @Date: 2020/5/6 10:47
 * @Description:
 */
@DubboService
@Slf4j
public class ExpressServiceImpl implements ExpressService {

    @Autowired
    private ExpressMapper expressMapper;

    @Autowired
    private Validator validator;

    @DubboReference
    private CompanyService companyService;
    @Autowired
    private BaseDataSyncService baseDataSyncService;

    @Override
//    @Cacheable(value = "ExpressWithCode", key = "#code", unless = "#result==null")
    public ExpressDTO findByCode(String code) {
        ExpressDO condition = new ExpressDO();
        condition.setCode(code);
        ExpressDO model = expressMapper.selectOne(condition);
        if (model == null) {
            return null;
        }
        return this.buildDTO(model);
    }

    @Override
    public ExpressDTO findOneByName(String name) {
        Example example = new Example(ExpressDO.class);
        example.createCriteria().andEqualTo("name", name);
        List<ExpressDO> list = expressMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(list)) return null;
        else {
            ExpressDO expressDO = list.get(0);
            ExpressDTO expressDTO = new ExpressDTO();
            BeanUtils.copyProperties(expressDO, expressDTO);
            return expressDTO;
        }
    }

    @Override
    public List<ExpressDTO> findByName(List<String> nameList) {
        if (CollUtil.isEmpty(nameList)) {
            return Collections.emptyList();
        }
        Example example = new Example(ExpressDO.class);
        example.createCriteria().andIn("name", nameList);
        List<ExpressDO> list = expressMapper.selectByExample(example);
        return ConvertUtil.listConvert(list, ExpressDTO.class);
    }

    @Override
    public void config(ExpressConfigParam param) {
        if (Objects.isNull(param)) {
            return;
        }
        String errorMsg = ValidatorUtils.doValidator(validator, param);
        if (StringUtil.isNotBlank(errorMsg)) {
            throw new ArgsInvalidException(errorMsg);
        }
        ExpressDO expressDO = new ExpressDO();
        expressDO.setId(param.getId());
        expressDO.setLogisticDeclareSystem(param.getLogisticDeclareSystem());
        expressMapper.updateByPrimaryKeySelective(expressDO);

        ExpressDO syncData = expressMapper.selectByPrimaryKey(expressDO.getId());
        baseDataSyncService.syncDataMQSend(BaseDataSyncTypeEnums.EXPRESS, BaseDataSyncTypeEnums.UPDATE, syncData);
    }

    @Override
    @PageSelect
    public ListVO<ExpressDTO> paging(ExpressSearch search) {
        Example example = new Example(ExpressDO.class);
        example.orderBy("createTime").desc();
        Example.Criteria criteria = example.createCriteria();
        if (!LongUtil.isNone(search.getExpressId())) {
            criteria.andEqualTo("id", search.getExpressId());
        }
        if (!LongUtil.isNone(search.getExpressCompanyId())) {
            criteria.andEqualTo("expressCompanyId", search.getExpressCompanyId());
        }
        Long createFrom = LongUtil.getFrom(search.getCreateFrom(), search.getCreateTo());
        Long createTo = LongUtil.getEnd(search.getCreateFrom(), search.getCreateTo());
        if (!LongUtil.isNone(createFrom)) {
            criteria.andGreaterThanOrEqualTo("createTime", new DateTime(createFrom).toString("yyyy-MM-dd HH:mm:ss"));
        }
        if (!LongUtil.isNone(createTo)) {
            criteria.andLessThanOrEqualTo("createTime", new DateTime(createTo).toString("yyyy-MM-dd HH:mm:ss"));
        }
        if (search.getEnable() != null) {
            criteria.andEqualTo("enable", search.getEnable());
        }
        List<ExpressDO> list = expressMapper.selectByExample(example);
        ListVO<ExpressDTO> result = new ListVO<>();
//        result.setDataList(JSON.parseArray(JSONUtils.toJSONString(list),ExpressDTO.class));
        List<ExpressDTO> dataList = list.stream().map((ExpressDO item) -> {
            ExpressDTO optionDTO = new ExpressDTO();
            BeanUtils.copyProperties(item, optionDTO);
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            optionDTO.setCreateTime(sf.format(item.getCreateTime()));
            return optionDTO;
        }).collect(Collectors.toList());
        result.setDataList(dataList);
        // 分页
        PageInfo<ExpressDTO> pageInfo = new PageInfo(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    @Override
//    @Caching(evict = {
//            @CacheEvict(value = "ExpressWithEnable", allEntries = true)
//            , @CacheEvict(value = "ExpressWithCode", key = "#submit.getCode()")
//            , @CacheEvict(value = "ExpressWithId", key = "#submit.getId()")
//    })
    public Long upset(ExpressSubmit submit) throws ArgsErrorException {
        // Step::入参校验
        String inputError = ValidatorUtils.doValidator(validator, submit);
        if (null != inputError) {
            throw new ArgsErrorException(inputError);
        }
        // Step::业务校验
        ExpressDTO old = this.findByCode(submit.getCode());
        if (old != null && (submit.getId() == null || submit.getId() == 0 || !submit.getId().equals(old.getId()))) {
            throw new ArgsErrorException("快递编码重复");
        }
        if ((submit.getId() == null || submit.getId() == 0) || (submit.getEnable() != null && submit.getEnable() == 1)) {
            CompanyDTO companyDTO = companyService.findById(submit.getExpressCompanyId());
            if (companyDTO.getEnable() != 1) {
                throw new ArgsErrorException(companyDTO.getName() + "企业未启用，请启用后再试");
            }
        }
        // Step::持久化
        ExpressDO entity = new ExpressDO();
        BeanUtils.copyProperties(submit, entity);
        if (LongUtil.isNone(submit.getId())) {
            entity.setId(null);
            UserUtils.setCreateAndUpdateBy(entity);
            expressMapper.insertSelective(entity);

            baseDataSyncService.syncDataMQSend(BaseDataSyncTypeEnums.EXPRESS, BaseDataSyncTypeEnums.INSERT, entity);
        } else {
            old = this.findById(submit.getId());
            if (old == null) {
                throw new ArgsErrorException("ID不正确");
            }
            if ((submit.getEnable() != null && submit.getEnable() == 0) || old.getEnable() == 0) {
                if (!Objects.equals(UserUtils.getUserId(), 0)) {
                    UserUtils.setUpdateBy(entity);
                }
                entity.setUpdateTime(new Date());
                expressMapper.updateByPrimaryKeySelective(entity);

                ExpressDO syncData = expressMapper.selectByPrimaryKey(entity.getId());
                baseDataSyncService.syncDataMQSend(BaseDataSyncTypeEnums.EXPRESS, BaseDataSyncTypeEnums.UPDATE, syncData);
            } else {
                throw new ArgsErrorException("快递未禁用，不可编辑");
            }
        }
        submit.setId(entity.getId());
        return entity.getId();
    }

    @Override
//    @Cacheable(value = "ExpressWithId", key = "#id", unless = "#result==null")
    public ExpressDTO findById(Long id) {
        return this.buildDTO(expressMapper.selectByPrimaryKey(id));
    }

    @Override
    public List<ExpressDTO> findByIds(List<Long> idList) {

        if (CollectionUtils.isEmpty(idList)) {
            return Collections.emptyList();
        }
        Example example = new Example(ExpressDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("id", idList);
        List<ExpressDO> statusMappingDOS = expressMapper.selectByExample(example);
        if (org.springframework.util.CollectionUtils.isEmpty(statusMappingDOS)) {
            return Collections.emptyList();
        }
        return statusMappingDOS.stream()
                .map(z -> this.buildDTO(z)).collect(Collectors.toList());
    }

    @Override
//    @Cacheable(value = "ExpressWithEnable", unless = "#result==null")
    public List<ExpressDTO> listEnable() {
        ExpressSearch search = new ExpressSearch();
        search.setPageIgnore(1);
        search.setEnable(1);
        ListVO<ExpressDTO> paging = this.paging(search);
        return paging.getDataList();
    }

    public List<ExpressDTO> listAll() {
        ExpressSearch search = new ExpressSearch();
        search.setPageIgnore(1);
        ListVO<ExpressDTO> paging = this.paging(search);
        return paging.getDataList();
    }

    private ExpressDTO buildDTO(ExpressDO routeDO){
        if (routeDO == null){
            return null;
        }
        ExpressDTO result = new ExpressDTO();
        BeanUtils.copyProperties(routeDO, result);
        return result;
    }
}
