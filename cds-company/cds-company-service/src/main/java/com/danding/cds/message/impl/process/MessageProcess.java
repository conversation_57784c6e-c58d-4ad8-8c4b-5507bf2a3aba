package com.danding.cds.message.impl.process;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.danding.cds.common.utils.HttpRequestUtil;
import com.danding.cds.message.api.dto.MessageDTO;
import com.danding.cds.message.api.dto.MessageTaskDTO;
import com.danding.cds.message.api.dto.MultiActiveDataDTO;
import com.danding.cds.message.api.dto.RequestDataDTO;
import com.danding.cds.message.api.enums.MessageType;
import com.github.kevinsawicki.http.HttpRequest;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
public abstract class MessageProcess {

    public abstract MessageType getType();

    public MessageType.MessageTaskType messageTaskType() {
        return null;
    }

    public Map<String, Object> buildSendInfo(MessageDTO messageDTO) {
        log.error("single param build error; businessCode: {}, type: {}", messageDTO.getBusinessCode(), messageDTO.getType());
        throw new UnsupportedOperationException();
    };

    /**
     * 多任务方法的messageDTO中的activeData 都需要转化成 MultiActiveDataDTO 对象
     * MultiActiveDataDTO.ActiveDataDTO#seq 代表顺序 必填 返回值需要和MultiActiveDataDTO.ActiveDataDTO#seq 一致
     * @param messageDTO
     * @return List<RequestDataDTO> list的元素顺序需要任务顺序一致
     */
    public List<RequestDataDTO> buildMultiSendInfo(MessageDTO messageDTO) {
        MultiActiveDataDTO multiActiveDataDTO = JSON.parseObject(messageDTO.getActiveData(), MultiActiveDataDTO.class);

        List<RequestDataDTO> list = new ArrayList<>();
        for (MultiActiveDataDTO.ActiveDataDTO activeDataDTO : multiActiveDataDTO.getActiveDataList()) {
            RequestDataDTO requestDataDTO = new RequestDataDTO();
            requestDataDTO.setRequestData(objToMap(activeDataDTO));
            requestDataDTO.setTag(activeDataDTO.getTag());
            requestDataDTO.setDelayLevel(activeDataDTO.getDelayLevel());
            requestDataDTO.setTaskType(activeDataDTO.getTaskType());
            list.add(requestDataDTO);
        }
        return list;
    };

    public MessageProcess(MessageRegistry registry) {
        registry.register(this);
    }

    public MessageExecuteResult execute(MessageTaskDTO messageTaskDTO) {
        MessageExecuteResult result = new MessageExecuteResult();
        log.info("消息回调 业务编码: {} ,请求数据信息: {}", messageTaskDTO.getBusinessCode(), JSON.toJSONString(messageTaskDTO));
        try {
            HttpRequest httpRequest = HttpRequestUtil.post(messageTaskDTO.getNotifyUrl(), messageTaskDTO.getRequestData());
            String body = httpRequest.body();
            log.info("消息回调 返回数据，业务编码: {} ,返回数据信息: {}", messageTaskDTO.getBusinessCode(), body);
            if (httpRequest.ok()) {
                this.processResultBody(result, body);
                result.setResponseMsg(body);
            } else {
                result.setSuccess(false);
                result.setResponseMsg("网络异常，状态码，" + httpRequest.code());
            }
        } catch (Exception e) {
            result.setSuccess(false);
            result.setResponseMsg("CCS内部系统异常，" + e.getMessage());
            log.error("业务编码 : {} ,消息回调处理异常：{}", messageTaskDTO.getBusinessCode(), e.getMessage(), e);
        }
        return result;
    }

    public void processResultBody(MessageExecuteResult result, String body) {
        if ("SUCCESS".equals(body)) {
            result.setSuccess(true);
        } else {
            result.setSuccess(false);
        }
    }

    protected Map<String, Object> objToMap(Object obj) {
        return JSON.parseObject(JSON.toJSONString(obj));
    }

    public static boolean isJSONValid(String json) {
        try {
            JSON.parse(json);
            return true;
        } catch (JSONException e) {
            return false;
        }
    }
}
