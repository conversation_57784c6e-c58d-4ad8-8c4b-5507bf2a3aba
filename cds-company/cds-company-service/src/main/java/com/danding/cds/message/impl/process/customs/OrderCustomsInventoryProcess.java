package com.danding.cds.message.impl.process.customs;

import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.rpc.OrderCRpc;
import com.danding.cds.callback.api.dto.OrderActiveInfo;
import com.danding.cds.common.config.OrderCCallConfig;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.customs.inventory.api.service.CustomsInventoryService;
import com.danding.cds.message.api.dto.MessageDTO;
import com.danding.cds.message.api.enums.MessageType;
import com.danding.cds.message.api.process.OrderCustomsInventoryMessage;
import com.danding.cds.message.impl.process.MessageProcess;
import com.danding.cds.message.impl.process.MessageRegistry;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.api.service.OrderService;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.joda.time.DateTime;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
@Slf4j
public class OrderCustomsInventoryProcess extends MessageProcess {

    @DubboReference
    private CustomsInventoryService customsInventoryService;

    @DubboReference
    private CompanyService companyService;

    @DubboReference
    private OrderService orderService;
    @DubboReference
    private OrderCRpc orderCRpc;
    @Resource
    private OrderCCallConfig orderCCallConfig;


    public OrderCustomsInventoryProcess(MessageRegistry registry) {
        super(registry);
    }

    @Override
    public MessageType getType() {
        return MessageType.ORDER_CUSTOMS_INVENTORY;
    }

    @Override
    public Map<String, Object> buildSendInfo(MessageDTO messageDTO) {
        OrderCustomsInventoryMessage message = new OrderCustomsInventoryMessage();
        OrderActiveInfo activeInfo = JSON.parseObject(messageDTO.getActiveData(), OrderActiveInfo.class);
        OrderDTO orderDTO = orderCCallConfig.isOrderCCall(this.getClass()) ? orderCRpc.findByIdFull(activeInfo.getId()) : orderService.findByIdFull(activeInfo.getId());
        if (Objects.isNull(orderDTO)) {
            // 定义匹配订单编号的正则表达式模式
            Pattern pattern = Pattern.compile("订单编号：(\\d+)");
            Matcher matcher = pattern.matcher(activeInfo.getCustomsDetail());
            if (matcher.find()) {
                String orderNumber = matcher.group(1);
                orderDTO = orderCRpc.findByDeclareOrderNo(orderNumber);
                log.info("提取到的订单编号为: " + orderNumber);
            } else {
                log.info("未找到匹配的订单编号");
            }
        }
        if (Objects.isNull(orderDTO)) {
//            String customsDetail = activeInfo.getCustomsDetail();
//            if (Objects.equals(customsDetail, "[Code:2600;Desc:放行]")) {
            String businessCode = messageDTO.getBusinessCode();
            log.info("单据根据业务编号查询订单 businessCode={}", businessCode);
            orderDTO = orderCRpc.findByDeclareOrderNo(businessCode);
//            }
        }
        if (Objects.isNull(orderDTO)) {
            throw new ArgsInvalidException("根据订单id未查询到对应申报单");
        }
        CustomsInventoryDTO inventoryDTO = customsInventoryService.findByOrder(orderDTO.getId(), orderDTO.getCustomsInventorySn());
        CompanyDTO ebp = companyService.findUnifiedCrossInfoById(inventoryDTO.getEbpId());
        message.setInventoryNo(inventoryDTO.getInventoryNo());
        message.setSystemGlobalSn(orderDTO.getSystemGlobalSn());
        message.setDeclareOrderNo(inventoryDTO.getDeclareOrderNo());
        message.setOutOrderNo(orderDTO.getOutOrderNo());
        message.setEbpCode(ebp.getCode());
        message.setCustomsStatus(activeInfo.getCustomsStatus());
        message.setCustomsDetail(activeInfo.getCustomsDetail());
        message.setTime(new DateTime(activeInfo.getCustomsTime()).getMillis());
        message.setLogisticsNo(inventoryDTO.getLogisticsNo());
        message.setOrigMessage("");
        message.setCompanyCode(activeInfo.getCompanyCode());
        message.setCompanyName(activeInfo.getCompanyName());
        return objToMap(message);
    }
}
