package com.danding.cds.message.impl.utils;

import cn.hutool.core.collection.CollUtil;
import com.danding.cds.message.api.dto.MultiActiveDataDTO;
import com.danding.cds.message.api.enums.MessageType;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: yousx
 * @Date: 2024/04/19
 * @Description: 用于构建消息多任务参数对象
 */
public class MultiActiveDataBuilder {


    /**
     *
     * @param useActiveTag 是否使用内部标签来获取地址
     * @param tagList 内部标签列表，若使用则需要与request数量一致并且顺序1:1对应
     * @param taskTypeList 消息任务类型，若使用则需要与request数量一致并且顺序1:1对应 @link com.danding.cds.message.api.enums.MessageType#MessageTaskType
     * @param delayLevel 延迟级别，可以设置多任务之间执行的间隔
     * @param requestData 请求数据，支持不同数据结构类型的数据
     * @return
     */
    public static MultiActiveDataDTO getMultiActiveData(boolean useActiveTag, List<String> tagList, List<MessageType.MessageTaskType> taskTypeList, Integer delayLevel, Object... requestData) {
        // 如果使用内部标签来获取地址，则需要判断tagList是否为空，并且长度是否和requestData长度一致
        if (useActiveTag && (CollUtil.isEmpty(tagList) || !Objects.equals(tagList.size(), requestData.length))) {
            throw new ArgsErrorException("使用内部标签来获取地址，则需要判断tagList是否为空，并且长度是否和requestData长度一致");
        }
        if (CollUtil.isEmpty(taskTypeList) || !Objects.equals(taskTypeList.size(), requestData.length)) {
            throw new ArgsErrorException("taskTypeList不能为空，并且长度必须和requestData长度一致");
        }
        Set<String> collect = taskTypeList.stream().map(MessageType.MessageTaskType::getCode).collect(Collectors.toSet());
        if (collect.size() != taskTypeList.size()) {
            throw new ArgsErrorException("taskTypeList不能有重复的code");
        }

        MultiActiveDataDTO multiActiveDataDTO = new MultiActiveDataDTO();
        multiActiveDataDTO.setUseActiveTag(useActiveTag);

        List<MultiActiveDataDTO.ActiveDataDTO> data = new ArrayList<>();
        for (int i = 0; i < requestData.length; i++) {
            MultiActiveDataDTO.ActiveDataDTO activeDataDTO = new MultiActiveDataDTO.ActiveDataDTO();
            activeDataDTO.setSeq(i);
            activeDataDTO.setData(requestData[i]);
            if (useActiveTag) {
                activeDataDTO.setTag(tagList.get(i));
            }
            activeDataDTO.setTaskType(taskTypeList.get(i).getCode());
            activeDataDTO.setDelayLevel(delayLevel);
            data.add(activeDataDTO);
        }
        multiActiveDataDTO.setActiveDataList(data);
        return multiActiveDataDTO;
    }
}
