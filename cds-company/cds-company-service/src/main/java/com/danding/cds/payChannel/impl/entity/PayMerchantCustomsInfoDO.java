package com.danding.cds.payChannel.impl.entity;

import com.danding.cds.common.model.BaseDO;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;

@Table(name = "ccs_pay_merchant_customs_info")
@Getter
@Setter
public class PayMerchantCustomsInfoDO extends BaseDO {
    /**
     * 商户id
     */
    @Column(name = "merchant_id")
    private Long merchantId;

    /**
     * 商户编码
     */
    @Column(name = "merchant_sn")
    private String merchantSn;

    /**
     * 申报海关编号
     */
    private String customs;

    /**
     * 商户海关备案编号
     */
    @Column(name = "merchant_customs_code")
    private String merchantCustomsCode;

    /**
     * 商户海关备案编号
     */
    @Column(name = "merchant_customs_name")
    private String merchantCustomsName;
}