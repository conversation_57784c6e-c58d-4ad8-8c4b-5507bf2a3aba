package com.danding.cds.message.impl.mapper;

import com.danding.cds.message.impl.entity.MessageSubscribeDO;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import tk.mybatis.mapper.additional.insert.InsertListMapper;
import tk.mybatis.mapper.common.Mapper;

public interface MessageSubscribeMapper extends Mapper<MessageSubscribeDO>, InsertListMapper<MessageSubscribeDO>, BatchUpdateMapper<MessageSubscribeDO>, AggregationPlusMapper<MessageSubscribeDO> {
}