package com.danding.cds.message.impl.process.taotian;

import com.danding.cds.message.api.dto.MessageDTO;
import com.danding.cds.message.api.enums.MessageType;
import com.danding.cds.message.api.process.TaotianReportMessage;
import com.danding.cds.message.impl.process.MessageProcess;
import com.danding.cds.message.impl.process.MessageRegistry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/3/13 14:51
 */
@Component
@Slf4j
public class TaotianReportProcess extends MessageProcess {
    public TaotianReportProcess(MessageRegistry registry) {
        super(registry);
    }

    @Override
    public MessageType getType() {
        return MessageType.TAOTIAN_REPORT;
    }

    @Override
    public Map<String, Object> buildSendInfo(MessageDTO messageDTO) {
        TaotianReportMessage taotianReportMessage = new TaotianReportMessage();
        taotianReportMessage.setMethod("taobao.logistics.order.process.status.report");
        taotianReportMessage.setBizData(messageDTO.getActiveData());
        return objToMap(taotianReportMessage);
    }
}
