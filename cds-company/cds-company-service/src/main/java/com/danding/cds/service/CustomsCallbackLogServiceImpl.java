package com.danding.cds.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.danding.cds.bean.dao.CallbackLogDO;
import com.danding.cds.bean.dto.CallbackLogDTO;
import com.danding.cds.callback.api.dto.CallbackLogSearch;
import com.danding.cds.callback.api.enums.CallbackCustomsTypeEnums;
import com.danding.cds.callback.api.enums.CallbackTypeEnums;
import com.danding.cds.common.utils.JSONUtils;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.declare.sdk.enums.CustomsType;
import com.danding.cds.mapper.CustomsCallbackLogMapper;
import com.danding.cds.mq.producer.CustomsCallbackLogRetryMQProducer;
import com.danding.cds.service.base.CustomsCallbackLogBaseService;
import com.danding.component.http.SaasHttpConstant;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.github.kevinsawicki.http.HttpRequest;
import com.github.pagehelper.PageInfo;
import com.umf.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Create 2021/8/31  16:08
 * @Describe
 **/
@Slf4j
@DubboService
public class CustomsCallbackLogServiceImpl implements CustomsCallbackLogService {
    @Autowired
    private CustomsCallbackLogBaseService customsCallbackLogBaseService;

    @Autowired
    private CustomsCallbackLogMapper customsCallbackLogMapper;

    @Autowired
    private CustomsCallbackLogRetryMQProducer customsCallbackLogRetryMQProducer;

    @Override
    public void saveLogs(List<CallbackLogDTO> list) {
        log.info("saveLogs list={}", JSONUtils.toJSONString(list));
        if (CollectionUtils.isEmpty(list)) {
            log.info("saveLogs list为空");
            return;
        }
//        List<CallbackLogDO> logDOList = list.stream().map(l -> {
//            CallbackLogDO callbackLogDO = new CallbackLogDO();
//            BeanUtils.copyProperties(l, callbackLogDO);
//            return callbackLogDO;
//        }).collect(Collectors.toList());
//        log.info("saveLogs logDOList={}", JSONUtils.toJSONString(logDOList));
//        customsCallbackLogBaseService.saveLogs(logDOList);
        customsCallbackLogBaseService.saveLogs(list);
    }

    @Override
    public void retryCallback(Long id) {
        CallbackLogDTO callbackLogDTO = customsCallbackLogBaseService.findById(id);
        log.info("retryCallback callbackLogDTO={}", JSONUtils.toJSONString(callbackLogDTO));
        CallbackLogDO logDO = new CallbackLogDO();
        logDO.setId(id).setRetryTime(callbackLogDTO.getRetryTime() + 1);
        customsCallbackLogBaseService.updateByPrimaryKey(logDO);
        try {
            Map<String, Object> paramMap = new HashMap<>();
            if ("HZDC".equalsIgnoreCase(callbackLogDTO.getCustoms())) {
                // 过滤转义符
                String requestParam = callbackLogDTO.getRequestParams().replaceAll("\\\\n", "")
                        .replaceAll("\\\\", "")
                        .replaceAll("^\"|\"$", "");
                paramMap.put("content", requestParam);
                paramMap.put("decipher", false);
                paramMap.put("msgType", callbackLogDTO.getCustomsType());
            } else {
                Map<String, String[]> map = JSON.parseObject(callbackLogDTO.getRequestParams(), new TypeReference<Map<String, String[]>>() {
                });

                Iterator<Map.Entry<String, String[]>> iterator = map.entrySet().iterator();
                Boolean encodeFlag = false;
                if (map.containsKey("decipher")) {
                    if ("true".equals(map.get("decipher")[0])) {
                        encodeFlag = true;
                    }
                }
                while (iterator.hasNext()) {
                    Map.Entry<String, String[]> next = iterator.next();
                    String content = next.getValue()[0];
                    if (encodeFlag) {
                        content = URLEncoder.encode(next.getValue()[0], "UTF-8");
                    }
                    paramMap.put(next.getKey(), content);
                }
            }
            HttpRequest result = HttpRequest.post(callbackLogDTO.getRetryUrl())
                    .header(SaasHttpConstant.HEADER_TENANT_ID, SimpleTenantHelper.getTenantIdStr())
                    .form(paramMap);
            log.info("retryCallback result={}", result.body());
        } catch (Exception e) {
            log.error("retryCallback error,message={}", e.getMessage(), e);
        }
    }

    @Override
    public void retryBatchByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ArgsErrorException("参数为空");
        }
        Example example = new Example(CallbackLogDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("id", ids);
        List<CallbackLogDO> list = customsCallbackLogMapper.selectByExample(example);
        retryBatch(list);
    }

    @Override
    public void retryBatchByCondition(CallbackLogSearch search) {
        List<CallbackLogDO> list = build(search);
        retryBatch(list);
    }

    private void retryBatch(List<CallbackLogDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            throw new ArgsErrorException("重试数据不存在");
        }
        for (CallbackLogDO callbackLogDO : list) {
            try {
                customsCallbackLogRetryMQProducer.send(callbackLogDO.getId());
            } catch (Exception e) {
                log.error("retryCallback error,message={}", e.getMessage(), e);
            }
        }
    }

    @Override
    @PageSelect
    public ListVO<CallbackLogDTO> paging(CallbackLogSearch search) {
        List<CallbackLogDO> callbackLogDOS = build(search);
        ListVO<CallbackLogDTO> result = new ListVO<>();
        result.setDataList(callbackLogDOS.stream().map(callbackLogDO -> {
            CallbackLogDTO callbackLogDTO = buildDTO(callbackLogDO);
            callbackLogDTO.setCustomsDesc(CustomsType.getEnum(callbackLogDTO.getCustoms()).getDesc());
            callbackLogDTO.setCallbackTypeDesc(CallbackTypeEnums.getEnum(callbackLogDTO.getCallbackType()).getDesc());
            callbackLogDTO.setCustomsTypeDesc(CallbackCustomsTypeEnums.getEnum(callbackLogDTO.getCustomsType()).getDesc());
            return callbackLogDTO;
        }).collect(Collectors.toList()));
        // 分页
        PageInfo<CallbackLogDO> pageInfo = new PageInfo(callbackLogDOS);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(search.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    private List<CallbackLogDO> build(CallbackLogSearch search) {
        Example example = new Example(CallbackLogDO.class);
        Example.Criteria criteria = example.createCriteria();
        if (Objects.nonNull(search.getCallbackType())) {
            criteria.andEqualTo("callbackType", search.getCallbackType());
        }
        if (Objects.nonNull(search.getCustomsType())) {
            criteria.andEqualTo("customsType", search.getCustomsType());
        }
        if (Objects.nonNull(search.getDecipher())) {
            criteria.andEqualTo("decipher", search.getDecipher());
        }
        if (!StringUtils.isEmpty(search.getBusinessCode())) {
            List<String> businessCodeList = Arrays.stream(search.getBusinessCode().split(",")).collect(Collectors.toList());
            criteria.andIn("businessCode", businessCodeList);
        }
        if (!LongUtil.isNone(search.getCreateTimeFrom())) {
            criteria.andGreaterThanOrEqualTo("createTime", new DateTime(search.getCreateTimeFrom()).toString("yyyy-MM-dd HH:mm:ss"));
        }
        if (!LongUtil.isNone(search.getCreateTimeTo())) {
            criteria.andLessThanOrEqualTo("createTime", new DateTime(search.getCreateTimeTo()).toString("yyyy-MM-dd HH:mm:ss"));
        }
        if (!LongUtil.isNone(search.getCustomsTimeFrom())) {
            criteria.andGreaterThanOrEqualTo("customsTime", new DateTime(search.getCustomsTimeFrom()).toString("yyyy-MM-dd HH:mm:ss"));
        }
        if (!LongUtil.isNone(search.getCustomsTimeTo())) {
            criteria.andLessThanOrEqualTo("customsTime", new DateTime(search.getCustomsTimeTo()).toString("yyyy-MM-dd HH:mm:ss"));
        }
        if (Objects.nonNull(search.getCallbackProperty())) {
            criteria.andEqualTo("callbackProperty", search.getCallbackProperty());
        }
        example.setOrderByClause("create_time DESC");
        List<CallbackLogDO> callbackLogDOS = customsCallbackLogMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(callbackLogDOS)) {
            return new ArrayList<>();
        }
        return callbackLogDOS;
    }

    private CallbackLogDTO buildDTO(CallbackLogDO callbackLogDO) {
        if (callbackLogDO == null) {
            return null;
        }
        CallbackLogDTO callbackLogDTO = new CallbackLogDTO();
        BeanUtils.copyProperties(callbackLogDO, callbackLogDTO);
        return callbackLogDTO;
    }
}
