package com.danding.cds.vehicle.impl.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.danding.cds.common.model.excel.ImportResultResVo;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.Assert;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.vehicle.impl.base.VehicleResourceBaseService;
import com.danding.cds.vehicle.impl.entity.VehicleResourceDO;
import com.danding.cds.vehicleResource.dto.VehicleResourceDTO;
import com.danding.cds.vehicleResource.dto.VehicleResourcePagingRequest;
import com.danding.cds.vehicleResource.service.VehicleResourceService;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.api.common.response.PageResult;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@DubboService
public class VehicleResourceServiceImpl implements VehicleResourceService {

    @Autowired
    private VehicleResourceBaseService vehicleResourceBaseService;

    @Override
    public List<VehicleResourceDTO> selectAll() {
        List<VehicleResourceDO> vehicleResourceDOS = vehicleResourceBaseService.selectAll();
        return ConvertUtil.listConvert(vehicleResourceDOS, VehicleResourceDTO.class);
    }

    @Override
    public VehicleResourceDTO findById(Long id) {
        VehicleResourceDO vehicleResourceDO = vehicleResourceBaseService.selectById(id);
        return ConvertUtil.beanConvert(vehicleResourceDO, VehicleResourceDTO.class);
    }

    @Override
    public List<VehicleResourceDTO> findById(List<Long> idList) {
        List<VehicleResourceDO> vehicleResourceDO = vehicleResourceBaseService.selectById(idList);
        return ConvertUtil.listConvert(vehicleResourceDO, VehicleResourceDTO.class);
    }

    @Override
    public List<VehicleResourceDTO> findByCode(List<String> vehicleResourceCodeList) {
        if (CollUtil.isEmpty(vehicleResourceCodeList)) {
            return new ArrayList<>();
        }
        Example example = new Example(VehicleResourceDO.class);
        example.createCriteria().andIn("vehicleResourceCode", vehicleResourceCodeList);
        List<VehicleResourceDO> vehicleResourceDOList = vehicleResourceBaseService.selectByExample(example);
        return ConvertUtil.listConvert(vehicleResourceDOList, VehicleResourceDTO.class);
    }

    @Override
    public List<VehicleResourceDTO> findByCode(String vehicleResourceCode) {
        if (StringUtil.isEmpty(vehicleResourceCode)) {
            return new ArrayList<>();
        }
        Example example = new Example(VehicleResourceDO.class);
        example.createCriteria().andEqualTo("vehicleResourceCode", vehicleResourceCode);
        List<VehicleResourceDO> vehicleResourceDOList = vehicleResourceBaseService.selectByExample(example);
        return ConvertUtil.listConvert(vehicleResourceDOList, VehicleResourceDTO.class);
    }

    @Override
    public List<VehicleResourceDTO> findByVehiclePlate(String vehiclePlate) {
        if (StringUtil.isEmpty(vehiclePlate)) {
            return new ArrayList<>();
        }
        Example example = new Example(VehicleResourceDO.class);
        example.createCriteria().andEqualTo("vehiclePlate", vehiclePlate);
        List<VehicleResourceDO> vehicleResourceDOList = vehicleResourceBaseService.selectByExample(example);
        return ConvertUtil.listConvert(vehicleResourceDOList, VehicleResourceDTO.class);
    }

    @Override
    public List<VehicleResourceDTO> findByVehiclePlate(List<String> vehiclePlateList) {
        if (CollUtil.isEmpty(vehiclePlateList)) {
            return new ArrayList<>();
        }
        Example example = new Example(VehicleResourceDO.class);
        example.createCriteria().andIn("vehiclePlate", vehiclePlateList);
        List<VehicleResourceDO> vehicleResourceDOList = vehicleResourceBaseService.selectByExample(example);
        return ConvertUtil.listConvert(vehicleResourceDOList, VehicleResourceDTO.class);
    }

    @Override
    @PageSelect
    public ListVO<VehicleResourceDTO> paging(VehicleResourcePagingRequest request) {
        Example example = new Example(VehicleResourceDO.class);
        Example.Criteria criteria = example.createCriteria();
        if (StringUtil.isNotEmpty(request.getVehicleResourceCode())) {
            criteria.andLike("vehicleResourceCode", "%" + request.getVehicleResourceCode() + "%");
        }
        if (StringUtil.isNotEmpty(request.getVehicleTypeName())) {
            criteria.andLike("vehicleTypeName", "%" + request.getVehicleTypeName() + "%");
        }
        if (StringUtil.isNotEmpty(request.getVehicleType())) {
            criteria.andEqualTo("vehicleType", request.getVehicleType());
        }
        if (StringUtil.isNotEmpty(request.getVehiclePlate())) {
            criteria.andEqualTo("vehiclePlate", request.getVehiclePlate());
        }
        if (Objects.nonNull(request.getEnable())) {
            criteria.andEqualTo("enable", request.getEnable());
        }
        if (Objects.nonNull(request.getVehiclePurpose())) {
            criteria.andEqualTo("vehiclePurpose", request.getVehiclePurpose());
        }
        example.orderBy("id").desc();
        List<VehicleResourceDO> vehicleResourceDOS = vehicleResourceBaseService.selectByExample(example);


        // 分页
        ListVO<VehicleResourceDTO> result = new ListVO<>();
        PageInfo<VehicleResourceDO> pageInfo = new PageInfo<>(vehicleResourceDOS);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        result.setPage(pageResult);
        result.setDataList(ConvertUtil.listConvert(vehicleResourceDOS, VehicleResourceDTO.class));
        return result;
    }

    @Override
    public void enable(List<Long> id, Integer enable) {
        if (CollUtil.isEmpty(id) || Objects.isNull(enable)) {
            throw new ArgsInvalidException("参数错误");
        }
        List<VehicleResourceDTO> list = this.findById(id);
        List<String> plateNoList = list.stream().map(VehicleResourceDTO::getVehiclePlate).collect(Collectors.toList());
        List<VehicleResourceDTO> dtoListByPlate = this.findByVehiclePlate(plateNoList);
        Map<String, List<VehicleResourceDTO>> plateNoMap = dtoListByPlate.stream()
                .filter(i -> Objects.equals(i.getEnable(), 1))
                .collect(Collectors.groupingBy(VehicleResourceDTO::getVehiclePlate));

        List<String> errorMsg = new ArrayList<>();
        for (VehicleResourceDTO vehicleResourceDTO : list) {
            if (plateNoMap.containsKey(vehicleResourceDTO.getVehiclePlate())) {
                List<VehicleResourceDTO> vehicleResourceDTOS = plateNoMap.get(vehicleResourceDTO.getVehiclePlate());
                if (CollUtil.isNotEmpty(vehicleResourceDTOS)
                        && vehicleResourceDTOS.stream().anyMatch(i -> !Objects.equals(vehicleResourceDTO.getVehicleResourceCode(), i.getVehicleResourceCode()))) {
                    errorMsg.add("已存在启用的车牌号【" + vehicleResourceDTO.getVehiclePlate() + "】");
                }
            }
        }
        if (CollUtil.isNotEmpty(errorMsg)) {
            throw new ArgsInvalidException(String.join(",", errorMsg));
        }

        Example example = new Example(VehicleResourceDO.class);
        example.createCriteria().andIn("id", id);
        VehicleResourceDO vehicleResourceDO = new VehicleResourceDO();
        vehicleResourceDO.setEnable(enable);
        vehicleResourceBaseService.updateByExampleSelective(vehicleResourceDO, example);
    }

    @Override
    public ImportResultResVo importExcel(VehicleResourceDTO importDTO) {
        try {
            List<VehicleResourceDTO> vehicleResourceDTOList = this.findByCode(importDTO.getVehicleResourceCode());
            if (CollUtil.isNotEmpty(vehicleResourceDTOList)) {
                VehicleResourceDTO existDTO = vehicleResourceDTOList.get(0);
                if (vehicleResourceDTOList.size() == 1 && StringUtils.isEmpty(existDTO.getVehiclePlate())) {
                    log.info("原资源编码【{}】匹配的数据，存在车牌号为空的数据 对该条更新", importDTO.getVehicleResourceCode());
                    VehicleResourceDO vehicleResourceDO = new VehicleResourceDO();
                    BeanUtil.copyProperties(importDTO, vehicleResourceDO);
                    vehicleResourceDO.setId(existDTO.getId());
                    vehicleResourceBaseService.updateByPrimaryKeySelective(vehicleResourceDO);
                    return new ImportResultResVo(true, "导入成功");
                }
                log.info("相同资源编码车辆资源code【{}】匹配的数据 更新基础数据", importDTO.getVehicleResourceCode());
                VehicleResourceDO vehicleResourceDO = new VehicleResourceDO();
                BeanUtil.copyProperties(importDTO, vehicleResourceDO);
                vehicleResourceDO.setVehiclePlate(null);
                Example example = new Example(VehicleResourceDO.class);
                example.createCriteria().andEqualTo("vehicleResourceCode", importDTO.getVehicleResourceCode());
                vehicleResourceBaseService.updateByExampleSelective(vehicleResourceDO, example);

                if (StringUtil.isEmpty(importDTO.getVehiclePlate())
                        || (vehicleResourceDTOList.stream().anyMatch(i -> Objects.equals(i.getVehiclePlate(), importDTO.getVehiclePlate())))) {
                    log.info("导入车牌号为空 or 原车辆资源匹配code匹配到的数据 车牌与导入数据一致，略过");
                } else {
                    log.info("【原资源编码-{} + 车牌号-{}】组合不存在 新增一条数据", importDTO.getVehicleResourceCode(), importDTO.getVehiclePlate());
                    vehicleResourceDO.setEnable(1);
                    vehicleResourceDO.setVehiclePlate(importDTO.getVehiclePlate());
                    vehicleResourceBaseService.insertSelective(vehicleResourceDO);
                }
            } else {
                log.info("原资源编码【{}】不存在，新增一条数据", importDTO.getVehicleResourceCode());
                VehicleResourceDO vehicleResourceDO = new VehicleResourceDO();
                BeanUtil.copyProperties(importDTO, vehicleResourceDO);
                vehicleResourceDO.setEnable(1);
                vehicleResourceBaseService.insertSelective(vehicleResourceDO);
            }
            return new ImportResultResVo(true, "导入成功");
        } catch (Exception e) {
            return new ImportResultResVo(false, "导入失败");
        }
    }

    @Override
    public VehicleResourceDTO findByEnableVehicleNo(String vehiclePlate) {
        VehicleResourceDO vehicleResourceDO = vehicleResourceBaseService.selectByEnableVehicleNo(vehiclePlate);
        return ConvertUtil.beanConvert(vehicleResourceDO, VehicleResourceDTO.class);
    }

    @Override
    public List<VehicleResourceDTO> findEnableByCode(String vehicleResourceCode) {
        Example example = new Example(VehicleResourceDO.class);
        example.createCriteria()
                .andEqualTo("vehicleResourceCode", vehicleResourceCode)
                .andEqualTo("enable", 1);
        List<VehicleResourceDO> vehicleResourceDOS = vehicleResourceBaseService.selectByExample(example);
        return ConvertUtil.listConvert(vehicleResourceDOS, VehicleResourceDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateVehiclePlateById(Long id, String truckNo) {
        Assert.notNull(id, "车辆资源id不能为空");
        Assert.notEmpty(truckNo, "车辆号牌不能为空");

        VehicleResourceDO vehicleResourceDO = new VehicleResourceDO();
        vehicleResourceDO.setId(id);
        UserUtils.setUpdateBy(vehicleResourceDO);
        vehicleResourceDO.setVehiclePlate(truckNo);
        vehicleResourceDO.setUpdateTime(new Date());
        vehicleResourceBaseService.updateByPrimaryKeySelective(vehicleResourceDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insert(VehicleResourceDTO dbTruck) {
        VehicleResourceDO vehicleResourceDO = ConvertUtil.beanConvert(dbTruck, VehicleResourceDO.class);
        vehicleResourceDO.setId(null);
        vehicleResourceBaseService.insertSelective(vehicleResourceDO);
    }
}
