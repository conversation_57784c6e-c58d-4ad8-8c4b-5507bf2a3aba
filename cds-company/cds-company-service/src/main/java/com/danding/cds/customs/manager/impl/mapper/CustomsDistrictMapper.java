package com.danding.cds.customs.manager.impl.mapper;

import com.danding.cds.customs.manager.impl.entity.CustomsDistrictDO;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import tk.mybatis.mapper.additional.insert.InsertListMapper;
import tk.mybatis.mapper.common.Mapper;

/**
 * @Author: yousx
 * @Date: 2023/11/23
 * @Description:
 */
public interface CustomsDistrictMapper extends Mapper<CustomsDistrictDO>, InsertListMapper<CustomsDistrictDO>, BatchUpdateMapper<CustomsDistrictDO>, AggregationPlusMapper<CustomsDistrictDO> {
}
