package com.danding.cds.payChannel.impl.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.cds.bean.dto.BaseDataSyncTypeEnums;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.EncryptionUtil;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.common.utils.ValidatorUtils;
import com.danding.cds.payChannel.api.dto.*;
import com.danding.cds.payChannel.api.service.PayMerchantAccountChannelService;
import com.danding.cds.payChannel.api.service.PayMerchantAccountService;
import com.danding.cds.payChannel.impl.entity.PayMerchantAccountChannelDO;
import com.danding.cds.payChannel.impl.mapper.PayMerchantAccountChannelMapper;
import com.danding.cds.service.BaseDataSyncService;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import javax.validation.Validator;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Author: Raymond
 * @Date: 2020/8/21 14:51
 * @Description:
 */

@Slf4j
@DubboService
public class PayMerchantAccountChannelServiceImpl implements PayMerchantAccountChannelService {
    @Autowired
    private PayMerchantAccountChannelMapper payMerchantAccountChannelMapper;

    @Autowired
    private PayMerchantAccountService payMerchantAccountService;

    @Autowired
    private Validator validator;

    @Autowired
    private BaseDataSyncService baseDataSyncService;

    @Override
    @PageSelect
    public ListVO<PayMerchantAccountChannelDTO> paging(PayMerchantAccountChannelSearch search) {
        Example example = new Example(PayMerchantAccountChannelDO.class);
        example.orderBy("createTime").desc();
        Example.Criteria criteria = example.createCriteria();

        if (!StringUtils.isEmpty(search.getRecpAccount())) {
            criteria.andLike("recpAccount", "%" + search.getRecpAccount() + "%");
        }

        if (!StringUtils.isEmpty(search.getMerchantSn())){
            criteria.andLike("merchantSn", "%" + search.getMerchantSn() + "%");
        }

        if (!LongUtil.isNone(search.getMerchantId())){
            criteria.andEqualTo("merchantId", search.getMerchantId());
        }

        List<PayMerchantAccountChannelDO> list = payMerchantAccountChannelMapper.selectByExample(example);
        ListVO<PayMerchantAccountChannelDTO> result = new ListVO<>();
        List<PayMerchantAccountChannelDTO> dataList = list.stream().map((PayMerchantAccountChannelDO item) -> {
            PayMerchantAccountChannelDTO optionDTO = new PayMerchantAccountChannelDTO();
            BeanUtils.copyProperties(item, optionDTO);
            return optionDTO;
        }).collect(Collectors.toList());
        result.setDataList(dataList);
        // 分页
        PageInfo<PayMerchantAccountChannelDTO> pageInfo = new PageInfo(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    @Override
    public List<PayMerchantAccountChannelDTO> queryListPayMerchantAccountChannelExport(PayMerchantAccountChannelSearch search) {
        Example example = new Example(PayMerchantAccountChannelDO.class);
        example.orderBy("createTime").desc();
        Example.Criteria criteria = example.createCriteria();

        if (!StringUtils.isEmpty(search.getRecpAccount())){
            criteria.andLike("recpAccount", "%" + search.getRecpAccount() + "%");
        }

        if (!StringUtils.isEmpty(search.getMerchantSn())){
            criteria.andLike("merchantSn", "%" + search.getMerchantSn() + "%");
        }

        if (!LongUtil.isNone(search.getMerchantId())){
            criteria.andEqualTo("merchantId", search.getMerchantId());
        }

        List<PayMerchantAccountChannelDO> list = payMerchantAccountChannelMapper.selectByExample(example);

        List<PayMerchantAccountChannelDTO> oretList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            for (PayMerchantAccountChannelDO info : list) {
                PayMerchantAccountChannelDTO infoDto = new PayMerchantAccountChannelDTO();
                BeanUtils.copyProperties(info, infoDto);
                oretList.add(infoDto);
            }
        }
        return oretList;
    }

    @Override
    public Long upset(PayMerchantAccountChannelSubmit submit) throws ArgsErrorException {
        // Step::入参校验
        String inputError = ValidatorUtils.doValidator(validator,submit);
        if (null != inputError){
            throw new ArgsErrorException(inputError);
        }
        //数据验证
        PayMerchantAccountChannelDO payMerchantAccountChannelDO = constructAnObject(submit);
        if (submit.getId() == null || submit.getId() == 0) {
            payMerchantAccountChannelDO.setId(null);
            PayMerchantAccountDTO payMerchantAccountDTO = payMerchantAccountService.findById(payMerchantAccountChannelDO.getMerchantId());
            payMerchantAccountChannelDO.setMerchantSn(payMerchantAccountDTO.getSn());
            PayMerchantAccountChannelDTO payMerchantAccountChannel = findByCodeAndChannel(payMerchantAccountChannelDO.getMerchantSn(), submit.getChannel());
            if (payMerchantAccountChannel != null) {
                throw new ArgsErrorException("该商户收款渠道不能重复添加");
            }
            UserUtils.setCreateAndUpdateBy(payMerchantAccountChannelDO);
            payMerchantAccountChannelMapper.insertSelective(payMerchantAccountChannelDO);

            baseDataSyncService.syncDataMQSend(BaseDataSyncTypeEnums.PAY_MERCHANT_ACCOUNT_CHANNEL, BaseDataSyncTypeEnums.INSERT, payMerchantAccountChannelDO);
        } else {
            PayMerchantAccountChannelDTO old = this.findById(submit.getId());
            if (old == null) {
                throw new ArgsErrorException("ID不正确");
            }
            if (!old.getChannel().equalsIgnoreCase(submit.getChannel())) {
                PayMerchantAccountChannelDTO payMerchantAccountChannel = findByCodeAndChannel(old.getMerchantSn(), submit.getChannel());
                if (payMerchantAccountChannel != null) {
                    throw new ArgsErrorException("该商户收款渠道不能重复添加");
                }
            }
            if (!Objects.equals(UserUtils.getUserId(), 0)) {
                UserUtils.setUpdateBy(payMerchantAccountChannelDO);
            }
            payMerchantAccountChannelDO.setUpdateTime(new Date());
            payMerchantAccountChannelMapper.updateByPrimaryKeySelective(payMerchantAccountChannelDO);

            PayMerchantAccountChannelDO syncData = payMerchantAccountChannelMapper.selectByPrimaryKey(payMerchantAccountChannelDO.getId());
            baseDataSyncService.syncDataMQSend(BaseDataSyncTypeEnums.PAY_MERCHANT_ACCOUNT_CHANNEL, BaseDataSyncTypeEnums.UPDATE, syncData);
        }
        submit.setId(payMerchantAccountChannelDO.getId());
        return payMerchantAccountChannelDO.getId();
    }

    /**
     * 构造持久化对象
     * @param submit
     * @return
     */
    private PayMerchantAccountChannelDO constructAnObject(PayMerchantAccountChannelSubmit submit) {
        // Step::构造持久化对象
        PayMerchantAccountChannelDO payMerchantAccountChannelDO = new PayMerchantAccountChannelDO();
        BeanUtils.copyProperties(submit, payMerchantAccountChannelDO);


        String regEx = "[ _`~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？]|\n|\r|\t";
        Pattern pattern = Pattern.compile(regEx);
        String key = EncryptionUtil.getEncryptionMsg(submit.getKey());

        // 基础信息
        if (submit.getChannel().equalsIgnoreCase("wechatpay")) {
            WechatpayTokenDTO token = new WechatpayTokenDTO();
            token.setAppId(submit.getAppId());
            token.setMchId(submit.getPartner());
            if (submit.getId() == null || submit.getId() == 0) {
                token.setPartnerKey(submit.getKey());
            }else {
                PayMerchantAccountChannelDTO channelDO = this.findById(submit.getId());
                WechatpayTokenDTO findTokenDTO = JSONObject.parseObject(channelDO.getTokenJson(), WechatpayTokenDTO.class);
                if (!Objects.equals(findTokenDTO.getPartnerKey().length(), submit.getKey().length())) {
                    if (pattern.matcher(key).find()) {
                        throw new ArgsErrorException("密钥包含加密字符请重新输入！");
                    }
                    token.setPartnerKey(submit.getKey());
                }else {
                    token.setPartnerKey(findTokenDTO.getPartnerKey());
                }

            }
            payMerchantAccountChannelDO.setTokenJson(JSON.toJSONString(token));
        } else if (submit.getChannel().equalsIgnoreCase("alipay")) {
            AlipayTokenDTO token = new AlipayTokenDTO();
            token.setAppId(submit.getAppId());
            token.setPartner(submit.getPartner());
            if (submit.getId() == null || submit.getId() == 0) {
                token.setKey(submit.getKey());
            }else {
                PayMerchantAccountChannelDTO channelDO = this.findById(submit.getId());
                AlipayTokenDTO findTokenDTO = JSONObject.parseObject(channelDO.getTokenJson(), AlipayTokenDTO.class);
                if (!Objects.equals(findTokenDTO.getKey().length(), submit.getKey().length())) {
                    if (pattern.matcher(key).find()) {
                        throw new ArgsErrorException("密钥包含加密字符请重新输入！");
                    }
                    token.setKey(submit.getKey());
                }else {
                    token.setKey(findTokenDTO.getKey());
                }

            }
            payMerchantAccountChannelDO.setTokenJson(JSON.toJSONString(token));
        } else if (submit.getChannel().equalsIgnoreCase("umf")) {
            Map<String, Object> map = new HashMap<>();
            map.put("clientId", submit.getPartner());
            map.put("clientSecret", submit.getKey());
            payMerchantAccountChannelDO.setTokenJson(JSON.toJSONString(map));
        }
        return payMerchantAccountChannelDO;
    }

    @Override
    public PayMerchantAccountChannelDTO findByCodeAndChannel(String merchantSn, String channel) {
        Example example = new Example(PayMerchantAccountChannelDO.class);
        example.orderBy("createTime").desc();
        Example.Criteria criteria = example.createCriteria();

        if (!StringUtils.isEmpty(channel)){
            criteria.andEqualTo("channel", channel);
        }

        if (!StringUtils.isEmpty(merchantSn)){
            criteria.andEqualTo("merchantSn", merchantSn);
        }


        PayMerchantAccountChannelDO channelDO = payMerchantAccountChannelMapper.selectOneByExample(example);
        if (channelDO == null) {
            return null;
        }
        PayMerchantAccountChannelDTO infoDto = new PayMerchantAccountChannelDTO();
        BeanUtils.copyProperties(channelDO, infoDto);
        return infoDto;
    }

    @Override
    public PayMerchantAccountChannelDTO findByMerchantAndChannel(Long id, String channel) {
        Example example = new Example(PayMerchantAccountChannelDO.class);
        example.orderBy("createTime").desc();
        Example.Criteria criteria = example.createCriteria();

        criteria.andEqualTo("merchantId", id);
        criteria.andEqualTo("channel", channel);

        PayMerchantAccountChannelDO channelDO = payMerchantAccountChannelMapper.selectOneByExample(example);
        if (channelDO == null) {
            return null;
        }
        PayMerchantAccountChannelDTO infoDto = new PayMerchantAccountChannelDTO();
        BeanUtils.copyProperties(channelDO, infoDto);
        return infoDto;
    }

    @Override
    public PayMerchantAccountChannelDTO findById(long id) {
        if (id == 0) {
            return null;
        } else {
            PayMerchantAccountChannelDO payMerchantAccountChannelDO = payMerchantAccountChannelMapper.selectByPrimaryKey(id);
            if (payMerchantAccountChannelDO == null) {
                return null;
            } else {
                PayMerchantAccountChannelDTO payMerchantAccountChannelDTO = new PayMerchantAccountChannelDTO();
                BeanUtils.copyProperties(payMerchantAccountChannelDO, payMerchantAccountChannelDTO);
                return payMerchantAccountChannelDTO;
            }
        }
    }
}
