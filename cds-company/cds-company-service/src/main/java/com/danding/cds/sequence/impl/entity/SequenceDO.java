package com.danding.cds.sequence.impl.entity;


import javax.persistence.*;
import java.util.Date;

@SuppressWarnings("serial")
@Entity
@Table(name = "ccs_sequence")
//@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class SequenceDO implements java.io.Serializable {
	@Id
	@Column(name = "seq_code", unique = true, nullable = false, length = 50)
	private String code;
	@Column(name = "seq_name", nullable = false, length = 100)
	private String name;
	@Column(name = "seq_value", nullable = false)
	private Long value;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "update_time", length = 7)
	private Date time;


	//@Column(name = "seq_code", unique = true, nullable = false, length = 50)
	public String getCode() {
		return this.code;
	}

	public void setCode(String code) {
		this.code = code;
	}


	public String getName() {
		return this.name;
	}

	public void setName(String name) {
		this.name = name;
	}


	public Long getValue() {
		return this.value;
	}

	public void setValue(Long value) {
		this.value = value;
	}


	public Date getTime() {
		return this.time;
	}

	public void setTime(Date time) {
		this.time = time;
	}
}
