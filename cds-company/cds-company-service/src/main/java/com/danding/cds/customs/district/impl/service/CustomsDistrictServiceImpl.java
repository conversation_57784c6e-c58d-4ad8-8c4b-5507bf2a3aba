package com.danding.cds.customs.district.impl.service;

import com.danding.cds.common.utils.Assert;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.customs.district.api.service.CustomsDistrictService;
import com.danding.cds.customs.manager.api.dto.CustomsDistrictDTO;
import com.danding.cds.customs.manager.impl.entity.CustomsDistrictDO;
import com.danding.cds.customs.manager.impl.mapper.CustomsDistrictMapper;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @Author: yousx
 * @Date: 2023/11/28
 * @Description:
 */
@Slf4j
@DubboService
public class CustomsDistrictServiceImpl implements CustomsDistrictService {

    @Resource
    private CustomsDistrictMapper customsDistrictMapper;
//    @Resource
//    private StringRedisTemplate stringRedisTemplate;

    @Override
    public CustomsDistrictDTO getByCode(String code) {
//        String json = stringRedisTemplate.opsForValue().get(code);
//        Gson gson = new Gson();
//        if (StrUtil.isEmpty(json)) {
        Example example = new Example(CustomsDistrictDO.class);
        example.createCriteria()
                .andEqualTo("code", code);
        CustomsDistrictDO customsDistrictDO = customsDistrictMapper.selectOneByExample(example);
        Assert.notNull(customsDistrictDO, "未找到该口岸");
        CustomsDistrictDTO customsDistrictDTO = BeanUtils.copyProperties(customsDistrictDO, CustomsDistrictDTO.class);
//        stringRedisTemplate.opsForValue().set(code, gson.toJson(customsDistrictDTO), 7, TimeUnit.DAYS);
        return customsDistrictDTO;
//        }
//        return gson.fromJson(json, CustomsDistrictDTO.class);
    }

    @Override
    public CustomsDistrictDTO getByName(String name) {
//        String json = stringRedisTemplate.opsForValue().get(code);
//        Gson gson = new Gson();
//        if (StrUtil.isEmpty(json)) {
        Example example = new Example(CustomsDistrictDO.class);
        example.createCriteria()
                .andEqualTo("name", name);
        List<CustomsDistrictDO> customsDistrictDOList = customsDistrictMapper.selectByExample(example);
        if (Objects.isNull(customsDistrictDOList)) {
            return null;
        }
        //        stringRedisTemplate.opsForValue().set(code, gson.toJson(customsDistrictDTO), 7, TimeUnit.DAYS);
        return BeanUtils.copyProperties(customsDistrictDOList.get(0), CustomsDistrictDTO.class);
//        }
//        return gson.fromJson(json, CustomsDistrictDTO.class);
    }

    @Override
    public List<CustomsDistrictDTO> listAll() {
//        String districtList = stringRedisTemplate.opsForValue().get("customsDistrictList");
//        Gson gson = new Gson();
//        if (StrUtil.isEmpty(districtList)) {
        Example example = new Example(CustomsDistrictDO.class);
        example.createCriteria()
                .andEqualTo("status", 1)
                .andEqualTo("deleted", false);
        List<CustomsDistrictDO> customsDistrictList = customsDistrictMapper.selectByExample(example);
        Assert.notEmpty(customsDistrictList, "口岸集合为空");
        List<CustomsDistrictDTO> customsDistrictDTOS = BeanUtils.copyProperties(customsDistrictList, CustomsDistrictDTO.class);
//            stringRedisTemplate.opsForValue().set("customsDistrictList", gson.toJson(customsDistrictDTOS), 7, TimeUnit.DAYS);
        return customsDistrictDTOS;
//        }
//        return gson.fromJson(districtList, new TypeToken<List<CustomsDistrictDTO>>() {
//        }.getType());
    }

    @Override
    public List<CustomsDistrictDTO> getByNameList(List<String> nameList) {
        Example example = new Example(CustomsDistrictDO.class);
        example.createCriteria()
                .andIn("name", nameList)
                .andEqualTo("status", 1)
                .andEqualTo("deleted", false);
        List<CustomsDistrictDO> customsDistrictList = customsDistrictMapper.selectByExample(example);
        return ConvertUtil.listConvert(customsDistrictList, CustomsDistrictDTO.class);
    }
}
