package com.danding.cds.message.impl.mapper;

import com.danding.cds.message.impl.entity.MessageTaskDO;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import org.apache.ibatis.annotations.Insert;
import tk.mybatis.mapper.additional.insert.InsertListMapper;
import tk.mybatis.mapper.common.Mapper;

public interface MessageTaskMapper extends Mapper<MessageTaskDO>, InsertListMapper<MessageTaskDO>, BatchUpdateMapper<MessageTaskDO>, AggregationPlusMapper<MessageTaskDO> {


    @Insert("INSERT INTO `ccs_message_task_history` " +
            "(`id`,`subscribe_id`,`message_id`,`type`,`business_code`,`notify_url`,`request_data`,`send_record_json`,`count`,`last_notify_time`,`next_notify_time`,`status`,`create_by`,`update_by`,`create_time`,`update_time`,`deleted`) " +
            "VALUES " +
            "(#{id},#{subscribeId},#{messageId},#{type},#{businessCode},#{notifyUrl},#{requestData},#{sendRecordJson},#{count},#{lastNotifyTime},#{nextNotifyTime},#{status},#{createBy},#{updateBy},#{createTime},#{updateTime},#{deleted});")
    void insertHistory(MessageTaskDO taskDO);
}