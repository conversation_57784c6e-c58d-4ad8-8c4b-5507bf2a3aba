package com.danding.cds.service.base;

import com.danding.cds.bean.dao.CallbackLogDO;
import com.danding.cds.bean.dto.CallbackLogDTO;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.common.utils.JSONUtils;
import com.danding.cds.http.saas.annotation.TenantHttpMethod;
import com.danding.cds.http.saas.enums.TenantHttpType;
import com.danding.cds.mapper.CustomsCallbackLogMapper;
import com.danding.cds.service.handler.CallBackLogTenantHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Create 2021/8/31  16:17
 * @Describe
 **/
@Slf4j
@Service
public class CustomsCallbackLogBaseService {
    @Autowired
    private CustomsCallbackLogMapper customsCallbackLogMapper;

    @Autowired
    public CustomsCallbackLogBaseService customsCallbackLogBaseService;


    public void saveLogs(List<CallbackLogDTO> callbackLogDTOList) {
        try {
            log.info("CustomsCallbackLogBaseService-saveLogs callbackLogDTO={}", JSONUtils.toJSONString(callbackLogDTOList));
            callbackLogDTOList.forEach(c -> customsCallbackLogBaseService.insertSelective(c));
        } catch (Exception e) {
            log.error("CustomsCallbackLogBaseService-saveLogs error,message={}", e.getMessage(), e);
        }
    }

    @TenantHttpMethod(type = TenantHttpType.CALLBACK, handler = CallBackLogTenantHandler.class)
    public void insert(CallbackLogDO callbackLogDO) {
        customsCallbackLogMapper.insert(callbackLogDO);
    }

    @TenantHttpMethod(type = TenantHttpType.CALLBACK, handler = CallBackLogTenantHandler.class)
    public void insertSelective(CallbackLogDO callbackLogDO) {
        customsCallbackLogMapper.insertSelective(callbackLogDO);
    }

    @TenantHttpMethod(type = TenantHttpType.CALLBACK, handler = CallBackLogTenantHandler.class)
    public void insertSelective(CallbackLogDTO callbackLogDTO) {
        CallbackLogDO callbackLogDO = ConvertUtil.beanConvert(callbackLogDTO, CallbackLogDO.class);
        customsCallbackLogMapper.insertSelective(callbackLogDO);
    }

    public CallbackLogDTO findById(Long id) {
        CallbackLogDO callbackLogDO = customsCallbackLogMapper.selectByPrimaryKey(id);
        CallbackLogDTO dto = new CallbackLogDTO();
        BeanUtils.copyProperties(callbackLogDO, dto);
        return dto;
    }

    public void updateByPrimaryKey(CallbackLogDO logDO) {
        customsCallbackLogMapper.updateByPrimaryKeySelective(logDO);
    }

}
