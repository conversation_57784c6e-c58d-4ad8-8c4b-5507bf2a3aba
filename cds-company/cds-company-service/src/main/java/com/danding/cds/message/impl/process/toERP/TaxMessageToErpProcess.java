package com.danding.cds.message.impl.process.toERP;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.cds.c.api.rpc.OrderCRpc;
import com.danding.cds.common.config.OrderCCallConfig;
import com.danding.cds.common.utils.JSONUtils;
import com.danding.cds.declare.ceb.domain.ceb816.CEB816Message;
import com.danding.cds.declare.ceb.domain.ceb816.Tax;
import com.danding.cds.declare.ceb.domain.ceb816.TaxHeadRd;
import com.danding.cds.declare.ceb.domain.ceb816.TaxListRd;
import com.danding.cds.declare.ceb.internal.utils.XMLUtil;
import com.danding.cds.declare.sdk.clear.base.callback.module.TaxResult;
import com.danding.cds.message.api.dto.MessageDTO;
import com.danding.cds.message.api.enums.MessageType;
import com.danding.cds.message.impl.process.MessageExecuteResult;
import com.danding.cds.message.impl.process.MessageProcess;
import com.danding.cds.message.impl.process.MessageRegistry;
import com.danding.cds.message.impl.process.toERP.entity.EntranceRpcParam;
import com.danding.cds.message.impl.process.toERP.entity.TaxMsgToErpResVO;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.api.dto.OrderExtra;
import com.danding.cds.order.api.service.OrderService;
import com.danding.cds.v2.api.ErpIOutOrderRpcFacadeService;
import com.danding.cds.v2.bean.dto.EntityWarehouseDTO;
import com.danding.cds.v2.service.EntityWarehouseService;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Component
@Slf4j
public class TaxMessageToErpProcess extends MessageProcess {


    @DubboReference
    private OrderService orderService;
    @DubboReference
    private OrderCRpc orderCRpc;
    @Resource
    private OrderCCallConfig orderCCallConfig;
    @DubboReference
    private EntityWarehouseService entityWarehouseService;


    @DubboReference
    private ErpIOutOrderRpcFacadeService erpIOutOrderRpcFacadeService;

    public TaxMessageToErpProcess(MessageRegistry registry) {
        super(registry);
    }

    @Override
    public MessageType getType() {
        return MessageType.ORDER_TAX_TO_ERP;
    }

    @Override
    public Map<String, Object> buildSendInfo(MessageDTO messageDTO) {

        TaxResult activeInfo = JSON.parseObject(messageDTO.getActiveData(), TaxResult.class);
        log.info("[op:buildSendInfo] 税单回执打印-原 - {}", activeInfo);
        OrderDTO orderDTO = orderCCallConfig.isOrderCCall(this.getClass()) ? orderCRpc.findByDeclareOrderNo(messageDTO.getBusinessCode()) : orderService.findByDeclareOrderNo(messageDTO.getBusinessCode());
        if (Objects.isNull(orderDTO)) {
            log.error("税单回执回传erp失败：订单不存在-申报单号={}", messageDTO.getBusinessCode());
        }
        String content = activeInfo.getResponseMsg();
//        //获取LP单号
//        String lpNo = erpIOutOrderRpcFacadeService.getLpNoByGsNo(orderDTO.getSystemGlobalSn());
//        //获取CB单号
//        String cbNo = erpIOutOrderRpcFacadeService.getCbNoByLpNo(lpNo, orderDTO.getSystemGlobalSn());        //将CB单号填入报文的copNo中
//        //将CB单号填入报文的copNo中
//        if (StringUtils.isNotEmpty(cbNo)) {
//            if (content.contains("<copNo>")) {
//                content = MyStringUtils.replaceBetween(content, "<copNo>", "</copNo>", cbNo);
//            } else {
//                String replacement = "<copNo>" + cbNo + "</copNo></CEB816Message>";
//                content = content.replace("</CEB816Message>", replacement);
//            }
//        }
        try {
            content = content.replace("xmlns=\"http://www.chinaport.gov.cn/ceb\"", "");
            CEB816Message ceb816Message = XMLUtil.converyToJavaBean(content, CEB816Message.class);
            Tax tax = ceb816Message.getTax().get(0);
            TaxHeadRd taxHeadRd = tax.getTaxHeadRd();
            taxHeadRd.setOrderNo(orderDTO.getOutOrderNo());
            List<TaxListRd> taxListRd = tax.getTaxListRd();

            TaxMsgToErpResVO taxMsgToErpResVO = new TaxMsgToErpResVO();
            TaxMsgToErpResVO.ErpTax erpTax = new TaxMsgToErpResVO.ErpTax();
            erpTax.setTaxHeadRd(taxHeadRd);
            erpTax.setTaxListRd(taxListRd);
            taxMsgToErpResVO.setTax(erpTax);
            content = JSONUtils.toJSONString(taxMsgToErpResVO);
        } catch (Exception e) {
            log.error("税金报文回传ERP 封装失败:" + e.getMessage(), e);
        }
        OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
        String warehouseCode = Objects.nonNull(orderExtra.getSubmit()) ? orderExtra.getSubmit().getErpPhyWarehouseSn() : "";
        if (StringUtil.isNotEmpty(warehouseCode)) {
            List<EntityWarehouseDTO> entityWarehouseDTOS = entityWarehouseService.findDTOByErpCode(warehouseCode);
            warehouseCode = entityWarehouseDTOS.stream().map(EntityWarehouseDTO::getWmsWarehouseCode).findFirst().orElse("");
        }
        log.info("回传ERP 税单回执打印 content={}", content);
        Map<String, String> businessData = new HashMap<>();
        businessData.put("warehouseCode", warehouseCode);
        businessData.put("ownerCode", "");
        businessData.put("content", content);
        //杭州总署交易单清单放行回执 关税回执
        EntranceRpcParam param = EntranceRpcParam.of(orderDTO.getSystemGlobalSn(),
                "CCS", "CNZXC", "82", 3, JSONUtils.toJSONString(businessData));
        return objToMap(param);
    }

    @Override
    public void processResultBody(MessageExecuteResult result, String body) {
        if (isJSONValid(body)) {
            JSONObject jsonObject = JSON.parseObject(body);
            String code = jsonObject.get("code").toString();
            result.setSuccess(Objects.equals(code, "200"));
        } else {
            super.processResultBody(result, body);
        }
    }
}
