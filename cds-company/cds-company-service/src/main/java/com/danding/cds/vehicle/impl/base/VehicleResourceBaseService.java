package com.danding.cds.vehicle.impl.base;

import com.danding.cds.common.user.UserUtils;
import com.danding.cds.vehicle.impl.entity.VehicleResourceDO;
import com.danding.cds.vehicle.impl.mapper.VehicleResourceMapper;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class VehicleResourceBaseService {

    @Resource
    private VehicleResourceMapper vehicleResourceMapper;

    public void insertSelective(VehicleResourceDO vehicleResourceDO) {
        UserUtils.setCommonData(vehicleResourceDO);
        vehicleResourceMapper.insertSelective(vehicleResourceDO);
    }

    public void insertList(List<VehicleResourceDO> vehicleResourceDOList) {
        vehicleResourceDOList.forEach(UserUtils::setCommonData);
        vehicleResourceMapper.insertList(vehicleResourceDOList);
    }

    public VehicleResourceDO selectById(Long id) {
        return vehicleResourceMapper.selectByPrimaryKey(id);
    }

    public List<VehicleResourceDO> selectById(List<Long> id) {
        Example example = new Example(VehicleResourceDO.class);
        example.createCriteria().andIn("id", id);
        return vehicleResourceMapper.selectByExample(example);
    }

    public List<VehicleResourceDO> selectAll() {
        return vehicleResourceMapper.selectAll();
    }

    public List<VehicleResourceDO> selectByExample(Example example) {
        return vehicleResourceMapper.selectByExample(example);
    }

    public List<VehicleResourceDO> selectByEnable(Example example) {
        example.createCriteria().andEqualTo("enable", 1);
        return vehicleResourceMapper.selectByExample(example);
    }

    public void updateByPrimaryKeySelective(VehicleResourceDO vehicleResourceDO) {
        UserUtils.setUpdateBy(vehicleResourceDO);
        vehicleResourceDO.setUpdateTime(new Date());
        vehicleResourceMapper.updateByPrimaryKeySelective(vehicleResourceDO);
    }

    public void updateByExampleSelective(VehicleResourceDO vehicleResourceDO, Example example) {
        UserUtils.setUpdateBy(vehicleResourceDO);
        vehicleResourceDO.setUpdateTime(new Date());
        vehicleResourceMapper.updateByExampleSelective(vehicleResourceDO, example);
    }

    public void updateByExample(VehicleResourceDO vehicleResourceDO, Example example) {
        UserUtils.setUpdateBy(vehicleResourceDO);
        vehicleResourceDO.setUpdateTime(new Date());
        vehicleResourceMapper.updateByExample(vehicleResourceDO, example);
    }

    public void updateByPrimaryKey(VehicleResourceDO vehicleResourceDO) {
        UserUtils.setUpdateBy(vehicleResourceDO);
        vehicleResourceDO.setUpdateTime(new Date());
        vehicleResourceMapper.updateByPrimaryKey(vehicleResourceDO);
    }


    public VehicleResourceDO selectByEnableVehicleNo(String vehiclePlate) {
        Example example = new Example(VehicleResourceDO.class);
        example.createCriteria()
                .andEqualTo("vehiclePlate", vehiclePlate)
                .andEqualTo("enable", 1);
        return vehicleResourceMapper.selectOneByExample(example);
    }
}
