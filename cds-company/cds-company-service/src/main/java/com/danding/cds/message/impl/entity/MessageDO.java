package com.danding.cds.message.impl.entity;

import com.danding.cds.common.model.BaseDO;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Table(name = "ccs_message")
@Getter
@Setter
public class MessageDO extends BaseDO {
    /**
     * 精准回传地址
     */
    @Column(name = "notify_url")
    private String notifyUrl;

    /**
     * 消息类型
     */
    private String type;

    /**
     * 多个消息标签
     */
    @Column(name = "tag_json")
    private String tagJson;

    /**
     * 消息状态
     */
    private Integer status;

    /**
     * 业务编号
     */
    @Column(name = "business_code")
    private String businessCode;

    /**
     * 触发回传的初始参数
     */
    @Column(name = "active_data")
    private String activeData;

    /**
     * 请求数据
     */
    @Column(name = "request_data")
    private String requestData;

    /**
     * 多消息任务标识
     */
    @Column(name = "multi_task")
    private Boolean multiTask;

    /**
     * 消息组id 当多条消息之间有关联时设置
     */
    @Column(name = "group_id")
    private Long groupId;

    @Column(name = "pre_message_id")
    private Long preMessageId;

    @Column(name = "next_message_id")
    private Long nextMessageId;
}