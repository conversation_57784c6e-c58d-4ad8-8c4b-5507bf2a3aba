package com.danding.cds.customs.hs.impl.service;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.common.enums.HsConsumptionFlagEnums;
import com.danding.cds.common.enums.HsFloatTypeEnums;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.common.utils.ValidatorUtils;
import com.danding.cds.customs.hs.api.dto.CustomsHsDTO;
import com.danding.cds.customs.hs.api.dto.CustomsHsSearchCondition;
import com.danding.cds.customs.hs.api.dto.CustomsHsSubmit;
import com.danding.cds.customs.hs.api.service.CustomsHsService;
import com.danding.cds.customs.hs.impl.entity.CustomsHsDO;
import com.danding.cds.customs.hs.impl.mapper.CustomsHsMapper;
import com.danding.cds.customs.uom.api.dto.CustomsUomDTO;
import com.danding.cds.customs.uom.api.service.CustomsUomService;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.danding.logistics.mq.common.handler.MessageSender;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import javax.validation.Validator;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;


/**
 * @Auther: shenfeng
 * @Date: 2020-04-29 10:23
 * @Description:
 */
@Slf4j
@DubboService
public class CustomsHsServicelmpl implements CustomsHsService {

    @Autowired
    CustomsHsMapper customsHsMapper;

    @DubboReference
    private CustomsUomService customsUomService;

    @Autowired
    private Validator validator;

    @Resource
    private MessageSender messageSender;

    @Override
    public CustomsHsDTO findById(Long id) {
        if (LongUtil.isNone(id)) {
            return null;
        } else {
            CustomsHsDO customsHsDO = customsHsMapper.selectByPrimaryKey(id);
            return this.buildDTO(customsHsDO);
        }
    }

    @Override
    public Long deleteById(Long id) {
        if (LongUtil.isNone(id)) {
            return null;
        } else {
            CustomsHsDTO customsHsDTO = this.findById(id);
            if (customsHsDTO == null) {
                return null;
            }
            CustomsHsDO hsDO = new CustomsHsDO();
            hsDO.setId(id);
            hsDO.setDeleted(true);
            if (!Objects.equals(UserUtils.getUserId(), 0)) {
                UserUtils.setUpdateBy(hsDO);
            }
            hsDO.setUpdateTime(new Date());
            customsHsMapper.updateByPrimaryKeySelective(hsDO);
            return id;
        }
    }

    @Override
//    @Cacheable(value = "customsHsByCode", key = "#code", unless = "#result==null")
    public CustomsHsDTO findByCode(String code) {
        CustomsHsDO condition = new CustomsHsDO();
        condition.setHsCode(code);
        return this.buildDTO(customsHsMapper.selectOne(condition));
    }

    @Override
    public List<CustomsHsDTO> findByCode(List<String> code) {
        if (CollUtil.isEmpty(code)) {
            return new ArrayList<>();
        }
        Example example = new Example(CustomsHsDO.class);
        example.createCriteria().andIn("hsCode", code).andEqualTo("deleted", 0);
        List<CustomsHsDO> customsHsDOS = customsHsMapper.selectByExample(example);
        return ConvertUtil.listConvert(customsHsDOS, CustomsHsDTO.class);
    }

    @Override
    public List<CustomsHsDTO> listEnable() {
        CustomsHsDO condition = new CustomsHsDO();
        condition.setEnable(1);
        return JSON.parseArray(JSON.toJSONString(customsHsMapper.select(condition)), CustomsHsDTO.class);
    }

    @Override
    public List<CustomsHsDTO> getCustomsHsAll() {
        return com.danding.logistics.rpc.common.utils.BeanUtils.copyProperties(this.customsHsMapper.selectAll(),CustomsHsDTO.class);
    }

    @Override
    @PageSelect
    public ListVO<CustomsHsDTO> paging(CustomsHsSearchCondition condition) {
        Example example = new Example(CustomsHsDO.class);
        Example.Criteria criteria = example.createCriteria();
        String hsCode = condition.getHsCode();
        if (!StringUtils.isEmpty(hsCode)) {
            if (hsCode.contains(",")) {
                List<String> hsCodeList = Lists.newArrayList(hsCode.split(","));
                criteria.andIn("hsCode", hsCodeList);
            } else {
                criteria.andEqualTo("hsCode", condition.getHsCode());
            }
        }
        if (!StringUtils.isEmpty(condition.getHsName())) {
            criteria.andLike("hsName", "%" + condition.getHsName() + "%");
        }
        if (Objects.nonNull(condition.getConsumptionFlag())) {
            criteria.andEqualTo("consumptionFlag", condition.getConsumptionFlag());
        }
        if (Objects.nonNull(condition.getFloatType())) {
            criteria.andEqualTo("floatType", condition.getFloatType());
        }
        criteria.andEqualTo("deleted", 0);
        //排序
        example.setOrderByClause("create_time DESC");
        List<CustomsHsDO> list = customsHsMapper.selectByExample(example);
        ListVO<CustomsHsDTO> result = new ListVO<>();
        result.setDataList(JSON.parseArray(JSON.toJSONString(list), CustomsHsDTO.class));
        // 分页
        PageInfo<CustomsHsDO> pageInfo = new PageInfo(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    @Override
    public Long updateEnable(Long id, Integer enable) {
        CustomsHsDO customsHsDO = new CustomsHsDO();
        customsHsDO.setId(id);
        customsHsDO.setEnable(enable);
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(customsHsDO);
        }
        customsHsDO.setUpdateTime(new Date());
        customsHsMapper.updateByPrimaryKeySelective(customsHsDO);
        return customsHsDO.getId();
    }

    @Override
//    @Caching(evict = {@CacheEvict(value = "customsHsByCode", key = "#submit.getHsCode()")})
    public Long upset(CustomsHsSubmit submit) throws ArgsErrorException {
        log.info("hs提交数据 :{}", JSON.toJSONString(submit));
        //校验必填项
        String inputError = ValidatorUtils.doValidator(validator, submit);
        if (Objects.nonNull(inputError)) {
            throw new ArgsErrorException(inputError);
        }
        /*if (submit.getHsCode().length() != 10) {
            throw new ArgsErrorException("海关编码为10位数字,请检查");
        }
        for (int i = submit.getHsCode().length(); --i >= 0; ) {
            if (!Character.isDigit(submit.getHsCode().charAt(i))) {
                throw new ArgsErrorException("海关编码为10位数字,请检查");
            }
        }*/
        CustomsHsDTO old = this.findByCode(submit.getHsCode());
        if (old != null) {
            if (LongUtil.isNone(submit.getId())) {
                //新增
                if (!submit.getIsImported()) {
                    throw new ArgsErrorException("HS编码" + old.getHsCode() + "已存在");
                }
                submit.setId(old.getId());
            } else {//编辑
                if (!submit.getId().equals(old.getId())) {
                    throw new ArgsErrorException("HS编码" + old.getHsCode() + "已存在");
                }
            }
        }
        CustomsUomDTO uomDTO = customsUomService.findByCode(submit.getFirstLegalUnit());
        if (uomDTO == null) {
            throw new ArgsErrorException("法定第一计量单位错误");
        }
        if (!StringUtils.isEmpty(submit.getSecondLegalUnit())) {
            uomDTO = customsUomService.findByCode(submit.getSecondLegalUnit());
            if (uomDTO == null) {
                throw new ArgsErrorException("法定第二计量单位错误");
            }
            if (submit.getFirstLegalUnit().equals(submit.getSecondLegalUnit())) {
                throw new ArgsErrorException("法定第二计量单位不能与法定第一计量单位相同");
            }
        }
        Integer consumptionFlag = submit.getConsumptionFlag();
        HsConsumptionFlagEnums consumptionFlagEnums = HsConsumptionFlagEnums.getEnum(consumptionFlag);
        if (Objects.isNull(consumptionFlagEnums)) {
            throw new ArgsErrorException("消费税率类型错误");
        }
        if (Objects.equals(consumptionFlag, HsConsumptionFlagEnums.SPECIFIC.getCode()) || Objects.equals(consumptionFlag, HsConsumptionFlagEnums.COMPOSITE.getCode())) {
            submit.setFloatType(HsFloatTypeEnums.NO_FLOAT.getCode());
        }
        Integer floatType = submit.getFloatType();
        HsFloatTypeEnums floatTypeEnums = HsFloatTypeEnums.getEnum(floatType);
        if (Objects.isNull(floatTypeEnums)) {
            throw new ArgsErrorException("浮动类型错误");
        }

        if (Objects.isNull(submit.getConsumptionTax())) {
            throw new ArgsErrorException("从价定率不能为空");
        }
        switch (consumptionFlagEnums) {
            case PRICE:
                if (!Objects.equals(floatTypeEnums, HsFloatTypeEnums.NO_FLOAT)) {
                    if (StringUtils.isEmpty(submit.getPricePerUnit())) {
                        throw new ArgsErrorException("从价单价不能为空");
                    }
                    if (StringUtils.isEmpty(submit.getUomName())) {
                        throw new ArgsErrorException("从价规格不能为空");
                    }
                }
                break;
            case SPECIFIC:
            case COMPOSITE:
                if (StringUtils.isEmpty(submit.getSpecificPricePerUnit())) {
                    throw new ArgsErrorException("从量单价不能为空");
                }
                if (StringUtils.isEmpty(submit.getSpecificUomName())) {
                    throw new ArgsErrorException("从量规格不能为空");
                }
                break;
            default:
                break;
        }
        CustomsHsDO customsHsDO = new CustomsHsDO();
        BeanUtils.copyProperties(submit, customsHsDO);
//        if (customsHsDO.getSecondLegalUnit() == null) {
//            customsHsDO.setSecondLegalUnit("");
//        }
//        if (submit.getConsumptionFlag() == 5) {//从量
//            customsHsDO.setConsumptionNumTax(submit.getConsumptionNumTax().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
//            customsHsDO.setConsumptionTax(BigDecimal.ZERO);
//        } else if (submit.getConsumptionFlag() == 10) {//从价
//            customsHsDO.setConsumptionTax(submit.getConsumptionTax().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
//            customsHsDO.setConsumptionNumTax(BigDecimal.ZERO);
//            customsHsDO.setUomName("");
//            customsHsDO.setPricePerUnit(BigDecimal.ZERO);
//        }
        customsHsDO.setConsumptionTax(submit.getConsumptionTax().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        customsHsDO.setImportDiscountTaxRate(submit.getImportDiscountTaxRate().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        customsHsDO.setImportGeneralTaxRate(submit.getImportGeneralTaxRate().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        if (submit.getImportTentativeTaxRate() != null) {
            customsHsDO.setImportTentativeTaxRate(submit.getImportTentativeTaxRate().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        }
        customsHsDO.setExportDrawbackTaxRate(submit.getExportDrawbackTaxRate().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        customsHsDO.setExportTaxRate(submit.getExportTaxRate().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        if (submit.getExportTentativeTaxRate() != null) {
            customsHsDO.setExportTentativeTaxRate(submit.getExportTentativeTaxRate().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        }
        customsHsDO.setVat(submit.getVat().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        if (LongUtil.isNone(submit.getId())) {
            customsHsDO.setId(null);
            UserUtils.setCommonData(customsHsDO);
            customsHsMapper.insertSelective(customsHsDO);
        } else {
            if (!Objects.equals(UserUtils.getUserId(), 0)) {
                UserUtils.setUpdateBy(customsHsDO);
            }
            customsHsDO.setUpdateTime(new Date());
            customsHsMapper.updateByPrimaryKeySelective(customsHsDO);
        }
        submit.setId(customsHsDO.getId());
        return customsHsDO.getId();
    }


    private CustomsHsDTO buildDTO(CustomsHsDO entity) {
        if (entity == null) {
            return null;
        } else {
            CustomsHsDTO dto = new CustomsHsDTO();
            BeanUtils.copyProperties(entity, dto);
            return dto;
        }
    }
}
