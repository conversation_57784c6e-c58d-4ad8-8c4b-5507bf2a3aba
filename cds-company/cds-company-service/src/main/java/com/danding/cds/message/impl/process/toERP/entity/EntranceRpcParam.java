package com.danding.cds.message.impl.process.toERP.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * 描述:
 * erp 上游入口参数
 *
 * <AUTHOR>
 * @date 2021/1/5 下午1:33
 */
@Data
public class EntranceRpcParam implements Serializable {

    private static final long serialVersionUID = 2204304033913558159L;

    /**
     * 业务号
     */
    private String businessNo;

    /**
     * 来源系统编码 如 OMS, QM 等
     */
    private String sourceSystemCode;

    /**
     * 目标系统编码 如 ERP,DT 等
     */
    private String targetSystemCode;

    /**
     * 方法类型 参考: 数据字典 SJZD_METHOD_TYPE
     */
    private String methodType;

    /**
     * 数据类型 DataType : 1 json , 2 xml
     */
    private Integer dateType = 1;

    /**
     * 映射的原始数据
     */
    private String sourceData;

    public EntranceRpcParam() {
    }

    private EntranceRpcParam(final String businessNo, final String sourceSystemCode, final String targetSystemCode, final String methodType, Integer dataType, final String sourceData) {
        this.businessNo = businessNo;
        this.sourceSystemCode = sourceSystemCode;
        this.targetSystemCode = targetSystemCode;
        this.methodType = methodType;
        this.dateType = dataType;
        this.sourceData = sourceData;
    }

    public static EntranceRpcParam of(final String businessNo, final String sourceSystemCode, final String targetSystemCode, final String methodType, Integer dataType, final String sourceData) {
        return new EntranceRpcParam(businessNo, sourceSystemCode, targetSystemCode, methodType, dataType, sourceData);
    }

}
