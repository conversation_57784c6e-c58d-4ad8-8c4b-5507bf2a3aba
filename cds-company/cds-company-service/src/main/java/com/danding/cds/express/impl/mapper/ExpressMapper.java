package com.danding.cds.express.impl.mapper;

import com.danding.cds.express.impl.entity.ExpressDO;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import tk.mybatis.mapper.additional.insert.InsertListMapper;
import tk.mybatis.mapper.common.Mapper;

public interface ExpressMapper extends Mapper<ExpressDO>, InsertListMapper<ExpressDO>, BatchUpdateMapper<ExpressDO>, AggregationPlusMapper<ExpressDO> {
}