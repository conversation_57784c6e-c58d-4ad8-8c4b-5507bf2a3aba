package com.danding.cds.message.impl.process.toERP.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.danding.cds.declare.ceb.domain.ceb816.TaxHeadRd;
import com.danding.cds.declare.ceb.domain.ceb816.TaxListRd;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 税金回执-回传erp ResVO  (菜鸟对接)
 */
@Data
public class TaxMsgToErpResVO {

    /**
     * 税金
     */
    @JSONField(name = "Tax")
    private ErpTax Tax;


    @Data
    public static class ErpTax implements Serializable {
        @JSONField(name = "TaxHeadRd")
        protected TaxHeadRd TaxHeadRd;
        @JSONField(name = "TaxListRd")
        protected List<TaxListRd> TaxListRd;
    }
}
