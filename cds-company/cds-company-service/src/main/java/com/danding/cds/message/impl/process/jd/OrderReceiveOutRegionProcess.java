package com.danding.cds.message.impl.process.jd;

import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.rpc.CustomsInventoryRpc;
import com.danding.cds.c.api.rpc.TrackLogEsRpc;
import com.danding.cds.common.config.OrderCCallConfig;
import com.danding.cds.common.enums.OrderInternalEnum;
import com.danding.cds.common.enums.TrackLogConstantMixAll;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.customs.inventory.api.service.CustomsInventoryService;
import com.danding.cds.log.api.service.TrackLogEsService;
import com.danding.cds.message.api.dto.MessageDTO;
import com.danding.cds.message.api.dto.MessageTaskDTO;
import com.danding.cds.message.api.enums.MessageType;
import com.danding.cds.message.api.process.OrderJdReceiveOutRegionMessage;
import com.danding.cds.message.impl.process.MessageExecuteResult;
import com.danding.cds.message.impl.process.MessageProcess;
import com.danding.cds.message.impl.process.MessageRegistry;
import com.danding.cds.v2.bean.dto.JdServProviderDTO;
import com.danding.cds.v2.bean.dto.TrackLogEsDTO;
import com.danding.cds.v2.enums.InventoryCheckOutStatusEnum;
import com.danding.cds.v2.service.JdServProviderService;
import com.jd.open.api.sdk.DefaultJdClient;
import com.jd.open.api.sdk.JdClient;
import com.jd.open.api.sdk.request.customsglobalAPI.CustomsCenterServiceSoaStorageStorageJsfServiceReceiveOutReginRequest;
import com.jd.open.api.sdk.response.customsglobalAPI.CustomsCenterServiceSoaStorageStorageJsfServiceReceiveOutReginResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
public class OrderReceiveOutRegionProcess extends MessageProcess {

    @DubboReference
    private TrackLogEsService trackLogEsService;
    @DubboReference
    private TrackLogEsRpc trackLogEsRpc;

    @DubboReference
    private CustomsInventoryService customsInventoryService;
    @DubboReference
    private CustomsInventoryRpc customsInventoryRpc;
    @Resource
    private OrderCCallConfig orderCCallConfig;


    @DubboReference
    private JdServProviderService jdServProviderService;

    public OrderReceiveOutRegionProcess(MessageRegistry registry) {
        super(registry);
    }

    @Override
    public MessageType getType() {
        return MessageType.RECEIVE_OUT_REGION_TO_JD;
    }

    @Override
    public Map<String, Object> buildSendInfo(MessageDTO messageDTO) {
        String activeData = messageDTO.getActiveData();
        OrderJdReceiveOutRegionMessage orderJdReceiveOutRegionMessage = JSON.parseObject(activeData, OrderJdReceiveOutRegionMessage.class);
        log.info("京东回传打印 - {}", JSON.toJSONString(orderJdReceiveOutRegionMessage));
        return objToMap(orderJdReceiveOutRegionMessage);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MessageExecuteResult execute(MessageTaskDTO messageTaskDTO) {
        MessageExecuteResult result = new MessageExecuteResult();
        log.info("京东消息回传 业务编码: {} ,请求数据信息: {}", messageTaskDTO.getBusinessCode(), JSON.toJSONString(messageTaskDTO));
        try {
            OrderJdReceiveOutRegionMessage orderJdReceiveOutRegionMessage = JSON.parseObject(messageTaskDTO.getRequestData(), OrderJdReceiveOutRegionMessage.class);
            CustomsInventoryDTO customsInventoryDTO = orderCCallConfig.isOrderCCall(this.getClass()) ? customsInventoryRpc.findByDeclareNo(orderJdReceiveOutRegionMessage.getOrderId(), orderJdReceiveOutRegionMessage.getCreateTime()) : customsInventoryService.findByDeclareNo(orderJdReceiveOutRegionMessage.getOrderId(), orderJdReceiveOutRegionMessage.getCreateTime());
            log.info("OrderReceiveOutRegionProcess  customsInventoryDTO={}", JSON.toJSONString(customsInventoryDTO));
            // 状态校验
            Assert.isTrue(InventoryCheckOutStatusEnum.GEN_MSG.getCode().equals(customsInventoryDTO.getCheckOutStatus()), "回传状态异常");

            JdServProviderDTO jdServProviderDTO = jdServProviderService.getServProviderByBookId(orderJdReceiveOutRegionMessage.getAccountBookId());
            if (Objects.nonNull(jdServProviderDTO)) {
                JdClient client = new DefaultJdClient(messageTaskDTO.getNotifyUrl(), jdServProviderDTO.getAccessToken(), jdServProviderDTO.getAppKey(), jdServProviderDTO.getAppSecret(), 30 * 1000, 30 * 1000);
                CustomsCenterServiceSoaStorageStorageJsfServiceReceiveOutReginRequest request = new CustomsCenterServiceSoaStorageStorageJsfServiceReceiveOutReginRequest();
                request.setCustomsId(jdServProviderDTO.getBondedAreaCode());
                request.setYunJiaoYi(false);
                request.setOrderId(customsInventoryDTO.getDeclareOrderNo());
                request.setPlatformId("1");
                request.setServiceId(jdServProviderDTO.getCode());
                request.setOperationTime(customsInventoryDTO.getCheckOutTime());
                log.info("OrderReceiveOutRegionProcess  request={}", JSON.toJSONString(request));
                CustomsCenterServiceSoaStorageStorageJsfServiceReceiveOutReginResponse response = client.execute(request);
                log.info("OrderReceiveOutRegionProcess  response={}", JSON.toJSONString(response));
                //插入日志
                TrackLogEsDTO trackLogEsDO = new TrackLogEsDTO();
                if (Objects.nonNull(response) && Objects.equals(response.getCode(), "0") && Objects.equals(response.getReturnType().getResultCode(), 1)) {
                    trackLogEsDO.setResult(TrackLogConstantMixAll.SUCCESS);
                    if (orderCCallConfig.isOrderCCall(this.getClass())) {
                        customsInventoryRpc.updateCheckoutStatusBySn(customsInventoryDTO.getSn(), InventoryCheckOutStatusEnum.REGIN_SUCCESS.getCode());
                    } else {
                        customsInventoryService.updateCheckoutStatusBySn(customsInventoryDTO.getSn(), InventoryCheckOutStatusEnum.REGIN_SUCCESS.getCode());
                    }
                    result.setSuccess(true);
                    result.setResponseMsg(JSON.toJSONString(response));

                } else {
                    trackLogEsDO.setResult(TrackLogConstantMixAll.FAIL);
                    if (orderCCallConfig.isOrderCCall(this.getClass())) {
                        customsInventoryRpc.updateCheckoutStatusBySn(customsInventoryDTO.getSn(), InventoryCheckOutStatusEnum.REGIN_FAIL.getCode());
                    } else {
                        customsInventoryService.updateCheckoutStatusBySn(customsInventoryDTO.getSn(), InventoryCheckOutStatusEnum.REGIN_FAIL.getCode());
                    }
                    result.setSuccess(false);
                    result.setResponseMsg(JSON.toJSONString(response));
                    log.info("[ 京东出区回传异常 ], 接口响应失败, 京东消息回传业务编码 : {} ,响应信息：{}", messageTaskDTO.getBusinessCode(), response);
                }
                trackLogEsDO.setOrderId(customsInventoryDTO.getOrderId());
                trackLogEsDO.setOrderSn(customsInventoryDTO.getOrderSn());
                trackLogEsDO.setDeclareOrderNo(customsInventoryDTO.getDeclareOrderNo());
                trackLogEsDO.setRequestMessage(JSON.toJSONString(request));
                trackLogEsDO.setSender(TrackLogConstantMixAll.DT_CCS);
                trackLogEsDO.setReceiver(TrackLogConstantMixAll.JD);
                trackLogEsDO.setEventDesc(TrackLogConstantMixAll.RECEIVE_OUT_REGIN);
                trackLogEsDO.setEventTime(customsInventoryDTO.getCheckOutTime());
                trackLogEsDO.setOperator(UserUtils.getUserRealName());
                trackLogEsDO.setInternalStatus(OrderInternalEnum.INVENTORY_DECLARE_SUCCESS.getCode());
                trackLogEsDO.setCustomsReceipt(JSON.toJSONString(response));
                if (orderCCallConfig.isOrderCCall(this.getClass())) {
                    trackLogEsRpc.submit(trackLogEsDO);
                } else {
                    trackLogEsService.submit(trackLogEsDO);
                }
            }
        } catch (Exception e) {
            result.setSuccess(false);
            result.setResponseMsg("CCS内部系统异常，" + e.getMessage());
            log.error("[ 京东出区回传异常 ], 系统异常, 京东消息回传业务编码 : {} ,消息回调处理异常：{}", messageTaskDTO.getBusinessCode(), e.getMessage(), e);
        }
        return result;
    }
}



