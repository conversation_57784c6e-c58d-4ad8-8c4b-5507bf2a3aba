package com.danding.cds.company.impl.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.cds.bean.dto.BaseDataSyncTypeEnums;
import com.danding.cds.bean.dto.CrossBorderDataExchangeNodeDTO;
import com.danding.cds.bean.dto.DeclareConfig;
import com.danding.cds.bean.dto.DeclareWayDTO;
import com.danding.cds.bean.vo.req.CompanyDeclareConfigReqVo;
import com.danding.cds.common.enums.CustomsTransferNodeEnums;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.common.utils.JSONUtils;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.common.utils.ValidatorUtils;
import com.danding.cds.company.api.dto.*;
import com.danding.cds.company.api.enmus.CompanyQualifyEnum;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.company.api.vo.CompanyDeclareConfigResVo;
import com.danding.cds.company.api.vo.CompanyResVo;
import com.danding.cds.company.api.vo.CrossBorderDataExchangeNodeResVO;
import com.danding.cds.company.impl.entity.CompanyDO;
import com.danding.cds.company.impl.mapper.CompanyMapper;
import com.danding.cds.customs.district.api.enums.CustomsDistrictEnum;
import com.danding.cds.service.BaseDataSyncService;
import com.danding.cds.service.base.DeclareWayBaseService;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.common.response.SelectItemVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.ibatis.exceptions.TooManyResultsException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import javax.validation.Validator;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Auther: Dante-Gxj
 * @Date: 2020/4/26 16:00
 * @Description:
 */
@Slf4j
@DubboService
public class CompanyServiceImpl implements CompanyService {

    @Autowired
    private CompanyMapper companyMapper;

    @Autowired
    private Validator validator;

    @Autowired
    private BaseDataSyncService baseDataSyncService;

    @Autowired
    private DeclareWayBaseService declareWayBaseService;

    /**
     * 根据id获取企业详情
     *
     * @param id
     * @return
     */
    @Override
    public CompanySubmit detailById(Long id) {
        if (Objects.isNull(id)) {
            log.error("detailById id为空");
            throw new ArgsInvalidException("id为空");
        }
        CompanyDTO companyDTO = this.findById(id);
        if (Objects.isNull(companyDTO)) {
            log.error("detailById companyDTO为空 id={}", id);
            throw new ArgsInvalidException("根据id:" + id + "未查询到企业详情");
        }
        CompanySubmit submit = new CompanySubmit();
        submit.setId(companyDTO.getId());
        submit.setName(companyDTO.getName());
        submit.setCode(companyDTO.getCode());
        submit.setQualifyList(JSON.parseArray(companyDTO.getQualifyJson(), String.class));
        if (!StringUtils.isEmpty(companyDTO.getUniformSocialCreditCode())) {
            submit.setUniformSocialCreditCode(companyDTO.getUniformSocialCreditCode());
        }
        List<CompanySubmitDistrict> districts = this.getDistricts(companyDTO);
        submit.setDistrictList(districts);
        String crossBorderDataExchangeNode = companyDTO.getCrossBorderDataExchangeNode();
        if (!StringUtils.isEmpty(crossBorderDataExchangeNode)) {
            List<CrossBorderDataExchangeNodeDTO> crossBorderDataExchangeNodeDTOS = JSON.parseArray(crossBorderDataExchangeNode, CrossBorderDataExchangeNodeDTO.class);
            submit.setCrossBorderDataExchangeNodeList(crossBorderDataExchangeNodeDTOS);
        }
        String nonCrossBorderDataExchangeNode = companyDTO.getNonCrossBorderDataExchangeNode();
        if (!StringUtils.isEmpty(nonCrossBorderDataExchangeNode)) {
            List<CrossBorderDataExchangeNodeDTO> nonCrossBorderDataExchangeNodeDTOS = JSON.parseArray(nonCrossBorderDataExchangeNode, CrossBorderDataExchangeNodeDTO.class);
            submit.setNonCrossBorderDataExchangeNodeList(nonCrossBorderDataExchangeNodeDTOS);
        }
        JSONObject extra = JSON.parseObject(companyDTO.getExtraJson());
        submit.setDxpId(extra.getString("dxpId"));
        submit.setOrderDxpId(extra.getString("orderDxpId"));
        submit.setAccountDxpId(extra.getString("accountDxpId"));
        submit.setEnable(companyDTO.getEnable());
        submit.setRemark(companyDTO.getRemark());
        if (!StringUtils.isEmpty(companyDTO.getGuaranteeCustoms())) {
            List<String> guaranteeCustomsList = JSON.parseArray(companyDTO.getGuaranteeCustoms(), String.class);
            submit.setGuaranteeCustoms(guaranteeCustomsList);
        }
        submit.setShipperAndConsigneeCode(companyDTO.getShipperAndConsigneeCode());
        submit.setCustomsCompanyCode(companyDTO.getCustomsCompanyCode());
        submit.setUnifiedCrossBroderCode(companyDTO.getUnifiedCrossBroderCode());
        submit.setSpecialClientCode(companyDTO.getSpecialClientCode());
        submit.setFbCode(companyDTO.getFbCode());

        return submit;
    }

    /**
     * 获取关区信息
     *
     * @param companyDTO
     */
    private List<CompanySubmitDistrict> getDistricts(CompanyDTO companyDTO) {
        List<CompanySubmitDistrict> districts = new ArrayList<>();
        if (Objects.nonNull(companyDTO.getDistrictJson())) {
            String districtJson = companyDTO.getDistrictJson();
            List<CompanyDistrictDTO> companyDistrictDTOS = JSON.parseArray(districtJson, CompanyDistrictDTO.class);
            for (CompanyDistrictDTO districtDTO : companyDistrictDTOS) {
                CompanySubmitDistrict district = ConvertUtil.beanConvert(districtDTO, CompanySubmitDistrict.class);
                JSONObject extra = JSON.parseObject(districtDTO.getExtraJson());
                district.setCiqSender(extra.getString("ciqSender"));
                district.setCiqSenderAddress(extra.getString("ciqSenderAddress"));
                district.setCiqSenderMobile(extra.getString("ciqSenderMobile"));
                district.setCiqCode(extra.getString("ciqCode"));
                district.setCustomsName(CustomsDistrictEnum.getEnum(districtDTO.getCustoms()).getDesc());
                districts.add(district);
            }
        }
        return districts;
    }

    private static final Pattern DXP_PATTERN = Pattern.compile("^[A-Z]{6}\\d{10}$");

    @Override
//    @Caching(evict = {
//            @CacheEvict(value = "CompanyWithQualify", allEntries = true)
//            , @CacheEvict(value = "CompanyWithCode", key = "#submit.getCode()")
//            , @CacheEvict(value = "CompanyWithId", key = "#submit.getId()")
//    })
    public Long upset(CompanySubmit submit) throws ArgsErrorException {
        String inputError = ValidatorUtils.doValidator(validator, submit);
        if (null != inputError) {
            throw new ArgsErrorException(inputError);
        }
        this.checkUniqueCode(submit);
        CompanyDTO old;
//        if (old != null && (submit.getId() == null || submit.getId() == 0 || !submit.getId().equals(old.getId()))) {
//            throw new ArgsErrorException("海关总署备案编码" + submit.getCode() + "已存在");
//        }
//        if (submit.getCode().length() != 9 && submit.getCode().length() != 10) {
//            throw new ArgsErrorException("海关编码为9或10位，请检查编码是否有误");// （含大写字母、数字）
//        }
        if (submit.getQualifyList().contains(CompanyQualifyEnum.BWCSQY.getCode())) {
            List<CrossBorderDataExchangeNodeDTO> crossBorderDataExchangeNodeList = submit.getCrossBorderDataExchangeNodeList();
            if (CollectionUtils.isEmpty(crossBorderDataExchangeNodeList)) {
                throw new ArgsErrorException("跨境数据交换申请传输节点配置不能为空");
            }
            String duplicateDxp = crossBorderDataExchangeNodeList.stream().map(CrossBorderDataExchangeNodeDTO::getDxpId)
                    .collect(Collectors.toMap(e -> e, e -> 1, (v1, v2) -> v1 + v2)).entrySet()
                    .stream().filter(entry -> entry.getValue() > 1).map(entry -> entry.getKey()).collect(Collectors.joining("/"));
            if (!StringUtils.isEmpty(duplicateDxp)) {
                throw new ArgsErrorException(duplicateDxp + "存在多条数据");
            }
            crossBorderDataExchangeNodeList.stream().forEach(c -> {
                Matcher matcher = DXP_PATTERN.matcher(c.getDxpId());
                if (!matcher.find()) {
                    throw new ArgsErrorException(c.getDxpId() + "不满足校验规则(6位大写字母+10位数字)");
                }
//                if (StringUtils.isEmpty(c.getConfig())) {
//                    throw new ArgsErrorException(c.getDxpId() + "未进行申报配置");
//                }
            });
        }
        if (submit.getQualifyList().contains(CompanyQualifyEnum.SBQY.getCode())) {
            List<CrossBorderDataExchangeNodeDTO> nonCrossBorderDataExchangeNodeList = submit.getNonCrossBorderDataExchangeNodeList();
            if (StringUtils.isEmpty(nonCrossBorderDataExchangeNodeList)) {
                throw new ArgsErrorException("非跨境数据交换申请传输节点配置不能为空");
            }
            String duplicateDxp = nonCrossBorderDataExchangeNodeList.stream().map(CrossBorderDataExchangeNodeDTO::getDxpId)
                    .collect(Collectors.toMap(e -> e, e -> 1, (v1, v2) -> v1 + v2)).entrySet()
                    .stream().filter(entry -> entry.getValue() > 1).map(entry -> entry.getKey()).collect(Collectors.joining("/"));
            if (!StringUtils.isEmpty(duplicateDxp)) {
                throw new ArgsErrorException(duplicateDxp + "存在多条数据");
            }
            nonCrossBorderDataExchangeNodeList.stream().forEach(c -> {
                Matcher matcher = DXP_PATTERN.matcher(c.getDxpId());
                if (!matcher.find()) {
                    throw new ArgsErrorException(c.getDxpId() + "不满足校验规则(6位大写字母+10位数字)");
                }
            });
        }
        List<CompanySubmitDistrict> listCompanySubmitDistrict = submit.getDistrictList();
        if (CustomsDistrictEnum.checkCustomCompanyDistrictRepeat(listCompanySubmitDistrict)) {
            throw new ArgsErrorException("海关地方备案编码已存在");
        }
        for (CompanySubmitDistrict companySubmitDistrict : submit.getDistrictList()) {
            if (StringUtils.isEmpty(companySubmitDistrict.getCode())) {
                throw new ArgsErrorException("地方关区备案编码不能为空");
            }
            if (StringUtils.isEmpty(companySubmitDistrict.getName())) {
                throw new ArgsErrorException("地方关区备案名称不能为空");
            }
            if (StringUtils.isEmpty(companySubmitDistrict.getCustoms()) || CustomsDistrictEnum.NULL.equals(CustomsDistrictEnum.getEnum(companySubmitDistrict.getCustoms()))) {
                throw new ArgsErrorException("地方关区口岸不能为空");
            }
//            if (companySubmitDistrict.getCustoms().equals(CustomsDistrictEnum.TIANJIN.getCode())){
//                if (StringUtils.isEmpty(companySubmitDistrict.getCiqSender())){
//                    throw new ArgsErrorException("天津关区检验检疫发件人不能为空");
//                }
//                if (StringUtils.isEmpty(companySubmitDistrict.getCiqSenderAddress())){
//                    throw new ArgsErrorException("天津关区检验检疫发件地址不能为空");
//                }
//                if (StringUtils.isEmpty(companySubmitDistrict.getCiqSenderMobile())){
//                    throw new ArgsErrorException("天津关区检验检疫发件人手机号不能为空");
//                }
//                if (StringUtils.isEmpty(companySubmitDistrict.getCiqCode())){
//                    throw new ArgsErrorException("天津关区检验检疫编码不能为空");
//                }
//            }
        }
        CompanyDO companyDO = ConvertUtil.beanConvert(submit, CompanyDO.class);
        companyDO.setQualifyJson(JSON.toJSONString(submit.getQualifyList())); // 资质
        List<CompanyDistrictDTO> districtDTOList = new ArrayList<>(); // 关区
        for (CompanySubmitDistrict companySubmitDistrict : submit.getDistrictList()) {
            CompanyDistrictDTO companyDistrictDTO = new CompanyDistrictDTO();
            BeanUtils.copyProperties(companySubmitDistrict, companyDistrictDTO);
            Map<String, String> distractExtra = new HashMap<>();
            distractExtra.put("ciqSender", companySubmitDistrict.getCiqSender());
            distractExtra.put("ciqSenderAddress", companySubmitDistrict.getCiqSenderAddress());
            distractExtra.put("ciqSenderMobile", companySubmitDistrict.getCiqSenderMobile());
            distractExtra.put("ciqCode", companySubmitDistrict.getCiqCode());
            companyDistrictDTO.setExtraJson(JSON.toJSONString(distractExtra));
            districtDTOList.add(companyDistrictDTO);
        }
        companyDO.setDistrictJson(JSON.toJSONString(districtDTOList));
        if (CollectionUtils.isEmpty(submit.getCrossBorderDataExchangeNodeList())) {
            companyDO.setCrossBorderDataExchangeNode(null);
        } else {
            companyDO.setCrossBorderDataExchangeNode(JSON.toJSONString(submit.getCrossBorderDataExchangeNodeList()));
        }
        if (CollectionUtils.isEmpty(submit.getNonCrossBorderDataExchangeNodeList())) {
            companyDO.setNonCrossBorderDataExchangeNode(null);
        } else {
            companyDO.setNonCrossBorderDataExchangeNode(JSON.toJSONString(submit.getNonCrossBorderDataExchangeNodeList()));
        }
        Map<String, String> extra = new HashMap<>(); // 额外信息
        extra.put("dxpId", submit.getDxpId());
        extra.put("orderDxpId", submit.getOrderDxpId());
        extra.put("accountDxpId", submit.getAccountDxpId());
        companyDO.setExtraJson(JSON.toJSONString(extra));
        if (!CollectionUtils.isEmpty(submit.getGuaranteeCustoms())) {
            companyDO.setGuaranteeCustoms(JSON.toJSONString(submit.getGuaranteeCustoms()));
        } else {
            companyDO.setGuaranteeCustoms(null);
        }
        // Step::提交持久化
        if (submit.getId() == null || submit.getId() == 0) {
            companyDO.setId(null);
            if (submit.getEnable() != null) {
                submit.setEnable(1);
            }
            companyDO.setCode(submit.getUnifiedCrossBroderCode());
            UserUtils.setCreateAndUpdateBy(companyDO);
            companyMapper.insertSelective(companyDO);

            baseDataSyncService.syncDataMQSend(BaseDataSyncTypeEnums.COMPANY, BaseDataSyncTypeEnums.INSERT, companyDO);
        } else {
            old = this.findById(submit.getId());
            if (old == null) {
                throw new ArgsErrorException("ID不正确");
            }
            if ((submit.getEnable() != null && submit.getEnable() == 0) || old.getEnable() == 0) {
                if (StrUtil.isNotBlank(old.getExtraJson())) {
                    Map map = JSON.parseObject(old.getExtraJson(), Map.class);
                    map.putAll(extra);
                    companyDO.setExtraJson(JSON.toJSONString(map));
                }
                companyDO.setUpdateTime(new Date());
                if (!Objects.equals(UserUtils.getUserId(), 0)) {
                    UserUtils.setUpdateBy(companyDO);
                }
                companyMapper.updateByPrimaryKeySelective(companyDO);
            } else {
                throw new ArgsErrorException("企业未禁用，不可编辑");
            }
            CompanyDO syncData = companyMapper.selectByPrimaryKey(companyDO.getId());
            baseDataSyncService.syncDataMQSend(BaseDataSyncTypeEnums.COMPANY, BaseDataSyncTypeEnums.UPDATE, syncData);
        }
        submit.setId(companyDO.getId());
        return companyDO.getId();
    }

    /**
     * 检查配置的code的唯一性
     *
     * @param submit
     */
    private void checkUniqueCode(CompanySubmit submit) {
        String shipperAndConsigneeCode = submit.getShipperAndConsigneeCode();
        String customsCompanyCode = submit.getCustomsCompanyCode();
        String unifiedCrossBroderCode = submit.getUnifiedCrossBroderCode();
        String specialClientCode = submit.getSpecialClientCode();
        String fbCode = submit.getFbCode();
        if (Objects.isNull(shipperAndConsigneeCode)) {
            throw new ArgsErrorException("进出口收发货人编码不能为空");
        }
        if (Objects.isNull(customsCompanyCode)) {
            throw new ArgsErrorException("报关企业编码不能为空");
        }
        if (Objects.isNull(unifiedCrossBroderCode)) {
            throw new ArgsErrorException("跨境进口统一编码编码不能为空");
        }
        if (Objects.isNull(specialClientCode)) {
            throw new ArgsErrorException("保税物流管理系统编码不能为空");
        }
        if (Objects.isNull(fbCode)) {
            throw new ArgsErrorException("分类监管编码不能为空");
        }
        if (unifiedCrossBroderCode.length() != 9 && unifiedCrossBroderCode.length() != 10) {
            throw new ArgsErrorException("跨境进口统一编码:" + unifiedCrossBroderCode + "海关编码为9或10位，请检查编码是否有误");// （含大写字母、数字）
        }
        if (specialClientCode.length() != 9 && specialClientCode.length() != 10) {
            throw new ArgsErrorException("保税物流管理系统编码:" + specialClientCode + "海关编码为9或10位，请检查编码是否有误");// （含大写字母、数字）
        }
        if (fbCode.length() != 9 && fbCode.length() != 10) {
            throw new ArgsErrorException("分类监管编码:" + fbCode + "海关编码为9或10位，请检查编码是否有误");// （含大写字母、数字）
        }
        List<String> codeList = new ArrayList<>();
        codeList.add(unifiedCrossBroderCode);
        codeList.add(specialClientCode);
        codeList.add(fbCode);
        codeList = codeList.stream().distinct().collect(Collectors.toList());
        Example example = new Example(CompanyDO.class);
        Example.Criteria criteria = example.createCriteria();
        if (Objects.nonNull(submit.getId())) {
            criteria.andNotEqualTo("id", submit.getId());
        }
        criteria.andEqualTo("deleted", false);
        Example.Criteria codeCriteria = example.createCriteria();
        codeCriteria.orIn("unifiedCrossBroderCode", codeList)
                .orIn("specialClientCode", codeList)
                .orIn("fbCode", codeList);
        example.and(codeCriteria);
        List<CompanyDO> companyDOList = companyMapper.selectByExample(example);
        if (!CollectionUtils.isEmpty(companyDOList)) {
            CompanyDO companyDO = companyDOList.get(0);
            String existUnifiedCrossBroderCode = companyDO.getUnifiedCrossBroderCode();
            String existSpecialClientCode = companyDO.getSpecialClientCode();
            String existFbCode = companyDO.getFbCode();
            if (codeList.contains(existUnifiedCrossBroderCode)) {
                throw new ArgsInvalidException("code:" + existUnifiedCrossBroderCode + "已在企业:" + companyDO.getName() + "中配置");
            } else if (companyDOList.contains(existSpecialClientCode)) {
                throw new ArgsInvalidException("code:" + existSpecialClientCode + "已在企业:" + companyDO.getName() + "中配置");
            } else {
                throw new ArgsInvalidException("code:" + existFbCode + "已在企业:" + companyDO.getName() + "中配置");
            }
        }
    }

    @Override
//    @Caching(evict={
//            @CacheEvict(value = "CompanyWithQualify",allEntries = true)
//            ,@CacheEvict(value = "CompanyWithCode",key = "#result.getCode()")
//            ,@CacheEvict(value = "CompanyWithId",key = "#result.getId()")
//    })
    public CompanyDTO updateEnable(Long id, Boolean enable) {
        CompanyDO condition = new CompanyDO();
        condition.setId(id);
        condition.setEnable(enable ? 1 : 0);
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(condition);
        }
        condition.setUpdateTime(new Date());
        companyMapper.updateByPrimaryKeySelective(condition);

        CompanyDO syncData = companyMapper.selectByPrimaryKey(condition.getId());
        baseDataSyncService.syncDataMQSend(BaseDataSyncTypeEnums.COMPANY, BaseDataSyncTypeEnums.UPDATE, syncData);
        return this.findById(id);
    }

    @Override
    @PageSelect
    public ListVO<CompanyDTO> paging(CompanySearch search) {
        Example example = new Example(CompanyDO.class);
        example.orderBy("createTime").desc();
        Example.Criteria criteria = example.createCriteria();
        if (!LongUtil.isNone(search.getId())) {
            criteria.andEqualTo("id", search.getId());
        }
        if (!StringUtils.isEmpty(search.getName())) {
            criteria.andEqualTo("name", search.getName());
        }
        if (!StringUtils.isEmpty(search.getLikeName())) {
            criteria.andLike("name", "%" + search.getLikeName() + "%");
        }
        if (!StringUtils.isEmpty(search.getCode())) {
            criteria.orEqualTo("shipperAndConsigneeCode", search.getCode()).orEqualTo("customsCompanyCode", search.getCode());
        }
        if (!StringUtils.isEmpty(search.getLikeCode())) {
            criteria.andLike("code", "%" + search.getLikeCode() + "%");
        }
        if (!StringUtils.isEmpty(search.getQualify())) {
            criteria.andLike("qualifyJson", "%" + search.getQualify() + "%");
        }
        if (search.getEnable() != null) {
            criteria.andEqualTo("enable", search.getEnable());
        }
        List<CompanyDO> list = companyMapper.selectByExample(example);
        ListVO<CompanyDTO> result = new ListVO<>();
        result.setDataList(JSON.parseArray(JSONUtils.toJSONString(list), CompanyDTO.class));
        // 分页
        PageInfo<CompanyDO> pageInfo = new PageInfo(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    @Override
//    @Cacheable(value = "CompanyWithId" ,key = "#id",unless = "#result==null")
    public CompanyDTO findById(long id) {
        if (id == 0) {
            return null;
        } else {
            CompanyDO companyDO = companyMapper.selectByPrimaryKey(id);
            if (companyDO == null) {
                return null;
            } else {
                CompanyDTO companyDTO = new CompanyDTO();
                BeanUtils.copyProperties(companyDO, companyDTO);
                return companyDTO;
            }
        }
    }

    @Override
    public CompanyDTO findUnifiedCrossInfoById(long id) {
        CompanyDTO companyDTO = this.findById(id);
        if (Objects.nonNull(companyDTO)) {
            if (Objects.nonNull(companyDTO.getUnifiedCrossBroderCode())) {
                companyDTO.setCode(companyDTO.getUnifiedCrossBroderCode());
            }
        }
        return companyDTO;
    }

    @Override
    public CompanyDTO findSpecialClientInfoById(long id) {
        CompanyDTO companyDTO = this.findById(id);
        if (Objects.nonNull(companyDTO)) {
            if (Objects.nonNull(companyDTO.getSpecialClientCode())) {
                companyDTO.setCode(companyDTO.getSpecialClientCode());
            }
        }
        return companyDTO;
    }

    @Override
    public List<CompanyDTO> findById(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        Example example = new Example(CompanyDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("id", ids);
        List<CompanyDO> companyDOS = companyMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(companyDOS)) {
            return new ArrayList<>();
        }
        return ConvertUtil.listConvert(companyDOS, CompanyDTO.class);
    }

    @Override
    public String viewDefaultDeclareWay(Long id, String type) {
        DeclareWayDTO declareWayDTO = this.viewDefaultDeclareWayDTO(id, type);
        if (Objects.isNull(declareWayDTO)) {
            return null;
        }
        return declareWayDTO.getName();
    }

    @Override
    public List<CrossBorderDataExchangeNodeResVO> listDxpIdById(Long id) {
        CompanyResVo companyResVo = this.findByIdV2(id);
        List<CrossBorderDataExchangeNodeResVO> crossBorderDataExchangeNodeList = companyResVo.getCrossBorderDataExchangeNodeList();
        if (!CollectionUtils.isEmpty(crossBorderDataExchangeNodeList)) {
            crossBorderDataExchangeNodeList = crossBorderDataExchangeNodeList.stream().sorted(Comparator.comparing(CrossBorderDataExchangeNodeResVO::getNodeCode)).collect(Collectors.toList());
        }
        return crossBorderDataExchangeNodeList;
    }

    @Override
    public CrossBorderDataExchangeNodeResVO getNodeByDxpAndId(Long id, String dxpId) {
        CompanyResVo companyResVo = this.findByIdV2(id);
        List<CrossBorderDataExchangeNodeResVO> crossBorderDataExchangeNodeList = companyResVo.getCrossBorderDataExchangeNodeList();
        CrossBorderDataExchangeNodeResVO crossBorderDataExchangeNodeResVO = crossBorderDataExchangeNodeList.stream().filter(c -> Objects.equals(c.getDxpId(), dxpId)).findFirst().orElse(null);
        return crossBorderDataExchangeNodeResVO;
    }


    @Override
    public DeclareWayDTO viewDefaultDeclareWayDTO(Long id, String type) {
        if (Objects.isNull(id) || Objects.isNull(type)) {
            return null;
        }
        CompanyDO companyDO = companyMapper.selectByPrimaryKey(id);
        if (Objects.isNull(companyDO) || Objects.isNull(companyDO.getDeclareConfig())) {
            return null;
        } else {
            DeclareWayDTO declareWayDTO = this.getDeclareWayDTOByConfig(type, companyDO.getDeclareConfig());
            if (declareWayDTO != null) {
                return declareWayDTO;
            }
        }
        return null;
    }

    /**
     * 根据declareConfig获得申报方式
     *
     * @param type
     * @param config
     * @return
     */
    private DeclareWayDTO getDeclareWayDTOByConfig(String type, String config) {
        List<DeclareConfig> declareConfigs = JSON.parseArray(config, DeclareConfig.class);
        DeclareConfig declareConfig = declareConfigs.stream().filter(d -> Objects.equals(type, d.getType())).filter(d -> Objects.nonNull(d.getDeclareCode())).findFirst().orElse(null);
        if (Objects.nonNull(declareConfig)) {
            String declareCode = declareConfig.getDeclareCode();
            DeclareWayDTO declareWayDTO = declareWayBaseService.findByCode(declareCode);
            if (Objects.nonNull(declareWayDTO)) {
                return declareWayDTO;
            }
        }
        return null;
    }

    @Override
    public DeclareWayDTO viewDeclareWayByDxpId(Long companyId, String dxpId, String type) {
        log.info("viewDeclareWayByDxpId companyId={} dxpId={} type={}", companyId, dxpId, type);
        if (Objects.isNull(companyId) || Objects.isNull(dxpId) || Objects.isNull(type)) {
            return null;
        }
        CompanyDO companyDO = companyMapper.selectByPrimaryKey(companyId);
        log.info("viewDeclareWayByDxpId companyDO={}", JSON.toJSONString(companyDO));
        if (Objects.isNull(companyDO)) {
            return null;
        }
        String crossBorderDataExchangeNode = companyDO.getCrossBorderDataExchangeNode();
        if (Objects.isNull(crossBorderDataExchangeNode)) {
            if (Objects.nonNull(companyDO.getDeclareConfig())) {
                DeclareWayDTO declareWayDTO = getDeclareWayDTOByConfig(type, companyDO.getDeclareConfig());
                if (Objects.nonNull(declareWayDTO)) {
                    return declareWayDTO;
                }
            }
            return null;
        } else {
            List<CrossBorderDataExchangeNodeDTO> crossBorderDataExchangeNodeDTOS = JSON.parseArray(crossBorderDataExchangeNode, CrossBorderDataExchangeNodeDTO.class);
            CrossBorderDataExchangeNodeDTO crossBorderDataExchangeNodeDTO = crossBorderDataExchangeNodeDTOS.stream().filter(c -> Objects.equals(c.getDxpId(), dxpId)).findFirst().orElse(null);
            if (Objects.isNull(crossBorderDataExchangeNodeDTO)) {
                return null;
            }
            String config = crossBorderDataExchangeNodeDTO.getConfig();
            DeclareWayDTO declareWayDTO = this.getDeclareWayDTOByConfig(type, config);
            if (Objects.isNull(declareWayDTO)) {
                return null;
            }
            declareWayDTO.setCustomsTransferNode(crossBorderDataExchangeNodeDTO.getNodeCode());
            return declareWayDTO;
        }
    }

    @Override
    public CompanyDTO findByName(String companyName) {
        CompanyDO companyDO = new CompanyDO();
        companyDO.setName(companyName);
        try {
            CompanyDO company = companyMapper.selectOne(companyDO);
            return ConvertUtil.beanConvert(company, CompanyDTO.class);
        } catch (TooManyResultsException tmr) {
            log.info("根据name:{} 查询到多条记录", companyName);
            return null;
        }
    }

    @Override
    public CompanyDTO findByUnifiedCrossBroderCode(String code) {
        CompanyDO companyDO = new CompanyDO();
        companyDO.setUnifiedCrossBroderCode(code);
        try {
            CompanyDO company = companyMapper.selectOne(companyDO);
            return ConvertUtil.beanConvert(company, CompanyDTO.class);
        } catch (TooManyResultsException tmr) {
            log.info("根据code:{} 查询到多条记录", code);
            return null;
        }
    }

    @Override
    public List<CompanyDTO> findByUnifiedCrossBroderCodes(List<String> code) {
        if (CollectionUtils.isEmpty(code)) {
            return new ArrayList<>();
        }
        Example example = new Example(CompanyDO.class);
        example.createCriteria().andIn("unifiedCrossBroderCode", code);
        List<CompanyDO> company = companyMapper.selectByExample(example);
        return ConvertUtil.listConvert(company, CompanyDTO.class);
    }

    @Override
    public CompanyDTO findBySpecialClientCode(String code) {
        CompanyDO companyDO = new CompanyDO();
        companyDO.setSpecialClientCode(code);
        try {
            CompanyDO company = companyMapper.selectOne(companyDO);
            return ConvertUtil.beanConvert(company, CompanyDTO.class);
        } catch (TooManyResultsException tmr) {
            log.info("根据code:{} 查询到多条记录", code);
            return null;
        }
    }

    @Override
    public CompanyDTO findByFbCode(String code) {
        CompanyDO companyDO = new CompanyDO();
        companyDO.setFbCode(code);
        try {
            CompanyDO company = companyMapper.selectOne(companyDO);
            return ConvertUtil.beanConvert(company, CompanyDTO.class);
        } catch (TooManyResultsException tmr) {
            log.info("根据code:{} 查询到多条记录", code);
            return null;
        }
    }

    @Override
    public List<SelectItemVO> listAllUsedCode() {
        Example example = new Example(CompanyDO.class);
        example.createCriteria().andEqualTo("deleted", false);
        List<CompanyDO> dataList = companyMapper.selectByExample(example);
        List<SelectItemVO> result = new ArrayList<>();
        for (CompanyDO companyDO : dataList) {
            SelectItemVO optionDTO = new SelectItemVO();
            optionDTO.setValue(companyDO.getCode());
            optionDTO.setName(companyDO.getCode());
            result.add(optionDTO);
        }
        return result.stream().distinct().filter(r -> !Objects.equals(r.getValue(), "")).collect(Collectors.toList());
    }

    @Override
    public CompanyResVo findByIdV2(Long id) {

        if (id == 0) {
            return null;
        } else {
            CompanyDO companyDO = companyMapper.selectByPrimaryKey(id);
            if (companyDO == null) {
                return null;
            } else {
                CompanyResVo companyResVo = new CompanyResVo();
                BeanUtils.copyProperties(companyDO, companyResVo);
                // 申报配置
                String declareConfig = companyDO.getDeclareConfig();
                if (!StringUtils.isEmpty(declareConfig)) {
                    companyResVo.setDeclareConfigResList(this.getDeclareConfigRes(declareConfig));
                }
                String crossBorderDataExchangeNode = companyDO.getCrossBorderDataExchangeNode();
                if (!StringUtils.isEmpty(crossBorderDataExchangeNode)) {
                    List<CrossBorderDataExchangeNodeResVO> crossBorderDataExchangeNodeResVO = this.getCrossBorderDataExchangeNodeResVO(crossBorderDataExchangeNode);
                    companyResVo.setCrossBorderDataExchangeNodeList(crossBorderDataExchangeNodeResVO);
                }
                String nonCrossBorderDataExchangeNode = companyDO.getNonCrossBorderDataExchangeNode();
                if (!StringUtils.isEmpty(nonCrossBorderDataExchangeNode)) {
                    List<CrossBorderDataExchangeNodeResVO> nonCrossBorderDataExchangeNodeResVO = this.getCrossBorderDataExchangeNodeResVO(nonCrossBorderDataExchangeNode);
                    companyResVo.setNonCrossBorderDataExchangeNodeList(nonCrossBorderDataExchangeNodeResVO);
                }
                return companyResVo;
            }
        }
    }

    @Override
    public CompanyResVo findUnifiedCrossInfoByIdV2(Long id) {
        CompanyResVo companyResVo = this.findByIdV2(id);
        if (Objects.nonNull(companyResVo)) {
            if (Objects.nonNull(companyResVo.getUnifiedCrossBroderCode())) {
                companyResVo.setCode(companyResVo.getUnifiedCrossBroderCode());
            }
        }
        return companyResVo;
    }

    private List<CompanyDeclareConfigResVo> getDeclareConfigRes(String declareConfig) {
        if (StringUtils.isEmpty(declareConfig)) {
            return new ArrayList<>();
        }
        List<CompanyDeclareConfigDto> declareConfigDtoList = JSON.parseArray(declareConfig, CompanyDeclareConfigDto.class);
        List<String> declareCodeList = declareConfigDtoList.stream().map(CompanyDeclareConfigDto::getDeclareCode).collect(Collectors.toList());
        List<DeclareWayDTO> declareWayDtoList = declareWayBaseService.findByCodes(declareCodeList);
        Map<String, DeclareWayDTO> declareWayDtoMap = declareWayDtoList.stream()
                .collect(Collectors.toMap(DeclareWayDTO::getCode, Function.identity(), (v1, v2) -> v1));
        return declareConfigDtoList.stream()
                .map(z -> {
                    CompanyDeclareConfigResVo declareConfigResVo = new CompanyDeclareConfigResVo();
                    declareConfigResVo.setDeclareCode(z.getDeclareCode());
                    DeclareWayDTO declareWayDto = declareWayDtoMap.get(z.getDeclareCode());
                    if (declareWayDto != null) {
                        declareConfigResVo.setDeclareImpl(declareWayDto.getDeclareImpl());
                    }
                    declareConfigResVo.setType(z.getType());
                    return declareConfigResVo;
                }).collect(Collectors.toList());
    }

    /**
     * 获取跨境/非跨境 申请传输节点
     *
     * @param exchangeNode
     * @return
     */
    private List<CrossBorderDataExchangeNodeResVO> getCrossBorderDataExchangeNodeResVO(String exchangeNode) {
        List<CrossBorderDataExchangeNodeDTO> crossBorderDataExchangeNodeDTOS = JSON.parseArray(exchangeNode, CrossBorderDataExchangeNodeDTO.class);
        List<CrossBorderDataExchangeNodeResVO> result = crossBorderDataExchangeNodeDTOS.stream().map(c -> {
            CrossBorderDataExchangeNodeResVO resVO = new CrossBorderDataExchangeNodeResVO();
            resVO.setDxpId(c.getDxpId());
            resVO.setDeclareQueue(c.getDeclareQueue());
            CustomsTransferNodeEnums transferNodeEnums = CustomsTransferNodeEnums.getEnums(c.getNodeCode());
            resVO.setNodeCode(transferNodeEnums.getCode());
            resVO.setNodeName(transferNodeEnums.getName());
            List<CompanyDeclareConfigResVo> declareConfigRes = this.getDeclareConfigRes(c.getConfig());
            resVO.setConfig(declareConfigRes);
            return resVO;
        }).collect(Collectors.toList());
        return result;
    }

    @Override
    //@Cacheable(value = "CompanyWithCode" ,key = "#code",unless = "#result==null")
    @Deprecated
    public CompanyDTO findByCode(String code) {
        CompanyDO condition = new CompanyDO();
        condition.setCode(code);
        CompanyDO companyDO = companyMapper.selectOne(condition);
        if (companyDO == null) {
            return null;
        } else {
            return ConvertUtil.beanConvert(companyDO, CompanyDTO.class);
        }
    }


    @Override
    //@Cacheable(value = "CompanyWithQualify" ,key = "#qualify",unless = "#result==null")
    public List<CompanyDTO> listWithQualify(String qualify) {
        CompanySearch search = new CompanySearch();
        search.setQualify(qualify);
        search.setPageIgnore(1);
        search.setEnable(1);
        ListVO<CompanyDTO> paging = this.paging(search);
        List<CompanyDTO> result = paging.getDataList();
        return result;
    }

    @Override
    public List<CompanyDTO> listWithQualifyAll(String qualify) {
        CompanySearch search = new CompanySearch();
        search.setQualify(qualify);
        search.setPageIgnore(1);
        ListVO<CompanyDTO> paging = this.paging(search);
        List<CompanyDTO> result = paging.getDataList();
        return result;
    }

    @Override
    public List<CompanyDTO> listAllLikeQuery(String code, String companyName) {
        CompanySearch search = new CompanySearch();
        search.setLikeCode(code);
        search.setPageIgnore(1);
        search.setLikeName(companyName);
        ListVO<CompanyDTO> paging = this.paging(search);
        List<CompanyDTO> result = paging.getDataList();
        return result;
    }

    @Override
    public void updateDeclareWay(CompanyDeclareConfigReqVo companyDeclareConfigReqVo) throws ArgsErrorException {
        if (Objects.isNull(companyDeclareConfigReqVo.getCompanyId())) {
            throw new ArgsErrorException("企业id获取失败");
        }
        Long companyId = companyDeclareConfigReqVo.getCompanyId();
        List<DeclareConfig> declareConfigList = companyDeclareConfigReqVo.getDeclareConfigList();
        CompanyDTO companyDTO = this.findById(companyId);
        if (Objects.isNull(companyDTO) || Objects.isNull(companyDTO.getId())) {
            throw new ArgsErrorException("未找到关联企业信息");
        }
        CompanyDO companyDO = new CompanyDO();
        companyDO.setId(companyDTO.getId());
        companyDO.setDeclareConfig(JSON.toJSONString(declareConfigList));
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(companyDO);
        }
        companyDO.setUpdateTime(new Date());
        companyMapper.updateByPrimaryKeySelective(companyDO);
    }

    @Override
    public List<DeclareConfig> viewDeclareWay(Long companyId) throws ArgsErrorException {
        List<DeclareConfig> declareConfigList = new ArrayList<>();
        if (companyId == null) {
            return declareConfigList;
        }
        CompanyDTO companyDTO = this.findById(companyId);
        try {
            declareConfigList = JSON.parseArray(companyDTO.getDeclareConfig(), DeclareConfig.class);
        } catch (Exception e) {
            log.warn("viewDeclareWay 申报方式解析失败 error={}", e.getMessage(), e);
        }
        return declareConfigList;
    }

}
