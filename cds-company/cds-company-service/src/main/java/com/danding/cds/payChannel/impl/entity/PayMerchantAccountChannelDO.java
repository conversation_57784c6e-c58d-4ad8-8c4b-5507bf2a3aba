package com.danding.cds.payChannel.impl.entity;

import com.danding.cds.common.model.BaseDO;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;

@Table(name = "ccs_pay_merchant_account_channel")
@Getter
@Setter
public class PayMerchantAccountChannelDO extends BaseDO {
    /**
     * 商户id
     */
    @Column(name = "merchant_id")
    private Long merchantId;

    /**
     * 商户编码
     */
    @Column(name = "merchant_sn")
    private String merchantSn;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 收款账号
     */
    @Column(name = "recp_account")
    private String recpAccount;

    /**
     * 收款企业编号
     */
    @Column(name = "recp_code")
    private String recpCode;

    /**
     * 收款企业名称
     */
    @Column(name = "recp_name")
    private String recpName;

    /**
     * 验签令牌Json字符串，根据渠道由不同类转化而来
     */
    @Column(name = "token_json")
    private String tokenJson;
}