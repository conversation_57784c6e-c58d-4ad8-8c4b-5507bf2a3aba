package com.danding.cds.message.impl.mq;

import com.danding.cds.message.api.dto.MessageTaskDTO;
import com.danding.cds.message.api.service.MessageService;
import com.danding.cds.mq.handler.MessageHandlerAfterInit;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RocketMQMessageListener(
        consumerGroup = "ccs-message-execute-consumer",
        topic = "ccs-message-execute-topic"
)
public class MessageExecuteConsumer extends MessageHandlerAfterInit {

    @DubboReference
    private MessageService messageService;

    @Override
    public void handle(Object message) throws RuntimeException {
        log.warn("[op:MQMessageListener 监听启动！]");
        String key = message.toString();
        Long taskId = Long.valueOf(key);
        MessageTaskDTO messageTaskDTO = messageService.findTaskById(taskId);
        log.warn("[op:MQMessageListener 执行监听任务]  messageTaskDTO - {}",messageTaskDTO);
        messageService.executeTask(messageTaskDTO);
    }

}
