package com.danding.cds.customs.manager.impl.service;

import cn.hutool.core.util.StrUtil;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.Assert;
import com.danding.cds.customs.manager.api.dto.CustomsDistrictDTO;
import com.danding.cds.customs.manager.api.service.CustomsDistrictManagerService;
import com.danding.cds.customs.manager.api.vo.CustomsDistrictAddParam;
import com.danding.cds.customs.manager.api.vo.CustomsDistrictEnableParam;
import com.danding.cds.customs.manager.api.vo.CustomsDistrictSearch;
import com.danding.cds.customs.manager.api.vo.CustomsDistrictUpdateParam;
import com.danding.cds.customs.manager.impl.entity.CustomsDistrictDO;
import com.danding.cds.customs.manager.impl.mapper.CustomsDistrictMapper;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.danding.soul.client.common.exception.BusinessException;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @Author: yousx
 * @Date: 2023/11/23
 * @Description:
 */
@Slf4j
@DubboService
public class CustomsDistrictManagerServiceImpl implements CustomsDistrictManagerService {

    @Resource
    private CustomsDistrictMapper customsDistrictMapper;
    @Resource
    private StringRedisTemplate stringRedisTemplate;


    @Override
    @PageSelect
    public ListVO<CustomsDistrictDTO> paging(CustomsDistrictSearch search) {
        Example example = new Example(CustomsDistrictDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("deleted", false);
        if (StrUtil.isNotEmpty(search.getName())) {
            criteria.andEqualTo("name", search.getName());
        }
        if (StrUtil.isNotEmpty(search.getCode())) {
            criteria.andLike("code", "%" + search.getCode() + "%");
        }
        if (StrUtil.isNotEmpty(search.getCustoms())) {
            criteria.andLike("customs", "%" + search.getCustoms() + "%");
        }
        if (StrUtil.isNotEmpty(search.getPortCode())) {
            criteria.andLike("portCode", "%" + search.getPortCode() + "%");
        }
        List<CustomsDistrictDO> list = customsDistrictMapper.selectByExample(example);
        ListVO<CustomsDistrictDTO> result = new ListVO<>();
        List<CustomsDistrictDTO> customsDistrictDTOS = BeanUtils.copyProperties(list, CustomsDistrictDTO.class);
        result.setDataList(customsDistrictDTOS);

        // 分页
        PageInfo<CustomsDistrictDTO> pageInfo = new PageInfo<>(customsDistrictDTOS);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insert(CustomsDistrictAddParam param) throws ArgsErrorException {

        Example example = new Example(CustomsDistrictDO.class);
        example.createCriteria().andEqualTo("code", param.getCode());
        int count = customsDistrictMapper.selectCountByExample(example);
        Assert.isTrue(count == 0, "该口岸编码已存在");
        example.clear();
        example.createCriteria().andEqualTo("name", param.getName());
        count = customsDistrictMapper.selectCountByExample(example);
        Assert.isTrue(count == 0, "该口岸名称已存在");
        example.clear();
        example.createCriteria().andEqualTo("portCode", param.getPortCode());
        count = customsDistrictMapper.selectCountByExample(example);
        Assert.isTrue(count == 0, "该关区编码已存在");


        CustomsDistrictDO customsDistrictDO = new CustomsDistrictDO();
        customsDistrictDO.setName(param.getName());
        customsDistrictDO.setCode(param.getCode());
        customsDistrictDO.setCustoms(param.getCustoms());
        customsDistrictDO.setPortCode(param.getPortCode());
        customsDistrictDO.setStatus(1);
        customsDistrictDO.setCreateTime(new Date());
        customsDistrictDO.setUpdateTime(new Date());
        customsDistrictDO.setCreateBy(UserUtils.getUserId());
        customsDistrictDO.setUpdateBy(UserUtils.getUserId());
        customsDistrictDO.setDeleted(false);
        customsDistrictMapper.insertSelective(customsDistrictDO);
        delCache();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(CustomsDistrictUpdateParam param) throws ArgsErrorException {
        CustomsDistrictDO customsDistrictDO = customsDistrictMapper.selectByPrimaryKey(param.getId());
        Assert.notNull(customsDistrictDO, "未找到对应数据");

        Example example = new Example(CustomsDistrictDO.class);
        example.createCriteria()
                .andEqualTo("name", param.getName())
                .andNotEqualTo("id", param.getId());
        int count = customsDistrictMapper.selectCountByExample(example);
        Assert.isTrue(count == 0, "该口岸名称已存在");
        example.clear();

        example.createCriteria()
                .andEqualTo("code", param.getCode())
                .andNotEqualTo("id", param.getId());
        count = customsDistrictMapper.selectCountByExample(example);
        Assert.isTrue(count == 0, "该口岸编码已存在");
        example.clear();

        example.createCriteria()
                .andEqualTo("portCode", param.getPortCode())
                .andNotEqualTo("id", param.getId());
        count = customsDistrictMapper.selectCountByExample(example);
        Assert.isTrue(count == 0, "该关区编码已存在");

        customsDistrictDO.setName(param.getName());
//        customsDistrictDO.setCode(param.getCode());
        customsDistrictDO.setCustoms(param.getCustoms());
        customsDistrictDO.setPortCode(param.getPortCode());
        customsDistrictDO.setUpdateTime(new Date());
        customsDistrictDO.setUpdateBy(UserUtils.getUserId());
        customsDistrictMapper.updateByPrimaryKeySelective(customsDistrictDO);
        delCache();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) throws ArgsErrorException {
        Assert.notNull(id, "id不能为空");
        CustomsDistrictDO customsDistrictDO = customsDistrictMapper.selectByPrimaryKey(id);
        if (customsDistrictDO == null) {
            throw new BusinessException("未找到对应数据");
        }
        customsDistrictDO.setDeleted(true);
        customsDistrictDO.setUpdateTime(new Date());
        customsDistrictDO.setUpdateBy(UserUtils.getUserId());
        customsDistrictMapper.updateByPrimaryKeySelective(customsDistrictDO);
        delCache();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enableSwitch(CustomsDistrictEnableParam param) throws ArgsErrorException {
        CustomsDistrictDO customsDistrictDO = customsDistrictMapper.selectByPrimaryKey(param.getId());
        Assert.notNull(customsDistrictDO, "未找到对应数据");
        customsDistrictDO.setStatus(param.getStatus());
        customsDistrictDO.setUpdateTime(new Date());
        customsDistrictDO.setUpdateBy(UserUtils.getUserId());
        customsDistrictMapper.updateByPrimaryKeySelective(customsDistrictDO);
        delCache();
    }

    @Override
    public List<CustomsDistrictDTO> list() {
        List<CustomsDistrictDO> list = customsDistrictMapper.selectAll();
        return BeanUtils.copyProperties(list, CustomsDistrictDTO.class);
    }


    private void delCache() {
        stringRedisTemplate.delete("customsDistrictList");
    }
}
