package com.danding.cds.message.impl.process.customs;

import com.alibaba.fastjson.JSON;
import com.danding.cds.common.bean.dto.JdlPostDTO;
import com.danding.cds.common.utils.JdlRequestUtils;
import com.danding.cds.message.api.dto.MessageDTO;
import com.danding.cds.message.api.dto.MessageTaskDTO;
import com.danding.cds.message.api.enums.MessageType;
import com.danding.cds.message.impl.process.MessageExecuteResult;
import com.danding.cds.message.impl.process.MessageProcess;
import com.danding.cds.message.impl.process.MessageRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@Slf4j
public class JdRefundCustomsCallbackProcess extends MessageProcess {

    public JdRefundCustomsCallbackProcess(MessageRegistry registry) {
        super(registry);
    }

    @Override
    public MessageType getType() {
        return MessageType.JD_REFUND_CUSTOMS_CALLBACK;
    }

    @Override
    public Map<String, Object> buildSendInfo(MessageDTO messageDTO) {
        String activeData = messageDTO.getActiveData();
        JdlPostDTO jdlPostDTO = JSON.parseObject(activeData, JdlPostDTO.class);
        log.info("京东退货回执消息打印 - {}", JSON.toJSONString(jdlPostDTO));
        return objToMap(jdlPostDTO);
    }

    @Override
    public MessageExecuteResult execute(MessageTaskDTO messageTaskDTO) {
        MessageExecuteResult result = new MessageExecuteResult();
        log.info("京东退货回执消息 业务编码: {} ,请求数据信息: {}", messageTaskDTO.getBusinessCode(), JSON.toJSONString(messageTaskDTO));
        try {
            String requestData = messageTaskDTO.getRequestData();
            JdlPostDTO jdlPostDTO = JSON.parseObject(requestData, JdlPostDTO.class);
            log.info("JdRefundCustomsCallbackProcess execute jdlPostDTO={}", JSON.toJSONString(jdlPostDTO));
            String res = JdlRequestUtils.doPostJDL(jdlPostDTO.getUri(), jdlPostDTO.getPath(), jdlPostDTO.getBody(), jdlPostDTO.getAppKey(), jdlPostDTO.getAppSecret());
            log.info("JdRefundCustomsCallbackProcess execute res={}", res);
            ObjectMapper objectMapper = new ObjectMapper();
            Map<String, Object> jdResult = objectMapper.readValue(res, Map.class);
            boolean success = (boolean) jdResult.get("success");
            result.setResponseMsg(res);
            if (success) {
                result.setSuccess(true);
                result.setResponseMsg(res);
            } else {
                String message = (String) jdResult.get("message");
                result.setSuccess(false);
                result.setResponseMsg("回传京东失败：" + message);
            }
        } catch (Exception e) {
            result.setSuccess(false);
            result.setResponseMsg("回传京东失败：" + e.getMessage());
            log.error("京东退货回执消息失败 系统异常 京东消息回传业务编码:{} 消息回调处理异常：{}", messageTaskDTO.getBusinessCode(), e.getMessage(), e);
        }
        return result;
    }
}
