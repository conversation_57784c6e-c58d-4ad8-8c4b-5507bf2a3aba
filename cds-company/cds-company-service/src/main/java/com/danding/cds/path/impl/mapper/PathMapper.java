package com.danding.cds.path.impl.mapper;


import com.danding.cds.path.impl.entity.PathDO;
import com.danding.logistics.mybatis.common.Mapper;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import tk.mybatis.mapper.additional.insert.InsertListMapper;

public interface PathMapper extends Mapper<PathDO>, InsertListMapper<PathDO>, BatchUpdateMapper<PathDO>, AggregationPlusMapper<PathDO> {
}
