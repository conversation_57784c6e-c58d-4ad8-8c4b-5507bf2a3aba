package com.danding.cds.customs.currency.impl.service;

import com.alibaba.fastjson.JSON;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.customs.currency.api.dto.CustomsCurrencyDTO;
import com.danding.cds.customs.currency.api.service.CustomsCurrencyService;
import com.danding.cds.customs.currency.impl.entity.CustomsCurrencyDO;
import com.danding.cds.customs.currency.impl.mapper.CustomsCurrencyMapper;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@DubboService
public class CustomsCurrencyServiceImpl implements CustomsCurrencyService {

    @Autowired
    private CustomsCurrencyMapper customsCurrencyMapper;

    @Override
    public CustomsCurrencyDTO findById(Long id) {
        if (LongUtil.isNone(id)) {
            return null;
        } else {
            CustomsCurrencyDO customsCurrencyDO = customsCurrencyMapper.selectByPrimaryKey(id);
            return this.buildDTO(customsCurrencyDO);
        }
    }

    @Override
//    @Cacheable(value = "customsCurrencyByCode", key = "#code", unless = "#result==null")
    public CustomsCurrencyDTO findByCode(String code) {
        CustomsCurrencyDO condition = new CustomsCurrencyDO();
        condition.setCode(code);
        return this.buildDTO(customsCurrencyMapper.selectOne(condition));
    }

    @Override
//    @Cacheable(value = "customsCurrencyByName", key = "#name", unless = "#result==null")
    public CustomsCurrencyDTO findByName(String name) {
        CustomsCurrencyDO condition = new CustomsCurrencyDO();
        condition.setName(name);
        return this.buildDTO(customsCurrencyMapper.selectOne(condition));
    }

    @Override
    public List<CustomsCurrencyDTO> listAll() {
        CustomsCurrencyDO condition = new CustomsCurrencyDO();
        condition.setDeleted(false);
        return JSON.parseArray(JSON.toJSONString(customsCurrencyMapper.select(condition)), CustomsCurrencyDTO.class);
    }

    private CustomsCurrencyDTO buildDTO(CustomsCurrencyDO entity) {
        if (entity == null) {
            return null;
        } else {
            CustomsCurrencyDTO dto = new CustomsCurrencyDTO();
            BeanUtils.copyProperties(entity, dto);
            return dto;
        }
    }
}
