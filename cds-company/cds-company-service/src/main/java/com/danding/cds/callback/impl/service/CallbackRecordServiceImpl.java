package com.danding.cds.callback.impl.service;

import com.alibaba.fastjson.JSON;
import com.danding.cds.callback.api.dto.CallbackRecordDTO;
import com.danding.cds.callback.api.dto.CallbackSearch;
import com.danding.cds.callback.api.enums.CallbackStatus;
import com.danding.cds.callback.api.enums.CallbackType;
import com.danding.cds.callback.api.service.CallbackRecordService;
import com.danding.cds.callback.impl.entity.CallbackRecordDO;
import com.danding.cds.callback.impl.mapper.CallbackRecordMapper;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.LongUtil;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
@Deprecated
@Slf4j
@DubboService
public class CallbackRecordServiceImpl implements CallbackRecordService {

    @Autowired
    private CallbackRecordMapper callbackRecordMapper;

    @Override
    public Boolean createTask(CallbackType type, String businessCode, Object info) {
//        return true; // 停止该回传服务
        CallbackRecordDO callbackRecordDO = new CallbackRecordDO();
        callbackRecordDO.setBusinessCode(businessCode);
        callbackRecordDO.setType(type.getCode());
        callbackRecordDO.setCount(0);
        callbackRecordDO.setStatus(CallbackStatus.DEC_WAIT.getValue());
        callbackRecordDO.setActiveData(JSON.toJSONString(info));
        UserUtils.setCreateAndUpdateBy(callbackRecordDO);
        return callbackRecordMapper.insertSelective(callbackRecordDO) > 0;
    }

    @Override
    public CallbackRecordDTO findById(Long id) {
        return this.buildDTO(callbackRecordMapper.selectByPrimaryKey(id));
    }

    @Override
    public List<CallbackRecordDTO> listByTypeAndBusinessCode(String type, String businessValue) {
        CallbackRecordDO condition = new CallbackRecordDO();
        condition.setType(type);
        condition.setBusinessCode(businessValue);
        return callbackRecordMapper.select(condition).stream().map(this::buildDTO).collect(Collectors.toList());
    }

    @Override
    public List<CallbackRecordDTO> listByStatus(Integer status) {
        CallbackRecordDO condition = new CallbackRecordDO();
        condition.setStatus(status);
        return callbackRecordMapper.select(condition).stream().map(this::buildDTO).collect(Collectors.toList());
    }

    @Override
    public Integer updateStatusById(Long id, Integer status) {
        CallbackRecordDO condition = new CallbackRecordDO();
        condition.setId(id);
        condition.setStatus(status);
        condition.setUpdateTime(new Date());
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(condition);
        }
        return callbackRecordMapper.updateByPrimaryKeySelective(condition);
    }

    @Override
    public Integer updateDataById(Long id, String requestData, String responseData) {
        CallbackRecordDO old = callbackRecordMapper.selectByPrimaryKey(id);
        CallbackRecordDO condition = new CallbackRecordDO();
        condition.setId(id);
        List<String> requestList = new ArrayList<>();
        requestList.add(requestData);
        if (!StringUtils.isEmpty(old.getRequestData())) {
            requestList.addAll(JSON.parseArray(old.getRequestData(), String.class));
        }
        condition.setRequestData(JSON.toJSONString(requestData));
        List<String> responseList = new ArrayList<>();
        responseList.add(responseData);
        if (!StringUtils.isEmpty(old.getResponseData())) {
            responseList.addAll(JSON.parseArray(old.getResponseData(), String.class));
        }
        condition.setResponseData(JSON.toJSONString(responseData));
        condition.setUpdateTime(new Date());
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(condition);
        }
        return callbackRecordMapper.updateByPrimaryKeySelective(condition);
    }

    @Override
    @PageSelect
    public ListVO<CallbackRecordDTO> paging(CallbackSearch search) {
        Example example = new Example(CallbackRecordDO.class);
        Example.Criteria criteria = example.createCriteria();
        if (search.getStatus() != null && search.getStatus() != 0) {
            criteria.andEqualTo("status", search.getStatus());
        }
        if (!StringUtils.isEmpty(search.getType())) {
            criteria.andEqualTo("type", search.getType());
        }
        if (!StringUtils.isEmpty(search.getBusinessCode())) {
            criteria.andEqualTo("businessCode", search.getBusinessCode());
        }

        Long createFrom = LongUtil.getFrom(search.getCreateFrom(),search.getCreateTo());
        Long createTo = LongUtil.getEnd(search.getCreateFrom(),search.getCreateTo());
        if (!LongUtil.isNone(createFrom) && !LongUtil.isNone(createTo)){
            criteria.andBetween("createTime",
                    new DateTime(createFrom).toString("yyyy-MM-dd HH:mm:ss"),
                    new DateTime(createTo).toString("yyyy-MM-dd HH:mm:ss"));
        }

        example.setOrderByClause("create_time DESC");
        List<CallbackRecordDO> list = callbackRecordMapper.selectByExample(example);
        ListVO<CallbackRecordDTO> result = new ListVO<>();
        result.setDataList(JSON.parseArray(JSON.toJSONString(list), CallbackRecordDTO.class));
        // 分页
        PageInfo<CallbackRecordDTO> pageInfo = new PageInfo(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    private CallbackRecordDTO buildDTO(CallbackRecordDO model){
        CallbackRecordDTO dto = new CallbackRecordDTO();
        BeanUtils.copyProperties(model,dto);
        return dto;
    }
}
