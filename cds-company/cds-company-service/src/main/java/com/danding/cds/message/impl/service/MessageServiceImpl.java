package com.danding.cds.message.impl.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.rpc.OrderCRpc;
import com.danding.cds.callback.api.dto.OrderActiveInfo;
import com.danding.cds.callback.api.dto.OrderEventActive;
import com.danding.cds.callback.api.enums.SubscribeStrategy;
import com.danding.cds.common.config.EnvironmentConfig;
import com.danding.cds.common.email.EmailConfig;
import com.danding.cds.common.email.EmailMessage;
import com.danding.cds.common.email.EmailUtils;
import com.danding.cds.common.enums.DelayLevelConfigEnums;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.common.utils.JSONUtils;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.common.utils.ValidatorUtils;
import com.danding.cds.invenorder.api.dto.InventoryOrderInfoDTO;
import com.danding.cds.invenorder.api.enums.InventoryOrderBusinessEnum;
import com.danding.cds.invenorder.api.enums.WorkOrderStatueEnum;
import com.danding.cds.invenorder.api.service.InventoryOrderInfoService;
import com.danding.cds.message.api.dto.*;
import com.danding.cds.message.api.enums.MessageContentEnums;
import com.danding.cds.message.api.enums.MessageStatus;
import com.danding.cds.message.api.enums.MessageTaskStatus;
import com.danding.cds.message.api.enums.MessageType;
import com.danding.cds.message.api.service.MessageService;
import com.danding.cds.message.api.vo.CreateMessageReqVO;
import com.danding.cds.message.impl.entity.MessageDO;
import com.danding.cds.message.impl.entity.MessageSubscribeDO;
import com.danding.cds.message.impl.entity.MessageTaskDO;
import com.danding.cds.message.impl.mapper.MessageMapper;
import com.danding.cds.message.impl.mapper.MessageSubscribeMapper;
import com.danding.cds.message.impl.mapper.MessageTaskMapper;
import com.danding.cds.message.impl.mq.MessageExecuteProducer;
import com.danding.cds.message.impl.mq.MessageGenerateProducer;
import com.danding.cds.message.impl.process.MessageExecuteResult;
import com.danding.cds.message.impl.process.MessageProcess;
import com.danding.cds.message.impl.process.MessageRegistry;
import com.danding.cds.message.impl.process.MessageSendRecord;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.api.dto.OrderExtra;
import com.danding.cds.snow.flake.service.IdService;
import com.danding.cds.v2.api.TaotianOrderService;
import com.danding.cds.v2.bean.dto.TTClearanceEntryorderCreateDTO;
import com.danding.cds.v2.bean.dto.TTOrderProcessStatusReport;
import com.danding.common.utils.CopyUtil;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Splitter;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.ibatis.session.RowBounds;
import org.assertj.core.util.Lists;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import javax.validation.Validator;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@Slf4j
@DubboService
@RefreshScope
public class MessageServiceImpl implements MessageService {

    @DubboReference
    private InventoryOrderInfoService inventoryOrderInfoService;

    @DubboReference
    private TaotianOrderService taotianOrderService;

    @Autowired
    private MessageMapper messageMapper;

    @Autowired
    private MessageSubscribeMapper messageSubscribeMapper;

    @Autowired
    private MessageTaskMapper messageTaskMapper;

    @Autowired
    private MessageRegistry registry;

    @Autowired
    private MessageGenerateProducer generateProducer;

    @Autowired
    private MessageExecuteProducer executeProducer;

    @Autowired
    private IdService idService;

    @DubboReference
    private OrderCRpc orderService;

    @Autowired
    private Validator validator;

    //    @Value("#{'${little_giant_route_code_list}'.split(',')}")
    @Value("${little_giant_route_code_list:[]}")
    private String[] littleGiantRouteCodeList;

    @Override
    public void createSubscribe(MessageSubscribeDTO messageSubscribeDTO) {
        MessageSubscribeDO model = new MessageSubscribeDO();
        model.setSubscribeTag(messageSubscribeDTO.getSubscribeTag());
        model.setMessageTypeJson(messageSubscribeDTO.getMessageTypeJson());
        model.setNotifyUrl(messageSubscribeDTO.getNotifyUrl());
        model.setDetail(messageSubscribeDTO.getDetail());
        model.setAlertEmails(messageSubscribeDTO.getAlertEmails());
        model.setMaxCount(messageSubscribeDTO.getMaxCount());
        model.setRetryStrategy(messageSubscribeDTO.getRetryStrategy());
        model.setEnable(true);
        UserUtils.setCreateAndUpdateBy(model);
        messageSubscribeMapper.insertSelective(model);
    }

    @Override
    public MessageSubscribeDTO findSubscribeById(Long id) {
        Example example = new Example(MessageSubscribeDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("id", id);
        MessageSubscribeDO subscribeDO = messageSubscribeMapper.selectOneByExample(example);
        return CopyUtil.copy(subscribeDO, MessageSubscribeDTO.class);
    }

    @Override
    @PageSelect
    public ListVO<MessageSubscribeDTO> subscribePaging(MessageSubscribeSearch search) {
        Example example = new Example(MessageSubscribeDO.class);
        Example.Criteria criteria = example.createCriteria();
        example.orderBy("id").desc();
        List<MessageSubscribeDO> list = messageSubscribeMapper.selectByExample(example);
        ListVO<MessageSubscribeDTO> result = new ListVO<>();
        result.setDataList(JSON.parseArray(JSONUtils.toJSONString(list), MessageSubscribeDTO.class));
        // 分页
        PageInfo<MessageSubscribeDO> pageInfo = new PageInfo(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        result.setPage(pageResult);
        return result;

    }

    @Override
    @PageSelect
    public ListVO<MessageTaskDTO> taskPaging(MessageTaskSearch search) {
        Example example = new Example(MessageTaskDO.class);
        Example.Criteria criteria = example.createCriteria();
        if (!LongUtil.isNone(search.getSubscribeId())) {
            criteria.andEqualTo("subscribeId", search.getSubscribeId());
        }
        if (!StringUtils.isEmpty(search.getType())) {
            criteria.andEqualTo("type", search.getType());
        }
        if (!StringUtils.isEmpty(search.getBusinessCode())) {
            criteria.andEqualTo("businessCode", search.getBusinessCode());
        }
        if (search.getStatus() != null && search.getStatus() > 0) {
            criteria.andEqualTo("status", search.getStatus());
        }
        if (!LongUtil.isNone(search.getCreateTimeFrom())) {
            criteria.andGreaterThan("createTime", new DateTime(search.getCreateTimeFrom()).toString("yyyy-MM-dd HH:mm:ss"));
        }
        if (!LongUtil.isNone(search.getCreateTimeFrom())) {
            criteria.andLessThan("createTime", new DateTime(search.getCreateTimeTo()).toString("yyyy-MM-dd HH:mm:ss"));
        }
        example.orderBy("id").desc();
        List<MessageTaskDO> list = messageTaskMapper.selectByExample(example);
        ListVO<MessageTaskDTO> result = new ListVO<>();
        List<MessageTaskDTO> dtoList = list.stream().map(d -> {
            MessageTaskDTO dto = new MessageTaskDTO();
            BeanUtils.copyProperties(d, dto);
            return dto;
        }).collect(Collectors.toList());
        result.setDataList(dtoList);
        // 分页
        PageInfo<MessageTaskDO> pageInfo = new PageInfo(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    @Override
    public List<MessageSubscribeDTO> listSubscribeByTag(String tag) {
        MessageSubscribeDO condition = new MessageSubscribeDO();
        condition.setSubscribeTag(tag);
        condition.setEnable(true);
        List<MessageSubscribeDO> subscribeDOList = messageSubscribeMapper.select(condition);
        return CopyUtil.copyList(subscribeDOList, MessageSubscribeDTO.class);
    }

    @Override
    public List<MessageSubscribeDTO> listSubscribeByTagAndType(String tag, String type) {
        Example example = new Example(MessageSubscribeDO.class);
        example.createCriteria()
                .andEqualTo("subscribeTag", tag)
                .andLike("messageTypeJson", "%" + type + "%")
                .andEqualTo("deleted", false);
        List<MessageSubscribeDO> messageSubscribeDOList = messageSubscribeMapper.selectByExample(example);
        return ConvertUtil.listConvert(messageSubscribeDOList, MessageSubscribeDTO.class);
    }

    @Override
    public List<MessageSubscribeDTO> listSubscribeAll() {
        List<MessageSubscribeDO> subscribeDOList = messageSubscribeMapper.selectAll();
        return CopyUtil.copyList(subscribeDOList, MessageSubscribeDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createMultiTaskMessage(MessageType type, List<String> tagList, String businessCode, MultiActiveDataDTO multiActiveData, Integer delayLevel) {
        MessageDO messageDO = new MessageDO();
        messageDO.setType(type.getCode());
        messageDO.setTagJson(JSON.toJSONString(tagList));
        messageDO.setStatus(MessageStatus.INIT.getCode());
        messageDO.setBusinessCode(businessCode);
        messageDO.setActiveData(JSON.toJSONString(multiActiveData));
        messageDO.setMultiTask(true);
        UserUtils.setCreateAndUpdateBy(messageDO);
        messageMapper.insertSelective(messageDO);
        generateProducer.send(messageDO.getId(), delayLevel);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createMultiMessage(List<MessageCreateDTO> createDTOS) {
        if (CollectionUtils.isEmpty(createDTOS)) {
            return;
        }
        List<MessageDO> messageDOS = new ArrayList<>();
        long groupId = idService.getUniqueId();
        createDTOS.forEach(dto -> {
            MessageDO messageDO = new MessageDO();
            messageDO.setType(dto.getType().getCode());
            messageDO.setTagJson(JSON.toJSONString(dto.getTagList()));
            messageDO.setStatus(MessageStatus.WAITING_CREATE.getCode());
            messageDO.setBusinessCode(dto.getBusinessCode());
            messageDO.setActiveData(dto.getActiveData());
            messageDO.setMultiTask(dto.getIsMultiTask());
            messageDO.setGroupId(groupId);
            UserUtils.setCreateAndUpdateBy(messageDO);
            messageDOS.add(messageDO);
        });
        messageDOS.get(0).setStatus(MessageStatus.INIT.getCode());
        messageMapper.insertList(messageDOS);

        updateAndSendMsg(messageDOS, createDTOS.get(0).getDelayLevel());
    }

    @Override
    public void createMessage(MessageType type, List<String> tagList, String businessCode, String activeData, String notifyUrl) {
        MessageDO messageDO = new MessageDO();
        messageDO.setNotifyUrl(notifyUrl);
        messageDO.setType(type.getCode());
        messageDO.setTagJson(JSON.toJSONString(tagList));
        messageDO.setStatus(MessageStatus.INIT.getCode());
        messageDO.setBusinessCode(businessCode);
        messageDO.setActiveData(activeData);
        UserUtils.setCreateAndUpdateBy(messageDO);
        messageMapper.insertSelective(messageDO);
        generateProducer.send(messageDO.getId());
    }

    @Override
    public void createMessage(MessageType type, List<String> tagList, String businessCode, String activeData, String notifyUrl, Integer delayLevel) {
        MessageDO messageDO = new MessageDO();
        messageDO.setNotifyUrl(notifyUrl);
        messageDO.setType(type.getCode());
        messageDO.setTagJson(JSON.toJSONString(tagList));
        messageDO.setStatus(MessageStatus.INIT.getCode());
        messageDO.setBusinessCode(businessCode);
        messageDO.setActiveData(activeData);
        UserUtils.setCreateAndUpdateBy(messageDO);
        messageMapper.insertSelective(messageDO);
        generateProducer.send(messageDO.getId(), delayLevel);
    }

    @Override
    public void createMessageNotThrowEx(MessageType type, List<String> tagList, String businessCode, String activeData, String notifyUrl) {
        try {
            this.createMessage(type, tagList, businessCode, activeData, notifyUrl);
        } catch (Exception e) {
            log.error("创建消息失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void createMessageNotThrowEx(MessageType type, List<String> tagList, String businessCode, String activeData, String notifyUrl, Integer delayLevel) {
        try {
            this.createMessage(type, tagList, businessCode, activeData, notifyUrl, delayLevel);
        } catch (Exception e) {
            log.error("创建消息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 不需要用路径 校验了，调用钱根据申报单标记是否为菜鸟WMS订单即可
     *
     * @param type
     * @param tagList
     * @param businessCode
     * @param activeData
     * @param notifyUrl
     * @param routeCode
     */
    @Deprecated
    public void createMessageCallBackERP(MessageType type, List<String> tagList, String businessCode, String activeData, String notifyUrl, String routeCode) {
        log.info("createMessageCallBackERP, littleGiantRouteCodeList={}", JSONUtils.toJSONString(littleGiantRouteCodeList));
        List<String> list = Arrays.asList(littleGiantRouteCodeList);
        if (CollectionUtil.isNotEmpty(list) && list.contains(routeCode)) {
            //菜鸟-小巨人回传erp消息
            this.createMessageNotThrowEx(type, tagList, businessCode, activeData, notifyUrl, Integer.valueOf(DelayLevelConfigEnums.FIVE_MINUTE.getCode()));
        }
    }

    /**
     * 建立调用订单申报异常消息
     *
     * @param mainOrderId  申报主单ID
     * @param declareNos   申报单号
     * @param tagList      业务标签
     * @param exceptionMsg 异常信息
     */
    @Override
    public void createInvokeOrderExceptionMsg(Long mainOrderId, String declareNos, List<String> tagList, String exceptionMsg) {

        if (mainOrderId == null || StringUtils.isEmpty(declareNos)) {
            return;
        }
        String orderActiveInfo = JSON.toJSONString(new OrderActiveInfo(mainOrderId).buildCustomsInfo("-1", exceptionMsg, new Date()));
        this.createMessage(MessageType.ORDER_CUSTOMS_ORDER, tagList, declareNos, orderActiveInfo, "");
    }

    /**
     * 建立调用清单申报异常消息
     *
     * @param mainOrderId  申报主单ID
     * @param declareNos   申报单号
     * @param tagList      业务标签
     * @param exceptionMsg 异常信息
     */
    @Override
    public void createInvokeInventoryExceptionMsg(Long mainOrderId, String declareNos, List<String> tagList, String exceptionMsg) {
        if (mainOrderId == null || StringUtils.isEmpty(declareNos)) {
            return;
        }
        String orderActiveInfo = JSON.toJSONString(new OrderActiveInfo(mainOrderId).buildCustomsInfo("-1", exceptionMsg, new Date()));
        this.createMessage(MessageType.ORDER_CUSTOMS_INVENTORY, tagList, declareNos, orderActiveInfo, "");
    }

    @Override
    public void createRefundMessage(String businessCode, String activeData) {
        this.createRefundMessage(businessCode, activeData, MessageType.ORDER_REFUND);
    }

    @Override
    public void createRefundMessage(String businessCode, String activeData, MessageType messageType) {
        if (true) {
            log.info("不再回传退货消息 businessCode={} activeData={} messageType={}", businessCode, activeData, JSON.toJSONString(messageType));
            return;
        }
        MessageDO messageDO = new MessageDO();
        messageDO.setNotifyUrl("");
        messageDO.setType(messageType.getCode());
        messageDO.setTagJson("[\"CHANNEL-1\"]");
        messageDO.setStatus(MessageStatus.INIT.getCode());
        messageDO.setBusinessCode(businessCode);
        messageDO.setActiveData(activeData);
        UserUtils.setCreateAndUpdateBy(messageDO);
        messageMapper.insertSelective(messageDO);
        generateProducer.send(messageDO.getId());
    }

    @Override
    public void createOrRetryMessage(MessageType type, List<String> tagList, String businessCode, String activeData, String notifyUrl) {
        log.info("createOrRetryMessage type={} businessCode={}, activeData={}", type, businessCode, activeData);
        MessageDTO oldMessageDTO = findByBusinessCodeAndType(businessCode, type);
        if (oldMessageDTO == null) {
            log.info("createOrRetryMessage type={} businessCode={} 消息不存在创建一条新消息", type, businessCode);
            MessageDO messageDO = new MessageDO();
            messageDO.setNotifyUrl(notifyUrl);
            messageDO.setType(type.getCode());
            messageDO.setTagJson(JSON.toJSONString(tagList));
            messageDO.setStatus(MessageStatus.INIT.getCode());
            messageDO.setBusinessCode(businessCode);
            messageDO.setActiveData(activeData);
            UserUtils.setCreateAndUpdateBy(messageDO);
            messageMapper.insertSelective(messageDO);
            generateProducer.send(messageDO.getId(), 1);
        } else {
            MessageTaskDTO messageTaskDTO = findTaskLastestByMsgId(oldMessageDTO.getId());
            if (messageTaskDTO == null) {
                log.info("createOrRetryMessage type={} businessCode={} 创建任务", type, businessCode);
                generateProducer.send(oldMessageDTO.getId(), 3);
                return;
            }
            MessageSubscribeDO subscribeDO = messageSubscribeMapper.selectByPrimaryKey(messageTaskDTO.getSubscribeId());
            if (messageTaskDTO.getCount() < subscribeDO.getMaxCount()) {
                log.info("createOrRetryMessage type={} businessCode={} 任务已存在直接执行 10秒后执行", type, businessCode);
                executeProducer.send(messageTaskDTO.getId(), 3);
            } else if (Objects.equals(messageTaskDTO.getStatus(), MessageTaskStatus.FAIL.getCode())) {
                log.info("createOrRetryMessage 任务失败，直接返回");
            } else {
                log.info("createOrRetryMessage type={} businessCode={} messageId{} 已经超过最大重试次数{}", type, businessCode, oldMessageDTO.getId(), subscribeDO.getMaxCount());
                MessageTaskDO updateTaskDO = new MessageTaskDO();
                updateTaskDO.setId(messageTaskDTO.getId());
                updateTaskDO.setStatus(MessageTaskStatus.FAIL.getCode());
                messageTaskMapper.updateByPrimaryKeySelective(updateTaskDO);
            }
        }
    }

    private MessageDTO findByBusinessCodeAndType(String businessCode, MessageType type) {
        if (businessCode == null || type == null) {
            return null;
        }
        Example example = new Example(MessageDO.class);
        example.setOrderByClause("id desc limit 1");
        example.createCriteria()
                .andEqualTo("businessCode", businessCode)
                .andEqualTo("type", type.getCode())
                .andEqualTo("deleted", false);
        List<MessageDO> messageDOList = messageMapper.selectByExample(example);
        if (CollUtil.isNotEmpty(messageDOList)) {
            MessageDO messageDO = messageDOList.get(0);
            return ConvertUtil.beanConvert(messageDO, MessageDTO.class);
        }
        return null;
    }

    @Override
    public List<MessageDTO> listByStatus(MessageStatus status) {
        MessageDO messageDO = new MessageDO();
        messageDO.setStatus(status.getCode());
        List<MessageDO> messageDOList = messageMapper.select(messageDO);
        log.warn("[op:MessageServiceImpl-listByStatus] total={}", messageDOList.size());
        return CopyUtil.copyList(messageDOList, MessageDTO.class);
    }

    @Override
    public List<MessageDTO> listByCreateTime(Date from, Date to, Integer limit) {
        Example example = new Example(MessageSubscribeDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andBetween("createTime", from, to);
        RowBounds rowBounds = new RowBounds(0, limit);
        List<MessageDO> list = messageMapper.selectByExampleAndRowBounds(example, rowBounds);
        return CopyUtil.copyList(list, MessageDTO.class);
    }

    @Override
    public MessageDTO findById(Long id) {
        return CopyUtil.copy(messageMapper.selectByPrimaryKey(id), MessageDTO.class);
    }

    @Override
    public MessageTaskDTO findTaskById(Long id) {
        return CopyUtil.copy(messageTaskMapper.selectByPrimaryKey(id), MessageTaskDTO.class);
    }

    public MessageTaskDTO findTaskLastestByMsgId(Long msgId) {
        Example example = new Example(MessageTaskDO.class);
        example.setOrderByClause("id desc limit 1");
        example.createCriteria().andEqualTo("deleted", false).andEqualTo("messageId", msgId);
        List<MessageTaskDO> messageTaskDOS = messageTaskMapper.selectByExample(example);
        if (CollUtil.isEmpty(messageTaskDOS)) {
            return null;
        }
        return CopyUtil.copy(messageTaskDOS.get(0), MessageTaskDTO.class);
    }

    @Override
    public List<MessageTaskDTO> listTaskByStatusAndTime(MessageTaskStatus status, Long startTime, Long endTime) {
        Example example = new Example(MessageTaskDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andBetween("nextNotifyTime", new Date(startTime), new Date(endTime));
        criteria.andEqualTo("status", status.getCode());
        List<MessageTaskDO> list = messageTaskMapper.selectByExample(example);
        return CopyUtil.copyList(list, MessageTaskDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generateTask(MessageDTO messageDTO) {
        if (!messageDTO.getStatus().equals(MessageStatus.INIT.getCode())) {
            log.info("[op:MessageServiceImpl-generateTask] status is error, status={}", MessageStatus.INIT.getCode());
            return;
        }
        log.info("[op:MessageServiceImpl-generateTask] message={}", JSON.toJSONString(messageDTO));
        MessageProcess process = registry.getProcess(MessageType.getEnum(messageDTO.getType()));
        if (process == null) {
            log.warn("[op:MessageServiceImpl-generateTask] process is null, type={}", MessageType.getEnum(messageDTO.getType()));
            return;
        }

        if (Boolean.TRUE.equals(messageDTO.getMultiTask())) {
            generateMultiTask(messageDTO, process);
        } else {
            generateSingleTask(messageDTO, process);
        }
    }

    private void generateMultiTask(MessageDTO messageDTO, MessageProcess process) {
        if (StringUtil.isEmpty(messageDTO.getActiveData())) {
            log.warn("[op:MessageServiceImpl-generateTask] activeData is empty, messageId={}", messageDTO.getId());
            return;
        }
        MultiActiveDataDTO multiActiveDataDTO = JSON.parseObject(messageDTO.getActiveData(), MultiActiveDataDTO.class);

        List<RequestDataDTO> requestDataDTOS = process.buildMultiSendInfo(messageDTO);
        if (CollectionUtils.isEmpty(requestDataDTOS)) {
            log.warn("业务单号: {} , 消息任务发送数据组装后为空 , 需要核查", messageDTO.getBusinessCode());
            return;
        }

        MessageDO messageModel = new MessageDO();
        messageModel.setId(messageDTO.getId());
        messageModel.setRequestData(JSON.toJSONString(requestDataDTOS));
        messageModel.setStatus(MessageStatus.READY.getCode());
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(messageModel);
        }
        messageModel.setUpdateTime(new Date());
        messageMapper.updateByPrimaryKeySelective(messageModel);

        if (multiActiveDataDTO.getUseActiveTag()) {
            generateInnerTagMultiTask(messageDTO, requestDataDTOS);
        } else {
            generateMsgTagMultiTask(messageDTO, requestDataDTOS);
        }
    }

    private void generateMsgTagMultiTask(MessageDTO messageDTO, List<RequestDataDTO> requestDataDTOS) {
        List<String> tags = JSON.parseArray(messageDTO.getTagJson(), String.class);
        // 根据tag分组
        for (String tag : tags) {
            long groupId = idService.getUniqueId();
            List<MessageSubscribeDTO> subscribeDTOList = this.listSubscribeByTag(tag);
            List<MessageTaskDO> taskDOS = new ArrayList<>();
            for (MessageSubscribeDTO messageSubscribeDTO : subscribeDTOList) {
                if (JSON.parseArray(messageSubscribeDTO.getMessageTypeJson(), String.class).contains(messageDTO.getType())) {
                    for (int i = 0; i < requestDataDTOS.size(); i++) {
                        RequestDataDTO requestDataDTO = requestDataDTOS.get(i);
                        MessageTaskDO template = getMessageTaskDO(messageDTO, requestDataDTO, groupId);
                        template.setSubscribeId(messageSubscribeDTO.getId());
                        template.setNotifyUrl(messageSubscribeDTO.getNotifyUrl());
                        UserUtils.setCreateAndUpdateBy(template);
                        messageTaskMapper.insertSelective(template);
                        taskDOS.add(template);
                    }
                    break;
                }
            }
            // 第一个节点更新为初始化状态
            updateAndSendMsg(taskDOS, messageDTO);
        }
    }

    private void generateInnerTagMultiTask(MessageDTO messageDTO, List<RequestDataDTO> requestDataDTOS) {
        // 使用内置tag
        // 消息组id
        long groupId = idService.getUniqueId();
        List<MessageTaskDO> taskDOS = new ArrayList<>();
        // 最后一个节点
        for (int i = 0; i < requestDataDTOS.size(); i++) {

            RequestDataDTO requestDataDTO = requestDataDTOS.get(i);
            MessageTaskDO template = getMessageTaskDO(messageDTO, requestDataDTO, groupId);

            String innerTag = requestDataDTO.getTag();
            List<MessageSubscribeDTO> subscribeDTOList = this.listSubscribeByTag(innerTag);

            // 目前这里的循环其实不会出现多个消息的情况，单个tag 对应的 type其实只有一个 这里tag目前使用是代表渠道，而type代表是具体渠道的业务接口
            // 不会出现tag 相同，还有重复type的情况
            for (MessageSubscribeDTO messageSubscribeDTO : subscribeDTOList) {
                if (JSON.parseArray(messageSubscribeDTO.getMessageTypeJson(), String.class).contains(messageDTO.getType())) {
                    template.setId(null);
                    template.setSubscribeId(messageSubscribeDTO.getId());
                    template.setNotifyUrl(messageSubscribeDTO.getNotifyUrl());
                    UserUtils.setCreateAndUpdateBy(template);
                    messageTaskMapper.insertSelective(template);
                    taskDOS.add(template);
                    break;
                }
            }
        }
        //
        updateAndSendMsg(taskDOS, messageDTO);
    }

    private void updateAndSendMsg(List<MessageTaskDO> taskDOS, MessageDTO messageDTO) {
        // 更新第一个节点的状态
        taskDOS.get(0).setStatus(MessageTaskStatus.INIT.getCode());
        // 更新最后一个任务的下一个消息id
        taskDOS.get(taskDOS.size() - 1).setNextMessageId(messageDTO.getNextMessageId());
        // 更新前后节点
        for (int i = 0; i < taskDOS.size() - 1; i++) {
            MessageTaskDO messageTaskDO = taskDOS.get(i);
            messageTaskDO.setNextTaskId(taskDOS.get(i + 1).getId());
        }
        for (int i = 1; i < taskDOS.size(); i++) {
            MessageTaskDO messageTaskDO = taskDOS.get(i);
            messageTaskDO.setPreTaskId(taskDOS.get(i - 1).getId());
        }
        messageTaskMapper.batchUpdateByPrimaryKey(taskDOS);
        executeProducer.send(taskDOS.get(0).getId());
    }

    private void updateAndSendMsg(List<MessageDO> messageDOS, Integer delayLevel) {
        // 更新前后节点
        for (int i = 0; i < messageDOS.size() - 1; i++) {
            MessageDO messageDO = messageDOS.get(i);
            messageDO.setNextMessageId(messageDOS.get(i + 1).getId());
        }
        for (int i = 1; i < messageDOS.size(); i++) {
            MessageDO messageDO = messageDOS.get(i);
            messageDO.setPreMessageId(messageDOS.get(i - 1).getId());
        }
        messageMapper.batchUpdateByPrimaryKey(messageDOS);
        MessageDO messageDO = messageDOS.get(0);
        if (Objects.nonNull(delayLevel)) {
            generateProducer.send(messageDO.getId(), delayLevel);
        } else {
            generateProducer.send(messageDO.getId());
        }
    }

    private MessageTaskDO getMessageTaskDO(MessageDTO messageDTO, RequestDataDTO requestDataDTO, long groupId) {
        MessageTaskDO template = new MessageTaskDO();
        template.setMessageId(messageDTO.getId());
        template.setType(messageDTO.getType());
        template.setTaskType(requestDataDTO.getTaskType());
        template.setBusinessCode(messageDTO.getBusinessCode());
        template.setGroupId(groupId);
        template.setCount(0);
        // 第一条消息会直接发送 下一条任务等待第一条消息完成后发送
        template.setStatus(MessageTaskStatus.WAITING_CREATE.getCode());
        template.setRequestData(JSON.toJSONString(requestDataDTO.getRequestData()));
        template.setDelayLevel(requestDataDTO.getDelayLevel());
        template.setSendRecordJson("[]");
        template.setNextNotifyTime(DateTime.now().plusMinutes(3).toDate()); // 延迟三分钟执行
        return template;
    }

    private void generateSingleTask(MessageDTO messageDTO, MessageProcess process) {
        Map<String, Object> sendMap = process.buildSendInfo(messageDTO);
        if (CollectionUtils.isEmpty(sendMap)) {
            log.warn("业务单号: {} , 消息任务发送数据组装后为空 , 需要核查", messageDTO.getBusinessCode());
            return;
        }
        MessageDO messageModel = new MessageDO();
        messageModel.setId(messageDTO.getId());
        messageModel.setRequestData(JSON.toJSONString(sendMap));
        messageModel.setStatus(MessageStatus.READY.getCode());
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(messageModel);
        }
        messageModel.setUpdateTime(new Date());
        messageMapper.updateByPrimaryKeySelective(messageModel);


        MessageTaskDO template = new MessageTaskDO();
        template.setMessageId(messageDTO.getId());
        template.setType(messageDTO.getType());
        template.setBusinessCode(messageDTO.getBusinessCode());
        template.setRequestData(messageDTO.getRequestData());
        template.setCount(0);
        template.setStatus(MessageTaskStatus.INIT.getCode());
        template.setRequestData(JSON.toJSONString(sendMap));
        template.setSendRecordJson("[]");
        template.setNextNotifyTime(DateTime.now().plusMinutes(3).toDate()); // 延迟三分钟执行
        template.setNextMessageId(messageDTO.getNextMessageId());
        if (!StringUtils.isEmpty(messageDTO.getNotifyUrl())) {
            template.setId(null);
            template.setSubscribeId(1L);
            template.setNotifyUrl(messageDTO.getNotifyUrl());
            UserUtils.setCreateAndUpdateBy(template);
            messageTaskMapper.insertSelective(template);
            executeProducer.send(template.getId());
        }
        List<String> tags = JSON.parseArray(messageDTO.getTagJson(), String.class);
        for (String tag : tags) {
            List<MessageSubscribeDTO> subscribeDTOList = this.listSubscribeByTag(tag);
            for (MessageSubscribeDTO messageSubscribeDTO : subscribeDTOList) {
                if (JSON.parseArray(messageSubscribeDTO.getMessageTypeJson(), String.class).contains(messageDTO.getType())) {
                    template.setId(null);
                    template.setSubscribeId(messageSubscribeDTO.getId());
                    template.setNotifyUrl(messageSubscribeDTO.getNotifyUrl());
                    UserUtils.setCreateAndUpdateBy(template);
                    messageTaskMapper.insertSelective(template);
                    executeProducer.send(template.getId());
                }
            }
        }
    }

    @Override
    public void executeTask(MessageTaskDTO messageTaskDTO) {
        log.warn("[op:executeTask 监听任务开始执行。。。]");
        MessageType messageType = MessageType.getEnum(messageTaskDTO.getType());
        // 若存在子处理器，则匹配子处理器
        List<MessageType.MessageTaskType> messageTaskTypes = messageType.getMessageTaskTypes();
        MessageProcess process = null;
        if (CollectionUtils.isEmpty(messageTaskTypes)) {
            process = registry.getProcess(messageType);
        } else {
            List<String> codes = messageType.getMessageTaskTypes().stream().map(MessageType.MessageTaskType::getCode).collect(Collectors.toList());
            if (codes.contains(messageTaskDTO.getTaskType())) {
                process = registry.getProcess(MessageType.MessageTaskType.getEnum(messageTaskDTO.getTaskType()));
            }
        }
        if (process == null) {
            log.warn("[op:executeTask 监听任务执行失败，未找到对应的处理器] type: {}, taskType: {}", messageTaskDTO.getType(), messageTaskDTO.getTaskType());
            return;
        }
        MessageSubscribeDO subscribeDO = messageSubscribeMapper.selectByPrimaryKey(messageTaskDTO.getSubscribeId());
        log.warn("[op:execute 开始发送任务。。。]");
        MessageExecuteResult result = process.execute(messageTaskDTO);

        MessageTaskDO model = new MessageTaskDO();
        model.setId(messageTaskDTO.getId());
        List<MessageSendRecord> recordList = JSON.parseArray(messageTaskDTO.getSendRecordJson(), MessageSendRecord.class);
        model.setCount(messageTaskDTO.getCount() + 1);
        model.setLastNotifyTime(DateTime.now().toDate());

        MessageSendRecord record = new MessageSendRecord();
        record.setRequestMsg(messageTaskDTO.getRequestData());
        record.setResponseMsg(result.getResponseMsg());
        record.setSendTime(System.currentTimeMillis());
        recordList.add(record);
        // 保留最近三次重试数据  要不然字段太长了
        recordList = recordList.subList(Math.max(recordList.size() - 3, 0), recordList.size());
        model.setSendRecordJson(JSON.toJSONString(recordList));


        if (result.getSuccess()) {
            // 回传成功
            model.setStatus(MessageTaskStatus.SUCCESS.getCode());
            //如果是多任务需要查询出下一个任务 并且 发送mq消息
            if (Objects.nonNull(messageTaskDTO.getNextTaskId())) {
                MessageTaskDO nextTask = messageTaskMapper.selectByPrimaryKey(messageTaskDTO.getNextTaskId());
                if (Objects.nonNull(nextTask) && MessageTaskStatus.WAITING_CREATE.getCode().equals(nextTask.getStatus())) {
                    nextTask.setStatus(MessageTaskStatus.INIT.getCode());
                    nextTask.setUpdateTime(new Date());
                    messageTaskMapper.updateByPrimaryKeySelective(nextTask);
                    // 默认延迟一秒
                    Integer delayLevel = Objects.nonNull(nextTask.getDelayLevel()) ? nextTask.getDelayLevel() : Integer.parseInt(DelayLevelConfigEnums.ONE_SECOND.getCode());
                    executeProducer.send(nextTask.getId(), delayLevel);
                }
            }
            if (Objects.nonNull(messageTaskDTO.getNextMessageId())) {
                // 如果消息之间也有序，这里还需要查询下一个message 并且发送 message的mq
                MessageDO messageDO = messageMapper.selectByPrimaryKey(messageTaskDTO.getNextMessageId());
                if (Objects.nonNull(messageDO) && MessageStatus.WAITING_CREATE.getCode().equals(messageDO.getStatus())) {
                    messageDO.setStatus(MessageStatus.INIT.getCode());
                    messageDO.setUpdateTime(new Date());
                    messageMapper.updateByPrimaryKeySelective(messageDO);
                    generateProducer.send(messageDO.getId(), Integer.parseInt(DelayLevelConfigEnums.ONE_SECOND.getCode()));
                }
            }
        } else {
            // 回传失败
            if (subscribeDO.getMaxCount() > model.getCount()) {
                int stepMinus = 10; // 步长十分钟
                if (subscribeDO.getRetryStrategy().equals(SubscribeStrategy.INCREASING.getCode())) {
                    model.setNextNotifyTime(DateTime.now().plusMinutes(stepMinus).toDate());
                } else {
                    model.setNextNotifyTime(DateTime.now().plusMinutes(stepMinus * Integer.parseInt(expString(2, messageTaskDTO.getCount()))).toDate());
                }
            } else {
                model.setStatus(MessageTaskStatus.FAIL.getCode());
                if (!StringUtils.isEmpty(subscribeDO.getAlertEmails())) {
                    try {
                        new EmailUtils(new EmailConfig("smtp.exmail.qq.com", 465, "<EMAIL>", "Dd111111", "Y800<<EMAIL>>")).send(new EmailMessage(subscribeDO.getAlertEmails(), EnvironmentConfig.getEnv() + "-CCS消息回执异常", JSON.toJSONString(record)));
                    } catch (Exception e) {
                        log.warn("处理异常：{}", e.getMessage(), e);
                    }
                }
            }
        }
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(model);
        }
        model.setUpdateTime(new Date());
        messageTaskMapper.updateByPrimaryKeySelective(model);
    }

    @Override
    @Transactional
    public void migrationHistory(Long id) {
        MessageDO messageDO = messageMapper.selectByPrimaryKey(id);
        UserUtils.setCreateAndUpdateBy(messageDO);
        messageMapper.insertHistory(messageDO);
        messageMapper.deleteByPrimaryKey(id);

        MessageTaskDO taskCondition = new MessageTaskDO();
        taskCondition.setMessageId(messageDO.getId());
        List<MessageTaskDO> taskDOList = messageTaskMapper.select(taskCondition);
        for (MessageTaskDO taskDO : taskDOList) {
            UserUtils.setCreateAndUpdateBy(messageDO);
            messageTaskMapper.insertHistory(taskDO);
            messageTaskMapper.deleteByPrimaryKey(id);
        }
    }

    //重新创建一个新的消息
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void retry(Long messageId) {
        try {
            MessageDO messageDO = messageMapper.selectByPrimaryKey(messageId);
            if (Objects.isNull(messageDO)) {
                log.warn("消息id不存在");
                return;
            }
            Example example = new Example(MessageTaskDO.class);
            example.createCriteria().andEqualTo("messageId", messageId);
            List<MessageTaskDO> taskDOS = messageTaskMapper.selectByExample(example);
            if (!CollectionUtils.isEmpty(taskDOS)) {
                List<MessageTaskDO> collect = taskDOS.stream()
                        .filter(taskDO -> Objects.equals(taskDO.getStatus(), MessageTaskStatus.SUCCESS.getCode()))
                        .collect(Collectors.toList());
                if (!Objects.equals(collect.size(), taskDOS.size())) {
                    log.warn("消息任务存在未成功的任务，请检查, messageId: {}", messageId);
                    return;
                }
            }
            messageDO.setStatus(MessageStatus.INIT.getCode());
            MessageDTO messageDTO = new MessageDTO();
            BeanUtils.copyProperties(messageDO, messageDTO);
            generateTask(messageDTO);
        } catch (Exception e) {
            log.error("消息管理重试处理异常：{}", e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void taskRetry(Long messageTaskId) {
        try {
            MessageTaskDO messageTaskDO = messageTaskMapper.selectByPrimaryKey(messageTaskId);
            if (Objects.isNull(messageTaskDO)) {
                log.warn("消息任务id不存在");
                return;
            }
            messageTaskDO.setStatus(MessageTaskStatus.INIT.getCode());
            MessageTaskDTO messageTaskDTO = new MessageTaskDTO();
            BeanUtils.copyProperties(messageTaskDO, messageTaskDTO);
            executeTask(messageTaskDTO);
        } catch (Exception e) {
            log.error("消息管理重试处理异常：{}", e.getMessage(), e);
        }
    }

    @Override
    @PageSelect
    public ListVO<MessageDTO> paging(MessageSearch search) {
        Example example = new Example(MessageDO.class);
        Example.Criteria criteria = example.createCriteria();
        if (!StringUtils.isEmpty(search.getType())) {
            criteria.andEqualTo("type", search.getType());
        }
        if (!StringUtils.isEmpty(search.getBusinessCode())) {
            List<String> businessCodeList = Splitter.on(",").splitToList(search.getBusinessCode());
            criteria.andIn("businessCode", businessCodeList);
        }
        if (search.getStatus() != null && search.getStatus() > 0) {
            criteria.andEqualTo("status", search.getStatus());
        }
        if (!LongUtil.isNone(search.getCreateTimeFrom())) {
            criteria.andGreaterThan("createTime", new DateTime(search.getCreateTimeFrom()).toString("yyyy-MM-dd HH:mm:ss"));
        }
        if (!LongUtil.isNone(search.getCreateTimeFrom())) {
            criteria.andLessThan("createTime", new DateTime(search.getCreateTimeTo()).toString("yyyy-MM-dd HH:mm:ss"));
        }
        if (Objects.nonNull(search.getId())) {
            criteria.andEqualTo("id", search.getId());
        }
        example.orderBy("id").desc();
        List<MessageDO> list = messageMapper.selectByExample(example);
        ListVO<MessageDTO> result = new ListVO<>();
        List<MessageDTO> dtoList = list.stream().map(d -> {
            MessageDTO dto = new MessageDTO();
            BeanUtils.copyProperties(d, dto);
            return dto;
        }).collect(Collectors.toList());
        result.setDataList(dtoList);
        // 分页
        PageInfo<MessageDO> pageInfo = new PageInfo(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    @Override
    public void regenerateReqData(MessageTaskDTO messageTaskDTO) {
        MessageDTO messageDTO = this.findById(messageTaskDTO.getMessageId());
        MessageProcess process = registry.getProcess(MessageType.getEnum(messageTaskDTO.getType()));
        if (process == null) {
            log.warn("[op:MessageServiceImpl-regenerateReqData] process is null, type={}", MessageType.getEnum(messageTaskDTO.getType()));
            return;
        }

        if (messageDTO.getMultiTask()) {
            List<RequestDataDTO> requestDataDTOS = process.buildMultiSendInfo(messageDTO);
            if (CollectionUtils.isEmpty(requestDataDTOS)) {
                log.warn("业务单号: {} , 消息任务发送数据组装后为空 , 需要核查", messageDTO.getBusinessCode());
                return;
            }
            for (RequestDataDTO requestDataDTO : requestDataDTOS) {
                if (Objects.equals(requestDataDTO.getTaskType(), messageTaskDTO.getTaskType())) {
                    messageTaskDTO.setRequestData(JSON.toJSONString(requestDataDTO));
                    break;
                }
            }
        } else {
            Map<String, Object> sendMap = process.buildSendInfo(messageDTO);
            if (CollectionUtils.isEmpty(sendMap)) {
                log.warn("业务单号: {} , 消息任务发送数据组装后为空 , 需要核查", messageDTO.getBusinessCode());
                return;
            }
            messageTaskDTO.setRequestData(JSON.toJSONString(sendMap));
        }
    }

    @Override
    public void manualCreateMessage(CreateMessageReqVO reqVO) {
        String validRes = ValidatorUtils.doValidator(validator, reqVO);
        if (StringUtil.isNotBlank(validRes)) {
            log.warn("消息管理-新增消息 参数校验-{}", validRes);
            throw new ArgsInvalidException(validRes);
        }
        // todo 这块消息回传需要整体考虑消息内容的取值，因为各个不同的消息类型，消息内容取值不同，需要做统一考虑
        MessageType typeEnums = MessageType.getEnum(reqVO.getMessageType());
        if (typeEnums == null) {
            throw new ArgsInvalidException("消息类型不存在");
        }
        if (!Objects.isNull(typeEnums) && MessageType.TAOTIAN_REPORT == typeEnums) {
            this.taotianMessgeSend(reqVO);
            return;
        }
//        if (MessageType.MULTI_TASK_TEST == typeEnums) {
//            this.multiTaskSend(reqVO);
//            return;
//        }
        MessageContentEnums contentEnums = MessageContentEnums.getEnums(reqVO.getMessageContentType());
        if (Objects.isNull(contentEnums) || !contentEnums.getMessageType().equals(typeEnums)) {
            log.warn("消息管理-新增消息 消息类型[{}],消息内容[{}]不存在", reqVO.getMessageType(), reqVO.getMessageContentType());
            throw new ArgsInvalidException("消息内容未设定");
        }
        List<String> businessCodeList = Splitter.on(",").splitToList(reqVO.getBusinessCodes());
        List<String> tagList;
        String activeData;
        for (String businessCode : businessCodeList) {
            OrderDTO orderDTO = orderService.findByDeclareOrderNo(businessCode);
            if (Objects.isNull(orderDTO)) {
                log.warn("业务编号[{}]无申报单记录", businessCode);
                throw new ArgsInvalidException(businessCode + "无申报单记录");
            }
            OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
            switch (contentEnums) {
                case ORDER_RESULT_DECLARE_START: //订单申报事件节点 + 开始申报
                    tagList = Lists.newArrayList("PLAT-PANGU", "CHANNEL-" + orderExtra.getSubmit().getChannel());
                    activeData = JSON.toJSONString(new OrderEventActive(orderDTO.getId(), "start", "开始申报"));
                    break;
                case JD_INVENTORY_CUSTOMS_CALLBACK:
                    tagList = Lists.newArrayList("PLAT-PANGU", "CHANNEL-" + orderExtra.getSubmit().getChannel());
                    activeData = JSON.toJSONString(new OrderActiveInfo(orderDTO.getId(), orderDTO.getCustomsInventorySn()));
                    break;
                default:
                    log.warn("未知消息内容[{}]，无法新增消息", contentEnums.getDesc());
                    throw new ArgsInvalidException("未知消息内容[" + contentEnums.getDesc() + "]，无法新增消息");
            }
            log.info("消息管理-新增消息 businessCode={},activeData={}, tagList={}", businessCode, activeData, tagList);
            this.createMessage(typeEnums, tagList, businessCode, activeData, "");
        }
    }

    @Override
    public List<MessageDTO> findByStatusAndType(Integer status, String type) {
        if (Objects.isNull(status) && StringUtil.isEmpty(type)) {
            return new ArrayList<>();
        }
        Example example = new Example(MessageDO.class);
        Example.Criteria criteria = example.createCriteria();
        if (Objects.nonNull(status)) {
            criteria.andEqualTo("status", status);
        }
        if (StringUtil.isNotEmpty(type)) {
            criteria.andEqualTo("type", type);
        }
        List<MessageDO> messageDOS = messageMapper.selectByExample(example);
        return ConvertUtil.listConvert(messageDOS, MessageDTO.class);
    }

    private static ExecutorService handleTasksThread = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors() * 2);

    @Override
    public void listInitTaskAndExecute(String type, Long start, Long end, String limit) {
        Example example = new Example(MessageTaskDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andBetween("createTime", new Date(start), new Date(end));
        criteria.andEqualTo("status", MessageTaskStatus.INIT.getCode());
        criteria.andEqualTo("type", type);
        example.setOrderByClause("id asc limit " + limit);
        List<MessageTaskDO> list = messageTaskMapper.selectByExample(example);
        int size = list.size();
        log.info("listInitTaskAndExecute size={}", size);
        int index = 1;
        Long tenantId = SimpleTenantHelper.getTenantId();
        for (MessageTaskDO messageTaskDO : list) {
            log.info("listInitTaskAndExecute declareOrderNo={} executeTask={} / total={}", messageTaskDO.getBusinessCode(), index++, size);
            MessageTaskDTO messageTaskDTO = ConvertUtil.beanConvert(messageTaskDO, MessageTaskDTO.class);
            handleTasksThread.submit(() -> {
                SimpleTenantHelper.setTenantId(tenantId);
                this.executeTask(messageTaskDTO);
            });
        }
    }

//    private void multiTaskSend(CreateMessageReqVO reqVO) {
//        List<MessageType.MessageTaskType> enumsByMessageType = MessageType.getTaskTypeByMessageType(reqVO.getMessageType());
//        if (CollectionUtils.isEmpty(enumsByMessageType)) {
//            log.warn("消息管理-新增消息 消息类型[{}]不存在", reqVO.getMessageType());
//            throw new ArgsInvalidException("淘天消息内容类型: " + reqVO.getMessageContentType() + "不存在");
//        }
//        WorkOrderStatueEnum workOrderStatueEnum1 = WorkOrderStatueEnum.CUSTOMS_CLEAR_ORDER_PASS;
//        WorkOrderStatueEnum workOrderStatueEnum2 = WorkOrderStatueEnum.M_ICUS_START;
//        WorkOrderStatueEnum workOrderStatueEnum3 = WorkOrderStatueEnum.EX_CUS_START;
//
//        List<String> businessCodeList = Splitter.on(",").splitToList(reqVO.getBusinessCodes());
//        for (String businessCode : businessCodeList) {
//            InventoryOrderInfoDTO inventoryOrderInfoDTO = inventoryOrderInfoService.findBySn(businessCode);
//            if (Objects.isNull(inventoryOrderInfoDTO)) {
//                log.warn("消息管理-新增消息 消息类型[{}]业务单号[{}]不存在", workOrderStatueEnum1.getDesc(), businessCode);
//                throw new ArgsInvalidException("清关单业务单号：" + businessCode + " 不存在(QG单号)");
//            }
//            InventoryOrderBusinessEnum businessEnum = InventoryOrderBusinessEnum.getEnum(inventoryOrderInfoDTO.getInveBusinessType());

    /// /            boolean match = WorkOrderStatueEnum.checkBusinessReportStatusMatch(businessEnum, workOrderStatueEnum);
    /// /            if (!match) {
    /// /                log.warn("消息管理-新增消息 消息类型[{}]和业务单号[{}]不匹配", workOrderStatueEnum.getDesc(), businessCode);
    /// /                throw new ArgsInvalidException("消息类型和业务单号对应的业务类型不匹配");
    /// /            }
//            TTOrderProcessStatusReport mock1 = this.getTTOrderProcessStatusReportMock(inventoryOrderInfoDTO, workOrderStatueEnum1.getCode());
//            TTOrderProcessStatusReport mock2 = this.getTTOrderProcessStatusReportMock(inventoryOrderInfoDTO, workOrderStatueEnum2.getCode());
//            TTOrderProcessStatusReport mock3 = this.getTTOrderProcessStatusReportMock(inventoryOrderInfoDTO, workOrderStatueEnum3.getCode());
//
//            String tag = "CHANNEL-" + inventoryOrderInfoDTO.getChannel();
//            MultiActiveDataDTO multiActiveData = MultiActiveDataBuilder.getMultiActiveData(false,
//                    Lists.newArrayList(tag, tag, tag),
//                    enumsByMessageType,
//                    3, mock1, mock2, mock3);
//            this.createMultiTaskMessage(MessageType.MULTI_TASK_TEST, Lists.newArrayList(tag), businessCode,  multiActiveData, 1);
//        }
//    }
    private TTOrderProcessStatusReport getTTOrderProcessStatusReportMock(InventoryOrderInfoDTO inventoryOrderInfoDTO, String status) {
        String upstreamOrigMsg = inventoryOrderInfoDTO.getUpstreamOrigMsg();
        TTClearanceEntryorderCreateDTO createDTO = JSON.parseObject(upstreamOrigMsg, TTClearanceEntryorderCreateDTO.class);
        if (Objects.isNull(createDTO)) {
            log.error("getTTOrderProcessStatusReport createDTO is null");
            return null;
        }
        TTOrderProcessStatusReport report = new TTOrderProcessStatusReport();
        report.setOwnerCode(createDTO.getOwnerCode());
        report.setResourceCode(createDTO.getResourceCode());
        report.setOrderCode(inventoryOrderInfoDTO.getUpstreamNo());
        report.setOrderType(createDTO.getOrderType());
        report.setOuterOrderCode(inventoryOrderInfoDTO.getInveCustomsSn());
        report.setOutBizCode(UUID.randomUUID().toString().replaceAll("-", ""));
        report.setBusinessValue(createDTO.getShopId());
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        report.setOperateTime(simpleDateFormat.format(new Date()));
        report.setStatus(String.valueOf(status));
        return report;
    }

    private void taotianMessgeSend(CreateMessageReqVO reqVO) {

        WorkOrderStatueEnum workOrderStatueEnum = WorkOrderStatueEnum.getEnumsByCode(reqVO.getMessageContentType());
        if (Objects.isNull(workOrderStatueEnum)) {
            log.warn("消息管理-新增消息 消息类型[{}]不存在", reqVO.getMessageType());
            throw new ArgsInvalidException("淘天消息内容类型: " + reqVO.getMessageContentType() + "不存在");
        }

        List<String> businessCodeList = Splitter.on(",").splitToList(reqVO.getBusinessCodes());
        for (String businessCode : businessCodeList) {
            InventoryOrderInfoDTO inventoryOrderInfoDTO = inventoryOrderInfoService.findBySn(businessCode);
            if (Objects.isNull(inventoryOrderInfoDTO)) {
                log.warn("消息管理-新增消息 消息类型[{}]业务单号[{}]不存在", workOrderStatueEnum.getDesc(), businessCode);
                throw new ArgsInvalidException("清关单业务单号：" + businessCode + " 不存在(QG单号)");
            }
            InventoryOrderBusinessEnum businessEnum = InventoryOrderBusinessEnum.getEnum(inventoryOrderInfoDTO.getInveBusinessType());
            boolean match = WorkOrderStatueEnum.checkBusinessReportStatusMatch(businessEnum, workOrderStatueEnum);
            if (!match) {
                log.warn("消息管理-新增消息 消息类型[{}]和业务单号[{}]不匹配", workOrderStatueEnum.getDesc(), businessCode);
                throw new ArgsInvalidException("消息类型和业务单号对应的业务类型不匹配");
            }
            taotianOrderService.buildReportMessage(inventoryOrderInfoDTO, workOrderStatueEnum.getCode());
        }
    }

    /**
     * @param bottom 底数
     * @param var    指数
     * @return
     */
    private static String expString(Integer bottom, Integer var) {
        int i = 0;
        String str = "1";
        while (i < var) {
            str = multiString(bottom, str);
            i++;
        }
        return str;
    }

    /**
     * @param bottom 底数
     * @param str    指数
     * @return
     */
    private static String multiString(Integer bottom, String str) {
        char[] c = str.toCharArray();
        //结果可能和原数组一样长或者比原数组长度长1， 2的3次幂是8,2的4次幂就是16了
        char[] result = new char[c.length + 1];
        //temp用来表示是否有进位
        int temp = 0;
        for (int i = c.length - 1; i >= 0; i--) {//从后向前遍历
            //48 是 0对应的ASCII码
            int j = (int) c[i] - 48;
            int all = j * bottom;
            //为什么是i+1呢？，因为最后可能会有进位
            result[i + 1] = ((char) ((all % 10 + temp) % 10 + 48));
            temp = (all + temp) / 10;
        }
        if (temp != 0) {
            //最后如果有进位
            result[0] = (char) (temp + 48);
        }
        //去掉不需要的0
        return String.valueOf(result).replaceAll("\u0000", "");
    }
}
