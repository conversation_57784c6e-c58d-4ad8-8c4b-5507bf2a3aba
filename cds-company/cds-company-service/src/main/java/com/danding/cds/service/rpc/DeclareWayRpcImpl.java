package com.danding.cds.service.rpc;

import com.alibaba.fastjson.JSON;
import com.danding.cds.bean.dto.DeclareWayDTO;
import com.danding.cds.bean.dto.DeclareWayPropertyEnum;
import com.danding.cds.bean.vo.req.DeclareWaySearch;
import com.danding.cds.bean.vo.req.DeclareWaySubmitReqVO;
import com.danding.cds.bean.vo.res.DeclareWayResVo;
import com.danding.cds.bean.vo.res.DeclareWayVO;
import com.danding.cds.common.enums.CustomsTransferNodeEnums;
import com.danding.cds.common.enums.DeclareEnum;
import com.danding.cds.common.model.IdParam;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.common.utils.ValidatorUtils;
import com.danding.cds.mapper.DeclareWayMapper;
import com.danding.cds.rpc.DeclareWayRpc;
import com.danding.cds.service.DeclareWayService;
import com.danding.cds.service.base.DeclareWayBaseService;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.api.exceptions.rpcException.ArgsInvalidException;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.validation.Validator;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @menu: 申报方式DeclareWay
 * @program: cds-center
 * @description: declare rpc impl
 * @author: 潘本乐（Belep）
 * @create: 2021-11-17 16:22
 **/
@Slf4j
@DubboService
public class DeclareWayRpcImpl implements DeclareWayRpc {
    @Autowired
    private DeclareWayService declareWayService;
    @Autowired
    private DeclareWayBaseService declareWayBaseService;
    @Autowired
    private Validator validator;
    @Autowired
    private DeclareWayMapper declareWayMapper;

    @Override
    public List<SelectOptionVO<Long>> listWithName() {
        List<DeclareWayDTO> dtoList = declareWayService.listAll();
        List<SelectOptionVO<Long>> optionVOList = new ArrayList<>();
        for (DeclareWayDTO dto : dtoList) {
            SelectOptionVO<Long> vo = new SelectOptionVO<>();
            vo.setId(dto.getId());
            vo.setName(dto.getName());
            optionVOList.add(vo);
        }
        return optionVOList;
    }

    @Override
    public List<SelectOptionVO<Long>> listWithCode() {
        List<DeclareWayDTO> dtoList = declareWayService.listAll();
        List<SelectOptionVO<Long>> optionVOList = new ArrayList<>();
        for (DeclareWayDTO dto : dtoList) {
            SelectOptionVO<Long> vo = new SelectOptionVO<>();
            vo.setId(dto.getId());
            vo.setName(dto.getCode());
            optionVOList.add(vo);
        }
        return optionVOList;
    }

    @Override
    public List<SelectOptionVO<String>> listDeclareEnum() {
        List<SelectOptionVO<String>> result = new ArrayList<>();
        for (DeclareEnum value : DeclareEnum.values()) {
            SelectOptionVO selectOptionVO = new SelectOptionVO();
            selectOptionVO.setId(value.getType());
            selectOptionVO.setName(value.getDesc());
            result.add(selectOptionVO);
        }
        return result;
    }

    /**
     * 分页查询
     *
     * @param search
     * @return
     * @describe: 根据搜索条件返回结果
     */
    @Override
    public ListVO<DeclareWayVO> paging(DeclareWaySearch search) {
        ListVO<DeclareWayDTO> paging = declareWayService.paging(search);
        ListVO<DeclareWayVO> result = new ListVO<>();
        result.setPage(paging.getPage());
        List<DeclareWayDTO> declareWayDTOS = paging.getDataList();
        List<DeclareWayVO> voList = declareWayDTOS.stream().map(d -> {
            DeclareWayVO declareWayVO = new DeclareWayVO();
            if (Objects.nonNull(d.getProperty())) {
                DeclareWayPropertyEnum wayPropertyEnum = DeclareWayPropertyEnum.getEnum(d.getProperty());
                declareWayVO.setProperty(wayPropertyEnum.getDesc());
            }
            if (Objects.nonNull(d.getCustomsTransferNode())) {
                CustomsTransferNodeEnums nodeEnums = CustomsTransferNodeEnums.getEnums(d.getCustomsTransferNode());
                declareWayVO.setCustomsTransferNodeName(nodeEnums.getName());
            }
            BeanUtils.copyProperties(d, declareWayVO);
            return declareWayVO;
        }).collect(Collectors.toList());
        result.setDataList(voList);
        return result;
    }

    /**
     * 根据类型和属性来过滤 都不传默认取所有
     * type:{@link com.danding.cds.common.enums.DeclareEnum}
     * property:{@link com.danding.cds.bean.dto.DeclareWayPropertyEnum}
     *
     * @param type
     * @param property
     * @return
     * @Describe: 下拉列表
     */
    @Override
    public List<DeclareWayVO> listByType(String type, String property) {
        List<DeclareWayDTO> declareWayDTOList = declareWayBaseService.listByType(type, property);
        List<DeclareWayVO> declareWayVOList = declareWayDTOList.stream().map(d -> {
            DeclareWayVO declareWayVO = new DeclareWayVO();
            BeanUtils.copyProperties(d, declareWayVO);
            return declareWayVO;
        }).collect(Collectors.toList());
        return declareWayVOList;
    }

    @Override
    public List<DeclareWayVO> listByTypeAndNode(String type, Integer transNode) {
        List<DeclareWayDTO> declareWayDTOList = declareWayBaseService.listByTypeAndNode(type, transNode);
        List<DeclareWayVO> declareWayVOList = declareWayDTOList.stream().map(d -> {
            DeclareWayVO declareWayVO = new DeclareWayVO();
            BeanUtils.copyProperties(d, declareWayVO);
            return declareWayVO;
        }).collect(Collectors.toList());
        return declareWayVOList;
    }


    /**
     * 新增declareWay
     *
     * @param submit
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(DeclareWaySubmitReqVO submit) {
        log.info("saveOrUpdate submit={}", JSON.toJSONString(submit));
        String validatorResult = ValidatorUtils.doValidator(validator, submit);
        if (!StringUtils.isEmpty(validatorResult)) {
            throw new ArgsInvalidException(validatorResult);
        }
        declareWayBaseService.checkDuplicate(submit.getId(), submit.getCode(), submit.getDeclareImpl());
        DeclareWayDTO dto = new DeclareWayDTO();
        BeanUtils.copyProperties(submit, dto);
        if (Objects.isNull(submit.getId())) {
            declareWayBaseService.insert(dto);
        } else {
            declareWayBaseService.update(dto);
        }
    }

    @Override
    public void changeEnable(IdParam idParam) {
        declareWayService.changeEnable(idParam.getId());
    }

    @Override
    public void delete(IdParam idParam) {
        log.info("DeclareWayRpcImpl delete id={}", idParam.getId());
        declareWayBaseService.deleteById(idParam.getId());
    }

    @Override
    public DeclareWayResVo findByCode(String code) {
        DeclareWayResVo declareWayResVo = new DeclareWayResVo();
        if (StringUtils.isEmpty(code)) {
            return declareWayResVo;
        }
        DeclareWayDTO byCode = declareWayBaseService.findByCode(code);
        if (byCode != null) {
            declareWayResVo.setCode(byCode.getCode());
            declareWayResVo.setName(byCode.getName());
            declareWayResVo.setType(byCode.getType());
            declareWayResVo.setDeclareImpl(byCode.getDeclareImpl());
            declareWayResVo.setEnable(byCode.getEnable());
        }
        return declareWayResVo;
    }

    @Override
    public List<DeclareWayResVo> findByCodes(List<String> codeList) {
        List<DeclareWayResVo> wayResVoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(codeList)) {
            return wayResVoList;
        }
        List<DeclareWayDTO> wayDtoList = declareWayBaseService.findByCodes(codeList);
        if (!CollectionUtils.isEmpty(wayDtoList)) {
            wayResVoList = wayDtoList.stream().map(k -> {
                DeclareWayResVo declareWayResVo = new DeclareWayResVo();
                declareWayResVo.setCode(k.getCode());
                declareWayResVo.setName(k.getName());
                declareWayResVo.setType(k.getType());
                declareWayResVo.setDeclareImpl(k.getDeclareImpl());
                declareWayResVo.setEnable(k.getEnable());
                return declareWayResVo;
            }).collect(Collectors.toList());
        }
        return wayResVoList;
    }
}
