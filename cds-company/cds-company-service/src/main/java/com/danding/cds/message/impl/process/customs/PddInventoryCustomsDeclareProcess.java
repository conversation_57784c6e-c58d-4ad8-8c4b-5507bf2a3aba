package com.danding.cds.message.impl.process.customs;

import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.bean.enums.OrderCallbackPlatformReceiptEnums;
import com.danding.cds.c.api.rpc.OrderCRpc;
import com.danding.cds.c.api.rpc.PddCustomsSendDataReceiptService;
import com.danding.cds.message.api.dto.MessageDTO;
import com.danding.cds.message.api.dto.MessageTaskDTO;
import com.danding.cds.message.api.dto.PlatformCustomsRequestDataDTO;
import com.danding.cds.message.api.enums.MessageType;
import com.danding.cds.message.impl.process.MessageExecuteResult;
import com.danding.cds.message.impl.process.MessageProcess;
import com.danding.cds.message.impl.process.MessageRegistry;
import com.danding.component.http.SaasHttpConstant;
import com.danding.core.tenant.SimpleTenantHelper;
import com.github.kevinsawicki.http.HttpRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@RefreshScope
@Slf4j
public class PddInventoryCustomsDeclareProcess extends MessageProcess {

    @DubboReference
    private PddCustomsSendDataReceiptService pddCustomsSendDataReceiptService;

    @DubboReference
    private OrderCRpc orderCRpc;

    @Value("${pdd.send.data.receipt.uri:/pdd/controller/declare/sendData/receipt}")
    private String PDD_SEND_DATA_RECEIPT_URI;
    @Value("${pdd.oversea.declaration.fail.notify.uri:/pdd/controller/fail/notify}")
    private String PDD_OVERSEA_DECLARATION_FAIL_NOTIFY_URI;

    public PddInventoryCustomsDeclareProcess(MessageRegistry registry) {
        super(registry);
    }

    @Override
    public MessageType getType() {
        return MessageType.PDD_PLATFORM_INVENTORY_CUSTOMS_DECLARE;
    }

    @Override
    public Map<String, Object> buildSendInfo(MessageDTO messageDTO) {
        PlatformCustomsRequestDataDTO requestDataDTO = new PlatformCustomsRequestDataDTO();
        requestDataDTO.setNotifyMethod(PDD_SEND_DATA_RECEIPT_URI);
        requestDataDTO.setRequestDataStr(pddCustomsSendDataReceiptService.buildCebRequestDataRequestStr(messageDTO.getActiveData(), "CEB621"));
        return objToMap(requestDataDTO);
    }



    @Override
    public MessageExecuteResult execute(MessageTaskDTO messageTaskDTO) {
        MessageExecuteResult result = new MessageExecuteResult();
        log.info("消息回调 业务编码: {} ,请求数据信息: {}", messageTaskDTO.getBusinessCode(), JSON.toJSONString(messageTaskDTO));
        try {
            PlatformCustomsRequestDataDTO requestDataDTO = JSON.parseObject(messageTaskDTO.getRequestData(), PlatformCustomsRequestDataDTO.class);
            String url = messageTaskDTO.getNotifyUrl() + requestDataDTO.getNotifyMethod();
            result = doPostRequest(url, messageTaskDTO.getRequestData());
            if (result.getSuccess()) {
                orderCRpc.updateCallbackPlatformReceiptFlag(messageTaskDTO.getBusinessCode(), OrderCallbackPlatformReceiptEnums.TYPE.CEB621);
            }
        } catch (Exception e) {
            result.setSuccess(false);
            result.setResponseMsg("CCS内部系统异常，" + e.getMessage());
            log.error("业务编码 : {} ,消息回调处理异常：{}", messageTaskDTO.getBusinessCode(), e.getMessage(), e);
        }
        return result;
    }

    private MessageExecuteResult doPostRequest(String url, String request) {
        MessageExecuteResult result = new MessageExecuteResult();
        log.info("拼多多报关数据回执上报 url={} param={}", url, JSON.toJSONString(request));
        HttpRequest httpRequest = doPost(JSON.toJSONString(request), url);
        String responseBody = httpRequest.body();
        int code = httpRequest.code();
        log.info("拼多多报关数据回执上报 Response-Body:{} HttpRequest-code: {}", JSON.toJSONString(responseBody), code);
        if (httpRequest.ok()) {
            this.processResultBody(result, responseBody);
            result.setResponseMsg(responseBody);
        } else {
            result.setSuccess(false);
            result.setResponseMsg("网络异常，状态码，" + httpRequest.code());
        }
        return result;
    }

    private static HttpRequest doPost(String request, String pddUrl) {
        return HttpRequest.post(pddUrl)
                .header(SaasHttpConstant.HEADER_TENANT_ID, SimpleTenantHelper.getTenantIdStr())
                .header("Content-Type", HttpRequest.CONTENT_TYPE_JSON)
                .readTimeout(30000)
                .connectTimeout(15000)
                .send(request);
    }
}
