package com.danding.cds.service;

import com.danding.cds.es.OrderSnEsDao;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @description:
 * @date 2022/12/12 11:39
 */
@DubboService
public class OrderSnTenantServiceImpl implements OrderSnTenantService {

    @Autowired
    private OrderSnEsDao orderSnEsDao;


    @Override
    public Long getTenantIdBySn(String sn) {
        return orderSnEsDao.getTenantIdBySn(sn);
    }

    @Override
    public Long getTenantIdByPreNo(String preNo) {
        return orderSnEsDao.getTenantIdByPreNo(preNo);
    }

    @Override
    public Long getTenantIdByRealNo(String realOrderNo) {
        return orderSnEsDao.getTenantIdByRealNo(realOrderNo);
    }

    @Override
    public void saveMapping(String sn, Long tenantId) {
        orderSnEsDao.saveMapping(sn, tenantId);
    }

    @Override
    public void savePreNo(String sn, String preNo) throws Exception {
        orderSnEsDao.savePreNo(sn, preNo);
    }

    @Override
    public void saveRealNo(String sn, String realOrderNo) throws Exception {
        orderSnEsDao.saveRealNo(sn, realOrderNo);
    }
}
