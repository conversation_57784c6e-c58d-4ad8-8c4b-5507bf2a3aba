package com.danding.cds.service.handler;

import com.danding.cds.bean.dto.CallbackLogDTO;
import com.danding.cds.c.api.rpc.TenantIdFindCRpc;
import com.danding.cds.callback.api.enums.CallbackTypeEnums;
import com.danding.cds.common.config.OrderCCallConfig;
import com.danding.cds.declare.ceb.domain.ceb624.CEB624Message;
import com.danding.cds.declare.ceb.domain.ceb624.InvtCancelReturn;
import com.danding.cds.declare.ceb.domain.ceb626.CEB626Message;
import com.danding.cds.declare.ceb.domain.ceb626.InvtRefundReturn;
import com.danding.cds.declare.ceb.internal.utils.XMLUtil;
import com.danding.cds.http.saas.annotation.TenantHttpMethod;
import com.danding.cds.http.saas.handler.TenantParser;
import com.danding.cds.v2.api.TenantIdFindRpc;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;


@Slf4j
@Component
public class CallBackLogTenantHandler extends TenantParser {

    @DubboReference
    private TenantIdFindRpc tenantIdFindRpc;
    @DubboReference
    private TenantIdFindCRpc tenantIdFindCRpc;
    @Resource
    private OrderCCallConfig orderCCallConfig;

    @Override
    public Long parseTenantId(TenantHttpMethod tenantHttpMethod, Object[] param) {
        CallbackLogDTO callbackLogDTO = (CallbackLogDTO) param[0];
        if (Objects.isNull(callbackLogDTO)) {
            return null;
        }
        String callbackType = callbackLogDTO.getCallbackType();
        CallbackTypeEnums callbackTypeEnums = CallbackTypeEnums.getEnum(callbackType);
        //与CustomsCallbackLogConsumer populateBusinessCode 一一对应
        switch (callbackTypeEnums) {
            case ORDER:
                if (orderCCallConfig.isOrderCCall(this.getClass())) {
                    return tenantIdFindCRpc.getTenantId(null, callbackLogDTO.getBusinessCode(), null, null);
                } else {
                    return tenantIdFindRpc.getTenantId(null, callbackLogDTO.getBusinessCode(), null, null);
                }
            case TAX:
            case TAXSTATUS:
                if (orderCCallConfig.isOrderCCall(this.getClass())) {
                    if (Objects.nonNull(callbackLogDTO.getTenantQueryCode()) && Objects.nonNull(callbackLogDTO.getTenantQueryCodeType())) {
                        if (Objects.equals(callbackLogDTO.getTenantQueryCodeType(), "logisticsNo")) {
                            return tenantIdFindCRpc.getTenantId(null, null, callbackLogDTO.getTenantQueryCode(), null);
                        }
                    }
                    return tenantIdFindCRpc.getTenantId(null, null, null, callbackLogDTO.getBusinessCode());
                } else {
                    return tenantIdFindRpc.getTenantId(null, null, null, callbackLogDTO.getBusinessCode());
                }
            case INVENTORY:
                if (Objects.nonNull(callbackLogDTO.getBusinessCode())) {
                    if (orderCCallConfig.isOrderCCall(this.getClass())) {
                        return tenantIdFindCRpc.getTenantIdByInventory(callbackLogDTO.getBusinessCode());
                    } else {
                        return tenantIdFindRpc.getTenantIdByInventory(callbackLogDTO.getBusinessCode());
                    }
                }
            case SHIPMENT:
                if (orderCCallConfig.isOrderCCall(this.getClass())) {
                    return tenantIdFindCRpc.getTenantId(null, null, callbackLogDTO.getBusinessCode(), null);
                } else {
                    return tenantIdFindRpc.getTenantId(null, null, callbackLogDTO.getBusinessCode(), null);
                }
            case CANCEL:
                try {
                    CEB624Message cancelResponse = XMLUtil.converyToJavaBean(callbackLogDTO.getContent().replace("xmlns=\"http://www.chinaport.gov.cn/ceb\"", ""), CEB624Message.class);
                    List<InvtCancelReturn> invtCancelReturn = cancelResponse.getInvtCancelReturn();
                    InvtCancelReturn cancelReturn = invtCancelReturn.get(0);
                    if (orderCCallConfig.isOrderCCall(this.getClass())) {
                        return tenantIdFindCRpc.getTenantId(null, null, null, cancelReturn.getInvtNo());
                    } else {
                        return tenantIdFindRpc.getTenantId(null, null, null, cancelReturn.getInvtNo());
                    }
                } catch (Exception e) {
                    log.error("CallBackLogTenantHandler 获取撤单租户失败");
                }
                return 1001L;
            case REFUND:
                try {
                    CEB626Message refundResponse = XMLUtil.converyToJavaBean(callbackLogDTO.getContent().replace("xmlns=\"http://www.chinaport.gov.cn/ceb\"", ""), CEB626Message.class);
                    List<InvtRefundReturn> invtRefundReturn = refundResponse.getInvtRefundReturn();
                    InvtRefundReturn refundReturn = invtRefundReturn.get(0);
                    if (orderCCallConfig.isOrderCCall(this.getClass())) {
                        return tenantIdFindCRpc.getTenantId(null, null, null, refundReturn.getInvtNo());
                    } else {
                        return tenantIdFindRpc.getTenantId(null, null, null, refundReturn.getInvtNo());
                    }
                } catch (Exception e) {
                    log.error("CallBackLogTenantHandler 获取退货租户失败");
                }
                return 1001L;
            default:
                break;
        }
        return 1001L;
    }
}
