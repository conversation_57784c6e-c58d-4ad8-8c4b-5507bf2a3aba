package com.danding.cds.customs.dictionary.impl.mapper;

import com.danding.cds.customs.dictionary.impl.entity.CustomsDictionaryDO;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import tk.mybatis.mapper.additional.insert.InsertListMapper;
import tk.mybatis.mapper.common.Mapper;

public interface CustomsDictionaryMapper extends Mapper<CustomsDictionaryDO>, InsertListMapper<CustomsDictionaryDO>, BatchUpdateMapper<CustomsDictionaryDO>, AggregationPlusMapper<CustomsDictionaryDO> {
}

