package com.danding.cds.callback.impl.entity;

import com.danding.cds.common.model.BaseDO;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

@Deprecated
@Table(name = "`ccs_callback_record`" )
@Getter
@Setter
public class CallbackRecordDO extends BaseDO {
    /**
     * 回传地址
     */
    @Column(name = "notify_url")
    private String notifyUrl;

    /**
     * 业务编号，用于查询关联的回传记录
     */
    @Column(name = "business_code")
    private String businessCode;

    /**
     * 通知类型
     */
    private String type;

    /**
     * 通知状态,1:待回传，10:回传成功，-10:回传失败
     */
    private Integer status;

    /**
     * 通知次数
     */
    private Integer count;

    /**
     * 触发回传的初始参数
     */
    @Column(name = "active_data")
    private String activeData;

    /**
     * 请求数据
     */
    @Column(name = "request_data")
    private String requestData;

    /**
     * 响应数据
     */
    @Column(name = "response_data")
    private String responseData;

    /**
     * 最后回传时间
     */
    @Column(name = "last_notify_time")
    private Date lastNotifyTime;
}