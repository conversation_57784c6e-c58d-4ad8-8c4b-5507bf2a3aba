package com.danding.cds;

import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.context.annotation.Configuration;

import java.io.File;
import java.io.IOException;
import java.io.Serializable;
import java.lang.reflect.Modifier;
import java.net.URL;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class SerializableScanner implements Serializable {

    public static void main(String[] args) throws Exception {

        String packageName = "com.danding.cds"; // 替换为你的包名
        //接口类关键字过滤，过滤接口类
        String filter = "(VO|vo|Utils|Util|Service|Server|Impl|Handler|Application|Client|Consumer|Facade|controller|CustomCellWriteHandler|AresRedisContext|AresContext|CommonDataListener|AresTopicContext|impl|utils|Interface|Controller|filter|Remote|ExportExecutor|Strategy|AresAdminPortalApplication|BizTenantConfig|ReadClient)";
        List<Class<?>> serializableClasses = findSerializableClasses(packageName, filter);
        //没有Serializable的类列表
        for (Class<?> clazz : serializableClasses) {
            System.out.println(clazz.getName());
        }

        System.out.println("-----------------------------------------------------");
        System.out.println("使用正则替换 查找内容:");
        StringBuffer sb = new StringBuffer();
        sb.append("class (");
        for (Class<?> clazz : serializableClasses) {
            String[] temp = clazz.getName().split("\\.");
            sb.append(temp[temp.length - 1]);
            sb.append("|");
        }
        sb.replace(sb.length() - 1, sb.length(), "");
        System.out.println(sb.append(") \\{"));
        System.out.println("替换内容:");
        System.out.println(" $1 implements Serializable \\{");
    }

    public static List<Class<?>> findSerializableClasses(String packageName, String filter) throws Exception {
        List<Class<?>> serializableClasses = new ArrayList<>();

        Pattern r = Pattern.compile(filter);
        Enumeration<URL> urls = ClassLoader.getSystemClassLoader().getResources(packageName.replace(".", "/"));
        while (urls.hasMoreElements()) {
            URL url = urls.nextElement();

            if (url.getPath().contains(packageName.replaceAll("\\.", "/"))) {
                List<Class<?>> classes = findClasses(packageName, url.getPath());
                // System.out.println(urls.nextElement());
                for (Class<?> clazz : classes) {
                    Matcher mm = r.matcher(clazz.getName());
                    if (clazz.isInterface()) {
                        continue;
                    }
                    if (clazz.isAnnotationPresent(Aspect.class)) {
                        continue;
                    }
                    if (clazz.isAnnotationPresent(Builder.class)) {
                        continue;
                    }
                    if (Modifier.isAbstract(clazz.getModifiers())) {
                        continue;
                    }
                    if (clazz.isAnnotationPresent(Configuration.class)) {
                        continue;
                    }
                    if (clazz.isAnnotationPresent(Slf4j.class)) {
                        continue;
                    }

                    if (mm.find())
                        continue;
                    if (Serializable.class.isAssignableFrom(clazz) && !Modifier.isAbstract(clazz.getModifiers())) {

                    } else {
                        serializableClasses.add(clazz);
                    }
                }
            }
        }
            /*
        for (URL url : urls) {
            if (url.getPath().contains(packageName.replaceAll("\\.", "/"))) {
                List<Class<?>> classes = findClasses(url.getPath(), packageName);
                for (Class<?> clazz : classes) {
                    if (Serializable.class.isAssignableFrom(clazz) && !Modifier.isAbstract(clazz.getModifiers())) {
                        serializableClasses.add(clazz);
                    }
                }
            }
        }
        */

        return serializableClasses;
    }

    private static List<Class<?>> findClasses(String packageName, String packageDirName) throws ClassNotFoundException, IOException {
        List<Class<?>> classes = new ArrayList<>();
        File dir = new File(packageDirName);
        if (!dir.exists() || !dir.isDirectory()) {
            return classes;
        }
        File[] files = dir.listFiles();
        for (File file : files) {
            if (file.isDirectory()) {
                assert !file.getName().contains(".");
                classes.addAll(findClasses(packageName + "." + file.getName(), file.getAbsolutePath()));
            } else if (file.getName().endsWith(".class")) {
                classes.add(Class.forName(packageName + '.' + file.getName().substring(0, file.getName().length() - 6)));
            }
        }
        return classes;
    }

    public static List<Class> getClasses(String packageName) {
        List<Class> classes = new ArrayList<>();
        String path = ClassLoader.getSystemResource("").getPath() + packageName.replace(".", "/");
        File dir = new File(path);
        for (File file : dir.listFiles()) {
            if (file.isDirectory()) {
                classes.addAll(getClasses(packageName + "." + file.getName()));
            } else if (file.getName().endsWith(".class")) {
                String className = packageName + "." + file.getName().substring(0, file.getName().length() - 6);
                try {
                    Class clazz = Class.forName(className);
                    classes.add(clazz);
                } catch (ClassNotFoundException e) {
                    e.printStackTrace();
                }
            }
        }
        return classes;
    }


}