package com.danding.cds.customs.currency.impl.mapper;

import com.danding.cds.customs.currency.impl.entity.CustomsCurrencyDO;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import tk.mybatis.mapper.additional.insert.InsertListMapper;
import tk.mybatis.mapper.common.Mapper;

public interface CustomsCurrencyMapper extends Mapper<CustomsCurrencyDO>, InsertListMapper<CustomsCurrencyDO>, BatchUpdateMapper<CustomsCurrencyDO>, AggregationPlusMapper<CustomsCurrencyDO> {
}
