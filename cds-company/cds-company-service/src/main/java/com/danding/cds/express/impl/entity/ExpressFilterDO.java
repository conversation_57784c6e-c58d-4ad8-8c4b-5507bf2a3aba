package com.danding.cds.express.impl.entity;

import com.danding.cds.common.model.BaseDO;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;

@Table(name = "ccs_express_filter")
@Getter
@Setter
public class ExpressFilterDO  extends BaseDO {
    /**
     *相关快递ID
     */
    @Column(name = "ref_express_id")
    private Long refExpressId;
    /**
     *相关快递代码
     */
    @Column(name = "ref_express_code")
    private String refExpressCode;
    /**
     * 省
     */
    @Column(name = "prov")
    private String prov;
    /**
     * 市
     */
    @Column(name = "city")
    private String city;
    /**
     * 区
     */
    @Column(name = "area")
    private String area;
    /*
    替换的快递ID
     */
    @Column(name = "replace_express_id")
    private Long replaceExpressId;
    /**
     * 替换的快递代码
     */
    @Column(name = "replace_express_code")
    private String replaceExpressCode;
}
