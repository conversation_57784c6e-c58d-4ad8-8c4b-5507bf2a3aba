package com.danding.cds.message.impl.process;

import com.danding.cds.message.api.enums.MessageType;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class MessageRegistry {

    private Map<MessageType,MessageProcess> lib;

    private Map<MessageType.MessageTaskType, MessageProcess> executeLib;

    public MessageRegistry() {
        lib = new HashMap<>();
        executeLib = new HashMap<>();
    }

    public void register(MessageProcess process){
        lib.put(process.getType(),process);
        if (process.messageTaskType() != null) {
            executeLib.put(process.messageTaskType(), process);
        }
    }

    public MessageProcess getProcess(MessageType messageType){
        return lib.get(messageType);
    }

    public MessageProcess getProcess(MessageType.MessageTaskType messageTaskType){
        return executeLib.get(messageTaskType);
    }
}
