package com.danding.cds.message.impl.process.customs;

import com.alibaba.fastjson.JSON;
import com.danding.cds.message.api.dto.MessageDTO;
import com.danding.cds.message.api.enums.MessageType;
import com.danding.cds.message.api.process.OrderRefundWarehouseCallbackMessage;
import com.danding.cds.message.impl.process.MessageProcess;
import com.danding.cds.message.impl.process.MessageRegistry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@Slf4j
public class OrderCustomsRefundWarehouseCallbackProcess extends MessageProcess {

    public OrderCustomsRefundWarehouseCallbackProcess(MessageRegistry registry) {
        super(registry);
    }

    @Override
    public MessageType getType() {
        return MessageType.ORDER_REFUND_REFUND_WAREHOUSE;
    }

    @Override
    public Map<String, Object> buildSendInfo(MessageDTO messageDTO) {
        String activeData = messageDTO.getActiveData();
        OrderRefundWarehouseCallbackMessage orderActiveInfo = JSON.parseObject(activeData, OrderRefundWarehouseCallbackMessage.class);
        log.info("退货仓回执消息打印 - {}", JSON.toJSONString(orderActiveInfo));
        return objToMap(orderActiveInfo);
    }
}
