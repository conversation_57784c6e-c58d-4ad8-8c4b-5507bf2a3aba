package com.danding.cds.service.rpc;

import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.out.api.CompanyRpc;
import com.danding.cds.out.bean.vo.res.CompanyVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * @Author: yousx
 * @Date: 2024/04/07
 * @Description:
 */
@DubboService
public class CompanyRpcImpl implements CompanyRpc {

    @DubboReference
    private CompanyService companyService;

    @Override
    public List<CompanyVO> getCompany(String qualify) {
        if (StringUtils.isEmpty(qualify)) {
            throw new ArgsErrorException("筛选企业类型不能为空");
        }
        List<CompanyDTO> dataList = companyService.listWithQualify(qualify);
        return ConvertUtil.listConvert(dataList, CompanyVO.class);
    }
}
