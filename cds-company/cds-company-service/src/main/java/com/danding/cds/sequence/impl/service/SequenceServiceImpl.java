package com.danding.cds.sequence.impl.service;

import com.danding.cds.es.OrderSnEsDao;
import com.danding.cds.sequence.api.service.SequenceService;
import com.danding.cds.sequence.impl.manager.SequenceManager;
import com.danding.cds.sequence.impl.mapper.SequenceMapper;
import com.danding.cds.snow.flake.service.IdService;
import com.danding.core.tenant.SimpleTenantHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.shardingsphere.core.strategy.keygen.SnowflakeShardingKeyGenerator;
import org.joda.time.DateTime;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.util.Objects;

@DubboService
@Slf4j
@RefreshScope
public class SequenceServiceImpl implements SequenceService {
    @Autowired
    private OrderSnEsDao orderSnEsDao;

    static protected SnowflakeShardingKeyGenerator snow = new SnowflakeShardingKeyGenerator();

    @Value("${distributed_id_enable:false}")
    private Boolean distributedIdEnable;

    @Autowired
    private IdService idService;

    @Autowired
    private SequenceManager sequenceManager;

    @Autowired
    private SequenceMapper sequenceMapper;

    @Autowired
    private RedissonClient redissonClient;

    private String dataFormat = "yyMMddHHmm";

    private String yyyyMMddFormat = "yyyyMMdd";

    private String yyMMddFormat = "yyMMdd";

    @Override
    public Long getTenantIdBySn(String sn) {
        return orderSnEsDao.getTenantIdBySn(sn);
    }

    @Override
    public Long nextSequence(String code, boolean reset) {
        //redisson 公平锁
        RLock fairLock = redissonClient.getFairLock(String.join(":", "CCS_ORDER_CODE_LOCK", code));
        try {
            fairLock.lock();
            Long key = sequenceManager.nextSequence(code, reset);
            return key % 1000000;
        } finally {
            fairLock.unlock();
        }
    }

    @Override
    public Long nextSequence(String code) {
        //redisson 公平锁
        RLock fairLock = redissonClient.getFairLock(String.join(":", "CCS_ORDER_CODE_LOCK", code));
        try {
            fairLock.lock();
            Long key = sequenceManager.nextSequence(code, false);
//		if (key == null){
//			SequenceDO sequenceDO = new SequenceDO();
//			sequenceDO.setCode(code);
//			sequenceDO.setName("未定义-"+code);
//			sequenceDO.setValue(1L);
//			sequenceDO.setTime(new Date());
//			sequenceMapper.insertSelective(sequenceDO);
//			key = 1L;
//		}
            return key % 1000000;
        } finally {
            fairLock.unlock();
        }
    }

    /**
     * 序列号批量步长
     *
     * @param code 类型
     * @param num  数量
     * @return
     */
    public Long stepSequence(String code, Long num) {
        //redisson 公平锁
        RLock fairLock = redissonClient.getFairLock(String.join(":", "CCS_ORDER_CODE_LOCK", code));
        try {
            fairLock.lock();
            Long key = sequenceManager.stepSequence(code, num);
            return key;
        } finally {
            fairLock.unlock();
        }
    }

    public Long getTenantId() {
        Long tenantId = SimpleTenantHelper.getTenantId();
        if (Objects.isNull(tenantId)) {
            throw new RuntimeException("获取不到租户id 无法生成sn!");
        }
        return tenantId;
    }

    @Override
    public String generateSn(String filter, String code) {
        return filter +
                DateTime.now().toString(dataFormat) +
                StringUtils.leftPad("" + this.nextSequence(code), 6, "0");
    }

    @Override
    public String generateExportOrderSn() {
        String sn = "EO" +
                DateTime.now().toString(dataFormat) +
                StringUtils.leftPad("" + this.nextSequence("exportOrder"), 6, "0");
        orderSnEsDao.saveMapping(sn, getTenantId());
        return sn;
    }

    @Override
    public String generateOrderSn() {
        String sn = "OS" +
                DateTime.now().toString(dataFormat) +
                StringUtils.leftPad("" + this.nextSequence("order"), 6, "0");
        return sn;
    }

    /**
     * ExternalDeclare
     *
     * @return
     */
    @Override
    public String generateExternalOrderSn() {
        String sn = "ED" +
                DateTime.now().toString(dataFormat) +
                StringUtils.leftPad("" + this.nextSequence("externalOrder"), 6, "0");
        return sn;
    }

    @Override
    public String generateCustomsInventorySn() {
        String sn = "CI" +
                DateTime.now().toString(dataFormat) +
                StringUtils.leftPad("" + this.nextSequence("customsInventory"), 6, "0");
        return sn;
    }

    @Override
    public String generateCustomsOrderSn() {
        String sn = "CO" +
                DateTime.now().toString(dataFormat) +
                StringUtils.leftPad("" + this.nextSequence("customsInventory"), 6, "0");
        return sn;
    }

    @Override
    public String generateCustomsLogisticsSn() {
        String sn = "CL" +
                DateTime.now().toString(dataFormat) +
                StringUtils.leftPad("" + this.nextSequence("customsInventory"), 6, "0");
        return sn;
    }

    @Override
    public String generateChecklistSn() {
        String sn = "HF" +
                DateTime.now().toString(dataFormat) +
                StringUtils.leftPad("" + this.nextSequence("checkList"), 6, "0");
        orderSnEsDao.saveMapping(sn, getTenantId());
        return sn;
    }

    @Override
    public String generateEndorsementSn() {
        String sn = "HZ" +
                DateTime.now().toString(dataFormat) +
                StringUtils.leftPad("" + this.nextSequence("endorsement"), 6, "0");
        orderSnEsDao.saveMapping(sn, getTenantId());
        return sn;
    }

    @Override
    public String generateRefundOrderSn() {
        String sn = "TH" +
                DateTime.now().toString(dataFormat) +
                StringUtils.leftPad("" + this.nextSequence("refund_code"), 4, "0");
        return sn;
    }

    @Override
    public String generateInventoryCancelOrderSn() {
        String sn = "CD" +
                DateTime.now().toString(dataFormat) +
                StringUtils.leftPad("" + this.nextSequence("inventory_cancel_code"), 6, "0");
        return sn;
    }

    @Override
    public String generateInventoryOrderSn() {
        String sn = "QG" +
                DateTime.now().toString(dataFormat) +
                StringUtils.leftPad("" + this.nextSequence("inventory_order"), 4, "0");
        orderSnEsDao.saveMapping(sn, getTenantId());
        return sn;
    }

    @Override
    public String generateTaxesRechargeOrderSn() {
        String sn = "TR" +
                DateTime.now().toString(dataFormat) +
                StringUtils.leftPad("" + this.nextSequence("taxes_recharge_code"), 6, "0");
        orderSnEsDao.saveMapping(sn, getTenantId());
        return sn;
    }

    @Override
    public String generateMerchantAccountSn() {
        String sn = "MC" +
                DateTime.now().toString(dataFormat) +
                StringUtils.leftPad("" + this.nextSequence("merchant_account_code"), 6, "0");
        orderSnEsDao.saveMapping(sn, getTenantId());
        return sn;
    }

    @Override
    public String generatePaymentDeclareSn() {
        String sn = "CP" +
                DateTime.now().toString(dataFormat) +
                StringUtils.leftPad("" + this.nextSequence("payment_declare_code"), 5, "0");
        orderSnEsDao.saveMapping(sn, getTenantId());
        return sn;
    }

    @Override
    public String generateCalloffOrderSn() {
        String sn = "CO" +
                DateTime.now().toString(dataFormat) +
                StringUtils.leftPad("" + this.nextSequence("calloff_order_code"), 6, "0");
        orderSnEsDao.saveMapping(sn, getTenantId());
        return sn;
    }

    @Override
    public String generateCustomsDeclareSn() {
        String sn = "BG" +
                DateTime.now().toString(dataFormat) +
                StringUtils.leftPad("" + this.nextSequence("customs_declare_code"), 6, "0");
        orderSnEsDao.saveMapping(sn, getTenantId());
        return sn;
    }

    @Override
    public String generateEntityWarehouseSn() {
        String sn = "CK" +
                DateTime.now().toString(dataFormat) +
                StringUtils.leftPad("" + this.nextSequence("entity_warehouse_code"), 6, "0");
        orderSnEsDao.saveMapping(sn, getTenantId());
        return sn;
    }

    @Override
    public String generateCollaborateOrderSn() {
        String sn = "XT" +
                DateTime.now().toString(dataFormat) +
                StringUtils.leftPad("" + this.nextSequence("collaborate_order_code"), 6, "0");
        orderSnEsDao.saveMapping(sn, getTenantId());
        return sn;
    }

    @Override
    public String generateDataDictionaryCode() {
        String sn = DateTime.now().toString(dataFormat) +
                StringUtils.leftPad("" + this.nextSequence("dictionaryCode"), 6, "0");
        orderSnEsDao.saveMapping(sn, getTenantId());
        return sn;
    }

    @Override
    public String generateStockInOutCode() {
        String sn = DateTime.now().toString(yyyyMMddFormat) +
                StringUtils.leftPad("" + this.nextSequence("stockInOut"), 10, "0");
        return sn;
    }

    @Override
    public String generateStockInOutCode(String customsBookNo) {
        String sn = customsBookNo + DateTime.now().toString(yyyyMMddFormat) +
                StringUtils.leftPad("" + this.nextSequence("stockInOut"), 10, "0");
        return sn;
    }

    @Override
    public String generateHZDCStockInOutCode(String customsBookNo) {
        String sn = customsBookNo + DateTime.now().toString(yyyyMMddFormat) +
                StringUtils.leftPad("" + this.nextSequence("stockInOut"), 6, "0");
        return sn;
    }

    /**
     * 生成非保核放单 sn
     *
     * @param companyCode 区内企业十位编码
     * @return
     */
    @Override
    public String generateFbChecklistSn(String companyCode) {
        String sn = companyCode + DateTime.now().toString(yyyyMMddFormat) +
                StringUtils.leftPad("" + this.nextSequence("fbChecklist"), 12, "0");
        return sn;
    }

    @Override
    public String generateInventoryOrderOverdueExceptionId() {
        String sn = DateTime.now().toString(yyMMddFormat) +
                StringUtils.leftPad("" + this.nextSequence("inventoryOrderOverdueExceptionId", true), 4, "0");
        return sn;
    }

    @Override
    public String generateStockInOutDeleteCode(String wmsCode) {
        String sn = "D" + wmsCode + DateTime.now().toString(yyyyMMddFormat) +
                StringUtils.leftPad("" + this.nextSequence("stockInOutDelete"), 8, "0");
        return sn;
    }

    @Override
    public String generateFbInventoryAdjustSn() {
        String sn = StringUtils.leftPad("" + this.nextSequence("fbInventoryAdjust"), 5, "0");
        return sn;
    }

    @Override
    public String generateChecklistAuthSn() {
        return "SQ" +
                DateTime.now().toString(dataFormat) +
                StringUtils.leftPad("" + this.nextSequence("checklistAuth"), 6, "0");
    }

    @Override
    public String generateProcessTradeBookSn() {
        return "JM" +
                DateTime.now().toString(yyyyMMddFormat) +
                StringUtils.leftPad("" + this.nextSequence("processTradeBook"), 3, "0");
    }

    @Override
    public String generateReconciliationSn() {
        return "DZ" +
                DateTime.now().toString(yyyyMMddFormat) +
                StringUtils.leftPad("" + this.nextSequence("checklistAuth"), 5, "0");
    }

    @Override
    public String generateBizDeclareFormSn() {
        return "JG" +
                DateTime.now().toString(yyyyMMddFormat) +
                StringUtils.leftPad("" + this.nextSequence("bizDeclareForm"), 5, "0");
    }

    @Override
    public Long generateId() {

        if (distributedIdEnable) {
            log.info("雪花算法 分布式ID生成方式");
            return idService.getUniqueId();
        } else {
            return (Long) snow.generateKey();
        }
    }

    /**
     * 批量获取申报单sn
     *
     * @param num 数量
     * @return index开始最小编号, 使用位置需要自己计算数据
     */
    @Override
    public Long batchStepOrder(Long num) {
        return stepSequence("order", num);
    }

    @Override
    public Long batchStepExternalOrder(Long num) {
        return stepSequence("externalOrder", num);
    }

    @Override
    public Long batchStepCustomsInventory(Long num) {
        return stepSequence("customsInventory", num);
    }

    @Override
    public Long batchStepCustomsOrder(Long num) {
        return stepSequence("customsInventory", num);
    }

    @Override
    public Long batchStepCustomsLogistics(Long num) {
        return stepSequence("customsInventory", num);
    }

    @Override
    public Long batchStepCustomsPayment(Long num) {
        return stepSequence("payment_declare_code", num);
    }
}
