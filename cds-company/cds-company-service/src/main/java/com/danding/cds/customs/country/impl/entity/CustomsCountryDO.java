package com.danding.cds.customs.country.impl.entity;

import com.danding.cds.common.model.BaseDO;
import lombok.Data;

import javax.persistence.Table;
import java.io.Serializable;


/**
 * 海关国家表(CustomsCountryDO)实体类
 *
 * <AUTHOR>
 * @since 2020-04-28 16:43:57
 */
@Data
@Table(name = "ccs_customs_country")
public class CustomsCountryDO extends BaseDO implements Serializable {
    private static final long serialVersionUID= 2970823698362101006L;

    /**
     * 海关国家编号
     */
    private String code;
    /**
     * 海关国家名称
     */
    private String name;

}
