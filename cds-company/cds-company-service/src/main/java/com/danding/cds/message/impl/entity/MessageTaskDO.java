package com.danding.cds.message.impl.entity;

import com.danding.cds.common.model.BaseDO;
import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Table(name = "ccs_message_task")
@Getter
@Setter
public class MessageTaskDO extends BaseDO {
    /**
     * 订阅ID
     */
    @Column(name = "subscribe_id")
    private Long subscribeId;

    /**
     * 订阅ID
     */
    @Column(name = "message_id")
    private Long messageId;

    /**
     * 消息类型|冗余
     */
    private String type;

    /**
     * 任务类型
     */
    @Column(name = "task_type")
    private String taskType;

    /**
     * 业务编号|冗余
     */
    @Column(name = "business_code")
    private String businessCode;

    /**
     * 执行回传地址
     */
    @Column(name = "notify_url")
    private String notifyUrl;

    /**
     * 发送记录
     */
    @Column(name = "send_record_json")
    private String sendRecordJson;

    /**
     * 通知次数
     */
    private Integer count;

    /**
     * 最后回传时间
     */
    @Column(name = "last_notify_time")
    private Date lastNotifyTime;

    /**
     * 下次回传时间
     */
    @Column(name = "next_notify_time")
    private Date nextNotifyTime;

    /**
     * 执行状态
     */
    private Integer status;

    /**
     * 请求数据
     */
    @Column(name = "request_data")
    private String requestData;

    @Column(name = "pre_task_id")
    private Long preTaskId;

    @Column(name = "next_task_id")
    private Long nextTaskId;

    @Column(name = "next_message_id")
    private Long nextMessageId;

    @Column(name = "group_id")
    private Long groupId;

    @Column(name = "delay_level")
    private Integer delayLevel;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;
}