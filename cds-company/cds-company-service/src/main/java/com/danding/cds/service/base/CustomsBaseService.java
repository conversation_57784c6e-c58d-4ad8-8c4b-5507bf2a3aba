package com.danding.cds.service.base;

import com.danding.cds.customs.district.api.enums.CustomsDistrictEnum;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class CustomsBaseService {

    public List<CustomsDistrictEnum> getAllCustoms(){
        List<CustomsDistrictEnum> list = Arrays.stream(CustomsDistrictEnum.values()).filter(o -> !o.equals(CustomsDistrictEnum.NULL)).collect(Collectors.toList());
        return list;
    }
}
