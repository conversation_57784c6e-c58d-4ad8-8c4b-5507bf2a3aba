package com.danding.cds.customs.uom.impl.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.customs.uom.api.dto.CustomsUomDTO;
import com.danding.cds.customs.uom.api.service.CustomsUomService;
import com.danding.cds.customs.uom.impl.entity.CustomsUomDO;
import com.danding.cds.customs.uom.impl.mapper.CustomsUomMapper;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * @Auther: shenfeng
 * @Date: 2020-04-28 16:43:57
 * @Description:
 */
@DubboService
public class CustomsUomServiceImpl implements CustomsUomService {

    @Autowired
    CustomsUomMapper customsUomMapper;

    @Override
    public CustomsUomDTO findById(Long id) {
        if(LongUtil.isNone(id)){
            return null;
        }else{
            CustomsUomDO customsUomDO = customsUomMapper.selectByPrimaryKey(id);
            return this.buildDTO(customsUomDO);
        }
    }

    @Override
//    @Cacheable(value = "customUmoByCode", key = "#code", unless = "#result==null")
    public CustomsUomDTO findByCode(String code) {
        CustomsUomDO condition = new CustomsUomDO();
        condition.setCode(code);
        return this.buildDTO(customsUomMapper.selectOne(condition));
    }

    @Override
    public List<CustomsUomDTO> listAll() {
        CustomsUomDO condition = new CustomsUomDO();
        condition.setDeleted(false);
        return JSON.parseArray(JSON.toJSONString(customsUomMapper.select(condition)),CustomsUomDTO.class);
    }

    @Override
    public CustomsUomDTO findByName(String name) {
        if (StrUtil.isBlank(name)) return null;
        CustomsUomDO condition = new CustomsUomDO();
        condition.setName(name);
        return this.buildDTO(customsUomMapper.selectOne(condition));
    }

    private CustomsUomDTO buildDTO(CustomsUomDO entity){
        if(entity == null){
            return null;
        }else {
            CustomsUomDTO dto = new CustomsUomDTO();
            BeanUtils.copyProperties(entity,dto);
            return dto;
        }
    }
}
