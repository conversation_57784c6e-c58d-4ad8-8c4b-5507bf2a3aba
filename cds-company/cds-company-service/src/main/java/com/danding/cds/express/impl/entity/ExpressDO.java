package com.danding.cds.express.impl.entity;

import com.danding.cds.common.model.BaseDO;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;

@Table(name = "ccs_express")
@Getter
@Setter
public class ExpressDO extends BaseDO {
    /**
     * 快递标识
     */
    private String code;

    /**
     * 快递名称
     */
    private String name;

    /**
     * 物流企业ID
     */
    @Column(name = "express_company_id")
    private Long expressCompanyId;

    /**
     * 状态:0.停用;1.启用(默认)
     */
    private Integer enable;

    /**
     * 备注
     */
    private String remark;

    /**
     * 运单申报系统
     */
    private String logisticDeclareSystem;
}