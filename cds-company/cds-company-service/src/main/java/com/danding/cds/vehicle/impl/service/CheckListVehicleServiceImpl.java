package com.danding.cds.vehicle.impl.service;

import com.danding.cds.common.user.UserUtils;
import com.danding.cds.vehicle.api.dto.CheckListVehicleDTO;
import com.danding.cds.vehicle.api.dto.CheckListVehicleSubmit;
import com.danding.cds.vehicle.api.service.CheckListVehicleService;
import com.danding.cds.vehicle.impl.entity.ChecklistVehicleDO;
import com.danding.cds.vehicle.impl.mapper.ChecklistVehicleMapper;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.stream.Collectors;

@DubboService
public class CheckListVehicleServiceImpl implements CheckListVehicleService {

    @Autowired
    private ChecklistVehicleMapper checklistVehicleMapper;

    @Override
    public CheckListVehicleDTO findByVehicleLicense(String vehicleLicense) {
        ChecklistVehicleDO condition = new ChecklistVehicleDO();
        condition.setVehicleLicensePlate(vehicleLicense);
        ChecklistVehicleDO vehicleDO = checklistVehicleMapper.selectOne(condition);
        if (vehicleDO == null) {
            return null;
        }
        return this.buildDTO(vehicleDO);
    }

    @Override
    public CheckListVehicleDTO findById(Long id) {
        ChecklistVehicleDO vehicleDO = checklistVehicleMapper.selectByPrimaryKey(id);
        if (vehicleDO == null) {
            return null;
        }
        return this.buildDTO(vehicleDO);
    }

    @Override
    public Long upset(CheckListVehicleSubmit submit) throws ArgsErrorException {
        ChecklistVehicleDO vehicleDO = new ChecklistVehicleDO();
        BeanUtils.copyProperties(submit, vehicleDO);
        vehicleDO.setId(null);
        UserUtils.setCreateAndUpdateBy(vehicleDO);
        checklistVehicleMapper.insertSelective(vehicleDO);
        submit.setId(vehicleDO.getId());
        return vehicleDO.getId();
    }

    @Override
    public List<CheckListVehicleDTO> listAll() {
        List<ChecklistVehicleDO> vehicleDOList = checklistVehicleMapper.selectAll();
        List<CheckListVehicleDTO> result = vehicleDOList.stream().map(this::buildDTO).collect(Collectors.toList());
        return result;
    }

    private CheckListVehicleDTO buildDTO(ChecklistVehicleDO routeDO) {
        CheckListVehicleDTO result = new CheckListVehicleDTO();
        BeanUtils.copyProperties(routeDO, result);
        return result;
    }
}
