package com.danding.cds.company.impl.entity;

import com.danding.cds.common.model.BaseDO;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * 企业表(CompanyDO)实体类
 *
 * <AUTHOR>
 * @since 2020-04-26 14:45:03
 */
@Data
@Table(name = "ccs_company")
public class CompanyDO extends BaseDO implements Serializable {
    private static final long serialVersionUID = 222315906574994992L;

    /**
     * 企业名称
     */
    private String name;

    /**
     * 海关十位备案编码
     */
    private String code;

    /**
     * 统一社会信用代码
     */
    @Column(name = "uniform_social_credit_code")
    private String uniformSocialCreditCode;

    /**
     * 企业资质列表
     */
    @Column(name = "qualify_json")
    private String qualifyJson;

    /**
     * 地方关区信息
     */
    @Column(name = "district_json")
    private String districtJson;

    /**
     * json储存的其他属性键值对
     */
    @Column(name = "extra_json")
    private String extraJson;

    /**
     * 状态:0.停用;1.启用(默认)
     */
    private Integer enable;

    /**
     * 申报配置
     */
    @Column(name = "declare_config")
    private String declareConfig;

    /**
     * 以json储存 跨境数据交换申请传输节点
     * cross_border_data_exchange_node
     */
    private String crossBorderDataExchangeNode;

    /**
     * 以json储存 非跨境数据交换申请传输节点
     * non_cross_border_data_exchange_node
     */
    private String nonCrossBorderDataExchangeNode;

    /**
     * 以json储存 保函关区
     * guarantee_customs
     */
    private String guaranteeCustoms;

    /**
     * 备注
     */
    private String remark;

    private Long tenantryId;

    /**
     * 进出口收发货人编码
     * 海关总署编码
     */
    private String shipperAndConsigneeCode;

    /**
     * 报关企业编码
     * 海关总署编码
     */
    private String customsCompanyCode;

    /**
     * 跨境进口统一编码
     * 业务配置类型
     */
    private String unifiedCrossBroderCode;

    /**
     * 保税物流管理系统编码
     */
    private String specialClientCode;

    /**
     * 分类监管编码
     * 浙电 非保
     */
    private String fbCode;

    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    public Date getCreateTime() {

        return super.getCreateTime();
    }
}