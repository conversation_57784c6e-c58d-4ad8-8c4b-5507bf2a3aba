package com.danding.cds.service;

import com.alibaba.fastjson.JSON;
import com.danding.cds.bean.dto.BaseDataSyncDTO;
import com.danding.cds.bean.dto.BaseDataSyncTypeEnums;
import com.danding.logistics.mq.common.handler.MessageSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Create 2021/10/20  14:04
 * @Describe
 **/
@Slf4j
@Service
public class BaseDataSyncServiceImpl implements BaseDataSyncService {

    @Autowired
    private MessageSender messageSender;

    @Override
    public void syncDataMQSend(String dataType, String changeType, Object data) {
        BaseDataSyncDTO baseDataSyncDTO = new BaseDataSyncDTO();
        baseDataSyncDTO.setData(data);
        baseDataSyncDTO.setChangeType(changeType);
        baseDataSyncDTO.setDataType(dataType);
        log.info("BaseDataSyncServiceImpl syncDataMQSend dto={}", JSON.toJSONString(baseDataSyncDTO));
        messageSender.sendMsg(baseDataSyncDTO, BaseDataSyncTypeEnums.SYNC_TOPIC);
    }
}
