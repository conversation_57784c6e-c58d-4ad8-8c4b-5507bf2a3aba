package com.danding.cds.bean.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 申报方式类型的枚举类
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum DeclareWayPropertyEnum {
    NULL("", "无"),
    BASIC("basic", "基础"),
    PROXY("proxy", "代理");
    private String code;
    private String desc;

    public static DeclareWayPropertyEnum getEnum(String code) {
        for (DeclareWayPropertyEnum value : DeclareWayPropertyEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return NULL;
    }
}
