package com.danding.cds.vehicle.impl.entity;

import com.danding.cds.common.model.BaseDO;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import java.math.BigDecimal;

@Table(name = "`ccs_checklist_vehicle`")
@Getter
@Setter
public class ChecklistVehicleDO extends BaseDO {
    /**
     * 车辆车牌
     */
    @Column(name = "vehicle_license_plate")
    private String vehicleLicensePlate;

    /**
     * 车重
     */
    @Column(name = "vehicle_weight")
    private BigDecimal vehicleWeight;

    /**
     * 车架号
     */
    @Column(name = "vehicle_frame_no")
    private String vehicleFrameNo;

    /**
     * 车架重
     */
    @Column(name = "vehicle_frame_weight")
    private BigDecimal vehicleFrameWeight;
}