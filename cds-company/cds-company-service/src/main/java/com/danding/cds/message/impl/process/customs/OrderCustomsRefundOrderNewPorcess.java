package com.danding.cds.message.impl.process.customs;

import com.alibaba.fastjson.JSON;
import com.danding.cds.common.bean.dto.RefundMessageDto;
import com.danding.cds.message.api.dto.MessageDTO;
import com.danding.cds.message.api.enums.MessageType;
import com.danding.cds.message.impl.process.MessageProcess;
import com.danding.cds.message.impl.process.MessageRegistry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@Slf4j
public class OrderCustomsRefundOrderNewPorcess extends MessageProcess {

    public OrderCustomsRefundOrderNewPorcess(MessageRegistry registry) {
        super(registry);
    }

    @Override
    public MessageType getType() {
        return MessageType.ORDER_REFUND_NEW;
    }

    @Override
    public Map<String, Object> buildSendInfo(MessageDTO messageDTO) {
        String activeData = messageDTO.getActiveData();
        RefundMessageDto refundMessageDto = JSON.parseObject(activeData, RefundMessageDto.class);
        log.info("退货新回执打印 - {}", refundMessageDto);
        return objToMap(refundMessageDto);
    }
}
