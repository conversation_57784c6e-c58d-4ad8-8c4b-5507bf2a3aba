package com.danding.cds.path.impl.entity;

import com.danding.cds.common.model.BaseDO;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;

@Table(name = "`ccs_path`")
@Getter
@Setter
public class PathDO extends BaseDO {
    /**
     * 路由标识
     */
    private String code;

    /**
     * 路由名称
     */
    private String name;

    /**
     * 路径id
     */
    @Column(name = "route_id")
    private Long routeId;

    /**
     * 标识1
     */
    @Column(name = "first_identify")
    private String firstIdentify;

    /**
     * 标识2
     */
    @Column(name = "second_identify")
    private String secondIdentify;

    /**
     * 标识3
     */
    @Column(name = "third_identify")
    private String thirdIdentify;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态:0.停用;1.启用(默认)
     */
    private Integer enable;

    /**
     * json储存的其他属性键值对
     */
    @Column(name = "extra_json")
    private String extraJson;
}