package com.danding.cds.service.rpc;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.danding.cds.customs.country.api.dto.CustomsCountryDTO;
import com.danding.cds.customs.country.api.service.CustomsCountryService;
import com.danding.cds.customs.currency.api.dto.CustomsCurrencyDTO;
import com.danding.cds.customs.currency.api.service.CustomsCurrencyService;
import com.danding.cds.customs.district.api.service.CustomsDistrictService;
import com.danding.cds.customs.hs.api.dto.CustomsHsDTO;
import com.danding.cds.customs.hs.api.service.CustomsHsService;
import com.danding.cds.customs.manager.api.dto.CustomsDistrictDTO;
import com.danding.cds.customs.uom.api.dto.CustomsUomDTO;
import com.danding.cds.customs.uom.api.service.CustomsUomService;
import com.danding.cds.out.api.CustomsBaseDataRpc;
import com.danding.cds.out.bean.vo.res.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@DubboService
public class CustomsBaseDataRpcImpl implements CustomsBaseDataRpc {
    @Autowired
    private CustomsCountryService countryService;
    @Autowired
    private CustomsCurrencyService currencyService;

    @Autowired
    private CustomsDistrictService districtService;

    @Autowired
    private CustomsHsService hsService;
    @Autowired
    private CustomsUomService uomService;

    @Override
    public CustomsCountryResVo findCountryByName(String name) {

        if (StringUtils.isEmpty(name)) {
            return null;
        }
        CustomsCountryDTO dto = countryService.findByName(name);
        if (Objects.isNull(dto)) {
            return null;
        }
        CustomsCountryResVo vo = new CustomsCountryResVo();
        BeanUtils.copyProperties(dto, vo);
        return vo;
    }

    @Override
    public CustomsCountryResVo findCountryByCode(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }
        CustomsCountryDTO dto = countryService.findByCode(code);
        if (Objects.isNull(dto)) {
            return null;
        }
        CustomsCountryResVo vo = new CustomsCountryResVo();
        BeanUtils.copyProperties(dto, vo);
        return vo;
    }

    @Override
    public List<CustomsCountryResVo> listCountry() {
        List<CustomsCountryDTO> dtoList = countryService.listAll();
        List<CustomsCountryResVo> resVoList = dtoList.stream().map(d -> {
            CustomsCountryResVo vo = new CustomsCountryResVo();
            BeanUtils.copyProperties(d, vo);
            return vo;
        }).collect(Collectors.toList());
        return resVoList;
    }

    @Override
    public CustomsCurrencyResVo findCurrencyByName(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        CustomsCurrencyDTO dto = currencyService.findByName(name);
        if (Objects.isNull(dto)) {
            return null;
        }
        CustomsCurrencyResVo vo = new CustomsCurrencyResVo();
        BeanUtils.copyProperties(dto, vo);
        return vo;
    }

    @Override
    public CustomsCurrencyResVo findCurrencyByCode(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }
        CustomsCurrencyDTO dto = currencyService.findByCode(code);
        if (Objects.isNull(dto)) {
            return null;
        }
        CustomsCurrencyResVo vo = new CustomsCurrencyResVo();
        BeanUtils.copyProperties(dto, vo);
        return vo;
    }

    @Override
    public List<CustomsCurrencyResVo> listCurrency() {
        List<CustomsCurrencyDTO> dtoList = currencyService.listAll();
        List<CustomsCurrencyResVo> voList = dtoList.stream().map(d -> {
            CustomsCurrencyResVo vo = new CustomsCurrencyResVo();
            BeanUtils.copyProperties(d, vo);
            return vo;
        }).collect(Collectors.toList());
        return voList;
    }

    @Override
    public CustomsHsResVo findHsByCode(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }
        CustomsHsDTO dto = hsService.findByCode(code);
        if (Objects.isNull(dto)) {
            return null;
        }
        CustomsHsResVo vo = new CustomsHsResVo();
        BeanUtils.copyProperties(dto, vo);
        return vo;
    }

    @Override
    public List<CustomsHsResVo> listHs() {
        List<CustomsHsDTO> dtoList = hsService.listEnable();
        List<CustomsHsResVo> voList = dtoList.stream().map(d -> {
            CustomsHsResVo vo = new CustomsHsResVo();
            BeanUtils.copyProperties(d, vo);
            return vo;
        }).collect(Collectors.toList());
        return voList;
    }

    @Override
    public CustomsUomResVo findUomByName(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        CustomsUomResVo resVo = new CustomsUomResVo();
        CustomsUomDTO uomDTO = uomService.findByName(name);
        if (Objects.isNull(uomDTO)) {
            return null;
        }
        resVo.setId(uomDTO.getId());
        resVo.setCode(uomDTO.getCode());
        resVo.setName(uomDTO.getName());
        return resVo;
    }

    @Override
    public CustomsDistrictResVo findDistrictByCode(String code) {
        if (StrUtil.isBlank(code)) {
            return null;
        }
        try {
            CustomsDistrictDTO customsDistrictDTO = districtService.getByCode(code);
            if (Objects.isNull(customsDistrictDTO)) {
                return null;
            }
            CustomsDistrictResVo resVo = new CustomsDistrictResVo();
            BeanUtils.copyProperties(customsDistrictDTO, resVo);
            return resVo;
        } catch (Exception e) {
            log.info("findDistrictByCode error:{}", e.getMessage());
            return null;
        }
    }

    @Override
    public CustomsDistrictResVo findDistrictByName(String name) {
        if (StrUtil.isBlank(name)) {
            return null;
        }
        try {
            CustomsDistrictDTO dto = districtService.getByName(name);
            if (Objects.isNull(dto)) {
                return null;
            }
            CustomsDistrictResVo resVo = new CustomsDistrictResVo();
            BeanUtils.copyProperties(dto, resVo);
            return resVo;
        } catch (Exception e) {
            log.info("findDistrictByName error:{}", e.getMessage());
            return null;
        }
    }

    @Override
    public List<CustomsDistrictResVo> findDistrictByName(List<String> nameList) {
        if (CollUtil.isEmpty(nameList)) {
            return Collections.emptyList();
        }
        try {
            List<CustomsDistrictDTO> dtoList = districtService.getByNameList(nameList);
            return dtoList.stream().map(d -> {
                CustomsDistrictResVo resVo = new CustomsDistrictResVo();
                BeanUtils.copyProperties(d, resVo);
                return resVo;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            log.info("findDistrictByName error:{}", e.getMessage());
            return Collections.emptyList();
        }
    }

    @Override
    public CustomsUomResVo findUomByCode(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }
        CustomsUomResVo resVo = new CustomsUomResVo();
        CustomsUomDTO uomDTO = uomService.findByCode(code);
        if (Objects.isNull(uomDTO)) {
            return null;
        }
        resVo.setId(uomDTO.getId());
        resVo.setCode(uomDTO.getCode());
        resVo.setName(uomDTO.getName());
        return resVo;
    }
}
