package com.danding.cds.vehicle.impl.entity;

import com.danding.cds.common.model.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Table;
import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
@Table(name = "`ccs_vehicle_resource`")
public class VehicleResourceDO extends BaseDO {

    /**
     * id
     */
    @Column(name = "id")
    private Long id;

    /**
     * 车辆资源code
     */
    @Column(name = "vehicle_resource_code")
    private String vehicleResourceCode;

    /**
     * 车型名称
     */
    @Column(name = "vehicle_type_name")
    private String vehicleTypeName;

    /**
     * 车辆类型
     */
    @Column(name = "vehicle_type")
    private String vehicleType;

    /**
     * 车牌号
     */
    @Column(name = "vehicle_plate")
    private String vehiclePlate;

    /**
     * 货品运输类型
     */
    @Column(name = "goods_transport_type")
    private String goodsTransportType;

    /**
     * 可装载集装箱类型
     */
    @Column(name = "loadable_container_type")
    private String loadableContainerType;

    /**
     * 车长/车宽/车高（cm）
     */
    @Column(name = "vehicle_dimension")
    private String vehicleDimension;

    /**
     * 额定载重（kg）
     */
    @Column(name = "load_capacity")
    private BigDecimal loadCapacity;

    /**
     * 额定载货体积（m³）
     */
    @Column(name = "load_volume")
    private BigDecimal loadVolume;

    /**
     * 启用状态
     */
    @Column(name = "enable")
    private Integer enable;

    /**
     * 车辆用途
     */
    @Column(name = "vehicle_purpose")
    private String vehiclePurpose;

    /**
     * 车长（cm）
     */
    @Column(name = "length")
    private BigDecimal length;

    /**
     * 车宽（cm）
     */
    @Column(name = "width")
    private BigDecimal width;

    /**
     * 车高（cm）
     */
    @Column(name = "height")
    private BigDecimal height;

    /**
     * 租户id
     */
    @Column(name = "tenantry_id")
    private Long tenantryId;
}
