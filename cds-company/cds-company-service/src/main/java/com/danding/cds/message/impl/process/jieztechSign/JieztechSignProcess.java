package com.danding.cds.message.impl.process.jieztechSign;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.cds.message.api.dto.MessageDTO;
import com.danding.cds.message.api.enums.MessageType;
import com.danding.cds.message.impl.process.MessageExecuteResult;
import com.danding.cds.message.impl.process.MessageProcess;
import com.danding.cds.message.impl.process.MessageRegistry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

@Component
@Slf4j
public class JieztechSignProcess extends MessageProcess {

    public JieztechSignProcess(MessageRegistry registry) {
        super(registry);
    }

    @Override
    public MessageType getType() {
        return MessageType.ORDER_CUSTOMS_TO_JIEZ_TECH;
    }

    @Override
    public Map<String, Object> buildSendInfo(MessageDTO messageDTO) {
        log.info("海关回执推送芥舟 请求报文 ：{}", messageDTO.getActiveData());
        return JSON.parseObject(messageDTO.getActiveData());
    }

    @Override
    public void processResultBody(MessageExecuteResult result, String body) {
        log.info("海关回执推送芥舟 响应报文 ：{}", body);
        if (isJSONValid(body)) {
            JSONObject jsonObject = JSON.parseObject(body);
            String code = jsonObject.get("code").toString();
            result.setSuccess(Objects.equals(code, "200"));
        } else {
            super.processResultBody(result, body);
        }
    }
}
