package com.danding.cds.message.impl.process.tax;

import com.alibaba.fastjson.JSON;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.declare.sdk.clear.base.callback.module.TaxResult;
import com.danding.cds.message.api.dto.MessageDTO;
import com.danding.cds.message.api.enums.MessageType;
import com.danding.cds.message.api.process.OrderTaxMessage;
import com.danding.cds.message.impl.process.MessageProcess;
import com.danding.cds.message.impl.process.MessageRegistry;
import org.joda.time.DateTime;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class ReundTaxMessageProcess extends MessageProcess {

    public ReundTaxMessageProcess(MessageRegistry registry) {
        super(registry);
    }

    @Override
    public MessageType getType() {
        return MessageType.ORDER_REFUND_TAX;
    }

    @Override
    public Map<String, Object> buildSendInfo(MessageDTO messageDTO) {
        TaxResult activeInfo = JSON.parseObject(messageDTO.getActiveData(), TaxResult.class);
        OrderTaxMessage message = new OrderTaxMessage();
        message.setDeclareOrderNo(activeInfo.getOrderNo());
        message.setInvtNo(activeInfo.getInvtNo());
        message.setReturnTime(new DateTime(activeInfo.getReturnTime()).getMillis());
        message.setTaxNo(activeInfo.getTaxNo());
        message.setCustomsTax(activeInfo.getCustomsTax());
        message.setValueAddedTax(activeInfo.getValueAddedTax());
        message.setConsumptionTax(activeInfo.getConsumptionTax());
        message.setStatus(activeInfo.getStatus());
        message.setEntDutyNo(activeInfo.getEntDutyNo());
        message.setEbcCode(activeInfo.getEbcCode());
        message.setLogisticsNo(activeInfo.getLogisticsNo());
        message.setSendType("1");
        List<OrderTaxMessage.TaxItem> taxItemList = ConvertUtil.listConvert(activeInfo.getItemList(), OrderTaxMessage.TaxItem.class);
        message.setItemList(taxItemList);
        return objToMap(message);
    }
}
