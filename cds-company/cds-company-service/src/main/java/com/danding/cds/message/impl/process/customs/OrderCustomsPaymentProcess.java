package com.danding.cds.message.impl.process.customs;

import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.rpc.OrderCRpc;
import com.danding.cds.callback.api.dto.PaymentActive;
import com.danding.cds.common.config.OrderCCallConfig;
import com.danding.cds.declare.sdk.enums.PayCustomsChannel;
import com.danding.cds.message.api.dto.MessageDTO;
import com.danding.cds.message.api.enums.MessageType;
import com.danding.cds.message.api.process.OrderCustomsPaymentMessage;
import com.danding.cds.message.impl.process.MessageProcess;
import com.danding.cds.message.impl.process.MessageRegistry;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.api.dto.OrderExtra;
import com.danding.cds.order.api.service.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * @Auther: Dante-GXJ
 * @Date: 2020/10/20 16:06
 * @Description:
 */
@Component
@Slf4j
public class OrderCustomsPaymentProcess extends MessageProcess {

    @DubboReference
    private OrderService orderService;
    @DubboReference
    private OrderCRpc orderCRpc;
    @Resource
    private OrderCCallConfig orderCCallConfig;


    public OrderCustomsPaymentProcess(MessageRegistry registry) {
        super(registry);
    }

    @Override
    public MessageType getType() {
        return MessageType.ORDER_CUSTOMS_PAYMENT;
    }

    @Override
    public Map<String, Object> buildSendInfo(MessageDTO messageDTO) {
        PaymentActive paymentActive = JSON.parseObject(messageDTO.getActiveData(), PaymentActive.class);
        OrderCustomsPaymentMessage message = new OrderCustomsPaymentMessage();
        OrderDTO orderDTO = orderCCallConfig.isOrderCCall(this.getClass()) ? orderCRpc.findBySnSection(paymentActive.getOrderSn()) : orderService.findBySnSection(paymentActive.getOrderSn());
        OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
        message.setOrderSn(orderDTO.getSn());
        if (PayCustomsChannel.TONGLIAN_PAY.getValue().equalsIgnoreCase(orderExtra.getSubmit().getPayChannel())) {
            log.info("通联支付 外部单号去前后空格 前={}, 后={}， 申报单号={}"
                    , orderDTO.getOutOrderNo(), orderDTO.getOutOrderNo().trim(), orderDTO.getDeclareOrderNo());
            message.setOutOrderNo(orderDTO.getOutOrderNo().trim());
        } else {
            message.setOutOrderNo(orderDTO.getOutOrderNo());
        }
        message.setDeclareOrderNo(orderDTO.getDeclareOrderNo());
        message.setSuccess(paymentActive.getSuccess());
        message.setIdentityCheck(paymentActive.getIdentityCheck());
        message.setVerDept(paymentActive.getVerDept());
        message.setPayTransactionId(paymentActive.getPayTransactionId());
        message.setSubBankNo(paymentActive.getSubBankNo());
        message.setErrorMsg(paymentActive.getErrorMsg());
        message.setExtra(paymentActive.getExtra());
        return objToMap(message);
    }
}
