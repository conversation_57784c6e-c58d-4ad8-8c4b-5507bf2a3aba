package com.danding.cds.route.impl.entity;

import com.danding.cds.common.model.BaseDO;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;

@Table(name = "ccs_route")
@Getter
@Setter
public class RouteDO extends BaseDO {
    /**
     * 路径标识
     */
    private String code;

    /**
     * 路径名称
     */
    private String name;

    /**
     * 申报项
     */
    @Column(name = "action_json")
    private String actionJson;

    /**
     * 状态:0.停用;1.启用(默认)
     */
    private Integer enable;

    /**
     * 备注
     */
    private String remark;

    /**
     * json储存的其他属性键值对
     */
    @Column(name = "extra_json")
    private String extraJson;

    /**
     * 申报方式；HZDC：杭州数据中心；auto：自动(根据配置)
     */
    @Column(name = "declare_way")
    private String declareWay;

    /**
     * 申报高级配置(支持代理申报或更换默认申报实现)
     */
    @Column(name = "declare_config")
    private String declareConfig;

    /**
     * 路径标签
     */
    private Integer routeTag;
}