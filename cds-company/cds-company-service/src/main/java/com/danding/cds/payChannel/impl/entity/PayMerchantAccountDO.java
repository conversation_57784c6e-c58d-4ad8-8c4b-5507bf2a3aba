package com.danding.cds.payChannel.impl.entity;

import com.danding.cds.common.model.BaseDO;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

@Table(name = "ccs_pay_merchant_account")
@Getter
@Setter
public class PayMerchantAccountDO extends BaseDO {
    /**
     * 商户编码
     */
    private String sn;

    /**
     * 商户名称
     */
    private String name;

    /**
     * 收款企业
     */
    @Column(name = "company_name")
    private String companyName;

    /**
     * 备注
     */
    private String note;

    @Column(name = "create_time")
    private Date createTime;
}