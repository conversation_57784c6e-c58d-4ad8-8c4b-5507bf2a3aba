package com.danding.cds.service;

import com.danding.cds.bean.dao.DeclareWayDO;
import com.danding.cds.bean.dto.DeclareWayDTO;
import com.danding.cds.bean.vo.req.DeclareWaySearch;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.mapper.DeclareWayMapper;
import com.danding.cds.service.base.DeclareWayBaseService;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.api.common.response.PageResult;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.github.pagehelper.PageInfo;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@DubboService
public class DeclareWayServiceImpl implements DeclareWayService {
    @Autowired
    private DeclareWayBaseService declareWayBaseService;
    @Autowired
    private DeclareWayMapper declareWayMapper;

    @Override
    @PageSelect
    public ListVO<DeclareWayDTO> paging(DeclareWaySearch search) {
        Example example = new Example(DeclareWayDO.class);
        example.orderBy("createTime").desc();
        Example.Criteria criteria = example.createCriteria();
        if (Objects.nonNull(search.getNameId())) {
            criteria.andEqualTo("id", search.getNameId());
        }
        if (Objects.nonNull(search.getCodeId())) {
            criteria.andEqualTo("id", search.getCodeId());
        }
        if (Objects.nonNull(search.getType())) {
            criteria.andEqualTo("type", search.getType());
        }
        if (Objects.nonNull(search.getEnable())) {
            criteria.andEqualTo("enable", search.getEnable());
        }
        if (Objects.nonNull(search.getCustomsTransferNode())) {
            criteria.andEqualTo("customsTransferNode", search.getCustomsTransferNode());
        }
        criteria.andEqualTo("deleted", false);
        List<DeclareWayDO> list = declareWayMapper.selectByExample(example);

        List<DeclareWayDTO> dtoList = list.stream().map(l -> {
            DeclareWayDTO declareWayDTO = new DeclareWayDTO();
            BeanUtils.copyProperties(l, declareWayDTO);
            return declareWayDTO;
        }).collect(Collectors.toList());

        ListVO result = new ListVO();
        result.setDataList(dtoList);

        PageInfo<DeclareWayDO> pageInfo = new PageInfo(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    @Override
    public void changeEnable(Long id) throws ArgsErrorException {
        if (id == null) {
            throw new ArgsErrorException("id为空");
        }
        DeclareWayDTO dto = declareWayBaseService.findById(id);
        if (Objects.isNull(dto)) {
            throw new ArgsErrorException("未找到关联申报方式");
        }
        DeclareWayDO declareWayDO = new DeclareWayDO();
        declareWayDO.setId(dto.getId());
        if (Objects.equals(1, dto.getEnable())) {
            declareWayDO.setEnable(0);
        } else if (Objects.equals(0, dto.getEnable())) {
            declareWayDO.setEnable(1);
        } else {
            throw new ArgsErrorException("启用状态异常");
        }
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(declareWayDO);
        }
        declareWayDO.setUpdateTime(new Date());
        declareWayBaseService.updateByPrimaryKey(declareWayDO);
    }

    @Override
    public List<DeclareWayDTO> listAll() {
        return declareWayBaseService.listAll();
    }

    @Override
    public List<DeclareWayDTO> listByType(String type, String property) {
        return declareWayBaseService.listByType(type, null);
    }

    @Override
    public List<DeclareWayDTO> listByTypeAndNode(String type) {
        return this.listByTypeAndNode(type);
    }

    @Override
    public List<DeclareWayDTO> listByTypeAndNode(String type, Integer customsTransferNode) {
        return declareWayBaseService.listByTypeAndNode(type, customsTransferNode);
    }
}
