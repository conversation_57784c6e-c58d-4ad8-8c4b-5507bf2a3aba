package com.danding.cds.message.impl.mapper;

import com.danding.cds.message.impl.entity.MessageDO;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.additional.insert.InsertListMapper;
import tk.mybatis.mapper.common.Mapper;

public interface MessageMapper extends Mapper<MessageDO>, InsertListMapper<MessageDO>, BatchUpdateMapper<MessageDO>, AggregationPlusMapper<MessageDO> {

    @Insert("INSERT INTO `ccs_message_history` " +
            "(`id`,`notify_url`,`type`,`tag_json`,`status`,`business_code`,`active_data`,`request_data`,`create_by`,`update_by`,`create_time`,`update_time`,`deleted`) " +
            "VALUES " +
            "(#{id},#{notifyUrl},#{type},#{tagJson},#{status},#{businessCode},#{activeData},#{requestData},#{createBy},#{updateBy},#{createTime},#{updateTime},#{deleted});")
    void insertHistory(MessageDO messageDO);
}