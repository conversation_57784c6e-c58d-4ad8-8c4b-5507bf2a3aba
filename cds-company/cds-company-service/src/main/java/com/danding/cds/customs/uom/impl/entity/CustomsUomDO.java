package com.danding.cds.customs.uom.impl.entity;

import com.danding.cds.common.model.BaseDO;
import lombok.Data;

import javax.persistence.Table;
import java.io.Serializable;

/**
 * 海关单位表(CustomsUomDO)实体类
 *
 * <AUTHOR>
 * @since 2020-04-28 16:43:57
 */
@Data
@Table(name = "ccs_customs_uom")
public class CustomsUomDO extends BaseDO implements Serializable {
    private static final long serialVersionUID = 5644196609300197093L;

    /**
     * 海关单位编号
     */
    private String code;
    /**
     * 海关单位名称
     */
    private String name;
}
