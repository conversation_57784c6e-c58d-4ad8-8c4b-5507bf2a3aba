package com.danding.cds.bean.dao;

import com.danding.cds.bean.dto.DeclareWayPropertyEnum;
import com.danding.cds.common.model.BaseDO;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;

@Table(name = "ccs_declare_way")
@Getter
@Setter
public class DeclareWayDO extends BaseDO {
    /**
     * 申报方式编码
     */
    private String code;

    /**
     * 申报方式类型
     * 基础:basic
     * 代理:proxy
     * {@link DeclareWayPropertyEnum}
     */
    private String property;

    /**
     * 申报方式名称
     */
    private String name;

    /**
     * 申报类型：支付单-payment;订单-customsOrder;运单-shipment;清单-inventory;清单取消-inventoryCancel;清单退货-inventoryRefund;
     */
    private String type;

    /**
     * 传输节点
     * customs_transfer_node
     */
    private Integer customsTransferNode;

    /**
     * 申报实现(申报方式实现的名称)
     */
    @Column(name = "declare_impl")
    private String declareImpl;

    /**
     * 状态:0.停用;1.启用(默认)
     */
    private Integer enable;
}