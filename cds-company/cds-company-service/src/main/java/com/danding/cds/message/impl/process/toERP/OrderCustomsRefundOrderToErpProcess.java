package com.danding.cds.message.impl.process.toERP;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.cds.c.api.rpc.OrderCRpc;
import com.danding.cds.callback.api.dto.OrderActiveInfo;
import com.danding.cds.common.config.OrderCCallConfig;
import com.danding.cds.common.utils.JSONUtils;
import com.danding.cds.message.api.dto.MessageDTO;
import com.danding.cds.message.api.enums.MessageType;
import com.danding.cds.message.impl.process.MessageExecuteResult;
import com.danding.cds.message.impl.process.MessageProcess;
import com.danding.cds.message.impl.process.MessageRegistry;
import com.danding.cds.message.impl.process.toERP.Utils.MyStringUtils;
import com.danding.cds.message.impl.process.toERP.entity.EntranceRpcParam;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.api.dto.OrderExtra;
import com.danding.cds.order.api.service.OrderService;
import com.danding.cds.v2.api.ErpIOutOrderRpcFacadeService;
import com.danding.cds.v2.bean.dto.EntityWarehouseDTO;
import com.danding.cds.v2.service.EntityWarehouseService;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Component
@Slf4j
public class OrderCustomsRefundOrderToErpProcess extends MessageProcess {


    @DubboReference
    private OrderService orderService;
    @DubboReference
    private OrderCRpc orderCRpc;
    @Resource
    private OrderCCallConfig orderCCallConfig;

    @DubboReference
    private EntityWarehouseService entityWarehouseService;


    @DubboReference
    private ErpIOutOrderRpcFacadeService erpIOutOrderRpcFacadeService;

    public OrderCustomsRefundOrderToErpProcess(MessageRegistry registry) {
        super(registry);
    }

    @Override
    public MessageType getType() {
        return MessageType.ORDER_CUSTOMS_REFUND_TO_ERP;
    }

    @Override
    public Map<String, Object> buildSendInfo(MessageDTO messageDTO) {
        OrderActiveInfo activeInfo = JSON.parseObject(messageDTO.getActiveData(), OrderActiveInfo.class);
        log.info("[op:buildSendInfo] 退货回执打印-原- {}", activeInfo);
        if (activeInfo == null) {
            log.error("海关退货回执消息任务生成异常，业务编码: {} ,空数据", messageDTO.getBusinessCode());
            return null;
        }
        OrderDTO orderDTO = orderCCallConfig.isOrderCCall(this.getClass()) ? orderCRpc.findByDeclareOrderNo(messageDTO.getBusinessCode()) : orderService.findByDeclareOrderNo(messageDTO.getBusinessCode());
        if (Objects.isNull(orderDTO)) {
            log.error("海关退货回执消息任务生成异常：订单不存在-申报单号={}", messageDTO.getBusinessCode());
        }
        String content = activeInfo.getResponseMsg();
        OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
        String cbNo = orderExtra.getSubmit().getOuterCustomsNo();

        String warehouseCode = Objects.nonNull(orderExtra.getSubmit()) ? orderExtra.getSubmit().getErpPhyWarehouseSn() : "";
        if (StringUtil.isNotEmpty(warehouseCode)) {
            List<EntityWarehouseDTO> entityWarehouseDTOS = entityWarehouseService.findDTOByErpCode(warehouseCode);
            warehouseCode = entityWarehouseDTOS.stream().map(EntityWarehouseDTO::getWmsWarehouseCode).findFirst().orElse("");
        }
        if (StrUtil.isBlank(cbNo)) {
            //获取LP单号
            String lpNo = erpIOutOrderRpcFacadeService.getLpNoByGsNo(orderDTO.getSystemGlobalSn());
            if (StringUtil.isEmpty(lpNo)) {
                log.error("海关退货回执回传erp失败:通过GS单号【{}】,获取LP单号为空", orderDTO.getSystemGlobalSn());
                return null;
            }
            //获取CB单号
            cbNo = erpIOutOrderRpcFacadeService.getCbNoByLpNo(lpNo, orderDTO.getSystemGlobalSn(), warehouseCode);
            if (StringUtil.isEmpty(cbNo)) {
                log.error("海关退货回执回传erp失败:通过LP单号【{}】,GS单号【{}】,获取CB单号为空", lpNo, orderDTO.getSystemGlobalSn());
                return null;
            }
        }
        //将CB单号填入报文的copNo中
        if (StringUtils.isNotEmpty(cbNo)) {
            if (content.contains("<copNo>")) {
                content = MyStringUtils.replaceBetween(content, "<copNo>", "</copNo>", cbNo);
            } else {
                String replacement = "<copNo>" + cbNo + "</copNo></CEB626Message>";
                content = content.replace("</CEB626Message>", replacement);
            }
        }
        log.info("回传ERP 退货回执打印 content={}", content);
        Map<String, String> businessData = new HashMap<>();
        businessData.put("content", content);
        businessData.put("warehouseCode", warehouseCode);

        //撤单回执回传接口
        EntranceRpcParam param = EntranceRpcParam.of(orderDTO.getSystemGlobalSn(),
                "CCS", "CNZXC", "54", 3, JSONUtils.toJSONString(businessData));
        return objToMap(param);
    }

    @Override
    public void processResultBody(MessageExecuteResult result, String body) {
        if (isJSONValid(body)) {
            JSONObject jsonObject = JSON.parseObject(body);
            String code = jsonObject.get("code").toString();
            result.setSuccess(Objects.equals(code, "200"));
        } else {
            super.processResultBody(result, body);
        }
    }
}
