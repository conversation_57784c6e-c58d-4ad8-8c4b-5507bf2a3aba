package com.danding.cds.express.impl.service;


import com.alibaba.fastjson.JSON;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.JSONUtils;
import com.danding.cds.express.api.dto.ExpressFilterDTO;
import com.danding.cds.express.api.dto.ExpressFilterSearch;
import com.danding.cds.express.api.service.ExpressFilterService;
import com.danding.cds.express.impl.entity.ExpressDO;
import com.danding.cds.express.impl.entity.ExpressFilterDO;
import com.danding.cds.express.impl.mapper.ExpressFilterMapper;
import com.danding.cds.express.impl.mapper.ExpressMapper;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;
import java.util.Objects;

@DubboService
@Slf4j
public class ExpressFilterServiceImpl implements ExpressFilterService {
    @Autowired
    private ExpressFilterMapper expressFilterMapper;
    @Autowired
    private ExpressMapper expressMapper;

    public boolean checkUnquie(ExpressFilterDTO entity) {
        Example example = new Example(ExpressFilterDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("refExpressId", entity.getRefExpressId());
        criteria.andEqualTo("deleted", false);
        criteria.andEqualTo("prov", entity.getProv());
        criteria.andEqualTo("city", entity.getCity());
        criteria.andEqualTo("area", entity.getArea());
        return CollectionUtils.isEmpty(expressFilterMapper.selectByExample(example));
    }

    public boolean createExpressFilter(ExpressFilterDTO entity)
    {
        ExpressFilterDO expressFilterDO = new ExpressFilterDO();
        BeanUtils.copyProperties(entity,expressFilterDO);
        ExpressDO  expressDO = expressMapper.selectByPrimaryKey(expressFilterDO.getRefExpressId());
        expressFilterDO.setRefExpressCode(expressDO.getCode());
        if(expressFilterDO.getReplaceExpressId()!=null&&expressFilterDO.getReplaceExpressId()>0) {
            expressDO = expressMapper.selectByPrimaryKey(expressFilterDO.getReplaceExpressId());
            expressFilterDO.setReplaceExpressCode(expressDO.getCode());
        }
        UserUtils.setCreateAndUpdateBy(expressFilterDO);
        expressFilterDO.setCreateTime(new Date());
        expressFilterDO.setUpdateTime(new Date());
        return expressFilterMapper.insertSelective(expressFilterDO) > 0;
    }

    @Override
    public boolean deleteExpressFilter(Long id) {
        ExpressFilterDO entity = new ExpressFilterDO();
        entity.setId(id);
        entity.setDeleted(true);
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(entity);
        }
        entity.setUpdateTime(new Date());
        return expressFilterMapper.updateByPrimaryKeySelective(entity) > 0;
    }

    @Override
    @PageSelect
    public ListVO<ExpressFilterDTO> paging(ExpressFilterSearch entity)
    {
        Example example = new Example(ExpressFilterDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("refExpressId", entity.getId());
        criteria.andEqualTo("deleted", false);
        List<ExpressFilterDO> list =  expressFilterMapper.selectByExample(example);
        ListVO<ExpressFilterDTO> result = new ListVO<>();
        //result.setDataList(outList);
        result.setDataList(JSON.parseArray(JSONUtils.toJSONString(list), ExpressFilterDTO.class));
        // 分页
        PageInfo<ExpressFilterDTO> pageInfo = new PageInfo(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        result.setPage(pageResult);
        return result;
    }
}
