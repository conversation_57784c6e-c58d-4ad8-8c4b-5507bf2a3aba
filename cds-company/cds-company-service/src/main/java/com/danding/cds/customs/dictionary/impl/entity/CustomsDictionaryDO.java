package com.danding.cds.customs.dictionary.impl.entity;

import com.danding.cds.common.model.BaseDO;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;

@Getter
@Setter
@Table(name = "ccs_customs_dictionary")
public class CustomsDictionaryDO extends BaseDO implements Serializable {

    /**
     * 编号
     */
    @Column(name = "code")
    private String code;

    /**
     * 编号名称
     */
    @Column(name = "name")
    private String name;

    /**
     * 类型
     */
    @Column(name = "type")
    private String type;

    /**
     * 数据类型
     */
    @Column(name = "main_type")
    private Integer mainType;

    /**
     * 启用状态
     */
    @Column(name = "enable")
    private Integer enable;

    /**
     * 是否允许编辑
     */
    @Column(name = "enable_modify")
    private Boolean enableModify;
}
