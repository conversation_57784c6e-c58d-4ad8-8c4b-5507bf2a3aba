package com.danding.cds.vehicle.impl.mapper;


import com.danding.cds.vehicle.impl.entity.VehicleResourceDO;
import com.danding.logistics.mybatis.common.Mapper;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import tk.mybatis.mapper.additional.insert.InsertListMapper;

public interface VehicleResourceMapper extends Mapper<VehicleResourceDO>, InsertListMapper<VehicleResourceDO>, BatchUpdateMapper<VehicleResourceDO>, AggregationPlusMapper<VehicleResourceDO> {
}