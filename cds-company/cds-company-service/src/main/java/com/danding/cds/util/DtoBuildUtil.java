package com.danding.cds.util;

import com.alibaba.fastjson.JSON;
import com.danding.cds.route.api.dto.RouteDTO;
import com.danding.cds.route.api.dto.RouteExtra;
import com.danding.cds.route.impl.entity.RouteDO;
import org.springframework.beans.BeanUtils;

import java.util.Objects;

/**
 * @program: cds-center
 * @description: dtobuild
 * @author: 潘本乐（Belep）
 * @create: 2021-10-25 20:49
 **/
public class DtoBuildUtil {

    public static RouteDTO buildRouteDTO(RouteDO routeDO) {
        if (Objects.isNull(routeDO)) {
            return null;
        }
        RouteDTO result = new RouteDTO();
        BeanUtils.copyProperties(routeDO, result);
        result.setActionList(JSON.parseArray(routeDO.getActionJson(), String.class));
        BeanUtils.copyProperties(JSON.parseObject(routeDO.getExtraJson(), RouteExtra.class), result);
        return result;
    }
}
