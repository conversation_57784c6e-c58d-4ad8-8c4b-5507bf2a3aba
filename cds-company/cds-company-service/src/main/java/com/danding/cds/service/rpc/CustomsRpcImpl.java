package com.danding.cds.service.rpc;

import com.danding.cds.company.out.api.CustomsRpc;
import com.danding.cds.company.out.bean.vo.CustomsDistrictVO;
import com.danding.cds.customs.district.api.enums.CustomsDistrictEnum;
import com.danding.cds.service.base.CustomsBaseService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.stream.Collectors;

@DubboService
public class CustomsRpcImpl implements CustomsRpc {
    @Autowired
    private CustomsBaseService customsBaseService;

    @Override
    public List<CustomsDistrictVO> getAllCustoms() {
        List<CustomsDistrictEnum> allCustoms = customsBaseService.getAllCustoms();
        List<CustomsDistrictVO> collect = allCustoms.stream().map(c -> {
            CustomsDistrictVO districtVO = new CustomsDistrictVO();
            districtVO.setCustoms(c.getCustoms());
            districtVO.setCode(c.getCode());
            districtVO.setDesc(c.getDesc());
            districtVO.setPortCode(c.getPortCode());
            return districtVO;
        }).collect(Collectors.toList());
        return collect;
    }
}
