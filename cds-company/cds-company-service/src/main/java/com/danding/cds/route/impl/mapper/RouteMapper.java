package com.danding.cds.route.impl.mapper;

import com.danding.cds.route.api.dto.RouteSearch;
import com.danding.cds.route.impl.entity.RouteDO;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.additional.insert.InsertListMapper;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;


public interface RouteMapper extends Mapper<RouteDO>, AggregationPlusMapper<RouteDO>, BatchUpdateMapper<RouteDO>, InsertListMapper<RouteDO> {

    @Results(id = "RouteMap", value = {
            @Result(property = "id", column = "id"),
            @Result(property = "name", column = "name"),
            @Result(property = "code", column = "code"),
            @Result(property = "actionJson", column = "action_json"),
            @Result(property = "extraJson", column = "extra_json"),
            @Result(property = "declareWay", column = "declare_way"),
            @Result(property = "declareConfig", column = "declare_config"),
            @Result(property = "enable", column = "enable"),
            @Result(property = "routeTag", column = "route_tag"),
            @Result(property = "createBy", column = "create_by"),
            @Result(property = "updateBy", column = "update_by"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time"),
            @Result(property = "deleted", column = "deleted")
    })

    @Select(value = "<script>" +
            "SELECT R.* " +
            "FROM `ccs_company`.`ccs_route` R " +
            "WHERE R.deleted = 0 " +
            "<if test='condition.ebpId != null'>" +
            "AND R.extra_json-&gt;'$.ebpId' = #{condition.ebpId} " +
            "</if>" +
            "<if test='condition.ebcId != null'>" +
            "AND R.extra_json-&gt;'$.ebcId' = #{condition.ebcId} " +
            "</if>" +
            "<if test='condition.assureCompanyId != null'>" +
            "AND R.extra_json-&gt;'$.assureCompanyId' = #{condition.assureCompanyId} " +
            "</if>" +
            "<if test='condition.bookIdList != null'>" +
            "AND R.extra_json -&gt; '$.customsBookId' in " +
            "<foreach collection='condition.bookIdList' item='item' index ='index' open='(' separator=',' close=')' > " +
            " #{item} " +
            "</foreach>" +
            "</if>" +
            "<if test='condition.msgDeclareCompanyId != null'>" +
            "AND (R.extra_json-&gt;'$.listDeclareCompanyId' = #{condition.msgDeclareCompanyId} " +
            "OR R.extra_json-&gt;'$.orderDeclareCompanyId' = #{condition.msgDeclareCompanyId} " +
            "OR R.extra_json-&gt;'$.logisticsDeclareCompanyId' = #{condition.msgDeclareCompanyId}) " +
            "</if>" +
            "<if test='condition.nameId != null'>" +
            "AND R.id = #{condition.nameId} " +
            "</if>" +
            "<if test='condition.codeId != null'>" +
            "AND R.id = #{condition.codeId} " +
            "</if>" +
            "<if test='condition.enable != null'>" +
            "<if test='condition.enable == 1'>" +
            "AND R.enable = 1 " +
            "</if>" +
            "<if test='condition.enable == 0'>" +
            "AND R.enable = 0 " +
            "</if>" +
            "</if>" +
            "<if test='condition.createFrom != null'>" +
            "AND R.create_time &gt;= #{condition.createFrom} " +
            "</if>" +
            "<if test='condition.createTo != null'>" +
            "AND R.create_time &lt;= #{condition.createTo} " +
            "</if>" +
            "<if test='condition.routeTags != null'>" +
            "AND R.`route_tag` &amp; #{condition.routeTags} = #{condition.routeTags} " +
            "</if>" +
            "ORDER BY R.CREATE_TIME DESC " +
            "</script>"
    )
    List<RouteDO> routeInfoSelect(@Param("condition") RouteSearch condition);
}