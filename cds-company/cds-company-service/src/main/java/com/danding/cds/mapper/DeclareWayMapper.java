package com.danding.cds.mapper;

import com.danding.cds.bean.dao.DeclareWayDO;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import tk.mybatis.mapper.additional.insert.InsertListMapper;
import tk.mybatis.mapper.common.Mapper;

public interface DeclareWayMapper extends Mapper<DeclareWayDO>, InsertListMapper<DeclareWayDO>,
        BatchUpdateMapper<DeclareWayDO>, AggregationPlusMapper<DeclareWayDO> {
}