package com.danding.cds.service.base;

import com.danding.cds.bean.dao.DeclareWayDO;
import com.danding.cds.bean.dto.DeclareWayDTO;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.mapper.DeclareWayMapper;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: cds-center
 * @description: DECLARE
 * @author: 潘本乐（Belep）
 * @create: 2021-11-17 16:20
 **/
@Service
public class DeclareWayBaseService {

    @Autowired
    private DeclareWayMapper declareWayMapper;

    public void checkDuplicate(Long id, String code, String declareImpl) throws ArgsErrorException {
        Example example = new Example(DeclareWayDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("deleted", false);
        if (StringUtils.isEmpty(code)) {
            criteria.orEqualTo("code", code);
        }
        if (StringUtils.isEmpty(declareImpl)) {
            criteria.orEqualTo("declareImpl", declareImpl);
        }
        if (Objects.nonNull(id)) {
            criteria.andNotEqualTo("id", id);
        }
        List<DeclareWayDO> declareWayDOList = declareWayMapper.selectByExample(example);
        if (!CollectionUtils.isEmpty(declareWayDOList)) {
            if (declareWayDOList.stream().filter(d -> Objects.nonNull(d.getCode())).anyMatch(d -> Objects.equals(d.getCode(), code))) {
                throw new ArgsErrorException("申报方式编码重复");
            }
            if (declareWayDOList.stream().filter(d -> Objects.nonNull(d.getDeclareImpl())).anyMatch(d -> Objects.equals(d.getDeclareImpl(), declareImpl))) {
                throw new ArgsErrorException("申报实现方式重复");
            }
        }
    }

    public DeclareWayDTO findByCode(String code) {
        return this.findByCode(code, null);
    }

    public DeclareWayDTO findByCode(String code, String property) {
        Example example = new Example(DeclareWayDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("code", code)
                .andEqualTo("deleted", false);
        if (Objects.nonNull(property)) {
            criteria.andEqualTo("property", property);
        }
        DeclareWayDO declareWayDO = declareWayMapper.selectOneByExample(example);
        DeclareWayDTO declareWayDto = new DeclareWayDTO();
        BeanUtils.copyProperties(declareWayDO, declareWayDto);
        return declareWayDto;
    }

    public List<DeclareWayDTO> findByCodes(Set<String> codes) {
        return findByCodesImpl(codes);
    }

    public List<DeclareWayDTO> findByCodes(List<String> codes) {
        return findByCodesImpl(codes);
    }

    private List<DeclareWayDTO> findByCodesImpl(Iterable<String> codes) {
        Example example = new Example(DeclareWayDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("code", codes);
        List<DeclareWayDO> declareWayDOList = declareWayMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(declareWayDOList)) {
            return Collections.emptyList();
        }
        return declareWayDOList.stream()
                .map(z -> {
                    DeclareWayDTO declareWayDto = new DeclareWayDTO();
                    BeanUtils.copyProperties(z, declareWayDto);
                    return declareWayDto;
                }).collect(Collectors.toList());
    }

    public void insert(DeclareWayDTO dto) {
        DeclareWayDO declareWayDO = new DeclareWayDO();
        BeanUtils.copyProperties(dto, declareWayDO);
        UserUtils.setCreateAndUpdateBy(declareWayDO);
        declareWayMapper.insertSelective(declareWayDO);
    }

    public void insert(DeclareWayDO declareWayDO) {
        UserUtils.setCreateAndUpdateBy(declareWayDO);
        declareWayMapper.insertSelective(declareWayDO);
    }

    public void update(DeclareWayDTO dto) {
        DeclareWayDO declareWayDO = new DeclareWayDO();
        BeanUtils.copyProperties(dto, declareWayDO);
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(declareWayDO);
        }
        declareWayDO.setUpdateTime(new Date());
        declareWayMapper.updateByPrimaryKeySelective(declareWayDO);
    }

    public DeclareWayDTO findById(Long id) {
        if (id == null) {
            return null;
        }
        DeclareWayDO declareWayDO = declareWayMapper.selectByPrimaryKey(id);
        DeclareWayDTO declareWayDTO = this.buildDTO(declareWayDO);
        return declareWayDTO;
    }

    private DeclareWayDTO buildDTO(DeclareWayDO declareWayDO) {
        DeclareWayDTO declareWayDTO = new DeclareWayDTO();
        BeanUtils.copyProperties(declareWayDO, declareWayDTO);
        return declareWayDTO;
    }

    public void updateByPrimaryKey(DeclareWayDO declareWayDO) {
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(declareWayDO);
        }
        declareWayDO.setUpdateTime(new Date());
        declareWayMapper.updateByPrimaryKeySelective(declareWayDO);
    }

    public void deleteById(Long id) {
        if (Objects.isNull(id)) {
            return;
        }
        DeclareWayDO declareWayDO = new DeclareWayDO();
        declareWayDO.setId(id);
        declareWayDO.setDeleted(true);
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(declareWayDO);
        }
        declareWayDO.setUpdateTime(new Date());
        declareWayMapper.updateByPrimaryKeySelective(declareWayDO);

    }

    public List<DeclareWayDTO> listByType(String type, String property) {
        DeclareWayDO declareWayDO = new DeclareWayDO();
        declareWayDO.setEnable(1);
        declareWayDO.setDeleted(false);
        declareWayDO.setType(type);
        if (Objects.nonNull(property)) {
            declareWayDO.setProperty(property);
        }
        List<DeclareWayDO> declareWayDOS = declareWayMapper.select(declareWayDO);
        List<DeclareWayDTO> declareWayDTOList = declareWayDOS.stream().map(d -> {
            DeclareWayDTO dto = new DeclareWayDTO();
            BeanUtils.copyProperties(d, dto);
            return dto;
        }).collect(Collectors.toList());
        return declareWayDTOList;
    }

    public List<DeclareWayDTO> listAll() {
        DeclareWayDO declareWayDO = new DeclareWayDO();
        declareWayDO.setDeleted(false);
        List<DeclareWayDO> declareWayDOS = declareWayMapper.select(declareWayDO);
        return declareWayDOS.stream().map(d -> {
            DeclareWayDTO dto = new DeclareWayDTO();
            BeanUtils.copyProperties(d, dto);
            return dto;
        }).collect(Collectors.toList());
    }

    public List<DeclareWayDTO> listByTypeAndNode(String type, Integer customsTransferNode) {
        DeclareWayDO declareWayDO = new DeclareWayDO();
        if (Objects.nonNull(customsTransferNode)) {
            declareWayDO.setCustomsTransferNode(customsTransferNode);
        }
        declareWayDO.setType(type);
        List<DeclareWayDO> declareWayDOS = declareWayMapper.select(declareWayDO);
        return ConvertUtil.listConvert(declareWayDOS, DeclareWayDTO.class);
    }
}
