package com.danding.cds.message.impl.process.customs;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.danding.cds.c.api.bean.dto.JdInventoryCustomsCallbackInfo;
import com.danding.cds.c.api.bean.dto.JdInventoryCustomsCallbackItem;
import com.danding.cds.c.api.bean.dto.JdInventoryCustomsCallbackRequest;
import com.danding.cds.c.api.rpc.CustomsInventoryRpc;
import com.danding.cds.callback.api.dto.OrderActiveInfo;
import com.danding.cds.common.bean.dto.JdlPostDTO;
import com.danding.cds.common.utils.JdlRequestUtils;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryItemDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryItemExtra;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.item.api.vo.CustomsBookResVo;
import com.danding.cds.message.api.dto.MessageDTO;
import com.danding.cds.message.api.dto.MessageTaskDTO;
import com.danding.cds.message.api.enums.MessageType;
import com.danding.cds.message.impl.entity.MessageSubscribeDO;
import com.danding.cds.message.impl.mapper.MessageSubscribeMapper;
import com.danding.cds.message.impl.process.MessageExecuteResult;
import com.danding.cds.message.impl.process.MessageProcess;
import com.danding.cds.message.impl.process.MessageRegistry;
import com.danding.cds.v2.bean.dto.JdServProviderDTO;
import com.danding.cds.v2.service.JdServProviderService;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

@Component
@Slf4j
@RefreshScope
public class JdInventoryCustomsCallbackProcess extends MessageProcess {

    @DubboReference
    private CustomsInventoryRpc customsInventoryRpc;

    @DubboReference
    private JdServProviderService jdServProviderService;

    @DubboReference
    private CompanyService companyService;

    @DubboReference
    private CustomsBookService customsBookService;

    @Resource
    private MessageSubscribeMapper messageSubscribeMapper;

    public JdInventoryCustomsCallbackProcess(MessageRegistry registry) {
        super(registry);
    }

    @Override
    public MessageType getType() {
        return MessageType.JD_INVENTORY_CUSTOMS_CALLBACK;
    }

    @Override
    public Map<String, Object> buildSendInfo(MessageDTO messageDTO) {
        String activeData = messageDTO.getActiveData();
        OrderActiveInfo orderActiveInfo = JSON.parseObject(activeData, OrderActiveInfo.class);
        try {
            JdlPostDTO jdlPostDTO = getJdlPostDTO(orderActiveInfo.getCustomsInventorySn(), orderActiveInfo.getCustomsTime());
            return objToMap(jdlPostDTO);
        } catch (ArgsInvalidException e) {
            log.error("JdInventoryCustomsCallbackProcess Error occurred while building send info error = {}", e.getErrorMessage());
        } catch (Exception e) {
            log.error("JdInventoryCustomsCallbackProcess Error occurred while building send info", e);
        }
        return new HashMap<>();
    }

    private JdlPostDTO getJdlPostDTO(String customsInventorySn, Date customsTime) {
        CustomsInventoryDTO customsInventoryDTO = customsInventoryRpc.findBySnSection(customsInventorySn);
        if (customsInventoryDTO == null) {
            throw new ArgsInvalidException("清单信息不存在");
        }
        List<CustomsInventoryItemDTO> customsInventoryItemDTOS = customsInventoryRpc
                .listItemByIdSection(customsInventoryDTO.getId(), customsInventoryDTO.getCreateTime());
        JdServProviderDTO jdServProviderDTO = jdServProviderService.getServProviderByBookId(customsInventoryDTO.getAccountBookId());
        if (Objects.isNull(jdServProviderDTO)) {
            log.info("JdInventoryCustomsCallbackProcess jdServProviderDTO为空 declareOrderNo={}", customsInventoryDTO.getDeclareOrderNo());
            throw new ArgsInvalidException("京东服务商信息不存在");
        }
        // 创建最外层的JSONArray
        JSONArray param1 = new JSONArray();
        JdInventoryCustomsCallbackRequest request = new JdInventoryCustomsCallbackRequest();
        JdInventoryCustomsCallbackInfo info = new JdInventoryCustomsCallbackInfo();
        // 先随机生成一个guid，有问题再解析报文中的
        info.setGuid(StrUtil.uuid().toUpperCase());
        customsTime = Objects.nonNull(customsTime) ? customsTime : customsInventoryDTO.getCustomsPassTime();
        info.setAppTime(DateUtil.format(customsTime, "yyyyMMddHHmmss"));
        info.setOrderId(customsInventoryDTO.getDeclareOrderNo());
        CompanyDTO ebp = companyService.findById(customsInventoryDTO.getEbpId());
        info.setEbpCode(ebp.getUnifiedCrossBroderCode());
        info.setEbpName(ebp.getName());
        CompanyDTO ebc = companyService.findById(customsInventoryDTO.getEbcId());
        info.setEbcCode(ebc.getUnifiedCrossBroderCode());
        info.setEbcName(ebc.getName());
        info.setLogisticsNo(customsInventoryDTO.getLogisticsNo());
        CompanyDTO logisticsCompany = companyService.findById(customsInventoryDTO.getLogisticsCompanyId());
        info.setLogisticsCode(logisticsCompany.getUnifiedCrossBroderCode());
        info.setLogisticsName(logisticsCompany.getName());
        CompanyDTO assureCompany = companyService.findById(customsInventoryDTO.getAssureCompanyId());
        info.setAssureCode(assureCompany.getUnifiedCrossBroderCode());
        CustomsBookResVo customsBookResVo = customsBookService.findByIdV2(customsInventoryDTO.getAccountBookId());
        info.setEmsNo(customsBookResVo.getBookNo());
        info.setInvtNo(customsInventoryDTO.getInventoryNo());
        info.setIeFlag("I");
        info.setDeclTime(DateUtil.format(customsInventoryDTO.getLastDeclareTime(), "yyyyMMdd"));
        info.setCustomsCode(customsBookResVo.getCustomsAreaCode());
        info.setPortCode(customsBookResVo.getCustomsAreaCode());
        info.setBuyerIdType(1);// 身份证
        info.setBuyerIdNumber(customsInventoryDTO.getBuyerIdNumber());
        info.setBuyerName(customsInventoryDTO.getBuyerName());
        info.setBuyerTelephone(customsInventoryDTO.getBuyerTelNumber());
        info.setConsigneeAddress(customsInventoryDTO.getConsigneeAddress());
        CompanyDTO agentCompany = companyService.findById(customsInventoryDTO.getAgentCompanyId());
        info.setAgentCode(agentCompany.getUnifiedCrossBroderCode());
        info.setAgentName(agentCompany.getName());
        CompanyDTO areaCompany = companyService.findById(customsInventoryDTO.getAreaCompanyId());
        info.setAreaCode(areaCompany.getCode());
        info.setAreaName(areaCompany.getName());
        info.setTradeMode("1210");
        info.setTransMode("7");
        info.setCountry("142");
        info.setFreight(customsInventoryDTO.getFeeAmount());
        info.setInsuranceFee(customsInventoryDTO.getInsureAmount());
        info.setCurrency("142");
        info.setQuantity(1);
        info.setGrossWeight(customsInventoryDTO.getGrossWeight());
        info.setNetWeight(customsInventoryDTO.getNetWeight());
        info.setNote(customsInventoryDTO.getNote());
        List<JdInventoryCustomsCallbackItem> items = new ArrayList<>();
        for (CustomsInventoryItemDTO customsInventoryItemDTO : customsInventoryItemDTOS) {
            JdInventoryCustomsCallbackItem item = new JdInventoryCustomsCallbackItem();
            CustomsInventoryItemExtra itemExtra = JSON.parseObject(customsInventoryItemDTO.getExtraJson(), CustomsInventoryItemExtra.class);
            item.setGnum(customsInventoryItemDTO.getSort());
            item.setItemRecordNo(itemExtra.getProductId());
            item.setSellerRecord(itemExtra.getProductId());
            item.setItemName(customsInventoryItemDTO.getItemName());
            item.setHsCode(itemExtra.getHsCode());
            item.setGoodsName(customsInventoryItemDTO.getItemName());
            item.setSpe(itemExtra.getGoodsModel());
            item.setBarCode(itemExtra.getBarCode());
            item.setOriginCountry(itemExtra.getOriginCountry());
            item.setCurrency("142");
            item.setQty(BigDecimal.valueOf(customsInventoryItemDTO.getCount()));
            item.setUnit(itemExtra.getGoodsUnit());
            item.setLegalAmount1(itemExtra.getFirstUnitAmount());
            item.setLegalUnit1(itemExtra.getFirstUnit());
            item.setLegalAmount2(itemExtra.getSecondUnitAmount());
            item.setLegalUnit2(itemExtra.getSecondUnit());
            item.setPrice(customsInventoryItemDTO.getUnitPrice());
            if (Objects.nonNull(customsInventoryItemDTO.getTotalPrice())) {
                item.setTotalPrice(customsInventoryItemDTO.getTotalPrice());
            } else if (Objects.nonNull(item.getPrice()) && Objects.nonNull(item.getQty())){
                item.setTotalPrice(item.getPrice().multiply(item.getQty()));
            } else {
                item.setTotalPrice(BigDecimal.ZERO);
            }
            items.add(item);
        }
        request.setInfo(info);
        request.setItems(items);
        param1.add(request);
        JdlPostDTO jdlPostDTO = new JdlPostDTO();
        jdlPostDTO.setPath("/servicecallbackjsfservice/customverification");
        jdlPostDTO.setBody(JSON.toJSONString(param1));
        jdlPostDTO.setAppKey(jdServProviderDTO.getJdlAppKey());
        jdlPostDTO.setAppSecret(jdServProviderDTO.getJdlAppSecret());
        log.info("京东电商清单消息打印 - {}", JSON.toJSONString(jdlPostDTO));
        return jdlPostDTO;
    }

    @Override
    public MessageExecuteResult execute(MessageTaskDTO messageTaskDTO) {
        MessageExecuteResult result = new MessageExecuteResult();
        log.info("京东电商清单回执消息 业务编码: {} ,请求数据信息: {}", messageTaskDTO.getBusinessCode(), JSON.toJSONString(messageTaskDTO));
        try {
            String requestData = messageTaskDTO.getRequestData();
            JdlPostDTO jdlPostDTO = JSON.parseObject(requestData, JdlPostDTO.class);
            MessageSubscribeDO subscribeDO = messageSubscribeMapper.selectByPrimaryKey(messageTaskDTO.getSubscribeId());
            jdlPostDTO.setUri(subscribeDO.getNotifyUrl());
            log.info("JdInventoryCustomsCallbackProcess execute jdlPostDTO={}", JSON.toJSONString(jdlPostDTO));
            String res = JdlRequestUtils.doPostJDL(jdlPostDTO.getUri(), jdlPostDTO.getPath(), jdlPostDTO.getBody(), jdlPostDTO.getAppKey(), jdlPostDTO.getAppSecret());
            log.info("JdInventoryCustomsCallbackProcess execute res={}", res);
            ObjectMapper objectMapper = new ObjectMapper();
            Map<String, Object> jdResult = objectMapper.readValue(res, Map.class);
            String resultCode = (String) jdResult.get("resultCode");
            result.setResponseMsg(res);
            if (Objects.equals(resultCode, "0")) {
                result.setSuccess(true);
                result.setResponseMsg(res);
            } else {
                String message = (String) jdResult.get("message");
                result.setSuccess(false);
                result.setResponseMsg("回传京东失败：" + message);
            }
        } catch (Exception e) {
            result.setSuccess(false);
            result.setResponseMsg("回传京东失败：" + e.getMessage());
            log.error("京东电商清单回执消息失败 系统异常 京东消息回传业务编码:{} 消息回调处理异常：{}", messageTaskDTO.getBusinessCode(), e.getMessage(), e);
        }
        return result;
    }
}
