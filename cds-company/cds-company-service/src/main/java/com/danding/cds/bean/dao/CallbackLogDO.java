package com.danding.cds.bean.dao;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Create 2021/8/31  15:34
 * @Describe
 **/
@Data
@Accessors(chain = true)
@Table(name = "ccs_callback_log")
public class CallbackLogDO implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "JDBC")
    private Long id;

    @Column(name = "retry_url")
    @ApiModelProperty("回调地址")
    private String retryUrl;

    @Column(name = "customs_type")
    @ApiModelProperty("回执海关类型")
    private String customsType;

    @Column(name = "callback_type")
    @ApiModelProperty("回执类型")
    private String callbackType;

    @Column(name = "request_head")
    @ApiModelProperty("请求头")
    private String requestHead;

    @Column(name = "request_params")
    @ApiModelProperty("请求参数")
    private String requestParams;

    @Column(name = "content")
    @ApiModelProperty("原始回执报文")
    private String content;

    @Column(name = "decipher")
    @ApiModelProperty("是否需要解密")
    private Boolean decipher;

    @Column(name = "business_code")
    @ApiModelProperty("业务单号")
    private String businessCode;

    @Column(name = "business_type")
    @ApiModelProperty("业务单号类型")
    private String businessType;

    @Column(name = "customs")
    @ApiModelProperty("口岸")
    private String customs;

    @Column(name = "retry_time")
    @ApiModelProperty("重试次数")
    private Integer retryTime;

    @Column(name = "customs_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("海关回执时间")
    private Date customsTime;

    @Column(name = "create_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 1逻辑删除
     */
    @Column(name = "deleted")
    @ApiModelProperty("删除标记")
    private Boolean deleted = false;

    @Column(name = "callback_property")
    private Integer callbackProperty;

}
