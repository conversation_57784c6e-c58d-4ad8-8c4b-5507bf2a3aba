package com.danding.cds.test.company.service;

import com.alibaba.fastjson.JSON;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.dto.CompanyDistrictDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.customs.district.api.enums.CustomsDistrictEnum;
import com.danding.cds.test.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Auther: Dante-Gxj
 * @Date: 2020/4/26 14:14
 * @Description:
 */
@Slf4j
public class CompanyServiceTest extends BaseTest {

    @Autowired
    private CompanyService companyService;

    @Test
    public void findById() {
        CompanyDTO companyDTO = companyService.findById(1L);
        log.info("[op:CompanyService-findById] result={}", JSON.toJSONString(companyDTO));
        CompanyDistrictDTO companyDistrictDTO = companyDTO.getDistrict(CustomsDistrictEnum.JINYI);
        if (companyDistrictDTO == null){
            log.info("[op:CompanyService-findById] 杭州地方关区为空");
        }else {
            log.info("[op:CompanyService-findById] CompanyDistrictDTO={}", JSON.toJSONString(companyDistrictDTO));
        }
    }

    @Test
    public void findByCode() {
        CompanyDTO companyDTO = companyService.findByCode("56782223");
        log.info("[op:CompanyService-findByCode] result={}", JSON.toJSONString(companyDTO));
        CompanyDistrictDTO companyDistrictDTO = companyDTO.getDistrict(CustomsDistrictEnum.JINYI);
        if (companyDistrictDTO == null){
            log.info("[op:CompanyService-findByCode] 杭州地方关区为空");
        }else {
            log.info("[op:CompanyService-findByCode] CompanyDistrictDTO={}", JSON.toJSONString(companyDistrictDTO));
        }
    }
}
