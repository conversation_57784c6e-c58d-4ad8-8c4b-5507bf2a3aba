package com.danding.cds.test;

import java.util.*;

class Knowledge
    {
        private String code;
        private String name;
          Knowledge(String code,String name)
        {
            this.code = code;
            this.name = name;
        }
        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }
public class Main {
    private  static  Boolean knowledgeIsRepeat(List<Knowledge> orderList) {
        Set<Knowledge> set = new TreeSet<Knowledge>(new Comparator<Knowledge>() {
            public int compare(Knowledge a, Knowledge b) {
                // 字符串则按照asicc码升序排列
                return a.getCode().compareTo(b.getCode());
            }
        });
        set.addAll(orderList);
        if (set.size() < orderList.size()) {
            return true;
        }
        return false;
    }

        public  static  void main(String[]args)
        {
            List<Knowledge> list = new ArrayList<Knowledge>();
            list.add(new Knowledge("001","xiao"));
            list.add(new Knowledge("002","xiao2"));
            list.add(new Knowledge("003","xiao6"));
            list.add(new Knowledge("004","xiao4"));
            System.out.println("list.size()===="+knowledgeIsRepeat(list));
        }
}
