package com.danding.cds.test;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.kevinsawicki.http.HttpRequest;
import org.junit.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PathCheckTest {

    @Test
    public void test() {
        StringBuilder sb = new StringBuilder();
        // 检查线上的店铺，创建的路由和预期是否一致
        sb.append("{\"success\":true,\"result\":{\"page\":{\"currentPage\":1,\"prePage\":1,\"nextPage\":1,\"pageSize\":200,\"offset\":0,\"totalPage\":1,\"totalCount\":133},\"dataList\":[{\"shopId\":134,\"outerId\":\"000000\",\"userId\":55,\"userName\":\"金义-益天\",\"supplierName\":\"清远贝思特电子商贸有限公司\",\"channelCode\":\"GXPT\",\"channelName\":\"供销平台\",\"shopName\":\"供销小店\",\"authStatus\":2,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[],\"tags\":[],\"tagsName\":\"\"},{\"shopId\":133,\"outerId\":\"000000\",\"userId\":71,\"userName\":\"金义-拓浪\",\"supplierName\":\"金义贝宠\",\"channelCode\":\"GXPT\",\"channelName\":\"供销平台\",\"shopName\":\"供销小店\",\"authStatus\":2,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[],\"tags\":[],\"tagsName\":\"\"},{\"shopId\":132,\"outerId\":\"000000\",\"userId\":126,\"userName\":\"香港宠盟\",\"supplierName\":\"\",\"channelCode\":\"GXPT\",\"channelName\":\"供销平台\",\"shopName\":\"供销小店\",\"authStatus\":2,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[],\"tags\":[],\"tagsName\":\"\"},{\"shopId\":131,\"outerId\":\"100182\",\"userId\":131,\"userName\":\"天津但丁云\",\"supplierName\":\"\",\"channelCode\":\"webB2C\",\"channelName\":\"但丁分销商城\",\"shopName\":\"天津但丁云分销小程序\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2017081000005656\",\"depotName\":\"天津东疆保税仓\",\"shopId\":131,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":1,\"assureCode\":\"3301964J31\",\"assureName\":\"杭州但丁云科技有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[],\"tagsName\":\"\"},{\"shopId\":130,\"outerId\":\"000000\",\"userId\":131,\"userName\":\"天津但丁云\",\"supplierName\":\"\",\"channelCode\":\"GXPT\",\"channelName\":\"供销平台\",\"shopName\":\"供销小店\",\"authStatus\":2,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2017081000005656\",\"depotName\":\"天津东疆保税仓\",\"shopId\":130,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":1,\"assureCode\":\"3301964J31\",\"assureName\":\"杭州但丁云科技有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[],\"tagsName\":\"\"},{\"shopId\":129,\"outerId\":\"100181\",\"userId\":31,\"userName\":\"金义-唯选\",\"supplierName\":\"金义-樱之恋\",\"channelCode\":\"PDD\",\"channelName\":\"拼多多\",\"shopName\":\"Move Free海外正品店\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":129,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":128,\"outerId\":\"100180\",\"userId\":83,\"userName\":\"合生元指向\",\"supplierName\":\"\",\"channelCode\":\"PDD\",\"channelName\":\"拼多多\",\"shopName\":\"合生元海外旗舰店\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[],\"tags\":[],\"tagsName\":\"\"},{\"shopId\":127,\"outerId\":\"100179\",\"userId\":58,\"userName\":\"金义-SCI\",\"supplierName\":\"奥利派\",\"channelCode\":\"NEWYUOU\",\"channelName\":\"新渝欧\",\"shopName\":\"渝欧（新）-SCI-金义仓2\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":127,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":126,\"outerId\":\"100178\",\"userId\":58,\"userName\":\"金义-SCI\",\"supplierName\":\"奥利派\",\"channelCode\":\"YUOU\",\"channelName\":\"渝欧\",\"shopName\":\"渝欧（新）-SCI-金义仓\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[],\"tags\":[],\"tagsName\":\"\"},{\"shopId\":125,\"outerId\":\"100176\",\"userId\":29,\"userName\":\"微米电子\",\"supplierName\":\"金义-訾昶\",\"channelCode\":\"PAYH\",\"channelName\":\"平安银行\",\"shopName\":\"平安银行-壹钱包\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":125,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\",\"customsData179\",\"splitTax\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报,无需海关179公告数据备查,商品拆分税费\"},{\"shopId\":124,\"outerId\":\"100174\",\"userId\":83,\"userName\":\"合生元指向\",\"supplierName\":\"\",\"channelCode\":\"AIKUCUN\",\"channelName\":\"爱库存\",\"shopName\":\"Swisse旗舰店\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":124,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":123,\"outerId\":\"000000\",\"userId\":18,\"userName\":\"捷购\",\"supplierName\":\"百德芳华\",\"channelCode\":\"GXPT\",\"channelName\":\"供销平台\",\"shopName\":\"供销小店\",\"authStatus\":2,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[],\"tags\":[],\"tagsName\":\"\"},{\"shopId\":122,\"outerId\":\"000000\",\"userId\":12,\"userName\":\"但丁国际\",\"supplierName\":\"天津-国科\",\"channelCode\":\"GXPT\",\"channelName\":\"供销平台\",\"shopName\":\"供销小店\",\"authStatus\":2,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":122,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"海关订单无需申报,海关清单申报\"},{\"shopId\":121,\"outerId\":\"000000\",\"userId\":127,\"userName\":\"金义怀捷\",\"supplierName\":\"\",\"channelCode\":\"GXPT\",\"channelName\":\"供销平台\",\"shopName\":\"供销小店\",\"authStatus\":2,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":121,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":120,\"outerId\":\"100173\",\"userId\":126,\"userName\":\"香港宠盟\",\"supplierName\":\"\",\"channelCode\":\"EPETV3\",\"channelName\":\"易宠\",\"shopName\":\"香港宠盟\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":120,\"copId\":54,\"copCode\":\"3301968FX\",\"copName\":\"杭州易宠科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":54,\"ebcCode\":\"3301968FX\",\"ebcName\":\"杭州易宠科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":119,\"outerId\":\"000000\",\"userId\":113,\"userName\":\"奥利派\",\"supplierName\":\"\",\"channelCode\":\"GXPT\",\"channelName\":\"供销平台\",\"shopName\":\"供销小店\",\"authStatus\":2,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[],\"tags\":[],\"tagsName\":\"\"},{\"shopId\":118,\"outerId\":\"000000\",\"userId\":9,\"userName\":\"Y800\",\"supplierName\":\"南京捷购\",\"channelCode\":\"GXPT\",\"channelName\":\"供销平台\",\"shopName\":\"供销小店\",\"authStatus\":2,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[],\"tags\":[],\"tagsName\":\"\"},{\"shopId\":117,\"outerId\":\"100172\",\"userId\":40,\"userName\":\"广州指向\",\"supplierName\":\"高浪\",\"channelCode\":\"DINGXIANG\",\"channelName\":\"丁香商城\",\"shopName\":\"丁香妈妈\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":117,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":116,\"outerId\":\"100171\",\"userId\":88,\"userName\":\"金义达丰\",\"supplierName\":\"\",\"channelCode\":\"HAIDAI\",\"channelName\":\"海带\",\"shopName\":\"达丰-海带\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":116,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":115,\"outerId\":\"100169\",\"userId\":115,\"userName\":\"pddtest\",\"supplierName\":\"\",\"channelCode\":\"PDD\",\"channelName\":\"拼多多\",\"shopName\":\"买买买小店\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[],\"tags\":[],\"tagsName\":\"\"},{\"shopId\":114,\"outerId\":\"100168\",\"userId\":116,\"userName\":\"testpdd\",\"supplierName\":\"\",\"channelCode\":\"PDD\",\"channelName\":\"拼多多\",\"shopName\":\"拼多多店铺\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[],\"tags\":[],\"tagsName\":\"\"},{\"shopId\":113,\"outerId\":\"100167\",\"userId\":115,\"userName\":\"pddtest\",\"supplierName\":\"\",\"channelCode\":\"PDD\",\"channelName\":\"拼多多\",\"shopName\":\"买买买小店\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[],\"tags\":[],\"tagsName\":\"\"},{\"shopId\":112,\"outerId\":\"100166\",\"userId\":86,\"userName\":\"香港成聚\",\"supplierName\":\"\",\"channelCode\":\"YOUZAN\",\"channelName\":\"有赞\",\"shopName\":\"环球臻选\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[],\"tags\":[],\"tagsName\":\"\"},{\"shopId\":111,\"outerId\":\"100165\",\"userId\":12,\"userName\":\"但丁国际\",\"supplierName\":\"天津-国科\",\"channelCode\":\"webB2C\",\"channelName\":\"但丁分销商城\",\"shopName\":\"奥利派测试\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":111,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":49,\"ebcCode\":\" 3307960ADD\",\"ebcName\":\"浙江奥利派科技有限公司\"}],\"tags\":[\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"海关订单无需申报,海关清单申报\"},{\"shopId\":110,\"outerId\":\"100161\",\"userId\":40,\"userName\":\"广州指向\",\"supplierName\":\"高浪\",\"channelCode\":\"webB2C\",\"channelName\":\"但丁分销商城\",\"shopName\":\"veta海外旗舰店\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":110,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":109,\"outerId\":\"100160\",\"userId\":86,\"userName\":\"香港成聚\",\"supplierName\":\"\",\"channelCode\":\"MIA\",\"channelName\":\"蜜芽\",\"shopName\":\"蜜芽-香港成聚\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":109,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":108,\"outerId\":\"100159\",\"userId\":71,\"userName\":\"金义-拓浪\",\"supplierName\":\"金义贝宠\",\"channelCode\":\"PDD\",\"channelName\":\"拼多多\",\"shopName\":\"测试店铺630\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":108,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\"],\"tagsName\":\"自动付款\"},{\"shopId\":107,\"outerId\":\"100158\",\"userId\":71,\"userName\":\"金义-拓浪\",\"supplierName\":\"金义贝宠\",\"channelCode\":\"RedBook\",\"channelName\":\"小红书\",\"shopName\":\"测试店铺\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[],\"tags\":[],\"tagsName\":\"\"},{\"shopId\":106,\"outerId\":\"100157\",\"userId\":113,\"userName\":\"奥利派\",\"supplierName\":\"\",\"channelCode\":\"webB2C\",\"channelName\":\"但丁分销商城\",\"shopName\":\"奥利派\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2020062800001103\",\"depotName\":\"海口仓\",\"shopId\":106,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":1,\"assureCode\":\"3301964J31\",\"assureName\":\"杭州但丁云科技有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"},{\"shopCompanyId\":2,\"depotSn\":\"CHONGQINGT3\",\"depotName\":\"重庆T3仓\",\"shopId\":106,\"copId\":49,\"copCode\":\" 3307960ADD\",\"copName\":\"浙江奥利派科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":49,\"ebcCode\":\" 3307960ADD\",\"ebcName\":\"浙江奥利派科技有限公司\"},{\"shopCompanyId\":3,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":106,\"copId\":49,\"copCode\":\" 3307960ADD\",\"copName\":\"浙江奥利派科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":49,\"ebcCode\":\" 3307960ADD\",\"ebcName\":\"浙江奥利派科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":105,\"outerId\":\"100156\",\"userId\":31,\"userName\":\"金义-唯选\",\"supplierName\":\"金义-樱之恋\",\"channelCode\":\"PDD\",\"channelName\":\"拼多多\",\"shopName\":\"电乐官方海外旗舰店\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[],\"tags\":[],\"tagsName\":\"\"},{\"shopId\":104,\"outerId\":\"100155\",\"userId\":29,\"userName\":\"微米电子\",\"supplierName\":\"金义-訾昶\",\"channelCode\":\"webB2C\",\"channelName\":\"但丁分销商城\",\"shopName\":\"微小米\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[],\"tags\":[],\"tagsName\":\"\"},{\"shopId\":103,\"outerId\":\"100153\",\"userId\":86,\"userName\":\"香港成聚\",\"supplierName\":\"\",\"channelCode\":\"WEINI\",\"channelName\":\"唯妮海购\",\"shopName\":\"香港成聚实业有限公司\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[],\"tags\":[],\"tagsName\":\"\"},{\"shopId\":102,\"outerId\":\"100151\",\"userId\":55,\"userName\":\"金义-益天\",\"supplierName\":\"清远贝思特电子商贸有限公司\",\"channelCode\":\"RedBook\",\"channelName\":\"小红书\",\"shopName\":\"a2海外旗舰店\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":102,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":101,\"outerId\":\"100149\",\"userId\":40,\"userName\":\"广州指向\",\"supplierName\":\"高浪\",\"channelCode\":\"PDD\",\"channelName\":\"拼多多\",\"shopName\":\"Fujun保健食品海外专营店\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":101,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":100,\"outerId\":\"100148\",\"userId\":29,\"userName\":\"微米电子\",\"supplierName\":\"金义-訾昶\",\"channelCode\":\"PDD\",\"channelName\":\"拼多多\",\"shopName\":\"萌娃母婴海外\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":100,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":99,\"outerId\":\"100147\",\"userId\":106,\"userName\":\"摩榭迩上海网络科技有限公司\",\"supplierName\":\"\",\"channelCode\":\"YOUZAN\",\"channelName\":\"有赞\",\"shopName\":\"妆集舍美妆店\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":99,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[],\"tagsName\":\"\"},{\"shopId\":98,\"outerId\":\"100144\",\"userId\":102,\"userName\":\"旺宝国际集团有限公司\",\"supplierName\":\"\",\"channelCode\":\"RedBook\",\"channelName\":\"小红书\",\"shopName\":\"ONE BEAUTY海外集合店\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":98,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":97,\"outerId\":\"100143\",\"userId\":101,\"userName\":\"清远贝思特电子商贸有限公司\",\"supplierName\":\"\",\"channelCode\":\"webB2C\",\"channelName\":\"但丁分销商城\",\"shopName\":\"清远贝思特电子商贸有限公司\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2017032300000003\",\"depotName\":\"杭州如意仓\",\"shopId\":97,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":1,\"assureCode\":\"3301964J31\",\"assureName\":\"杭州但丁云科技有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[],\"tagsName\":\"\"},{\"shopId\":96,\"outerId\":\"100141\",\"userId\":55,\"userName\":\"金义-益天\",\"supplierName\":\"清远贝思特电子商贸有限公司\",\"channelCode\":\"webB2C\",\"channelName\":\"但丁分销商城\",\"shopName\":\"MOKO海外专营店\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":96,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":95,\"outerId\":\"100140\",\"userId\":12,\"userName\":\"但丁国际\",\"supplierName\":\"天津-国科\",\"channelCode\":\"RedBook\",\"channelName\":\"小红书\",\"shopName\":\"Ostelin海外品牌店\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":95,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"海关订单无需申报,海关清单申报\"},{\"shopId\":93,\"outerId\":\"100137\",\"userId\":80,\"userName\":\"金华优海\",\"supplierName\":\"\",\"channelCode\":\"PDD\",\"channelName\":\"拼多多\",\"shopName\":\"UHI海外专营店\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":93,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"海关订单无需申报,海关清单申报\"},{\"shopId\":92,\"outerId\":\"100136\",\"userId\":40,\"userName\":\"广州指向\",\"supplierName\":\"高浪\",\"channelCode\":\"PDD\",\"channelName\":\"拼多多\",\"shopName\":\"SwisseFujun海外专卖店\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":92,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":91,\"outerId\":\"100135\",\"userId\":55,\"userName\":\"金义-益天\",\"supplierName\":\"清远贝思特电子商贸有限公司\",\"channelCode\":\"webB2C\",\"channelName\":\"但丁分销商城\",\"shopName\":\"益天海外专营店\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":91,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"noBuyLimit\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,无购买限制,海关订单无需申报,海关清单申报\"},{\"shopId\":90,\"outerId\":\"100132\",\"userId\":40,\"userName\":\"广州指向\",\"supplierName\":\"高浪\",\"channelCode\":\"YOUZAN\",\"channelName\":\"有赞\",\"shopName\":\"有赞&swisse店\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":90,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":89,\"outerId\":\"100131\",\"userId\":57,\"userName\":\"金义-浩汉\",\"supplierName\":\"摩榭迩上海网络科技有限公司\",\"channelCode\":\"XXB\",\"channelName\":\"小小包\",\"shopName\":\"小小包\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[],\"tags\":[],\"tagsName\":\"\"},{\"shopId\":88,\"outerId\":\"100130\",\"userId\":80,\"userName\":\"金华优海\",\"supplierName\":\"\",\"channelCode\":\"YOUHAI\",\"channelName\":\"优海\",\"shopName\":\"优哥精选\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":88,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":87,\"outerId\":\"100129\",\"userId\":55,\"userName\":\"金义-益天\",\"supplierName\":\"清远贝思特电子商贸有限公司\",\"channelCode\":\"RedBook\",\"channelName\":\"小红书\",\"shopName\":\"ECO TOUCH海外品牌店\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[],\"tags\":[],\"tagsName\":\"\"},{\"shopId\":86,\"outerId\":\"100128\",\"userId\":55,\"userName\":\"金义-益天\",\"supplierName\":\"清远贝思特电子商贸有限公司\",\"channelCode\":\"SUNING\",\"channelName\":\"苏宁\",\"shopName\":\"Bubs贝儿海外旗舰店\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":86,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":85,\"outerId\":\"100127\",\"userId\":55,\"userName\":\"金义-益天\",\"supplierName\":\"清远贝思特电子商贸有限公司\",\"channelCode\":\"SUNING\",\"channelName\":\"苏宁\",\"shopName\":\"益天生活馆海外专营店\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":85,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":84,\"outerId\":\"100125\",\"userId\":87,\"userName\":\"金义益天苏宁\",\"supplierName\":\"\",\"channelCode\":\"SUNING\",\"channelName\":\"苏宁\",\"shopName\":\"益天生活馆海外专营店\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[],\"tags\":[],\"tagsName\":\"\"},{\"shopId\":83,\"outerId\":\"100124\",\"userId\":87,\"userName\":\"金义益天苏宁\",\"supplierName\":\"\",\"channelCode\":\"SUNING\",\"channelName\":\"苏宁\",\"shopName\":\"Bubs贝儿海外旗舰店\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[],\"tags\":[],\"tagsName\":\"\"},{\"shopId\":82,\"outerId\":\"100123\",\"userId\":31,\"userName\":\"金义-唯选\",\"supplierName\":\"金义-樱之恋\",\"channelCode\":\"PDD\",\"channelName\":\"拼多多\",\"shopName\":\"沃德隆官方海外旗舰店\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":82,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":81,\"outerId\":\"100122\",\"userId\":57,\"userName\":\"金义-浩汉\",\"supplierName\":\"摩榭迩上海网络科技有限公司\",\"channelCode\":\"WEINI\",\"channelName\":\"唯妮海购\",\"shopName\":\"唯妮海购-澳佳宝\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":81,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":80,\"outerId\":\"100012\",\"userId\":74,\"userName\":\"VetriScience海外旗舰店\",\"supplierName\":\"\",\"channelCode\":\"PDD\",\"channelName\":\"拼多多\",\"shopName\":\"造物游传-拼多多-VETRISCIENCE宠物用品海外旗舰店\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2017032300000003\",\"depotName\":\"杭州如意仓\",\"shopId\":80,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":1,\"assureCode\":\"3301964J31\",\"assureName\":\"杭州但丁云科技有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\"],\"tagsName\":\"自动付款\"},{\"shopId\":79,\"outerId\":\"100121\",\"userId\":74,\"userName\":\"VetriScience海外旗舰店\",\"supplierName\":\"\",\"channelCode\":\"RedBook\",\"channelName\":\"小红书\",\"shopName\":\"VetriScience海外旗舰店\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2017032300000003\",\"depotName\":\"杭州如意仓\",\"shopId\":79,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":1,\"assureCode\":\"3301964J31\",\"assureName\":\"杭州但丁云科技有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\"],\"tagsName\":\"自动付款\"},{\"shopId\":78,\"outerId\":\"100120\",\"userId\":40,\"userName\":\"广州指向\",\"supplierName\":\"高浪\",\"channelCode\":\"BD\",\"channelName\":\"贝店\",\"shopName\":\"孚骏贝店专卖店\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":78,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":77,\"outerId\":\"100119\",\"userId\":86,\"userName\":\"香港成聚\",\"supplierName\":\"\",\"channelCode\":\"MeiRiYiTao\",\"channelName\":\"每日一淘\",\"shopName\":\"每日一淘-香港成聚\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":77,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":76,\"outerId\":\"100080\",\"userId\":86,\"userName\":\"香港成聚\",\"supplierName\":\"\",\"channelCode\":\"HIPAC\",\"channelName\":\"海拍客\",\"shopName\":\"香港成聚在海拍客的店铺\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":76,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":75,\"outerId\":\"100065\",\"userId\":86,\"userName\":\"香港成聚\",\"supplierName\":\"\",\"channelCode\":\"HIPAC\",\"channelName\":\"海拍客\",\"shopName\":\"海拍客-KJ-香港-XGCJ\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":75,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":74,\"outerId\":\"100032\",\"userId\":86,\"userName\":\"香港成聚\",\"supplierName\":\"\",\"channelCode\":\"PDD\",\"channelName\":\"拼多多\",\"shopName\":\"香港成聚在拼多多的店铺\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":74,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"noBuyLimit\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,无购买限制,海关订单无需申报,海关清单申报\"},{\"shopId\":73,\"outerId\":\"100007\",\"userId\":86,\"userName\":\"香港成聚\",\"supplierName\":\"\",\"channelCode\":\"HAIDAI\",\"channelName\":\"海带\",\"shopName\":\"成聚海带店铺\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":73,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":72,\"outerId\":\"100118\",\"userId\":83,\"userName\":\"合生元指向\",\"supplierName\":\"\",\"channelCode\":\"PDD\",\"channelName\":\"拼多多\",\"shopName\":\"Biostime合生元孚骏海外专卖店\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":72,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"noBuyLimit\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,无购买限制,海关订单无需申报,海关清单申报\"},{\"shopId\":71,\"outerId\":\"100115\",\"userId\":79,\"userName\":\"杭州匡沃\",\"supplierName\":\"\",\"channelCode\":\"webB2C\",\"channelName\":\"但丁分销商城\",\"shopName\":\"杭州匡沃\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2017032300000003\",\"depotName\":\"杭州如意仓\",\"shopId\":71,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":1,\"assureCode\":\"3301964J31\",\"assureName\":\"杭州但丁云科技有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"noBuyLimit\"],\"tagsName\":\"自动付款,无购买限制\"},{\"shopId\":70,\"outerId\":\"100114\",\"userId\":57,\"userName\":\"金义-浩汉\",\"supplierName\":\"摩榭迩上海网络科技有限公司\",\"channelCode\":\"BD\",\"channelName\":\"贝店\",\"shopName\":\"贝店-澳佳宝联营店铺\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":70,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":69,\"outerId\":\"100113\",\"userId\":61,\"userName\":\"金义-博杰\",\"supplierName\":\"testpdd\",\"channelCode\":\"AXGJ\",\"channelName\":\"澳新国际\",\"shopName\":\"OCEANZ海外专营店\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":69,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":68,\"outerId\":\"100112\",\"userId\":29,\"userName\":\"微米电子\",\"supplierName\":\"金义-訾昶\",\"channelCode\":\"HQBS\",\"channelName\":\"环球捕手\",\"shopName\":\"a2海外旗舰店\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":68,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[],\"tagsName\":\"\"},{\"shopId\":67,\"outerId\":\"100111\",\"userId\":61,\"userName\":\"金义-博杰\",\"supplierName\":\"testpdd\",\"channelCode\":\"PDD\",\"channelName\":\"拼多多\",\"shopName\":\"MAJOR GOAL母婴海外专营店\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":67,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":66,\"outerId\":\"100110\",\"userId\":52,\"userName\":\"金义-依然\",\"supplierName\":\"金义益天苏宁\",\"channelCode\":\"DALING\",\"channelName\":\"达令家\",\"shopName\":\"依然-达令\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":66,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[],\"tagsName\":\"\"},{\"shopId\":65,\"outerId\":\"100011\",\"userId\":71,\"userName\":\"金义-拓浪\",\"supplierName\":\"金义贝宠\",\"channelCode\":\"PDD\",\"channelName\":\"拼多多\",\"shopName\":\"高浪-拼多多-拓浪海外旗舰店\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":65,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":64,\"outerId\":\"100109\",\"userId\":68,\"userName\":\"金义-七炅\",\"supplierName\":\"Neal\",\"channelCode\":\"YOUZAN\",\"channelName\":\"有赞\",\"shopName\":\"唯它可睡VitaDreamz-七炅\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[],\"tags\":[],\"tagsName\":\"\"},{\"shopId\":63,\"outerId\":\"100019\",\"userId\":67,\"userName\":\"BESR0518\",\"supplierName\":\"金义怀捷\",\"channelCode\":\"YINGBO\",\"channelName\":\"倾伊美\",\"shopName\":\"高浪映博店铺\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":63,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},");
        sb.append("{\"shopId\":62,\"outerId\":\"100107\",\"userId\":63,\"userName\":\"金义-杰俊\",\"supplierName\":\"gaolang\",\"channelCode\":\"YOUZAN\",\"channelName\":\"有赞\",\"shopName\":\"JAYJUN美妆旗舰店-杰俊\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":62,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":61,\"outerId\":\"100106\",\"userId\":62,\"userName\":\"金义-健康\",\"supplierName\":\"ds058\",\"channelCode\":\"YOUZAN\",\"channelName\":\"有赞\",\"shopName\":\"快乐严选-健康\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":61,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":60,\"outerId\":\"100058\",\"userId\":52,\"userName\":\"金义-依然\",\"supplierName\":\"金义益天苏宁\",\"channelCode\":\"JM\",\"channelName\":\"聚美\",\"shopName\":\"依然-聚美店铺\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":60,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":59,\"outerId\":\"100063\",\"userId\":52,\"userName\":\"金义-依然\",\"supplierName\":\"金义益天苏宁\",\"channelCode\":\"PDD\",\"channelName\":\"拼多多\",\"shopName\":\"金义-依然在拼多多的店铺\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":59,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":58,\"outerId\":\"100089\",\"userId\":61,\"userName\":\"金义-博杰\",\"supplierName\":\"testpdd\",\"channelCode\":\"PDD\",\"channelName\":\"拼多多\",\"shopName\":\"博杰海外专营店\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":58,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"noBuyLimit\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,无购买限制,海关订单无需申报,海关清单申报\"},{\"shopId\":57,\"outerId\":\"100104\",\"userId\":61,\"userName\":\"金义-博杰\",\"supplierName\":\"testpdd\",\"channelCode\":\"MeiRiYiTao\",\"channelName\":\"每日一淘\",\"shopName\":\"每日一淘-金义-博杰\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":57,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":56,\"outerId\":\"100101\",\"userId\":58,\"userName\":\"金义-SCI\",\"supplierName\":\"奥利派\",\"channelCode\":\"MeiRiYiTao\",\"channelName\":\"每日一淘\",\"shopName\":\"每日一淘-金义-SCI\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":56,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":55,\"outerId\":\"100017\",\"userId\":58,\"userName\":\"金义-SCI\",\"supplierName\":\"奥利派\",\"channelCode\":\"PDD\",\"channelName\":\"拼多多\",\"shopName\":\"金义-SCI-拼多多-雀巢海外旗舰店\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":55,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":54,\"outerId\":\"100015\",\"userId\":58,\"userName\":\"金义-SCI\",\"supplierName\":\"奥利派\",\"channelCode\":\"PDD\",\"channelName\":\"拼多多\",\"shopName\":\"金义-SCI-拼多多-联合利华海外旗舰店 \",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":54,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":53,\"outerId\":\"100008\",\"userId\":58,\"userName\":\"金义-SCI\",\"supplierName\":\"奥利派\",\"channelCode\":\"PDD\",\"channelName\":\"拼多多\",\"shopName\":\"SCI-拼多多-NAS海外专营店\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":53,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":52,\"outerId\":\"100047\",\"userId\":58,\"userName\":\"金义-SCI\",\"supplierName\":\"奥利派\",\"channelCode\":\"YUOU\",\"channelName\":\"渝欧\",\"shopName\":\"金义-SCI-渝欧店铺\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":52,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":51,\"outerId\":\"100028\",\"userId\":58,\"userName\":\"金义-SCI\",\"supplierName\":\"奥利派\",\"channelCode\":\"JM\",\"channelName\":\"聚美\",\"shopName\":\"金义-SCI-聚美店铺\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":51,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":50,\"outerId\":\"100022\",\"userId\":58,\"userName\":\"金义-SCI\",\"supplierName\":\"奥利派\",\"channelCode\":\"XXB\",\"channelName\":\"小小包\",\"shopName\":\"小小包店铺\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":50,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":49,\"outerId\":\"100021\",\"userId\":58,\"userName\":\"金义-SCI\",\"supplierName\":\"奥利派\",\"channelCode\":\"YUOU\",\"channelName\":\"渝欧\",\"shopName\":\"金义-SCI-渝欧店铺\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":49,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":48,\"outerId\":\"100005\",\"userId\":58,\"userName\":\"金义-SCI\",\"supplierName\":\"奥利派\",\"channelCode\":\"AXGJ\",\"channelName\":\"澳新国际\",\"shopName\":\"澳新国际店铺\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":48,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":47,\"outerId\":\"100100\",\"userId\":57,\"userName\":\"金义-浩汉\",\"supplierName\":\"摩榭迩上海网络科技有限公司\",\"channelCode\":\"MeiRiYiTao\",\"channelName\":\"每日一淘\",\"shopName\":\"每日一淘-金义-浩汉\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":47,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":46,\"outerId\":\"100045\",\"userId\":57,\"userName\":\"金义-浩汉\",\"supplierName\":\"摩榭迩上海网络科技有限公司\",\"channelCode\":\"FENQILE\",\"channelName\":\"分期乐\",\"shopName\":\"浩汉澳佳宝在分期乐的店铺\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":46,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":45,\"outerId\":\"100030\",\"userId\":57,\"userName\":\"金义-浩汉\",\"supplierName\":\"摩榭迩上海网络科技有限公司\",\"channelCode\":\"AXGJ\",\"channelName\":\"澳新国际\",\"shopName\":\"澳新国际浩汉店铺\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":45,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":44,\"outerId\":\"100027\",\"userId\":56,\"userName\":\"金义-樱之恋\",\"supplierName\":\"旺宝国际集团有限公司\",\"channelCode\":\"PDD\",\"channelName\":\"拼多多\",\"shopName\":\"金义-樱之恋在拼多多的店铺\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":44,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[],\"tagsName\":\"\"},{\"shopId\":43,\"outerId\":\"100001\",\"userId\":56,\"userName\":\"金义-樱之恋\",\"supplierName\":\"旺宝国际集团有限公司\",\"channelCode\":\"JM\",\"channelName\":\"聚美\",\"shopName\":\"樱之恋聚美店铺\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":43,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[],\"tagsName\":\"\"},{\"shopId\":42,\"outerId\":\"100006\",\"userId\":55,\"userName\":\"金义-益天\",\"supplierName\":\"清远贝思特电子商贸有限公司\",\"channelCode\":\"HAIDAI\",\"channelName\":\"海带\",\"shopName\":\"益天海带店铺\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":42,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":41,\"outerId\":\"100020\",\"userId\":55,\"userName\":\"金义-益天\",\"supplierName\":\"清远贝思特电子商贸有限公司\",\"channelCode\":\"YUOU\",\"channelName\":\"渝欧\",\"shopName\":\"15800971273\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":41,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":40,\"outerId\":\"100066\",\"userId\":55,\"userName\":\"金义-益天\",\"supplierName\":\"清远贝思特电子商贸有限公司\",\"channelCode\":\"HIPAC\",\"channelName\":\"海拍客\",\"shopName\":\"17717368881\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":40,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":39,\"outerId\":\"100096\",\"userId\":56,\"userName\":\"金义-樱之恋\",\"supplierName\":\"旺宝国际集团有限公司\",\"channelCode\":\"MeiRiYiTao\",\"channelName\":\"每日一淘\",\"shopName\":\"每日一淘-金义-樱之恋\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":39,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[],\"tagsName\":\"\"},{\"shopId\":38,\"outerId\":\"100095\",\"userId\":55,\"userName\":\"金义-益天\",\"supplierName\":\"清远贝思特电子商贸有限公司\",\"channelCode\":\"MeiRiYiTao\",\"channelName\":\"每日一淘\",\"shopName\":\"每日一淘-金义-益天\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":38,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":37,\"outerId\":\"100094\",\"userId\":54,\"userName\":\"金义-訾昶\",\"supplierName\":\"小文哥\",\"channelCode\":\"webB2C\",\"channelName\":\"但丁分销商城\",\"shopName\":\"分销商城及小程序-金义-訾昶\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":37,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"noBuyLimit\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,无购买限制,海关订单无需申报,海关清单申报\"},{\"shopId\":36,\"outerId\":\"100093\",\"userId\":52,\"userName\":\"金义-依然\",\"supplierName\":\"金义益天苏宁\",\"channelCode\":\"MeiRiYiTao\",\"channelName\":\"每日一淘\",\"shopName\":\"每日一淘-一淘-依然\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":36,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":35,\"outerId\":\"100046\",\"userId\":52,\"userName\":\"金义-依然\",\"supplierName\":\"金义益天苏宁\",\"channelCode\":\"Doctor\",\"channelName\":\"好医生\",\"shopName\":\"金义-依然好医生店铺\",\"authStatus\":2,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":35,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":34,\"outerId\":\"100031\",\"userId\":52,\"userName\":\"金义-依然\",\"supplierName\":\"金义益天苏宁\",\"channelCode\":\"HQBS\",\"channelName\":\"环球捕手\",\"shopName\":\"金义-依然环球捕手店铺\",\"authStatus\":2,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":34,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":33,\"outerId\":\"100091\",\"userId\":29,\"userName\":\"微米电子\",\"supplierName\":\"金义-訾昶\",\"channelCode\":\"SUTANG\",\"channelName\":\"醉红酥糖\",\"shopName\":\"醉红酥糖\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":33,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":32,\"outerId\":\"100087\",\"userId\":12,\"userName\":\"但丁国际\",\"supplierName\":\"天津-国科\",\"channelCode\":\"PDD\",\"channelName\":\"拼多多\",\"shopName\":\"成泰海外旗舰店\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":32,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":31,\"outerId\":\"100086\",\"userId\":50,\"userName\":\"shanghaiwanglun123\",\"supplierName\":\"合生元指向\",\"channelCode\":\"WANGLUN\",\"channelName\":\"旺仑\",\"shopName\":\"旺仑的应用-旺仑网\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2017032300000003\",\"depotName\":\"杭州如意仓\",\"shopId\":31,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":1,\"assureCode\":\"3301964J31\",\"assureName\":\"杭州但丁云科技有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"},{\"shopCompanyId\":2,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":31,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"海关订单无需申报,海关清单申报\"},{\"shopId\":30,\"outerId\":\"100085\",\"userId\":50,\"userName\":\"shanghaiwanglun123\",\"supplierName\":\"合生元指向\",\"channelCode\":\"webB2C\",\"channelName\":\"但丁分销商城\",\"shopName\":\"shanghaiwanglun123分销店\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[],\"tags\":[],\"tagsName\":\"\"},{\"shopId\":29,\"outerId\":\"100084\",\"userId\":49,\"userName\":\"杭州-飞萌\",\"supplierName\":\"金华优海\",\"channelCode\":\"webB2C\",\"channelName\":\"但丁分销商城\",\"shopName\":\"杭州-飞萌分销店\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2017032300000003\",\"depotName\":\"杭州如意仓\",\"shopId\":29,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":1,\"assureCode\":\"3301964J31\",\"assureName\":\"杭州但丁云科技有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"},{\"shopCompanyId\":2,\"depotSn\":\"D2017081000005656\",\"depotName\":\"天津东疆保税仓\",\"shopId\":29,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":1,\"assureCode\":\"3301964J31\",\"assureName\":\"杭州但丁云科技有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[],\"tagsName\":\"\"},{\"shopId\":28,\"outerId\":\"100083\",\"userId\":46,\"userName\":\"雪糕心品\",\"supplierName\":\"宜度\",\"channelCode\":\"xuegao\",\"channelName\":\"雪糕心品\",\"shopName\":\"雪糕心品店铺\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2017032300000003\",\"depotName\":\"杭州如意仓\",\"shopId\":28,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":1,\"assureCode\":\"3301964J31\",\"assureName\":\"杭州但丁云科技有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"},{\"shopCompanyId\":2,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":28,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\"],\"tagsName\":\"自动付款\"},{\"shopId\":27,\"outerId\":\"100082\",\"userId\":47,\"userName\":\"杭州-海慈\",\"supplierName\":\"森棠\",\"channelCode\":\"HZHC\",\"channelName\":\"海慈\",\"shopName\":\"杭州海慈-杭州-海慈\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2017032300000003\",\"depotName\":\"杭州如意仓\",\"shopId\":27,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":1,\"assureCode\":\"3301964J31\",\"assureName\":\"杭州但丁云科技有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"customsPaymentDeclare\",\"autoPay\"],\"tagsName\":\"支付单申报+179备案,自动付款\"},{\"shopId\":26,\"outerId\":\"100081\",\"userId\":46,\"userName\":\"雪糕心品\",\"supplierName\":\"宜度\",\"channelCode\":\"webB2C\",\"channelName\":\"但丁分销商城\",\"shopName\":\"雪糕心品分销店\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2017032300000003\",\"depotName\":\"杭州如意仓\",\"shopId\":26,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":1,\"assureCode\":\"3301964J31\",\"assureName\":\"杭州但丁云科技有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[],\"tagsName\":\"\"},{\"shopId\":25,\"outerId\":\"100078\",\"userId\":45,\"userName\":\"ZH-TSN\",\"supplierName\":\"金义-博雅\",\"channelCode\":\"webB2C\",\"channelName\":\"但丁分销商城\",\"shopName\":\"ZH-TSN分销店\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2017081000005656\",\"depotName\":\"天津东疆保税仓\",\"shopId\":25,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":1,\"assureCode\":\"3301964J31\",\"assureName\":\"杭州但丁云科技有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[],\"tagsName\":\"\"},{\"shopId\":24,\"outerId\":\"100077\",\"userId\":40,\"userName\":\"广州指向\",\"supplierName\":\"高浪\",\"channelCode\":\"PDD\",\"channelName\":\"拼多多\",\"shopName\":\"Swisse保健食品海外旗舰店\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":24,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"noBuyLimit\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"无购买限制,海关订单无需申报,海关清单申报\"},{\"shopId\":23,\"outerId\":\"100076\",\"userId\":40,\"userName\":\"广州指向\",\"supplierName\":\"高浪\",\"channelCode\":\"PDD\",\"channelName\":\"拼多多\",\"shopName\":\"Swisse孚骏海外专卖店\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":23,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"noBuyLimit\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,无购买限制,海关订单无需申报,海关清单申报\"},{\"shopId\":22,\"outerId\":\"100033\",\"userId\":38,\"userName\":\"金义-悦衡\",\"supplierName\":\"金义-七炅\",\"channelCode\":\"HAIDAI\",\"channelName\":\"海带\",\"shopName\":\"香港悦衡海带店铺\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":22,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":21,\"outerId\":\"100029\",\"userId\":38,\"userName\":\"金义-悦衡\",\"supplierName\":\"金义-七炅\",\"channelCode\":\"XINGYUN\",\"channelName\":\"行云\",\"shopName\":\"行云店铺\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":21,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"海关订单无需申报,海关清单申报\"},{\"shopId\":20,\"outerId\":\"100073\",\"userId\":33,\"userName\":\"PETFUN\",\"supplierName\":\"金义-SCI\",\"channelCode\":\"webB2C\",\"channelName\":\"但丁分销商城\",\"shopName\":\"新视野宠粮\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2017032300000003\",\"depotName\":\"杭州如意仓\",\"shopId\":20,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":1,\"assureCode\":\"3301964J31\",\"assureName\":\"杭州但丁云科技有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"noBuyLimit\"],\"tagsName\":\"自动付款,无购买限制\"},{\"shopId\":19,\"outerId\":\"100072\",\"userId\":34,\"userName\":\"新视野宠粮\",\"supplierName\":\"金义-博杰\",\"channelCode\":\"webB2C\",\"channelName\":\"但丁分销商城\",\"shopName\":\"新视野宠粮\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[],\"tags\":[],\"tagsName\":\"\"},{\"shopId\":18,\"outerId\":\"100003\",\"userId\":32,\"userName\":\"金义-鑫乐\",\"supplierName\":\"金义-浩汉\",\"channelCode\":\"HQBS\",\"channelName\":\"环球捕手\",\"shopName\":\"鑫乐环球捕手店铺\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":18,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\"],\"tagsName\":\"自动付款\"},{\"shopId\":17,\"outerId\":\"100071\",\"userId\":32,\"userName\":\"金义-鑫乐\",\"supplierName\":\"金义-浩汉\",\"channelCode\":\"webB2C\",\"channelName\":\"但丁分销商城\",\"shopName\":\"金义-鑫乐分销店\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":17,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"noBuyLimit\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,无购买限制,海关订单无需申报,海关清单申报\"},{\"shopId\":16,\"outerId\":\"100070\",\"userId\":29,\"userName\":\"微米电子\",\"supplierName\":\"金义-訾昶\",\"channelCode\":\"AXGJ\",\"channelName\":\"澳新国际\",\"shopName\":\"爱客澳新国际\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":16,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"noBuyLimit\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,无购买限制,海关订单无需申报,海关清单申报\"},{\"shopId\":15,\"outerId\":\"100069\",\"userId\":29,\"userName\":\"微米电子\",\"supplierName\":\"金义-訾昶\",\"channelCode\":\"HZW\",\"channelName\":\"孩子王\",\"shopName\":\"犀牛国际（香港）有限公司\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":15,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[],\"tagsName\":\"\"},{\"shopId\":14,\"outerId\":\"100034\",\"userId\":31,\"userName\":\"金义-唯选\",\"supplierName\":\"金义-樱之恋\",\"channelCode\":\"PDD\",\"channelName\":\"拼多多\",\"shopName\":\"humanwell海外专营店\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":14,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":13,\"outerId\":\"100064\",\"userId\":28,\"userName\":\"上海葛博\",\"supplierName\":\"每日一淘渠道账号\",\"channelCode\":\"webB2C\",\"channelName\":\"但丁分销商城\",\"shopName\":\"朵俪思琪\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":13,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,海关订单无需申报,海关清单申报\"},{\"shopId\":12,\"outerId\":\"100062\",\"userId\":27,\"userName\":\"金义-拓美\",\"supplierName\":\"金义-依然\",\"channelCode\":\"PDD\",\"channelName\":\"拼多多\",\"shopName\":\"东立信美妆海外专营店\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":12,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"海关订单无需申报,海关清单申报\"},{\"shopId\":11,\"outerId\":\"100060\",\"userId\":27,\"userName\":\"金义-拓美\",\"supplierName\":\"金义-依然\",\"channelCode\":\"webB2C\",\"channelName\":\"但丁分销商城\",\"shopName\":\"金义-拓美分销店\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[],\"tags\":[],\"tagsName\":\"\"},{\"shopId\":10,\"outerId\":\"100059\",\"userId\":26,\"userName\":\"天津-国科\",\"supplierName\":\"上海旺仑\",\"channelCode\":\"webB2C\",\"channelName\":\"但丁分销商城\",\"shopName\":\"分销商城及小程序-天津-国科\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2017081000005656\",\"depotName\":\"天津东疆保税仓\",\"shopId\":10,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":1,\"assureCode\":\"3301964J31\",\"assureName\":\"杭州但丁云科技有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\"],\"tagsName\":\"自动付款\"},{\"shopId\":9,\"outerId\":\"100056\",\"userId\":25,\"userName\":\"但丁云_法蔓\",\"supplierName\":\"杭州-飞萌\",\"channelCode\":\"webB2C\",\"channelName\":\"但丁分销商城\",\"shopName\":\"法蔓国际\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2017032300000003\",\"depotName\":\"杭州如意仓\",\"shopId\":9,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":1,\"assureCode\":\"3301964J31\",\"assureName\":\"杭州但丁云科技有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[],\"tagsName\":\"\"},{\"shopId\":8,\"outerId\":\"100055\",\"userId\":9,\"userName\":\"Y800\",\"supplierName\":\"南京捷购\",\"channelCode\":\"PDD\",\"channelName\":\"拼多多\",\"shopName\":\"123\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":8,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[],\"tagsName\":\"\"},{\"shopId\":7,\"outerId\":\"100053\",\"userId\":18,\"userName\":\"捷购\",\"supplierName\":\"百德芳华\",\"channelCode\":\"JieGou\",\"channelName\":\"百洋商城\",\"shopName\":\"百洋商城\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":7,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[],\"tagsName\":\"\"},{\"shopId\":6,\"outerId\":\"100009\",\"userId\":18,\"userName\":\"捷购\",\"supplierName\":\"百德芳华\",\"channelCode\":\"PDD\",\"channelName\":\"拼多多\",\"shopName\":\"泛欧海外专营店\",\"authStatus\":2,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":6,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"noBuyLimit\",\"customsOrderNotDeclare\",\"customsListDeclare\",\"splitTax\"],\"tagsName\":\"无购买限制,海关订单无需申报,海关清单申报,商品拆分税费\"},{\"shopId\":5,\"outerId\":\"100016\",\"userId\":18,\"userName\":\"捷购\",\"supplierName\":\"百德芳华\",\"channelCode\":\"PDD\",\"channelName\":\"拼多多\",\"shopName\":\"拼多多捷购\",\"authStatus\":2,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":5,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[],\"tagsName\":\"\"},{\"shopId\":4,\"outerId\":\"100050\",\"userId\":14,\"userName\":\"普罗旺斯国际\",\"supplierName\":\"上海葛博\",\"channelCode\":\"webB2C\",\"channelName\":\"但丁分销商城\",\"shopName\":\"普罗旺斯国际\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":4,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\"],\"tagsName\":\"自动付款\"},{\"shopId\":3,\"outerId\":\"100048\",\"userId\":12,\"userName\":\"但丁国际\",\"supplierName\":\"天津-国科\",\"channelCode\":\"webB2C\",\"channelName\":\"但丁分销商城\",\"shopName\":\"但丁国际\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":3,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"noBuyLimit\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,无购买限制,海关订单无需申报,海关清单申报\"},{\"shopId\":2,\"outerId\":\"100043\",\"userId\":9,\"userName\":\"Y800\",\"supplierName\":\"南京捷购\",\"channelCode\":\"webB2C\",\"channelName\":\"但丁分销商城\",\"shopName\":\"凡尔纳国际\",\"authStatus\":1,\"takeStatus\":0,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":2,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"},{\"shopCompanyId\":2,\"depotSn\":\"D2017032300000003\",\"depotName\":\"杭州如意仓\",\"shopId\":2,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":1,\"assureCode\":\"3301964J31\",\"assureName\":\"杭州但丁云科技有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"noBuyLimit\"],\"tagsName\":\"自动付款,无购买限制\"},{\"shopId\":1,\"outerId\":\"100041\",\"userId\":3,\"userName\":\"上海-高歌\",\"supplierName\":\"\",\"channelCode\":\"webB2C\",\"channelName\":\"但丁分销商城\",\"shopName\":\"高歌分销店\",\"authStatus\":1,\"takeStatus\":1,\"shopInfo\":\"\",\"shopCompanyList\":[{\"shopCompanyId\":1,\"depotSn\":\"D2019042501274160\",\"depotName\":\"金义综合保税区\",\"shopId\":1,\"copId\":1,\"copCode\":\"3301964J31\",\"copName\":\"杭州但丁云科技有限公司\",\"assureId\":50,\"assureCode\":\"330766K00W\",\"assureName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":1,\"ebcCode\":\"3301964J31\",\"ebcName\":\"杭州但丁云科技有限公司\"}],\"tags\":[\"autoPay\",\"noBuyLimit\",\"customsOrderNotDeclare\",\"customsListDeclare\"],\"tagsName\":\"自动付款,无购买限制,海关订单无需申报,海关清单申报\"}]},\"error\":null,\"errorMessage\":\"\",\"code\":0}");

        StringBuffer routeSb = new StringBuffer();
        routeSb.append("[");
        routeSb.append("{\"id\":110,\"name\":\"OMS-Move Free海外正品店\",\"code\":\"OMS-SHOP-129\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"上海寻梦信息技术有限公司\",\"ebpId\":12,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-08-19 13:10:02\",\"enable\":1},{\"id\":109,\"name\":\"XYO-宝妈会选-渝欧（新）-SCI-金义仓2\",\"code\":\"CQBMHX\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"重庆宝妈会选电子商务有限公司\",\"ebpId\":61,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-08-14 14:40:41\",\"enable\":1},{\"id\":108,\"name\":\"XYO-妙玛特-渝欧（新）-SCI-金义仓2\",\"code\":\"CQMDB\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"杭州门店帮电子商务有限公司\\t\",\"ebpId\":60,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-08-14 14:39:22\",\"enable\":1},{\"id\":107,\"name\":\"XYO-洛远宝宝-渝欧（新）-SCI-金义仓2\",\"code\":\"CQLYZH\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"重庆洛远正昊电子商务有限公司\",\"ebpId\":59,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-08-14 14:38:39\",\"enable\":1},{\"id\":106,\"name\":\"XYO-忘忧宝贝-渝欧（新）-SCI-金义仓2\",\"code\":\"CQYC\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"重庆谖草电子商务有限公司\",\"ebpId\":58,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-08-14 14:36:56\",\"enable\":1},{\"id\":105,\"name\":\"OMS-渝欧（新）-SCI-金义仓2\",\"code\":\"OMS-SHOP-127\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"重庆宝妈会选电子商务有限公司\",\"ebpId\":61,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-08-12 16:33:03\",\"enable\":0},{\"id\":104,\"name\":\"OMS-渝欧（新）-SCI-金义仓\",\"code\":\"OMS-SHOP-126\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"重庆渝欧跨境电子商务股份有限公司\",\"ebpId\":10,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-08-12 14:53:52\",\"enable\":0},{\"id\":103,\"name\":\"OMS-微米平安\",\"code\":\"OMS-SHOP-125\",\"actionList\":[\"DECLARE_INVENTORY\",\"DECLARE_ORDER\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"上海捷银电子商务有限公司\",\"ebpId\":56,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-08-10 10:59:45\",\"enable\":1},{\"id\":102,\"name\":\"OMS-Swisse旗舰店\",\"code\":\"OMS-SHOP-124\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"宁波商量信息技术有限公司\",\"ebpId\":51,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-08-06 09:52:49\",\"enable\":1},{\"id\":101,\"name\":\"OMS-金义怀捷-供销小店\",\"code\":\"OMS-SHOP-121\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"浙江奥利派科技有限公司\",\"ebpId\":20,\"ebcName\":\"浙江奥利派科技有限公司\",\"ebcId\":20,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"浙江奥利派科技有限公司\",\"orderDeclareCompanyId\":20,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-08-05 13:35:32\",\"enable\":1},{\"id\":100,\"name\":\"OMS-但丁国际-供销小店\",\"code\":\"OMS-SHOP-122\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"上海寻梦信息技术有限公司\",\"ebpId\":12,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-08-05 13:29:36\",\"enable\":1},{\"id\":99,\"name\":\"OMS-veta海外旗舰店\",\"code\":\"OMS-SHOP-110\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"杭州但丁云科技有限公司\",\"ebpId\":1,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-08-04 15:06:24\",\"enable\":1},{\"id\":98,\"name\":\"OMS-易宠\",\"code\":\"OMS-SHOP-120\",\"actionList\":[\"DECLARE_INVENTORY\"],\"actionDescList\":[\"清单\"],\"ebpName\":\"杭州易宠科技有限公司\",\"ebpId\":54,\"ebcName\":\"杭州易宠科技有限公司\",\"ebcId\":54,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":null,\"orderDeclareCompanyId\":null,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-08-03 17:26:31\",\"enable\":1},{\"id\":97,\"name\":\"OMS-丁香妈妈\",\"code\":\"OMS-SHOP-117\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"杭州丁香健康管理有限公司\",\"ebpId\":52,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-30 16:18:41\",\"enable\":1},{\"id\":96,\"name\":\"OMS-达丰-海带\",\"code\":\"OMS-SHOP-116\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"宁波诚淘电子商务有限公司\",\"ebpId\":17,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-30 10:53:01\",\"enable\":1},{\"id\":95,\"name\":\"AX-重庆欧品家\",\"code\":\"CQOP\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"重庆欧品家科技有限公司\",\"ebpId\":47,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-15 13:17:45\",\"enable\":1},{\"id\":94,\"name\":\"AX-四川景铭达\",\"code\":\"SCJM\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"四川景铭达科技有限公司\",\"ebpId\":46,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-15 13:17:11\",\"enable\":1},{\"id\":93,\"name\":\"AX-贝店\",\"code\":\"BD_1\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"杭州贝兴电子商务有限公司\",\"ebpId\":33,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-15 13:16:18\",\"enable\":1},{\"id\":92,\"name\":\"AX-海带\",\"code\":\"HD_YT\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"宁波诚淘电子商务有限公司\",\"ebpId\":17,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-15 13:15:45\",\"enable\":1},{\"id\":91,\"name\":\"AX-聚欧惠\",\"code\":\"QBK\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"杭州亲宝科技有限责任公司\",\"ebpId\":45,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-15 13:14:24\",\"enable\":1},{\"id\":90,\"name\":\"AX-大小宝贝\",\"code\":\"RNB\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"成都瑞奈贝儿科技有限公司\",\"ebpId\":44,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-15 13:13:54\",\"enable\":1},{\"id\":89,\"name\":\"AX-上禾购\",\"code\":\"DSK\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"重庆单双科技有限公司\",\"ebpId\":43,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-15 13:13:19\",\"enable\":1},{\"id\":88,\"name\":\"AX-海购天下 HGTX\",\"code\":\"LYJ\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"重庆路易吉科技有限公司\",\"ebpId\":40,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-15 13:12:35\",\"enable\":1},{\"id\":87,\"name\":\"AX-新欧汇\",\"code\":\"MG\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"重庆美广科技有限公司\",\"ebpId\":42,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-15 13:12:02\",\"enable\":1},{\"id\":86,\"name\":\"AX-渝新汇\",\"code\":\"SJ\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"重庆双击科技有限公司\",\"ebpId\":41,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-15 13:11:29\",\"enable\":1},{\"id\":85,\"name\":\"AX-海购天下 LYJ\",\"code\":\"HGTX\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"重庆路易吉科技有限公司\",\"ebpId\":40,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-15 13:10:39\",\"enable\":1},{\"id\":84,\"name\":\"AX-澳新国际\",\"code\":\"AXYX\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"广州澳新国际供应链管理有限公司\",\"ebpId\":14,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-15 11:54:09\",\"enable\":1},{\"id\":83,\"name\":\"AX-环球捕手\",\"code\":\"HQBS\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"浙江格家网络技术有限公司\",\"ebpId\":39,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-15 11:53:07\",\"enable\":1},{\"id\":82,\"name\":\"AX-苏宁\",\"code\":\"SUNING\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"江苏苏宁易购电子商务有限公司\",\"ebpId\":9,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-15 11:52:22\",\"enable\":1},{\"id\":81,\"name\":\"AX-有赞\",\"code\":\"GL-YOUZAN\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"杭州起码科技有限公司\",\"ebpId\":25,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-15 11:51:30\",\"enable\":1},{\"id\":80,\"name\":\"AX-孩子王\",\"code\":\"HZW\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"江苏孩子王实业有限公司\",\"ebpId\":22,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-15 11:50:48\",\"enable\":1},{\"id\":79,\"name\":\"AX-海拍客\",\"code\":\"HPK\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"杭州洋驼网络科技有限公司\",\"ebpId\":18,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-15 11:50:17\",\"enable\":1},{\"id\":78,\"name\":\"AX-拼多多\",\"code\":\"JG_PDD\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"上海寻梦信息技术有限公司\",\"ebpId\":12,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-15 11:49:31\",\"enable\":1},{\"id\":77,\"name\":\"OMS-UHI海外专营店\",\"code\":\"OMS-SHOP-93\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"上海寻梦信息技术有限公司\",\"ebpId\":12,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-15 10:09:12\",\"enable\":1},{\"id\":76,\"name\":\"OMS-金义-依然在拼多多的店铺\",\"code\":\"OMS-SHOP-59\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"上海寻梦信息技术有限公司\",\"ebpId\":12,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-15 10:08:22\",\"enable\":1},{\"id\":75,\"name\":\"金义-SCI-聚美店铺\",\"code\":\"OMS-SHOP-51\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"郑州维纳斯信息科技有限公司\",\"ebpId\":29,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-15 10:06:58\",\"enable\":1},{\"id\":74,\"name\":\"OMS-成泰海外旗舰店\",\"code\":\"OMS-SHOP-32\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"杭州但丁云科技有限公司\",\"ebpId\":1,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-15 10:04:35\",\"enable\":1},{\"id\":73,\"name\":\"OMS-Biostime合生元孚骏海外专卖店\",\"code\":\"OMS-SHOP-72\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"上海寻梦信息技术有限公司\",\"ebpId\":12,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-14 20:12:30\",\"enable\":1},{\"id\":72,\"name\":\"OMS-蜜芽-香港成聚\",\"code\":\"OMS-SHOP-109\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"北京花旺在线商贸有限公司\",\"ebpId\":35,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-14 15:47:43\",\"enable\":1},{\"id\":71,\"name\":\"OMS-奥利派\",\"code\":\"OMS-SHOP-106\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"浙江奥利派科技有限公司\",\"ebpId\":20,\"ebcName\":\"浙江奥利派科技有限公司\",\"ebcId\":20,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"浙江奥利派科技有限公司\",\"orderDeclareCompanyId\":20,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-14 15:46:58\",\"enable\":1},{\"id\":70,\"name\":\"OMS-Fujun保健食品海外专营店\",\"code\":\"OMS-SHOP-101\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"上海寻梦信息技术有限公司\",\"ebpId\":12,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-14 15:46:26\",\"enable\":1},{\"id\":69,\"name\":\"OMS-清远贝思特电子商贸有限公司\",\"code\":\"OMS-SHOP-97\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"杭州但丁云科技有限公司\",\"ebpId\":1,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-14 15:45:46\",\"enable\":1},{\"id\":68,\"name\":\"OMS-Ostelin海外品牌店\",\"code\":\"OMS-SHOP-95\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"行吟信息科技(上海)有限公司\",\"ebpId\":8,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-14 15:45:08\",\"enable\":1},{\"id\":67,\"name\":\"OMS-SwisseFujun海外专卖店\",\"code\":\"OMS-SHOP-92\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"上海寻梦信息技术有限公司\",\"ebpId\":12,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-14 15:44:01\",\"enable\":1},{\"id\":66,\"name\":\"OMS-有赞&swisse店\",\"code\":\"OMS-SHOP-90\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"杭州起码科技有限公司\",\"ebpId\":25,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-14 15:43:21\",\"enable\":1},{\"id\":65,\"name\":\"OMS-沃德隆官方海外旗舰店\",\"code\":\"OMS-SHOP-82\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"上海寻梦信息技术有限公司\",\"ebpId\":12,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-14 15:41:33\",\"enable\":1},{\"id\":64,\"name\":\"OMS-唯妮海购-澳佳宝\",\"code\":\"OMS-SHOP-81\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"湖州妮威贸易有限公司\",\"ebpId\":34,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-14 15:39:50\",\"enable\":1},{\"id\":63,\"name\":\"OMS-孚骏贝店专卖店\",\"code\":\"OMS-SHOP-78\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"杭州贝兴电子商务有限公司\",\"ebpId\":33,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-14 15:38:47\",\"enable\":1},{\"id\":62,\"name\":\"OMS-每日一淘-香港成聚\",\"code\":\"OMS-SHOP-77\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"北京每日一淘共享科技有限公司\",\"ebpId\":11,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-14 15:38:00\",\"enable\":1},{\"id\":61,\"name\":\"OMS-香港成聚在海拍客的店铺\",\"code\":\"OMS-SHOP-76\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"杭州洋驼网络科技有限公司\",\"ebpId\":18,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-14 15:37:20\",\"enable\":1}");
        routeSb.append(",");
        routeSb.append("{\"id\":60,\"name\":\"OMS-海拍客-KJ-香港-XGCJ\",\"code\":\"OMS-SHOP-75\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"杭州洋驼网络科技有限公司\",\"ebpId\":18,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-14 15:36:35\",\"enable\":1},{\"id\":59,\"name\":\"OMS-香港成聚在拼多多的店铺\",\"code\":\"OMS-SHOP-74\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"上海寻梦信息技术有限公司\",\"ebpId\":12,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-14 15:35:41\",\"enable\":1},{\"id\":58,\"name\":\"OMS-成聚海带店铺\",\"code\":\"OMS-SHOP-73\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"宁波诚淘电子商务有限公司\",\"ebpId\":17,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-14 15:35:00\",\"enable\":1},{\"id\":57,\"name\":\"OMS-杭州匡沃\",\"code\":\"OMS-SHOP-71\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"杭州但丁云科技有限公司\",\"ebpId\":1,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-14 15:33:10\",\"enable\":1},{\"id\":56,\"name\":\"OMS-贝店-澳佳宝联营店铺\",\"code\":\"OMS-SHOP-70\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"杭州贝兴电子商务有限公司\",\"ebpId\":33,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-14 15:32:27\",\"enable\":1},{\"id\":55,\"name\":\"OMS-拼多多-拓浪海外旗舰店\",\"code\":\"OMS-SHOP-65\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"上海寻梦信息技术有限公司\",\"ebpId\":12,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-14 15:31:33\",\"enable\":1},{\"id\":54,\"name\":\"OMS-高浪映博店铺\",\"code\":\"OMS-SHOP-63\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"杭州映博科技有限公司\",\"ebpId\":31,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-14 15:30:48\",\"enable\":1},{\"id\":53,\"name\":\"OMS-JAYJUN美妆旗舰店-杰俊\",\"code\":\"OMS-SHOP-62\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"杭州起码科技有限公司\",\"ebpId\":25,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-14 15:29:38\",\"enable\":1},{\"id\":52,\"name\":\"OMS-快乐严选-健康\",\"code\":\"OMS-SHOP-61\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"杭州起码科技有限公司\",\"ebpId\":25,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-14 15:28:30\",\"enable\":1},{\"id\":51,\"name\":\"OMS-依然-聚美店铺\",\"code\":\"OMS-SHOP-60\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"郑州维纳斯信息科技有限公司\",\"ebpId\":29,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-14 15:26:58\",\"enable\":1},{\"id\":50,\"name\":\"OMS-金义-浩汉\",\"code\":\"OMS-SHOP-47\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"北京每日一淘共享科技有限公司\",\"ebpId\":11,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-14 15:25:55\",\"enable\":1},{\"id\":49,\"name\":\"OMS-浩汉澳佳宝在分期乐的店铺\",\"code\":\"OMS-SHOP-46\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"深圳市分期乐网络科技有限公司\",\"ebpId\":30,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-14 15:24:25\",\"enable\":1},{\"id\":48,\"name\":\"OMS-澳新国际浩汉店铺\",\"code\":\"OMS-SHOP-45\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"广州澳新国际供应链管理有限公司\",\"ebpId\":14,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-14 15:22:33\",\"enable\":1},{\"id\":47,\"name\":\"OMS-一淘-依然\",\"code\":\"OMS-SHOP-36\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"北京每日一淘共享科技有限公司\",\"ebpId\":11,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-14 15:21:35\",\"enable\":1},{\"id\":46,\"name\":\"OMS-依然好医生店铺\",\"code\":\"OMS-SHOP-35\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"平安健康互联网股份有限公司\",\"ebpId\":28,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-14 15:20:07\",\"enable\":1},{\"id\":45,\"name\":\"OMS-依然环球捕手店铺\",\"code\":\"OMS-SHOP-34\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"杭州迅兰电子商务有限公司\",\"ebpId\":23,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-14 15:16:17\",\"enable\":1},{\"id\":44,\"name\":\"OMS-旺仑的应用-旺仑网\",\"code\":\"OMS-SHOP-31\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"上海旺仑实业有限公司\",\"ebpId\":27,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-14 15:14:37\",\"enable\":1},{\"id\":43,\"name\":\"OMS-雪糕心品店铺\",\"code\":\"OMS-SHOP-28\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"杭州雪糕文化创意有限公司\",\"ebpId\":26,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-14 15:09:33\",\"enable\":1},{\"id\":42,\"name\":\"OMS-Swisse保健食品海外旗舰店\",\"code\":\"OMS-SHOP-24\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"上海寻梦信息技术有限公司\",\"ebpId\":12,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-14 15:03:34\",\"enable\":1},{\"id\":41,\"name\":\"OMS-Swisse孚骏海外专卖店\",\"code\":\"OMS-SHOP-23\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"上海寻梦信息技术有限公司\",\"ebpId\":12,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-14 15:02:24\",\"enable\":1},{\"id\":40,\"name\":\"OMS-香港悦衡海带店铺\",\"code\":\"OMS-SHOP-22\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"宁波诚淘电子商务有限公司\",\"ebpId\":17,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-14 15:01:05\",\"enable\":1},{\"id\":39,\"name\":\"OMS-行云店铺\",\"code\":\"OMS-SHOP-21\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"天虹商场股份有限公司\",\"ebpId\":24,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-14 14:58:28\",\"enable\":1},{\"id\":38,\"name\":\"OMS-金义-鑫乐分销店\",\"code\":\"OMS-SHOP-17\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"杭州但丁云科技有限公司\",\"ebpId\":1,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-14 14:56:55\",\"enable\":1},{\"id\":37,\"name\":\"OMS-humanwell海外专营店\",\"code\":\"OMS-SHOP-14\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"上海寻梦信息技术有限公司\",\"ebpId\":12,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-14 14:55:35\",\"enable\":1},{\"id\":36,\"name\":\"OMS-朵俪思琪\",\"code\":\"OMS-SHOP-13\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"杭州但丁云科技有限公司\",\"ebpId\":1,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-14 14:54:55\",\"enable\":1},{\"id\":35,\"name\":\"OMS-天津-国科\",\"code\":\"OMS-SHOP-10\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"杭州但丁云科技有限公司\",\"ebpId\":1,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-14 14:54:00\",\"enable\":1},{\"id\":34,\"name\":\"OMS-法蔓国际\",\"code\":\"OMS-SHOP-9\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"杭州但丁云科技有限公司\",\"ebpId\":1,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-14 14:53:00\",\"enable\":1},{\"id\":33,\"name\":\"OMS-泛欧海外专营店\",\"code\":\"OMS-SHOP-6\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"上海寻梦信息技术有限公司\",\"ebpId\":12,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-14 14:51:39\",\"enable\":1},{\"id\":32,\"name\":\"OMS-普罗旺斯国际\",\"code\":\"OMS-SHOP-4\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"杭州但丁云科技有限公司\",\"ebpId\":1,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-14 14:50:24\",\"enable\":1},{\"id\":31,\"name\":\"OMS-奥利派\",\"code\":\"OMS-SHOP-111\",\"actionList\":[\"DECLARE_ORDER\",\"DECLARE_INVENTORY\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"浙江奥利派科技有限公司\",\"ebpId\":20,\"ebcName\":\"浙江奥利派科技有限公司\",\"ebcId\":20,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"浙江奥利派科技有限公司\",\"orderDeclareCompanyId\":20,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-13 14:15:23\",\"enable\":1},{\"id\":30,\"name\":\"OMS-爱客澳新国际\",\"code\":\"OMS-SHOP-16\",\"actionList\":[\"DECLARE_INVENTORY\",\"DECLARE_ORDER\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"广州澳新国际供应链管理有限公司\",\"ebpId\":14,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-10 17:59:33\",\"enable\":1},{\"id\":29,\"name\":\"OMS-醉红酥糖\",\"code\":\"OMS-SHOP-33\",\"actionList\":[\"DECLARE_INVENTORY\",\"DECLARE_ORDER\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"杭州但丁云科技有限公司\",\"ebpId\":1,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-10 17:58:27\",\"enable\":1},{\"id\":28,\"name\":\"OMS-萌娃母婴海外\",\"code\":\"OMS-SHOP-100\",\"actionList\":[\"DECLARE_INVENTORY\",\"DECLARE_ORDER\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"上海寻梦信息技术有限公司\",\"ebpId\":12,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-10 17:57:12\",\"enable\":1},{\"id\":27,\"name\":\"OMS-东立信美妆海外专营店\",\"code\":\"OMS-SHOP-12\",\"actionList\":[\"DECLARE_INVENTORY\",\"DECLARE_ORDER\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"上海寻梦信息技术有限公司\",\"ebpId\":12,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-09 17:50:06\",\"enable\":1},{\"id\":26,\"name\":\"OMS-博杰-每日一淘\",\"code\":\"OMS-SHOP-57\",\"actionList\":[\"DECLARE_INVENTORY\",\"DECLARE_ORDER\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"北京每日一淘共享科技有限公司\",\"ebpId\":11,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-09 17:48:36\",\"enable\":1},{\"id\":25,\"name\":\"OMS-博杰-博杰海外专营店\",\"code\":\"OMS-SHOP-58\",\"actionList\":[\"DECLARE_INVENTORY\",\"DECLARE_ORDER\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"上海寻梦信息技术有限公司\",\"ebpId\":12,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-09 17:47:43\",\"enable\":1},{\"id\":24,\"name\":\"OMS-博杰 -MAJOR GOAL母婴海外专营店\",\"code\":\"OMS-SHOP-67\",\"actionList\":[\"DECLARE_INVENTORY\",\"DECLARE_ORDER\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"上海寻梦信息技术有限公司\",\"ebpId\":12,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-09 17:46:00\",\"enable\":1},{\"id\":23,\"name\":\"OMS-博杰-OCEANZ海外专营店\",\"code\":\"OMS-SHOP-69\",\"actionList\":[\"DECLARE_INVENTORY\",\"DECLARE_ORDER\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"北京每日一淘共享科技有限公司\",\"ebpId\":11,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-09 17:44:37\",\"enable\":1},{\"id\":22,\"name\":\"OMS-金义-訾昶\",\"code\":\"OMS-SHOP-37\",\"actionList\":[\"DECLARE_INVENTORY\",\"DECLARE_ORDER\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"杭州但丁云科技有限公司\",\"ebpId\":1,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-09 17:43:16\",\"enable\":1},{\"id\":21,\"name\":\"OMS-SCI-渝欧店铺\",\"code\":\"OMS-SHOP-49\",\"actionList\":[\"DECLARE_INVENTORY\",\"DECLARE_ORDER\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"重庆渝欧跨境电子商务股份有限公司\",\"ebpId\":10,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-07 11:33:45\",\"enable\":1},{\"id\":20,\"name\":\"OMS-SCI-澳新国际店铺\",\"code\":\"OMS-SHOP-48\",\"actionList\":[\"DECLARE_INVENTORY\",\"DECLARE_ORDER\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"广州澳新国际供应链管理有限公司\",\"ebpId\":14,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-07 11:27:16\",\"enable\":1},{\"id\":19,\"name\":\"OMS-SCI-小小包店铺\",\"code\":\"OMS-SHOP-50\",\"actionList\":[\"DECLARE_INVENTORY\",\"DECLARE_ORDER\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"郑州百宝供应链管理有限公司\",\"ebpId\":13,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-07 11:23:51\",\"enable\":1},{\"id\":18,\"name\":\"OMS-SCI-渝欧店铺\",\"code\":\"OMS-SHOP-52\",\"actionList\":[\"DECLARE_INVENTORY\",\"DECLARE_ORDER\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"重庆渝欧跨境电子商务股份有限公司\",\"ebpId\":10,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-07 11:21:54\",\"enable\":1},{\"id\":17,\"name\":\"OMS-SCI-NAS海外专营店\",\"code\":\"OMS-SHOP-53\",\"actionList\":[\"DECLARE_INVENTORY\",\"DECLARE_ORDER\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"上海寻梦信息技术有限公司\",\"ebpId\":12,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-07 11:21:11\",\"enable\":1},{\"id\":16,\"name\":\"OMS-SCI-联合利华海外旗舰店\",\"code\":\"OMS-SHOP-54\",\"actionList\":[\"DECLARE_INVENTORY\",\"DECLARE_ORDER\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"上海寻梦信息技术有限公司\",\"ebpId\":12,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-07 11:20:21\",\"enable\":1},{\"id\":15,\"name\":\"OMS-SCI-雀巢海外旗舰店\",\"code\":\"OMS-SHOP-55\",\"actionList\":[\"DECLARE_INVENTORY\",\"DECLARE_ORDER\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"上海寻梦信息技术有限公司\",\"ebpId\":12,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-07 11:19:31\",\"enable\":1},{\"id\":14,\"name\":\"OMS-金义-SCI\",\"code\":\"OMS-SHOP-56\",\"actionList\":[\"DECLARE_INVENTORY\",\"DECLARE_ORDER\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"北京每日一淘共享科技有限公司\",\"ebpId\":11,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-07 11:12:25\",\"enable\":1},{\"id\":13,\"name\":\"OMS-益天-每日一淘\",\"code\":\"OMS-SHOP-38\",\"actionList\":[\"DECLARE_INVENTORY\",\"DECLARE_ORDER\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"北京每日一淘共享科技有限公司\",\"ebpId\":11,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-07 11:11:16\",\"enable\":1},{\"id\":12,\"name\":\"OMS-益天-17717368881\",\"code\":\"OMS-SHOP-40\",\"actionList\":[\"DECLARE_INVENTORY\",\"DECLARE_ORDER\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"杭州洋驼网络科技有限公司\",\"ebpId\":18,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-07 10:53:51\",\"enable\":1},{\"id\":11,\"name\":\"OMS-益天-15800971273\",\"code\":\"OMS-SHOP-41\",\"actionList\":[\"DECLARE_INVENTORY\",\"DECLARE_ORDER\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"重庆渝欧跨境电子商务股份有限公司\",\"ebpId\":10,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-07 10:51:31\",\"enable\":1}");
        routeSb.append(",");
        routeSb.append("{\"id\":10,\"name\":\"OMS-益天-益天海带店铺\",\"code\":\"OMS-SHOP-42\",\"actionList\":[\"DECLARE_INVENTORY\",\"DECLARE_ORDER\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"宁波诚淘电子商务有限公司\",\"ebpId\":17,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-07 10:49:22\",\"enable\":1},{\"id\":9,\"name\":\"OMS-益天-益天生活馆海外专营店\",\"code\":\"OMS-SHOP-85\",\"actionList\":[\"DECLARE_INVENTORY\",\"DECLARE_ORDER\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"江苏苏宁易购电子商务有限公司\",\"ebpId\":9,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-07 10:45:13\",\"enable\":1},{\"id\":8,\"name\":\"OMS-益天-Bubs贝儿海外旗舰店\",\"code\":\"OMS-SHOP-86\",\"actionList\":[\"DECLARE_INVENTORY\",\"DECLARE_ORDER\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"江苏苏宁易购电子商务有限公司\",\"ebpId\":9,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-07 10:43:26\",\"enable\":1},{\"id\":7,\"name\":\"OMS-益天-益天海外专营店\",\"code\":\"OMS-SHOP-91\",\"actionList\":[\"DECLARE_INVENTORY\",\"DECLARE_ORDER\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"杭州但丁云科技有限公司\",\"ebpId\":1,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-07 10:41:07\",\"enable\":1},{\"id\":6,\"name\":\"OMS-益天-MOKO海外专营店\",\"code\":\"OMS-SHOP-96\",\"actionList\":[\"DECLARE_INVENTORY\",\"DECLARE_ORDER\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"杭州但丁云科技有限公司\",\"ebpId\":1,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-07 10:37:04\",\"enable\":1},{\"id\":5,\"name\":\"OMS-益天-A2海外旗舰店\",\"code\":\"OMS-SHOP-102\",\"actionList\":[\"DECLARE_INVENTORY\",\"DECLARE_ORDER\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"行吟信息科技(上海)有限公司\",\"ebpId\":8,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-07 10:35:24\",\"enable\":1},{\"id\":4,\"name\":\"OMS-优哥精选\",\"code\":\"OMS-SHOP-88\",\"actionList\":[\"DECLARE_INVENTORY\",\"DECLARE_ORDER\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"金华优海贸易有限公司\",\"ebpId\":7,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-07 10:29:44\",\"enable\":1},{\"id\":3,\"name\":\"OMS-金华旺宝\",\"code\":\"OMS-SHOP-98\",\"actionList\":[\"DECLARE_INVENTORY\",\"DECLARE_ORDER\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"行吟信息科技(上海)有限公司\",\"ebpId\":8,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-07-02 15:53:20\",\"enable\":1},{\"id\":2,\"name\":\"OMS-高歌坦途\",\"code\":\"OMS-SHOP-1\",\"actionList\":[\"DECLARE_INVENTORY\",\"DECLARE_ORDER\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"杭州但丁云科技有限公司\",\"ebpId\":1,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-06-29 10:23:30\",\"enable\":1},{\"id\":1,\"name\":\"oms-但丁国际店铺\",\"code\":\"OMS-SHOP-3\",\"actionList\":[\"DECLARE_INVENTORY\",\"DECLARE_ORDER\"],\"actionDescList\":[\"订单\",\"清单\"],\"ebpName\":\"杭州但丁云科技有限公司\",\"ebpId\":1,\"ebcName\":\"金华凡尔纳供应链管理有限公司\",\"ebcId\":3,\"listDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"listDeclareCompanyId\":3,\"assureCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"orderDeclareCompanyId\":3,\"assureCompanyId\":3,\"customsBookId\":9,\"areaCompanyName\":\"金华凡尔纳供应链管理有限公司\",\"createTime\":\"2020-06-24 10:44:36\",\"enable\":1}");
        routeSb.append("]");
        JSONArray routes = JSON.parseArray(routeSb.toString());
        Map<String,JSONObject> routeMap = new HashMap<>();
        Map<Long, JSONObject> routeLib = new HashMap<>();
        for (Object routeObj : routes) {
            JSONObject route = (JSONObject) routeObj;
            String code = route.getString("code");
            routeMap.put(code,route);
            routeLib.put(route.getLong("id"),route);
        }
        HttpRequest httpRequest = HttpRequest.get("http://ccs.backend.yang800.com/xhr/path/paging?currentPage=1&pageSize=100");
        Map<String, JSONObject> pathMap = new HashMap<>();
        if (httpRequest.ok()){
            String body = httpRequest.body();
            JSONObject pathRes = JSON.parseObject(body);
            JSONObject pathRet = JSON.parseObject(pathRes.getString("result"));
            JSONArray paths = JSON.parseArray(pathRet.getString("dataList"));
            for (Object pathObj : paths) {
                JSONObject path = (JSONObject) pathObj;
                String firstIdentify = path.getString("firstIdentify");
                String secondIdentify = path.getString("secondIdentify");
                String thirdIdentify = path.getString("thirdIdentify");
                JSONObject route = routeLib.get(path.getLong("routeId"));
                pathMap.put(firstIdentify + "|" + secondIdentify + "|" + thirdIdentify, route);
            }
        }

        /**
         * 1.通过店铺ID查找已配置的路径
         * 2.通过仓库、渠道、店铺ID查找已配置的路由与路径
         * 3.比较两者路径是否一致
         */
        JSONObject shopRes = JSON.parseObject(sb.toString());
        JSONObject shopRet = JSON.parseObject(shopRes.getString("result"));
        JSONArray shops = JSON.parseArray(shopRet.getString("dataList"));
        List<String> notRouteList = new ArrayList<>();
        List<String> notPathList = new ArrayList<>();
        List<String> pathMatchList = new ArrayList<>();
        List<String> pathNotMatchList = new ArrayList<>();

        for (Object shopObj : shops) {
            JSONObject shop = (JSONObject) shopObj;
            Long id = shop.getLong("shopId");
            String name = shop.getString("shopName");
            String routeCode = "OMS-SHOP-" + id;
            JSONObject route = routeMap.get(routeCode);
            if (route == null){
                // System.out.println(id + "|" + name + "|" + "无路由");
                notRouteList.add(id + "|" + name + "|" + "无路由");
            }else {
                String routeName = route.getString("name");
                JSONObject pathRoute = pathMap.get("D2019042501274160" + "|" + shop.getString("channelCode") + "|" + id);
                if (pathRoute == null){
                    pathRoute = pathMap.get("D2019042501274160" + "|" + shop.getString("channelCode") + "|");
                }
                if (pathRoute == null){
                    pathRoute = pathMap.get("D2019042501274160" + "|"  + "|" );
                }
                if (pathRoute == null){
                    // System.out.println(id + "|" + name + "|对应路由未匹配");
                    notPathList.add(id + "|" + name + "|对应路由未匹配");
                }else {
                    String origEbp = (String) route.getOrDefault("ebpName","");
                    String origEbc = (String)route.getOrDefault("ebcName","");
                    String origList = (String)route.getOrDefault("listDeclareCompanyName","");
                    String origAssure = (String)route.getOrDefault("assureCompanyName","");
                    String origOrder = (String)route.getOrDefault("orderDeclareCompanyName","");
                    if (origOrder == null){
                        origOrder = "";
                    }
                    String origArea = (String)route.getOrDefault("areaCompanyName","");

                    String Ebp = (String) pathRoute.getOrDefault("ebpName","");
                    String Ebc = (String)pathRoute.getOrDefault("ebcName","");
                    String List = (String)pathRoute.getOrDefault("listDeclareCompanyName","");
                    String Assure = (String)pathRoute.getOrDefault("assureCompanyName","");
                    String Order = (String)pathRoute.getOrDefault("orderDeclareCompanyName","");
                    if (Order == null){
                        Order = "";
                    }
                    String Area = (String)pathRoute.getOrDefault("areaCompanyName","");
                    Boolean match = true;
                    match = match && origEbp.equals(Ebp);
                    match = match && origEbc.equals(Ebc);
                    match = match && origList.equals(List);
                    match = match && origAssure.equals(Assure);
                    if (origOrder == null){
                        return;
                    }
                    match = match && origOrder.equals(Order);
                    match = match && origArea.equals(Area);
                    if (match){
                        pathMatchList.add(id + "|" + name + "|路由找到且匹配");
//                        System.out.println(id + "|" + name + "|路由找到且匹配");
                    }else {
                        pathNotMatchList.add(id + "|" + name + "|路由找到但不匹配");
//                        System.out.println(id + "|" + name + "|路由找到但不匹配");
                    }
                }

            }

        }
        System.out.println("统计:");
        System.out.println("无原始路径:" + notRouteList.size());
        System.out.println("无匹配路由:" + notPathList.size());
        System.out.println("路径不一致:" + pathNotMatchList.size());
        System.out.println("路径有效:" + pathMatchList.size());

        System.out.println("---------------------------");
        for (String s : notPathList) {
            System.out.println(s);
        }System.out.println("---------------------------");
        for (String s : pathNotMatchList) {
            System.out.println(s);
        }System.out.println("---------------------------");
        for (String s : pathMatchList) {
            System.out.println(s);
        }
        System.out.println("---------------------------");
        for (String s : notRouteList) {
            System.out.println(s);
        }

    }

}
