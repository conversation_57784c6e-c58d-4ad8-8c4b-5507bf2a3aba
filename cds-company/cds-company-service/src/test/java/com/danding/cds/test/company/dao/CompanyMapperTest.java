package com.danding.cds.test.company.dao;

import com.alibaba.fastjson.JSON;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.company.api.dto.CompanyDistrictDTO;
import com.danding.cds.company.impl.entity.CompanyDO;
import com.danding.cds.company.impl.mapper.CompanyMapper;
import com.danding.cds.customs.district.api.enums.CustomsDistrictEnum;
import com.danding.cds.test.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Auther: Dante-Gxj
 * @Date: 2020/4/26 14:53
 * @Description:
 */
@Slf4j
public class CompanyMapperTest extends BaseTest {

    @Autowired
    private CompanyMapper companyMapper;

    @Test
    public void insert() {
        int i = 10;
        while (i > 0){
            CompanyDO companyDO = new CompanyDO();
            companyDO.setCode("56782223" + i);
            companyDO.setName("杭州但丁云");
            companyDO.setQualifyJson("[1,2,3]");
            CompanyDistrictDTO districtDTO = new CompanyDistrictDTO();
            districtDTO.setCode(companyDO.getCode());
            districtDTO.setName(companyDO.getName());
            districtDTO.setCustoms(CustomsDistrictEnum.JINYI.getCode());
            companyDO.setDistrictJson(JSON.toJSONString(Lists.newArrayList(districtDTO)));
            companyDO.setExtraJson("{}");
            UserUtils.setCreateAndUpdateBy(companyDO);
            log.info("[op:CompanyMapperTest-insert] result={}", companyMapper.insert(companyDO));
            i--;
        }
    }

    @Test
    public void findById() {
        log.info("{}", JSON.toJSONString(companyMapper.selectByPrimaryKey(1L)));
    }
}
