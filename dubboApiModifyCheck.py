import os
import re
import git
import requests
import json

# dubbo接口类文件路径,会校验是否有删减动作
DUBBO_API_INTERFACE_PATH = set()
# dubbo入参出参类
DUBBO_PARAM_OBJ_CLASS = set()
# GET文件有修改的路径
GIT_DIFF_ALL_PATH = set()
# GET文件有删减动作的路径
GIT_DIFF_REDUCE_PATH = set()
# GIT信息
GIT_MESSAGE = ''


def apiCheck(_path, orgin_branch, target_branch):
    # NO.1 根据Git的比对记录，查找出相对应类的更改
    diff = parseGitDiff(orgin_branch, target_branch)
    if not diff:
        return 'Dubbo接口差异比较，源分支:<font color="info">**{}**</font>,目标分支:<font color="info">**{}**</font>,没有差异'.format(
            orgin_branch, target_branch)
    # NO.2 获取下DUBBO-接口类，以及所有的入参和出参对象
    dubboApiModify(_path)
    # print('Dubbo-Api-接口路径:{}'.format(DUBBO_API_INTERFACE_PATH))
    # print('Dubbo-Api-入参出参类路径:{}'.format(DUBBO_PARAM_OBJ_CLASS))

    # NO.3 遍历下所有的Dubbo接口文件看下是否有删减动作，有就提醒
    DUBBO_INTERFACE_DIFF_SET = set()
    for interface in DUBBO_API_INTERFACE_PATH:
        class_name = interface[interface.rindex("\\") + 1:len(interface)]
        for diff in GIT_DIFF_REDUCE_PATH:
            if diff.__contains__(class_name):
                DUBBO_INTERFACE_DIFF_SET.add(class_name)
                break

    # NO.4 判断Dubbo入参和出参类是否有修改，有修改就提醒
    DUBBO_PARAM_DIFF_SET = set()
    for param in DUBBO_PARAM_OBJ_CLASS:
        for diff in GIT_DIFF_ALL_PATH:
            if diff.__contains__(param):
                DUBBO_PARAM_DIFF_SET.add(param)
                break

    # 返回信息
    message = 'Dubbo接口差异比较，源分支:<font color="info">**{}**</font>,目标分支:<font color="info">**{}**</font>\n'.format(
        orgin_branch, target_branch)
    if DUBBO_INTERFACE_DIFF_SET:
        message += '> 1. Dubbo接口类[目标分支]存在<font color="warning">**删减**</font>请核查:\n  {}\n'.format(
            DUBBO_INTERFACE_DIFF_SET)
    else:
        message += '> 1. Dubbo接口类[目标分支]不存在变更\n'
    if DUBBO_PARAM_DIFF_SET:
        message += '> 2. Dubbo接口方法参数类[目标分支]存在<font color="warning">**修改**</font>请核查:\n  {}\n'.format(
            DUBBO_PARAM_DIFF_SET)
    else:
        message += '> 2. Dubbo接口方法参数类[目标分支]不存在变更\n'
    return message


# 获取API-或是RPC接口的文件夹下所以的Interface
def dubboApiModify(_path):
    list = os.listdir(_path)
    for i in range(len(list)):
        name = list[i]
        name_upper = str.upper(name)
        path = os.path.join(_path, name)
        if os.path.isdir(path):
            # 现认为文件目录("-api"或"-rpc")中的下级目录中的接口都认为是rpc接口
            if name_upper.__contains__("API") | name_upper.__contains__("RPC"):
                getDubboApiInterface(path)
            else:
                dubboApiModify(path)


# 现认为文件目录("-api"或"-rpc")中的下级目录中的接口都认为是rpc接口
def getDubboApiInterface(_path):
    list = os.listdir(_path)
    for i in range(len(list)):
        name = list[i]
        name_upper = str.upper(name)
        path = os.path.join(_path, name)
        if os.path.isfile(path) & name_upper.__contains__(".JAVA"):
            # JAVA_FILE.add(path)
            # 读取文件，并解析方法和Dubbo接口
            addDubboInterface(path)
        elif os.path.isdir(path):
            getDubboApiInterface(path)


def addDubboInterface(_path):
    file = open(_path, mode='r', encoding='utf-8')
    dubbo_interface = False
    while 1:
        line = file.readline()
        if not line:
            break
        # 如果是Dubbo接口，记录下
        if re.match("public\s+interface\s+.*{", line):
            DUBBO_API_INTERFACE_PATH.add(_path)
            dubbo_interface = True
        if not dubbo_interface:
            continue
        # 匹配下方法
        if re.match("\s+.*\(.*\);", line):
            # 截取请求入参数
            addDubboParamToCache(line)
        pass


def parseGitDiff(orign_branch, target_branch):
    length = '--stat=100000'
    GIT_MESSAGE = 'Git分支差异比较，源分支:{},目标分支:{}'.format(orign_branch, target_branch)
    print(GIT_MESSAGE)
    diff = git.diff(orign_branch, target_branch, length)
    # print('diff：{}'.format(diff))
    if not diff:
        GIT_NO_DIFF_MESSAGE = "Git分支差异比较，源分支:{},目标分支:{},没有差异".format(orign_branch, target_branch)
        print(GIT_NO_DIFF_MESSAGE)
        return False
    diff_list = re.split("\n", diff)
    for k, v in enumerate(diff_list):
        if re.match(".*changed.*(insertion(s)?\(+\)|deletion(s)?\(-\))?", v):
            continue
        # 截取类路径 --> InventoryChangeService.java => api/InventoryChangeService.java |    1 -
        data = ''
        if v.__contains__("=>"):
            data = v[v.rindex("=>") + 2:v.rindex("|")].strip()
        else:
            data = v[0:v.rindex("|")].strip()
        # print('data:{}'.format(data))
        change = v[v.rindex("|") + 1:len(v)].strip()
        num = re.findall("\s*[0-9]+\s*", change)[0].strip()
        # print('变更文件:{}，change_num:{},num:{}'.format(data, change_num, num))
        # 如果变更为0，则跳过
        if num == '0':
            continue
        GIT_DIFF_ALL_PATH.add(data)
        # 判断是否有删减 api/InventoryChangeServices.java  |   21 +++++++++++++++++--
        if change.__contains__("-"):
            GIT_DIFF_REDUCE_PATH.add(data)

        # 再次git比对文件，不知道为什么会有错误，先不用着这种方式了
        # file_diff = git.diff(orign_branch, target_branch, length, data)
        # if parseDiffContainReduce(file_diff):
        #     GIT_DIFF_REDUCE_PATH.add(data)
    # print('变更文件:{},注意如下文件有删除操作：{}'.format(GIT_DIFF_ALL_PATH, GIT_DIFF_REDUCE_PATH))
    return True


# 解析
def parseDiffContainReduce(file_diff):
    diff_list = re.split("\n", file_diff)
    reduce = False
    for k, v in enumerate(diff_list):
        if re.match(".*changed.*(deletion(s)?\(-\))", v):
            reduce = True
    return reduce


def addDubboParamToCache(method):
    # 方法请求参数
    # 截取入参
    in_param = method[method.rindex("(") + 1:method.rindex(")")].strip().split(",")
    for _class in in_param:
        _class = _class.strip()
        if not _class:
            continue
        # 如果是泛型 List<A<T>> a
        if _class.__contains__("<") & _class.__contains__(">"):
            _class = _class[0:_class.rindex(">") + 1]
            for data in _class.split("<"):
                data_strip = data.strip()
                if data_strip.__contains__(">"):
                    data_strip = data_strip[0:data_strip.index(">")]
                addObjFile(data_strip)
        elif _class.__contains__("<"):
            for data in _class.split("<"):
                addObjFile(data.strip())
        elif _class.__contains__(">"):
            d = _class[0:_class.index(">")]
            addObjFile(d)
        # 否则直接认为是对象 A a
        else:
            addObjFile(re.findall("[a-zA-Z0-9_]+\s+", _class)[0].strip())

    # 截取返回参数 RpcResult<List<JSONObject>> manualPull(Date beginTime, List<M> endTime, JdGoodsRecordType type);
    # 截取返回参数 RpcResult manualPull(Date beginTime, List<M> endTime, JdGoodsRecordType type);
    _math = re.compile("[a-zA-Z0-9_]+\s*\(.*\)\s*;$")
    _method_param = re.findall(_math, method)[0]
    out_param_str = method[0:method.rindex(_method_param)].strip()
    out_param = out_param_str.split("<")
    for param in out_param:
        _param_strip = param.strip()
        if _param_strip.__contains__(">"):
            temp = _param_strip[0:_param_strip.index(">")].strip()
            if temp.__contains__(","):
                for d in temp.split(","):
                    d_strip = d.strip()
                    if d_strip:
                        addObjFile(d_strip)
            else:
                addObjFile(temp)
        else:
            addObjFile(_param_strip)


def invokeAddParamToCache(in_param_pattern, in_param_obj_pattern, method):
    in_param_match = re.findall(in_param_pattern, method)
    if in_param_match:
        _in_param_match = in_param_match[0].strip().lstrip("(")
        generic = _in_param_match[0:_in_param_match.index("<")].strip()
        addObjFile(generic)
        class_generic = _in_param_match[_in_param_match.index("<") + 1:_in_param_match.index(">")].strip()
        addObjFile(class_generic)
    else:
        obj_val = re.findall(in_param_obj_pattern, method)
        if obj_val:
            _obj_val = obj_val[0].strip().lstrip("(")
            addObjFile(_obj_val)


def addObjFile(_class):
    DUBBO_PARAM_OBJ_CLASS.add('{}.java'.format(_class))


if __name__ == "__main__":
    # GIT配置
    curr_path = os.getcwd()
    # print('[当前路径]:{}'.format(curr_path))
    repo = git.Repo(curr_path)
    git = repo.git
    _message = apiCheck(curr_path, "master", repo.active_branch)
    print('[校验结果]:{}'.format(_message))
    webhook = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=5f9f05e2-c4ef-449f-bb22-f4c67dc7ce20'
    message_dict = {}
    message_dict["msgtype"] = 'markdown'
    content_dict = {}
    content_dict['content'] = _message
    message_dict['markdown'] = content_dict
    json_data = json.dumps(message_dict, sort_keys=True, ensure_ascii=False)
    requests.post(webhook, json=message_dict)
