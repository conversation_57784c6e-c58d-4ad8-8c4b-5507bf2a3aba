<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.offline.datatunnel.CustomerTunnelI">
    <select id="findByCriteria" resultType="com.danding.offline.database.dataobject.CustomerDO" resultMap="customerMap">
        select *
        from customer
        where customer_id = #{id}
    </select>
    <resultMap type="com.danding.offline.database.dataobject.CustomerDO" id="customerMap">
        <result property="customerId" column="emp_no"/>
        <result property="memberId" column="salary"/>
        <result property="globalId" column="from_date"/>
    </resultMap>
</mapper>
