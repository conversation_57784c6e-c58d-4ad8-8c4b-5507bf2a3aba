package ${webRpcImplPackage};

import io.swagger.annotations.ApiModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.config.annotation.DubboReference;
/**
* <p>
    * ${table.comment!} 服务实现类
    * </p>
*
* <AUTHOR>
* @since ${date}
* @menu ${table.comment!}
*/
@Slf4j
@ApiModel("${table.comment!}")
@DubboService(version = "${"$"}{dubbo.service.version}")
public class ${webRpcImplClassName} implements ${webRpcClassName} {

@DubboReference
private ${serviceClassName} ${serviceClassName?substring(0,1)?lower_case}${serviceClassName?substring(2)};

}
