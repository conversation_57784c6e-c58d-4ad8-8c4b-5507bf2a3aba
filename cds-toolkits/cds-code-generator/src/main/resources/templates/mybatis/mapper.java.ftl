package ${mapperPackage};

import com.danding.logistics.mybatis.common.Mapper;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import tk.mybatis.mapper.additional.insert.InsertListMapper;

/**
* <p>
    * ${table.comment!} Mapper 接口
    * </p>
*
* <AUTHOR>
* @since ${date}
*/
public interface ${mapperClassName} extends Mapper<${entityClassName}>, InsertListMapper<${entityClassName}>,BatchUpdateMapper<${entityClassName}>, AggregationPlusMapper<${entityClassName}>{

}
