package ${baseServicePackage};

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.danding.cds.common.user.UserUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
* <p>
    * ${table.comment!} baseService 接口
    * </p>
*
* <AUTHOR>
* @since ${date}
*/
@Service
public class ${baseServiceClassName}{

@Resource
private ${mapperClassName} mapper;

public ${entityClassName} findById(Long id) {
if (Objects.isNull(id)) {
return null;
}
return this.selectByPrimaryKey(id);
}

public List<${entityClassName}> findById(List<${r"Long"}> idList) {
if (CollUtil.isEmpty(idList)) {
return null;
}
Example example = new Example(${entityClassName}.class);
example.createCriteria().andIn("id", idList);
return this.selectByExample(example);
}

public ${entityClassName} selectByPrimaryKey(Long id) {
return mapper.selectByPrimaryKey(id);
}

public List<${entityClassName}> selectByExample(Example example) {
return mapper.selectByExample(example);
}

public ${entityClassName} selectOneByExample(Example example) {
return mapper.selectOneByExample(example);
}

public void updateByPrimaryKey(${entityClassName} record) {
mapper.updateByPrimaryKey(record);
}

public void updateByPrimaryKeySelective(${entityClassName} record) {
mapper.updateByPrimaryKeySelective(record);
}

public void updateByExample(${entityClassName} record, Example example) {
mapper.updateByExample(record, example);
}

public void updateByExampleSelective(${entityClassName} record, Example example) {
mapper.updateByExampleSelective(record, example);
}

public Integer selectCountByExample(Example example) {
return mapper.selectCountByExample(example);
}

public void insertSelective(${entityClassName} record) {
UserUtils.setCommonData(record);
mapper.insertSelective(record);
}

}