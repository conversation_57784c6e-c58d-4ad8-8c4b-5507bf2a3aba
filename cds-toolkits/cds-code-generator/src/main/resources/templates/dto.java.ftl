package ${dtoPackage};

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;

/**
* <p>
    * ${table.comment!}
    * </p>
*
* <AUTHOR>
* @since ${date}
*/
@Data
@Accessors(chain = true)
@ApiModel(value="${entity}对象", description="${table.comment!}")
public class ${dtoClassName} implements Serializable{

<#-- ----------  BEGIN 字段循环遍历  ---------->
<#list table.fields as field>

    /**
    * ${field.comment}
    */
    @ApiModelProperty(value = "${field.comment}")
    private ${field.propertyType} ${field.propertyName};
</#list>

<#------------  END 字段循环遍历  ---------->
@ApiModelProperty("创建时间")
private Date createTime;
@ApiModelProperty("更新时间")
private Date updateTime;
@ApiModelProperty("创建人ID")
private Integer createBy;
@ApiModelProperty("更新人ID")
private Integer updateBy;
@ApiModelProperty("逻辑删除")
private Boolean deleted = false;
@ApiModelProperty("id")
private Long id;
}
