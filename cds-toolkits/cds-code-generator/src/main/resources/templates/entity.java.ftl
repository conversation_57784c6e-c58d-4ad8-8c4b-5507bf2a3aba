package ${entityPackage};

import com.danding.cds.common.model.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.Table;

/**
* <p>
    * ${table.comment!}
    * </p>
*
* <AUTHOR>
* @since ${date}
*/
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="${entity}对象", description="${table.comment!}")
@Table(name = "${table.name}")
public class ${entityClassName} extends BaseDO {

private static final long serialVersionUID = 1L;

<#-- ----------  BEGIN 字段循环遍历  ---------->
<#list table.fields as field>

    /**
    * ${field.comment}
    */
    @ApiModelProperty(value = "${field.comment}")
    private ${field.propertyType} ${field.propertyName};
</#list>

<#------------  END 字段循环遍历  ---------->
}
