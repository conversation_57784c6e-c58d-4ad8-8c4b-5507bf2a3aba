package com.danding.cds.generator.templates.impl;

import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.danding.cds.generator.templates.FileTemplate;
import com.danding.cds.generator.util.CodeConstant;
import lombok.Data;

@Data
public class Xml implements FileTemplate {

    @Override
    public String templates() {
        return "/templates/mybatis/mapper.xml.ftl";
    }

    @Override
    public String packageName() {
        return "mapper." + CodeConstant.MODULE_NAME;
    }

    @Override
    public String variablePrefix() {
        return "xml";
    }

    @Override
    public String className() {
        return CodeConstant.ENTITY_NAME.get() + "Mapper";
    }

    @Override
    public String fileName() {
        return className() + StringPool.DOT_XML;
    }

    /**
     * 包路径
     *
     * @return
     */
    @Override
    public String srcMain() {
        return "/src/main/resources/";
    }
}