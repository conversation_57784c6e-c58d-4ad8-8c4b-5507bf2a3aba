package com.danding.cds.generator.templates.impl;

import com.danding.cds.generator.templates.FileTemplate;
import com.danding.cds.generator.util.CodeConstant;
import lombok.Data;

@Data
public class DTO implements FileTemplate {

    @Override
    public String templates() {
        return "/templates/dto.java.ftl";
    }

    @Override
    public String packageName() {
        return CodeConstant.PACKAGE_NAME + "." + CodeConstant.MODULE_NAME + ".dto.data";
    }

    @Override
    public String className() {
        // BaseRecordDO.java
        return CodeConstant.ENTITY_NAME.get() + "DTO";
    }

    @Override
    public String variablePrefix() {
        return "dto";
    }
}
