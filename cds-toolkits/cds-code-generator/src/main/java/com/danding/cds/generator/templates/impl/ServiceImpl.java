package com.danding.cds.generator.templates.impl;

import com.danding.cds.generator.templates.FileTemplate;
import com.danding.cds.generator.util.CodeConstant;
import lombok.Data;

@Data
public class ServiceImpl implements FileTemplate {

    @Override
    public String templates() {
        return "/templates/serviceImpl.java.ftl";
    }

    @Override
    public String packageName() {
        return CodeConstant.PACKAGE_NAME + "." + CodeConstant.MODULE_NAME;
    }

    @Override
    public String variablePrefix() {
        return "serviceImpl";
    }

    @Override
    public String className() {
        return CodeConstant.ENTITY_NAME.get() + "ServiceImpl";
    }

}