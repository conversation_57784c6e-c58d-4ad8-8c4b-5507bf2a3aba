package com.danding.cds.generator.templates.impl;

import com.danding.cds.generator.templates.FileTemplate;
import com.danding.cds.generator.util.CodeConstant;
import lombok.Data;

@Data
public class WebRpc implements FileTemplate {

    @Override
    public String templates() {
        return "/templates/webRpc.java.ftl";
    }

    @Override
    public String packageName() {
        return CodeConstant.PACKAGE_NAME + ".web." + CodeConstant.MODULE_NAME;
    }

    @Override
    public String variablePrefix() {
        return "webRpc";
    }

    @Override
    public String className() {
        return CodeConstant.ENTITY_NAME.get() + "WebRpc";
    }
}