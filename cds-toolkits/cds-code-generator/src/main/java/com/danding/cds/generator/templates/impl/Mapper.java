package com.danding.cds.generator.templates.impl;

import com.danding.cds.generator.templates.FileTemplate;
import com.danding.cds.generator.util.CodeConstant;
import lombok.Data;

@Data
public class Mapper implements FileTemplate {

    @Override
    public String templates() {
        return "/templates/mybatis/mapper.java.ftl";
    }

    @Override
    public String packageName() {
        return CodeConstant.PACKAGE_NAME + ".database." + CodeConstant.MODULE_NAME + ".mapper";
    }

    @Override
    public String variablePrefix() {
        return "mapper";
    }

    @Override
    public String className() {
        return CodeConstant.ENTITY_NAME.get() + "Mapper";
    }
}