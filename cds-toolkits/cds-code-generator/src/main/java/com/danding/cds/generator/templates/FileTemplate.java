package com.danding.cds.generator.templates;

import com.baomidou.mybatisplus.core.toolkit.StringPool;

public interface FileTemplate {

    /**
     * 模板地址 resources下
     *
     * @return
     */
    String templates();

    /**
     * 类名
     *
     * @return
     */
    String className();

    /**
     * 包名
     *
     * @return
     */
    String packageName();

    /**
     * 该文件类型赋予模板变量的统一前缀
     *
     * @return
     */
    String variablePrefix();

    /**
     * 包路径
     *
     * @return
     */
    default String packagePath() {
        return srcMain() + packageName().replace(".", "/");
    }

    /**
     * 包路径
     *
     * @return
     */
    default String srcMain() {
        return "/src/main/java/";
    }

    /**
     * 类全名，用于被其他文件引用
     *
     * @return
     */
    default String importPackage() {
        return packageName() + "." + className();
    }

    /**
     * java文件名
     *
     * @return
     */
    default String fileName() {
        return className() + StringPool.DOT_JAVA;
    }

    /**
     * 模板变量的引用名： 类名
     *
     * @return
     */
    default String keyClassName() {
        return variablePrefix() + "ClassName";
    }

    /**
     * 模板变量的引用名： 包名
     *
     * @return
     */
    default String keyPackage() {
        return variablePrefix() + "Package";
    }

    /**
     * 模板变量的引用名： 包名.类名
     *
     * @return
     */
    default String keyImport() {
        return variablePrefix() + "Import";
    }
}
