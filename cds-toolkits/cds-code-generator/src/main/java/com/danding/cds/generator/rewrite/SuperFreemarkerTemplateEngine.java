package com.danding.cds.generator.rewrite;

import com.baomidou.mybatisplus.generator.config.po.TableInfo;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.util.Map;

/**
 * Freemarker 模板引擎实现文件输出
 */
public class SuperFreemarkerTemplateEngine extends FreemarkerTemplateEngine {

    /**
     * 输出自定义模板文件： 原自定义输出路径有问题：含entity类名
     *
     * @param customFile 自定义配置模板文件信息
     * @param tableInfo  表信息
     * @param objectMap  渲染数据
     * @since 3.5.1
     */
    @Override
    protected void outputCustomFile(@NotNull Map<String, String> customFile, @NotNull TableInfo tableInfo, @NotNull Map<String, Object> objectMap) {
        customFile.forEach((key, value) -> {
            // key： 完整输出路径  value：模板地址
            outputFile(new File(key), objectMap, value, getConfigBuilder().getInjectionConfig().isFileOverride());
        });
    }
}
