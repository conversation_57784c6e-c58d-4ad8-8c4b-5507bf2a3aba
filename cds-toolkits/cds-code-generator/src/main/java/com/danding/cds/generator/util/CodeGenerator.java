package com.danding.cds.generator.util;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.fill.Column;
import com.baomidou.mybatisplus.generator.fill.Property;
import com.danding.cds.generator.rewrite.SuperFastAutoGenerator;
import com.danding.cds.generator.rewrite.SuperFreemarkerTemplateEngine;
import com.danding.cds.generator.templates.FileTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public abstract class CodeGenerator {

    /**
     * 要生成那些文件、以及对应的文件模板
     *
     * @return
     */
    protected abstract Map<Class<? extends FileTemplate>, String> moduleFullName();

    /**
     * 数据库名
     *
     * @return
     */
    protected abstract String dbName();

    /**
     * 包名
     *
     * @return
     */
    protected abstract String packageName();

    /**
     * entity父类
     *
     * @return
     */
    protected abstract String baseEntityClass();

    /**
     * mapping父类
     *
     * @return
     */
    protected abstract String baseMapping();

    protected abstract List<String> entityFields();

    /**
     * 表前缀
     *
     * @return
     */
    protected abstract String tablePrefix();

    private SuperFastAutoGenerator fastAutoGenerator;

    public void start() {
        // 覆盖dbName
        CodeConstant.DRIVER_URL = CodeConstant.DRIVER_URL.replace("DB_NAME", dbName());
        fastAutoGenerator = SuperFastAutoGenerator.create(CodeConstant.DRIVER_URL, CodeConstant.USERNAME, CodeConstant.PASSWORD);

        setGlobalConfig();
        setPackageConfig();
        templateConfig();
        setStrategyConfig();
        setInjectionConfig();
        templateEngine();
        fastAutoGenerator.execute();

    }

    /**
     * 全局配置
     */
    private void setGlobalConfig() {
        fastAutoGenerator.globalConfig(builder ->
                builder
                        // 开启 swagger 模式
                        .enableSwagger()
                        // 时间策略
                        .dateType(DateType.ONLY_DATE)
                        // 作者
                        .author(CodeConstant.AUTHOR)
                        // 注释日期，默认值: yyyy-MM-dd
                        .commentDate("yyyy-MM-dd")
                        .disableOpenDir()
                        .build()
        );
    }


    /**
     * 包配置
     *
     * @return
     */
    private void setPackageConfig() {
        //全使用自定义输出、可不设置包配置
//        fastAutoGenerator.packageConfig(builder -> {
//            // 设置父包名
//            builder.parent(CodeConstant.PACKAGE_NAME)
//                    // 设置父包模块名
//                    .moduleName(CodeConstant.MODULE_NAME)
////                    .serviceImpl("service")
////                    .mapper("mapper")
//                    .other("")
//            ;
//        });
    }

    /**
     * 基本模板配置
     */
    public void templateConfig() {
        fastAutoGenerator.templateConfig((scanner, builder) -> builder
                .disable()
        );
    }

    /**
     * 策略配置
     *
     * @return
     */
    private void setStrategyConfig() {
        fastAutoGenerator.strategyConfig(builder -> {
            builder.addInclude(CodeConstant.TABLE_NAME)
                    .addTablePrefix(tablePrefix())
                    .addTableSuffix(CodeConstant.TABLE_SUFFIX)
                    .entityBuilder()
                    .superClass(baseEntityClass())
//                    .disableSerialVersionUID()
                    // 开启 lombok 模型
                    .enableLombok()
                    // 开启 Boolean 类型字段移除 is 前缀
//                    .enableRemoveIsPrefix()
                    // 开启生成实体时生成字段注解
                    .enableTableFieldAnnotation()
//                    .enableActiveRecord()
//                    .versionColumnName("version")
//                    .versionPropertyName("version")
//                    .logicDeleteColumnName("deleted")
//                    .logicDeletePropertyName("deleted")
                    .naming(NamingStrategy.underline_to_camel)
                    .columnNaming(NamingStrategy.underline_to_camel)
                    // 父类公共的字段，生产字段对象时不放入
                    .addSuperEntityColumns(entityFields())
                    // 忽略的字段，生产字段对象时不放入
//                    .addIgnoreColumns("create_by", "update_by")
                    .addTableFills(new Column("create_time", FieldFill.INSERT))
                    .addTableFills(new Property("update_time", FieldFill.INSERT_UPDATE))
                    .idType(IdType.AUTO)
            ;
        });
    }


    /**
     * 配置模板引擎
     */
    public void templateEngine() {
        fastAutoGenerator.templateEngine(new SuperFreemarkerTemplateEngine());
    }

    /**
     *
     */
    private void setInjectionConfig() {
        Map<String, Object> fieldMap = new HashMap<>();
        Map<String, String> fileMap = new HashMap<>();


        fastAutoGenerator.injectionConfig(builder -> {
            builder
                    // 输出文件之前消费者
                    .beforeOutputFile((tableInfo, objectMap) -> {
                        // 设置entityName
                        CodeConstant.ENTITY_NAME.set(tableInfo.getEntityName());

                        Map<String, Object> aPackageMap = (Map) objectMap.get("package");
                        objectMap.put("baseEntityPackageName", baseEntityClass());
                        objectMap.put("baseMappingImport", baseMapping());
                        objectMap.put("packageName", packageName());

                        Map<Class<? extends FileTemplate>, String> fileClassMap = moduleFullName();
                        for (Map.Entry<Class<? extends FileTemplate>, String> entry : fileClassMap.entrySet()) {
                            Class<? extends FileTemplate> fileClass = entry.getKey();
                            String projectPath = entry.getValue();

                            FileTemplate file = getFileTemplate(fileClass);
//                            String filePath = CodeConstant.RELATIVE_PATH + projectPath + file.packagePath() + "/" + file.fileName();
                            String filePath = CodeConstant.RELATIVE_PATH + "/generate/" + projectPath + "/" + file.fileName();
                            String templatesPath = file.templates();

                            fileMap.put(filePath, templatesPath);
                            System.out.println("生成文件地址： " + filePath);

                            objectMap.put(file.keyClassName(), file.className());
                            objectMap.put(file.keyPackage(), file.packageName());
                            objectMap.put(file.keyImport(), file.importPackage());
                        }
                    })
                    // 自定义属性，模板变量
                    .customMap(fieldMap)
                    .customFile(fileMap);

            // 是否覆盖已有的文件
            if (CodeConstant.FILE_OVERRIDE) {
                builder.fileOverride();
            }
        });
    }

    private FileTemplate getFileTemplate(Class<? extends FileTemplate> fileClass) {
        try {
            return fileClass.newInstance();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }
}
