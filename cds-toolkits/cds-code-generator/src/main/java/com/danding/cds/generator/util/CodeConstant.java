package com.danding.cds.generator.util;

import com.baomidou.mybatisplus.core.exceptions.MybatisPlusException;
import org.apache.commons.lang3.StringUtils;

import java.util.Scanner;

public class CodeConstant {

    /**
     * 新生成的文件是否覆盖当前的文件
     */
    public static Boolean FILE_OVERRIDE = true;
//    public static Boolean FILE_OVERRIDE = Boolean.valueOf(scanner("是否覆盖当前文件: 覆盖输入1 不覆盖输入0"));

    /**
     * 作者： 不想输入的话可以提供默认值
     */
    public static String AUTHOR = "CodeGenerator";
//    public static String AUTHOR = scanner("请输入作者名");

    /**
     * 模块包名
     */
    public static String MODULE_NAME = scanner("请输入代码生成的模块名");

    /**
     * 数据库URL
     */
    public static String DRIVER_URL =
            "****************************************************************************************************************************************************";
    /**
     * 数据库账号
     */
    public static String USERNAME = "root";
    /**
     * 数据库密码
     */
    public static String PASSWORD = "Dd82727893!p";
    /**
     * 文件绝对路径
     */
    public static String PROJECT_PATH = System.getProperty("user.dir");
    // 当前应用路径
    public static String RELATIVE_PATH = System.getProperty("user.dir") + "/cds-toolkits/cds-code-generator";
    /**
     * 模块包名
     */
    public static String TABLE_NAME = scanner("请输入目标表名");

    public static ThreadLocal<String> ENTITY_NAME = new ThreadLocal<>();

    // domian 包路径
    public static String PACKAGE_NAME = "";

    // 表前缀：生产实体类名时会排除
//    public static String TABLE_PREFIX = "p_";

    // 表后缀：生产实体类名时会排除
    public static String TABLE_SUFFIX = "";

    /**
     * <p>
     * 读取控制台内容
     * </p>
     */
    public static String scanner(String tip) {
        Scanner scanner = new Scanner(System.in);
        StringBuilder help = new StringBuilder();
        help.append(tip + "：");
        System.out.println(help);
        if (scanner.hasNext()) {
            String ipt = scanner.next();
            if (StringUtils.isNotEmpty(ipt)) {
                return ipt;
            }
        }
        throw new MybatisPlusException("请输入正确的" + tip + "！");
    }
}
