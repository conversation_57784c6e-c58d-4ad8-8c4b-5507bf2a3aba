package com.danding.cds.generator.templates;


import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.common.special.InsertListMapper;

public interface BaseCommonMapper<T> extends Mapper<T>, InsertListMapper<T>, BatchUpdateMapper<T>, AggregationPlusMapper<T> {

}