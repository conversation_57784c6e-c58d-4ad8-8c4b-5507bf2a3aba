package com.danding.cds.generator.templates.impl;

import com.danding.cds.generator.templates.FileTemplate;
import com.danding.cds.generator.util.CodeConstant;
import lombok.Data;

@Data
public class Service implements FileTemplate {

    @Override
    public String templates() {
        return "/templates/service.java.ftl";
    }

    @Override
    public String packageName() {
        return CodeConstant.PACKAGE_NAME + "." + CodeConstant.MODULE_NAME + ".api";
    }

    @Override
    public String variablePrefix() {
        return "service";
    }

    @Override
    public String className() {
        return CodeConstant.ENTITY_NAME.get() + "Service";
    }

}