package com.danding.cds.generator.templates.impl;

import com.danding.cds.generator.templates.FileTemplate;
import com.danding.cds.generator.util.CodeConstant;
import lombok.Data;

@Data
public class BaseService implements FileTemplate {

    @Override
    public String templates() {
        return "/templates/baseService.java.ftl";
    }

    @Override
    public String packageName() {
        return CodeConstant.PACKAGE_NAME + ".domain." + CodeConstant.MODULE_NAME + ".gateway";
    }

    @Override
    public String variablePrefix() {
        return "baseService";
    }

    @Override
    public String className() {
        return CodeConstant.ENTITY_NAME.get() + "BaseService";
    }

}