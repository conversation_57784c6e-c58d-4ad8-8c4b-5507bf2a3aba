package com.danding.cds.generator.templates.impl;

import com.danding.cds.generator.templates.FileTemplate;
import com.danding.cds.generator.util.CodeConstant;
import lombok.Data;

@Data
public class Entity implements FileTemplate {

    @Override
    public String templates() {
        return "/templates/entity.java.ftl";
    }

    @Override
    public String packageName() {
        return CodeConstant.PACKAGE_NAME + ".database." + CodeConstant.MODULE_NAME + ".entity";
    }

    @Override
    public String variablePrefix() {
        return "entity";
    }

    @Override
    public String className() {
        return CodeConstant.ENTITY_NAME.get() + "DO";
    }

}
