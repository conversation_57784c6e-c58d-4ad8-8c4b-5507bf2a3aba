package com.danding.cds.generator;

import com.danding.cds.generator.templates.FileTemplate;
import com.danding.cds.generator.templates.impl.*;
import com.danding.cds.generator.util.CodeConstant;
import com.danding.cds.generator.util.CodeGenerator;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ProjectCodeGenerator extends CodeGenerator {

    /**
     * BASE_SERVICE
     */
    public static String BASE_SERVICE = "/baseService";
    /**
     * SERVICE
     */
    public static String SERVICE = "/service";
    /**
     * SERVICE_API
     */
    public static String SERVICE_API = "/serviceApi";

    /**
     * WEB
     */
    public static String WEB = "/web";

    @Override
    public Map<Class<? extends FileTemplate>, String> moduleFullName() {
        Map<Class<? extends FileTemplate>, String> map = new HashMap<>();


        map.put(WebRpc.class, WEB);
        map.put(WebRpcImpl.class, WEB);

        map.put(DTO.class, SERVICE_API);
        map.put(Service.class, SERVICE_API);
        map.put(ServiceImpl.class, SERVICE);

        map.put(BaseService.class, BASE_SERVICE);
        map.put(Entity.class, BASE_SERVICE);
        map.put(Mapper.class, BASE_SERVICE);
        map.put(Xml.class, BASE_SERVICE);
        return map;
    }

    @Override
    protected String dbName() {
        return "ccs_item";
    }

    @Override
    protected String packageName() {
        return "com.danding.cds.generator";
    }

    @Override
    protected String baseEntityClass() {
        return "com.danding.cds.common.model.BaseDO";
    }

    @Override
    protected String baseMapping() {
        return "com.danding.cds.generator.templates.BaseCommonMapper";
    }

    @Override
    protected String tablePrefix() {
        return "ccs_";
    }

    @Override
    protected List<String> entityFields() {
        List<String> entityFields = new ArrayList<>();
        entityFields.add("id");
        entityFields.add("create_by");
        entityFields.add("create_time");
        entityFields.add("update_by");
        entityFields.add("update_time");
        entityFields.add("deleted");
        return entityFields;
    }

    public static void main(String[] args) {
        ProjectCodeGenerator distribution = new ProjectCodeGenerator();
        CodeConstant.PACKAGE_NAME = new ProjectCodeGenerator().packageName();
        distribution.start();
    }
}
