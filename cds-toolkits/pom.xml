<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>cds-center</artifactId>
        <groupId>com.danding</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>cds-toolkits</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>cds-stock-helper</module>
        <module>cds-snow-flake</module>
        <module>cds-exception</module>
        <module>cds-es-component</module>
        <module>cds-track-log-helper</module>
        <module>cds-jcq-component</module>
        <module>cds-mq-component</module>
        <module>cds-http-saas-component</module>
        <module>cds-xxl-saas-component</module>
        <module>cds-thread-pool-monitor-component</module>
        <module>cds-es-search-component</module>
        <module>cds-log-component</module>
    </modules>

</project>