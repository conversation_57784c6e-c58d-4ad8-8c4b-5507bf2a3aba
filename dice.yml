version: '2.0'

#addons:
#  terminus-elasticsearch:
#    plan: terminus-elasticsearch:basic
#    options:
#      version: 6.2.2
#  redis:
#    plan: redis:basic
#    options:
#      version: 3.2.12
#  rocketmq:
#    plan: rocketmq:basic
#    options:
#      version: 4.3.0

envs:
  SEATA_ENV: pro #seata环境配置
  ENV: PRO  #apollo环境配置

services:
  cds-fen-service:
    ports:
      - 8088
    expose:
      - 8088
    resources:
      cpu: 0.5
      mem: 1024
    deployments:
      replicas: 1

  cds-company-service:
    resources:
      cpu: 0.5
      mem: 1024
    deployments:
      replicas: 1

  cds-item-service:
    resources:
      cpu: 0.5
      mem: 1024
    deployments:
      replicas: 1

  cds-order-service:
    resources:
      cpu: 1
      mem: 2048
    deployments:
      replicas: 1

  cds-web:
    ports:
      - 8083
    expose:
      - 8083
    resources:
      cpu: 0.5
      mem: 1024
    deployments:
      replicas: 1